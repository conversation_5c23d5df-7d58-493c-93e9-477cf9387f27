
// src/components/SettingsPage.tsx
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
// useSettings hook import removed
import type { AppSettings, Project, KnowledgeTome, ModelOption, AgentCoreSettingId, CoreMemory, CoreMemoryPersonaTarget, CoreMemoryImportance, GlobalQuickCommandItem, CMSItemBase, CMSType, RolePlayingCard, LinLuoDetailedStatus, PaginationOptions, PropAsset, CostumeAsset, PoseAsset, LinLuoBodyDevelopment } from '../types';
import { AVAILABLE_EMBEDDING_MODELS, DEFAULT_SETTINGS } from '@/config/globalConfig'; 
import { Icon } from '@/components/common/Icon';
import { SmartIcon, VisualHeading, VisualContainer } from '@/components/common/VisualUtils';
import { CMSManagementModal } from '@/components/cms/CMSManagementModal';
import { AchievementHallModal } from '@/components/AchievementHallModal';
import { AssetManagementModal } from '@/components/AssetManagementModal';
import { runTrainingRoomDiagnostics } from '@/utils/trainingRoomDiagnostics';
import { GenericModal } from './GenericModal';
import { CoreMemoryManagement } from './settings/CoreMemoryManagement'; 
import { PersonnelAssignmentPage } from './settings/PersonnelAssignmentPage'; 


interface SettingsPageProps {
  settings: AppSettings; // Settings now come as a prop
  setSettings: (value: AppSettings | ((prevState: AppSettings) => AppSettings)) => void; 
  projects: Project[]; 
  globalKnowledgeTomes: KnowledgeTome[]; 
  availableChatModels: ModelOption[]; 
  onAddCoreMemory: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => Promise<CoreMemory | null>;
}

interface SettingSectionProps { 
  title: string;
  iconName: string;
  children: React.ReactNode;
  description?: string; 
  customHeaderContent?: React.ReactNode;
}

const SettingSection: React.FC<SettingSectionProps> = ({ title, iconName, children, description, customHeaderContent }) => (
  <VisualContainer level="secondary" variant="card" className="mb-8">
    <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <SmartIcon name={iconName as any} level="accent" context="content" className="mr-4" />
          <VisualHeading level="primary" size="xl">{title}</VisualHeading>
        </div>
        {customHeaderContent}
    </div>
    {description && <p className="text-sm text-tg-text-secondary mb-6 leading-relaxed">{description}</p>}
    <div className="space-y-4">{children}</div>
  </VisualContainer>
);

const SettingItem: React.FC<{ label: string; children: React.ReactNode; description?: string }> = ({ label, children, description }) => (
 <div className="mb-5">
    <label className="block text-sm font-medium mb-1.5 text-tg-text-primary">{label}</label>
    {children}
    {description && <p className="text-xs mt-1.5 text-tg-text-secondary">{description}</p>}
  </div>
);

interface AgentCoreSettingEditorProps {
  settingId: AgentCoreSettingId;
  label: string;
  placeholder: string;
  initialContent?: string;
  lastUpdated?: string;
  onSave: (settingId: AgentCoreSettingId, content: string) => Promise<void>;
  onClose: () => void;
}

const AgentCoreSettingEditor: React.FC<AgentCoreSettingEditorProps> = ({
  settingId, label, placeholder, initialContent = '', lastUpdated, onSave, onClose
}) => {
  const [content, setContent] = useState(initialContent);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  
  useEffect(() => {
    setContent(initialContent);
    setSaveStatus('idle');
  }, [initialContent, settingId]);

  const handleSave = async () => {
    setSaveStatus('saving');
    try {
      await onSave(settingId, content);
      setSaveStatus('saved');
      setTimeout(() => {
        setSaveStatus('idle');
        onClose(); 
      }, 1500);
    } catch (e) {
      console.error(`Failed to save ${settingId}:`, e);
      setSaveStatus('error');
    }
  };

  return (
    <div className="flex flex-col h-full">
      <textarea
        id={`agent-core-editor-${settingId}`}
        value={content}
        onChange={e => { setContent(e.target.value); if(saveStatus !== 'idle') setSaveStatus('idle');}}
        placeholder={placeholder}
        className="w-full p-2.5 bg-tg-bg-primary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30 text-sm min-h-[300px] flex-grow resize-y"
        style={{lineHeight: '1.6', whiteSpace: 'pre-wrap'}}
      />
      {lastUpdated && <p className="text-xs text-tg-text-placeholder mt-2">最后更新于: {new Date(lastUpdated).toLocaleString()}</p>}
    </div>
  );
};


export const SettingsPage = ({ settings, setSettings, projects, globalKnowledgeTomes, availableChatModels, onAddCoreMemory }: SettingsPageProps): React.ReactElement => {
  const [tempApiKey, setTempApiKey] = React.useState(settings.apiKey || ''); 
  const [apiKeySaveStatus, setApiKeySaveStatus] = React.useState<'idle' | 'saved' | 'error' | 'saving'>('idle');
  const [agentCoreSettings, setAgentCoreSettings] = React.useState<Record<string, {content: string, last_updated_at: string}>>({});
  const [isLoadingAgentSettings, setIsLoadingAgentSettings] = React.useState(true);
  const [editingCoreSetting, setEditingCoreSetting] = useState<{id: AgentCoreSettingId, label: string, placeholder: string} | null>(null);

  const [currentATPassword, setCurrentATPassword] = useState('');
  const [newATPassword, setNewATPassword] = useState('');
  const [confirmATPassword, setConfirmATPassword] = useState('');
  const [atPasswordSaveStatus, setAtPasswordSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [atPasswordError, setAtPasswordError] = useState<string | null>(null);
  const [isATPasswordSet, setIsATPasswordSet] = useState(false);

  type SettingsTab = 'general' | 'agent' | 'memory' | 'organization' | 'data' | 'assets' | 'achievements';
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');

  // 资产管理相关状态
  const [showCMSModal, setShowCMSModal] = useState(false);
  const [propsItems, setPropsItems] = useState<PropAsset[]>([]);
  const [costumesItems, setCostumesItems] = useState<CostumeAsset[]>([]);
  const [posesItems, setPosesItems] = useState<PoseAsset[]>([]);
  const [roleCards, setRoleCards] = useState<RolePlayingCard[]>([]);

  // 成就相关状态
  const [showAchievementModal, setShowAchievementModal] = useState(false);
  const [bodyDevelopmentData, setBodyDevelopmentData] = useState<LinLuoBodyDevelopment[]>([]);
  const [linLuoStatus, setLinLuoStatus] = useState<LinLuoDetailedStatus | null>(null);

  // 资产管理相关状态
  const [showAssetManagementModal, setShowAssetManagementModal] = useState(false);

  // 诊断相关状态
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [diagnosticsReport, setDiagnosticsReport] = useState<string | null>(null);

  // 获取资产数据
  const fetchAssetsData = async () => {
    try {
      // 获取合并后的资产数据（包含 CMS 数据库和资产包文件中的资产）
      const assets = await window.api.assets.getLoadedAssets();

      // 为资产添加默认的 owner 属性（如果没有的话）
      const propsWithOwner = (assets.props || []).map(item => ({
        ...item,
        owner: item.owner || 'master' // 默认为主人用
      }));
      const costumesWithOwner = (assets.costumes || []).map(item => ({
        ...item,
        owner: item.owner || 'master'
      }));
      const posesWithOwner = (assets.poses || []).map(item => ({
        ...item,
        owner: item.owner || 'master'
      }));

      setPropsItems(propsWithOwner);
      setCostumesItems(costumesWithOwner);
      setPosesItems(posesWithOwner);

      // 角色卡从专门的 API 获取
      const roleCardsData = await window.api.cms.getRolePlayingCards();
      setRoleCards(roleCardsData || []);
    } catch (error) {
      console.error('获取资产数据失败:', error);
    }
  };

  // 获取成就数据
  const fetchAchievementData = async () => {
    try {
      // 获取身体发展数据
      const bodyData = await window.api.linluo.getBodyDevelopment();
      setBodyDevelopmentData(bodyData || []);

      // 获取林珞状态
      const status = await window.api.linluo.getDetailedStatus();
      setLinLuoStatus(status);
    } catch (error) {
      console.error('获取成就数据失败:', error);
    }
  };



  // 在组件挂载时获取数据
  useEffect(() => {
    if (activeTab === 'assets') {
      fetchAssetsData();
    } else if (activeTab === 'achievements') {
      fetchAchievementData();
    }
  }, [activeTab]);

  // 组件挂载时立即加载资产数据（用于军械库管理显示）
  useEffect(() => {
    fetchAssetsData();
  }, []);

  // 运行训练室诊断
  const handleRunDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    try {
      const report = await runTrainingRoomDiagnostics();
      setDiagnosticsReport(report);
      console.log('🔍 训练室诊断报告:', report);
    } catch (error) {
      console.error('诊断失败:', error);
      setDiagnosticsReport(`诊断失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsRunningDiagnostics(false);
    }
  };


  const agentSettingDefinitions: Array<{id: AgentCoreSettingId, label: string, placeholder: string, iconName: string}> = [
    { id: 'user_profile', label: "我的资料 (User Profile)", placeholder: "请在此处输入您的昵称、偏好、项目目标等信息...", iconName: "CircleUser" },
    { id: 'linluo_persona', label: "林珞的核心人格模块", placeholder: "请在此处定义林珞的核心人格、语言风格、互动模式等...", iconName: "Sparkles" },
    { id: 'xiaolan_persona', label: "小岚的核心人格模块", placeholder: "请在此处定义小岚的核心人格、技术特点、沟通方式等...", iconName: "Settings" },
    { id: 'yujing_persona', label: "语镜的核心人格模块", placeholder: "请在此处定义语镜的核心人格、美学偏好、交互准则等...", iconName: "Eye" },
    { id: 'summarizer_persona', label: "AI摘要助手人格", placeholder: "定义AI执行摘要任务时的人格和指令...", iconName: "File" },
    { id: 'classifier_persona', label: "AI意图分类器人格", placeholder: "定义AI执行用户意图分类时的人格和指令...", iconName: "Cpu" },
    { id: 'task_resource_suggester_persona', label: "AI任务资源推荐人格", placeholder: "定义AI在神谕罗盘中推荐任务资源时的人格和指令...", iconName: "Layers" },
    { id: 'OrchestratorPersona', label: "AI舰桥文件指令解析人格", placeholder: "定义AI在舰桥中解析文件操作指令时的人格和输出格式...", iconName: "TerminalSquare" }, 
    { id: 'AideProjectAnalyzerPersona', label: "AI AIDE项目结构分析人格", placeholder: "定义AI分析AIDE项目结构并逆向生成任务时的人格和输出格式...", iconName: "Users" },
    { id: 'TaskCockpitIntentRouterPersona', label: "AI任务驾驶舱指令路由人格", placeholder: "定义AI在任务驾驶舱中解析用户指令并路由到不同服务时的人格和输出格式...", iconName: "Share"}
  ];


  React.useEffect(() => {
    setTempApiKey(settings.apiKey || '');
    window.api.settings.getAbsoluteTerritoryPassword().then(pass => {
        setIsATPasswordSet(!!pass);
    });
  }, [settings.apiKey]);

  useEffect(() => {
    const loadAgentSettings = async () => {
      setIsLoadingAgentSettings(true);
      try {
        const allSettings = await window.api.database.getAllAgentCoreSettings();
        const settingsMap: Record<string, {content: string, last_updated_at: string}> = {};
        (allSettings || []).forEach(s => {
          settingsMap[s.setting_id] = { content: s.content, last_updated_at: s.last_updated_at };
        });
        setAgentCoreSettings(settingsMap);
      } catch (err) {
        console.error("Failed to load agent core settings:", err);
      } finally {
        setIsLoadingAgentSettings(false);
      }
    };
    loadAgentSettings();
  }, []);

  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempApiKey(e.target.value);
    setApiKeySaveStatus('idle'); 
  };
  
  const handleSaveApiKey = () => {
    setApiKeySaveStatus('saving');
    setSettings(prev => ({ 
        ...prev, 
        apiKey: tempApiKey.trim() 
    })); 
    setApiKeySaveStatus('saved');
    setTimeout(() => setApiKeySaveStatus('idle'), 2000); 
  };
  
  const handleInputChange = (key: keyof AppSettings, value: string | null | AppSettings['currentTheme']) => { 
    setSettings(prev => ({ ...prev, [key]: value }));
  };
  
  const handleAvatarUpload = async (avatarType: 'user' | 'linluo' | 'xiaolan') => {
    const filePath = await window.api.fs.openFileDialog({
        title: `选择 ${avatarType === 'user' ? '您' : (avatarType === 'linluo' ? '林珞' : '小岚')} 的头像`,
        filters: [{ name: 'Images', extensions: ['png', 'jpg', 'jpeg', 'webp', 'gif'] }]
    });
    if (filePath) {
        try {
            const uniqueFilename = `${avatarType}_${Date.now()}_${filePath.split(/[\\/]/).pop()}`;
            const avatarRelativePath = await window.api.fs.copyFileToUserData(filePath, 'avatars', uniqueFilename);
            if (typeof avatarRelativePath === 'string') {
                setSettings(prev => ({
                    ...prev,
                    [`${avatarType}_avatar_path`]: avatarRelativePath
                }));
            } else {
                throw new Error(avatarRelativePath.error || "复制头像图片失败");
            }
        } catch (error: any) {
            console.error(`Failed to set ${avatarType} avatar:`, error);
            alert(`设置 ${avatarType} 头像失败: ${error.message}`);
        }
    }
  };

  const handleDefaultCoverUpload = async () => {
    const filePath = await window.api.fs.openFileDialog({
        title: "选择默认项目封面",
        filters: [{ name: 'Images', extensions: ['png', 'jpg', 'jpeg', 'webp'] }]
    });
    if (filePath) {
        try {
            const uniqueFilename = `default_cover_${Date.now()}_${filePath.split(/[\\/]/).pop()}`;
            const coverRelativePath = await window.api.fs.copyFileToUserData(filePath, 'default_covers', uniqueFilename);
            if (typeof coverRelativePath === 'string') {
                handleInputChange('defaultCover', coverRelativePath);
            } else {
                throw new Error(coverRelativePath.error || "复制默认封面图片失败");
            }
        } catch (error: any) {
            console.error("Failed to set default project cover:", error);
            alert(`设置默认项目封面失败: ${error.message}`);
        }
    }
  };


  const handleResetSettings = () => {
    if (window.confirm("确定要将所有设置重置为默认值吗？此操作无法撤销。")) {
      setSettings(DEFAULT_SETTINGS); 
      setTempApiKey(DEFAULT_SETTINGS.apiKey); 
       alert("所有设置已重置为默认值。");
    }
  };

  const handleExportData = () => {
    try {
       window.api.database.getAllCoreMemories({}, { field: 'created_at', order: 'DESC' }, { limit: 10000, offset: 0 }).then(coreMemories => {
        const dataToExport = {
            projects,
            settings: { ...settings, onAiThinkingStateChange: undefined }, 
            globalKnowledgeTomes,
            globalQuickCommands: [], 
            agentCoreSettings,
            coreMemories: coreMemories || [], 
            exportedAt: new Date().toISOString(),
        };
        const jsonString = JSON.stringify(dataToExport, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'tiangongge_backup_' + new Date().toISOString().split('T')[0] + '.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        alert("数据已成功导出！");
       }).catch(err => {
         console.error("Error fetching core memories for export:", err);
         alert("导出核心记忆时出错，数据导出可能不完整。");
       });

    } catch (error) {
      console.error("Error exporting data:", error);
      alert("数据导出失败。");
    }
  };
  
  const handleSaveAgentCoreSetting = async (settingId: AgentCoreSettingId, content: string) => {
    await window.api.database.saveAgentCoreSetting(settingId, content);
    setAgentCoreSettings(prev => ({
        ...prev,
        [settingId]: { content, last_updated_at: new Date().toISOString() }
    }));
  };

  const handleSaveAbsoluteTerritoryPassword = async () => {
    setAtPasswordError(null);
    setAtPasswordSaveStatus('saving');

    if (isATPasswordSet && (newATPassword || !newATPassword && currentATPassword)) {
        if (!currentATPassword) {
            setAtPasswordError("请输入当前通行令以进行更改或清除。");
            setAtPasswordSaveStatus('error');
            return;
        }
        const verifyResult = await window.api.settings.verifyAbsoluteTerritoryPassword(currentATPassword);
        if (!verifyResult.isValid) {
            setAtPasswordError("当前通行令不正确。");
            setAtPasswordSaveStatus('error');
            return;
        }
    }
    
    if (newATPassword && newATPassword !== confirmATPassword) {
      setAtPasswordError("新通行令和确认通行令不匹配。");
      setAtPasswordSaveStatus('error');
      return;
    }
    
    const passwordToSet = newATPassword ? newATPassword : (isATPasswordSet && !newATPassword ? null : settings.absolute_territory_password);

    try {
      const result = await window.api.settings.setAbsoluteTerritoryPassword(passwordToSet!); 
      if (result.success) {
        setSettings(prev => ({...prev, absolute_territory_password: passwordToSet }));
        setAtPasswordSaveStatus('saved');
        setIsATPasswordSet(!!passwordToSet);
        setCurrentATPassword('');
        setNewATPassword('');
        setConfirmATPassword('');
        setTimeout(() => setAtPasswordSaveStatus('idle'), 2000);
      } else {
        throw new Error(result.error || "保存通行令失败。");
      }
    } catch (e: any) {
      setAtPasswordError(`保存通行令时出错: ${e.message}`);
      setAtPasswordSaveStatus('error');
    }
  };
  
  const handleResetATPassword = async () => {
    if (!window.confirm("重置通行令将清除绝对领域内的所有聊天记录，并允许您设置新的通行令。此操作不可撤销，确定要继续吗？")) {
      return;
    }
    setAtPasswordSaveStatus('saving'); 
    setAtPasswordError(null);
    try {
      await window.api.database.clearAbsoluteTerritoryHistory();
      await window.api.settings.setAbsoluteTerritoryPassword(null);
      setSettings(prev => ({ ...prev, absolute_territory_password: null }));
      setIsATPasswordSet(false);
      setCurrentATPassword('');
      setNewATPassword('');
      setConfirmATPassword('');
      setAtPasswordError("通行令已重置。绝对领域聊天记录已清除。下次进入将提示设置新通行令。");
      setAtPasswordSaveStatus('saved'); 
    } catch (e: any) {
      setAtPasswordError(`重置失败: ${e.message}`);
      setAtPasswordSaveStatus('error');
    } finally {
       setTimeout(() => {
           setAtPasswordSaveStatus('idle');
           if (atPasswordError && atPasswordError.startsWith("通行令已重置")) setAtPasswordError(null);
       }, 4000);
    }
  };


  const getButtonClasses = (variant: 'primary' | 'secondary' | 'danger' | 'success' | 'teal' = 'primary', localSaveStatus?: typeof apiKeySaveStatus | typeof atPasswordSaveStatus) => {
    let base = "px-4 py-2 rounded-md transition-colors font-medium text-sm flex items-center";
    const currentStatus = localSaveStatus;
    
    if (currentStatus === 'saving') return base + ' bg-tg-accent-primary text-white opacity-70 cursor-not-allowed';
    if (currentStatus === 'saved') return base + ' bg-tg-success text-tg-bg-primary hover:brightness-110';
    if (currentStatus === 'error') return base + ' bg-tg-danger text-white hover:bg-tg-danger-hover';


    switch(variant) {
      case 'danger': return base + ' bg-tg-danger text-white hover:bg-tg-danger-hover';
      case 'success': return base + ' bg-tg-success text-tg-bg-primary hover:brightness-110';
      case 'teal': return base + ' bg-teal-600 text-white hover:bg-teal-700';
      case 'secondary': return base + ' bg-tg-bg-secondary text-tg-text-primary hover:bg-tg-bg-hover border border-tg-border-primary focus:ring-tg-border-interactive';
      case 'primary':
      default: return base + ' bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover';
    }
  };
  
  const getTabClassName = (tabName: SettingsTab) => {
    return `py-2.5 px-5 rounded-t-lg text-sm font-medium transition-colors flex items-center focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-tg-accent-primary focus:ring-offset-tg-bg-primary ${
      activeTab === tabName
        ? 'bg-tg-bg-secondary text-tg-accent-primary shadow-md'
        : 'text-tg-text-secondary hover:bg-tg-bg-tertiary hover:text-tg-text-primary border-b-2 border-transparent'
    } whitespace-nowrap`;
  };

  const getSidebarTabClassName = (tabName: SettingsTab) => {
    return `w-full p-4 rounded-xl text-left transition-all duration-300 flex items-center focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-tg-accent-primary ${
      activeTab === tabName
        ? 'bg-tg-accent-primary text-white shadow-lg transform scale-105'
        : 'text-tg-text-secondary hover:text-tg-text-primary hover:bg-tg-bg-hover card-hover'
    }`;
  };


  return (
    <div className="min-h-full bg-gradient-dark text-tg-text-primary flex flex-col lg:flex-row">
      {/* 左侧导航栏 */}
      <aside className="w-full lg:w-80 lg:min-w-80 bg-gradient-card border-b lg:border-b-0 lg:border-r border-tg-border-primary flex flex-col">
        <header className="p-6 border-b border-tg-border-primary">
          <div className="flex items-center mb-3">
            <SmartIcon name="Settings" level="accent" context="navigation" className="mr-4" />
            <VisualHeading level="accent" size="2xl" gradient>天工阁设置</VisualHeading>
          </div>
          <p className="text-tg-text-secondary">管理您的应用配置、AI人格、核心记忆及数据。</p>
        </header>

        <nav className="flex-1 p-4">
          <div className="space-y-2 lg:space-y-2 flex lg:flex-col overflow-x-auto lg:overflow-x-visible">
            <button onClick={() => setActiveTab('general')} className={getSidebarTabClassName('general') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Settings" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">通用设定</div>
                <div className="text-xs opacity-75 hidden lg:block">API密钥、模型、外观</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('agent')} className={getSidebarTabClassName('agent') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Sparkles" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">智能体核心</div>
                <div className="text-xs opacity-75 hidden lg:block">AI人格、行为设定</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('memory')} className={getSidebarTabClassName('memory') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Brain" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">核心记忆管理</div>
                <div className="text-xs opacity-75 hidden lg:block">长期记忆、重要信息</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('organization')} className={getSidebarTabClassName('organization') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Users" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">舰桥人员配置</div>
                <div className="text-xs opacity-75 hidden lg:block">角色分配、权限管理</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('data')} className={getSidebarTabClassName('data') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Database" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">数据与备份</div>
                <div className="text-xs opacity-75 hidden lg:block">备份、导出、重置</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('assets')} className={getSidebarTabClassName('assets') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Shield" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">军械库</div>
                <div className="text-xs opacity-75 hidden lg:block">主人用/女王用军械管理</div>
              </div>
            </button>
            <button onClick={() => setActiveTab('achievements')} className={getSidebarTabClassName('achievements') + ' min-w-max lg:min-w-0'}>
              <SmartIcon name="Trophy" level="secondary" context="navigation" className="mr-3 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium">成就殿堂</div>
                <div className="text-xs opacity-75 hidden lg:block">查看解锁的成就</div>
              </div>
            </button>
          </div>
        </nav>
      </aside>

      {/* 右侧内容区域 */}
      <main className="flex-1 overflow-auto">
        <div className="p-6 md:p-8">
        {activeTab === 'general' && (
        <>
          <SettingSection title="Gemini API 密钥" iconName="KeyRound" description="您的Gemini API密钥将安全地存储在本地，仅用于与Gemini服务通信。">
            <SettingItem label="API Key">
              <div className="flex items-center space-x-2">
                <input
                  type="password"
                  value={tempApiKey}
                  onChange={handleApiKeyChange}
                  className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
                  placeholder="输入您的 Gemini API Key"
                />
                <button 
                    onClick={handleSaveApiKey} 
                    className={getButtonClasses('primary', apiKeySaveStatus)}
                    disabled={apiKeySaveStatus === 'saving'}
                >
                  {apiKeySaveStatus === 'saving' ? <Icon name="Loader2" className="w-5 h-5 mr-1.5 animate-spin"/> : (apiKeySaveStatus === 'saved' ? <Icon name="CheckCircle" className="w-5 h-5 mr-1.5"/> : null)}
                  {apiKeySaveStatus === 'saved' ? '已保存' : (apiKeySaveStatus === 'error' ? '保存失败' : (apiKeySaveStatus === 'saving' ? '保存中' : '保存密钥'))}
                </button>
              </div>
            </SettingItem>
             <SettingItem label="默认对话模型" description="选择用于常规对话和研讨的默认Gemini模型。">
              <select
                value={settings.chatModel}
                onChange={e => handleInputChange('chatModel', e.target.value)}
                className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
              >
                {availableChatModels.map(model => (
                  <option key={model.id} value={model.id}>{model.name}</option>
                ))}
              </select>
            </SettingItem>
            <SettingItem label="默认向量化模型" description="选择用于内容向量化（如RAG知识索引）的默认模型。">
              <select
                value={settings.embeddingModel}
                onChange={e => handleInputChange('embeddingModel', e.target.value)}
                className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
              >
                {AVAILABLE_EMBEDDING_MODELS.map(modelId => (
                  <option key={modelId} value={modelId}>{modelId}</option>
                ))}
              </select>
            </SettingItem>
          </SettingSection>

          <SettingSection title="个性化外观" iconName="Palette" description="自定义您的天工阁界面与体验。">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
              <SettingItem label="我的头像" description="设定您在研讨区显示的头像。">
                  <div className="flex items-center space-x-3">
                      {settings.user_avatar_path && <img src={`app-avatar://${settings.user_avatar_path}`} alt="User Avatar Preview" className="w-12 h-12 rounded-full object-cover border-2 border-tg-accent-primary shadow-lg transition-all duration-300 hover:scale-110"/>}
                      <button onClick={() => handleAvatarUpload('user')} className={getButtonClasses('secondary', undefined) + ' text-xs'}>
                           <Icon name="Upload" className="w-4 h-4 mr-1"/>{settings.user_avatar_path ? '更改' : '上传'}
                      </button>
                      {settings.user_avatar_path && <button onClick={() => setSettings(prev => ({...prev, user_avatar_path: null}))} className={getButtonClasses('danger', undefined)+ ' text-xs'}><Icon name="X" className="w-3 h-3 mr-0.5"/>移除</button>}
                  </div>
              </SettingItem>
              <SettingItem label="林珞姐姐的头像" description="设定林珞在研讨区和绝对领域的头像。">
                  <div className="flex items-center space-x-3">
                      {settings.linluo_avatar_path && <img src={`app-avatar://${settings.linluo_avatar_path}?t=${Date.now()}`} alt="LinLuo Avatar Preview" className="w-12 h-12 rounded-full object-cover border-2 border-pink-400"/>}
                      <button onClick={() => handleAvatarUpload('linluo')} className={getButtonClasses('secondary', undefined)+ ' text-xs'}>
                           <Icon name="Upload" className="w-4 h-4 mr-1"/>{settings.linluo_avatar_path ? '更改' : '上传'}
                      </button>
                      {settings.linluo_avatar_path && <button onClick={() => setSettings(prev => ({...prev, linluo_avatar_path: null}))} className={getButtonClasses('danger', undefined)+ ' text-xs'}><Icon name="X" className="w-3 h-3 mr-0.5"/>移除</button>}
                  </div>
              </SettingItem>
              <SettingItem label="小岚妹妹的头像" description="设定小岚在研讨区的头像。">
                   <div className="flex items-center space-x-3">
                      {settings.xiaolan_avatar_path && <img src={`app-avatar://${settings.xiaolan_avatar_path}`} alt="XiaoLan Avatar Preview" className="w-12 h-12 rounded-full object-cover border-2 border-sky-400 shadow-lg transition-all duration-300 hover:scale-110"/>}
                      <button onClick={() => handleAvatarUpload('xiaolan')} className={getButtonClasses('secondary', undefined)+ ' text-xs'}>
                          <Icon name="Upload" className="w-4 h-4 mr-1"/>{settings.xiaolan_avatar_path ? '更改' : '上传'}
                      </button>
                      {settings.xiaolan_avatar_path && <button onClick={() => setSettings(prev => ({...prev, xiaolan_avatar_path: null}))} className={getButtonClasses('danger', undefined)+ ' text-xs'}><Icon name="X" className="w-3 h-3 mr-0.5"/>移除</button>}
                  </div>
              </SettingItem>
            </div>
             <SettingItem label="项目默认封面图片" description="选择新项目卡片的默认视觉图片。若不设置，则使用主题相关的程序化图案。">
                <div className="flex items-center space-x-3">
                    {settings.defaultCover && settings.defaultCover.startsWith('default_covers/') && <img src={`app-avatar://${settings.defaultCover}?t=${Date.now()}`} alt="Default Cover Preview" className="w-24 h-16 rounded-md object-cover border-2 border-tg-accent-secondary"/>}
                    <button onClick={handleDefaultCoverUpload} className={getButtonClasses('secondary', undefined)}>
                         <Icon name="Upload" className="w-4 h-4 mr-2"/>{settings.defaultCover && settings.defaultCover.startsWith('default_covers/') ? '更改图片' : '上传图片'}
                    </button>
                    {settings.defaultCover && settings.defaultCover.startsWith('default_covers/') && <button onClick={() => handleInputChange('defaultCover', null)} className={getButtonClasses('danger', undefined)}><Icon name="X" className="w-4 h-4 mr-1"/>移除图片</button>}
                </div>
            </SettingItem>
            <SettingItem label="应用主题" description="选择天工阁的整体视觉主题。">
                <select 
                    value={settings.currentTheme || 'theme-default'} 
                    onChange={e => handleInputChange('currentTheme', e.target.value as AppSettings['currentTheme'])}
                    className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
                >
                    <option value="theme-default">默认深色主题</option>
                    <option value="theme-light">简约浅色主题</option>
                </select>
            </SettingItem>
          </SettingSection>
          
          <SettingSection title="绝对领域通行令" iconName="Lock" description="管理进入“绝对领域”的通行令。若未设置，首次进入时会提示创建。">
             <SettingItem label={isATPasswordSet ? "更改或清除通行令" : "设置新的通行令"}>
                {isATPasswordSet && (
                    <input type="password" value={currentATPassword} onChange={e => setCurrentATPassword(e.target.value)} placeholder="输入当前通行令以验证..." 
                        className="w-full p-2.5 mb-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30" />
                )}
                <input type="password" value={newATPassword} onChange={e => setNewATPassword(e.target.value)} placeholder={isATPasswordSet ? "输入新通行令 (留空则不更改或用于清除)" : "输入新通行令..."}
                    className="w-full p-2.5 mb-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30" />
                {newATPassword && (
                    <input type="password" value={confirmATPassword} onChange={e => setConfirmATPassword(e.target.value)} placeholder="确认新通行令..."
                        className="w-full p-2.5 mb-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30" />
                )}
                {atPasswordError && <p className="text-xs text-red-400 mb-2 bg-red-900/30 p-1.5 rounded">{atPasswordError}</p>}
                <div className="flex items-center space-x-2">
                    <button onClick={handleSaveAbsoluteTerritoryPassword} className={getButtonClasses(isATPasswordSet && !newATPassword && currentATPassword ? 'danger' : 'primary', atPasswordSaveStatus)} disabled={atPasswordSaveStatus === 'saving'}>
                        {atPasswordSaveStatus === 'saving' ? <Icon name="Loader2" className="w-5 h-5 mr-1.5 animate-spin"/> : (atPasswordSaveStatus === 'saved' ? <Icon name="CheckCircle" className="w-5 h-5 mr-1.5"/> : null)}
                        {atPasswordSaveStatus === 'saved' ? '通行令已更新' : 
                         (atPasswordSaveStatus === 'error' ? '操作失败' : 
                         (atPasswordSaveStatus === 'saving' ? '处理中...' : 
                         (isATPasswordSet && !newATPassword && currentATPassword ? '清除通行令' : '保存通行令')))}
                    </button>
                    {isATPasswordSet && (
                         <button onClick={handleResetATPassword} className={getButtonClasses('secondary', atPasswordSaveStatus === 'saving' || atPasswordSaveStatus === 'error' ? atPasswordSaveStatus : undefined)} disabled={atPasswordSaveStatus === 'saving'}>
                            忘记通行令？点击重置
                        </button>
                    )}
                </div>
             </SettingItem>
          </SettingSection>
        </>
        )}

        {activeTab === 'agent' && (
            isLoadingAgentSettings ? (
                <div className="text-center py-10 text-tg-text-placeholder"><Icon name="Loader2" className="w-8 h-8 animate-spin mx-auto"/> 加载智能体设定中...</div>
            ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {agentSettingDefinitions.map(def => (
                    <div key={def.id} className="p-4 rounded-lg shadow-md bg-tg-bg-secondary border border-tg-border-primary flex flex-col">
                        <h4 className="text-md font-semibold flex items-center text-tg-accent-secondary mb-2">
                            <Icon name={def.iconName as any} className="w-5 h-5 mr-2" />
                            {def.label}
                        </h4>
                        <p className="text-xs text-tg-text-secondary mb-2 line-clamp-2" title={agentCoreSettings[def.id]?.content || def.placeholder}>
                            当前内容: {agentCoreSettings[def.id]?.content ? `${agentCoreSettings[def.id].content.substring(0, 60)}...` : '未设置'}
                        </p>
                        {agentCoreSettings[def.id]?.last_updated_at && <p className="text-xs text-tg-text-placeholder mb-3">更新于: {new Date(agentCoreSettings[def.id].last_updated_at).toLocaleDateString()}</p>}
                        <button 
                            onClick={() => setEditingCoreSetting({id: def.id, label: def.label, placeholder: def.placeholder})}
                            className="mt-auto w-full "
                        >
                             <div className={getButtonClasses('secondary', undefined) + " w-full justify-center"}>
                                <Icon name="Pencil" className="w-4 h-4 mr-1.5"/>编辑{def.label.split('(')[0].trim()}
                             </div>
                        </button>
                    </div>
                ))}
            </div>
            )
        )}
        {activeTab === 'memory' && (
             <CoreMemoryManagement onAddCoreMemoryProp={onAddCoreMemory} />
        )}
        {activeTab === 'organization' && ( 
            <PersonnelAssignmentPage />
        )}

        {activeTab === 'data' && (
            <SettingSection title="数据管理" iconName="Server" description="管理应用数据备份与恢复。">
            <div className="space-y-4">
                <button onClick={handleExportData} className={getButtonClasses('teal', undefined) + " w-full md:w-auto justify-center"}>
                    <Icon name="Download" className="w-5 h-5 mr-2"/>导出全部数据 (JSON备份)
                </button>
                <button onClick={handleResetSettings} className={getButtonClasses('danger', undefined) + " w-full md:w-auto justify-center"}>
                    <Icon name="AlertTriangle" className="w-5 h-5 mr-2"/>重置所有应用设置
                </button>
            </div>
            </SettingSection>
        )}

        {activeTab === 'assets' && (
            <SettingSection title="军械库管理" iconName="Shield" description="管理绝对领域中的军械资产，按角色分类管理道具、服装、姿势等。">
                <div className="space-y-8">
                    {/* 主人用军械 */}
                    <VisualContainer level="secondary" variant="card">
                        <div className="flex items-center mb-6">
                            <SmartIcon name="Crown" level="accent" context="content" className="mr-3" />
                            <VisualHeading level="primary" size="xl">主人用军械</VisualHeading>
                            <div className="ml-auto text-sm text-tg-text-secondary">
                                总计 {propsItems.filter(item => item.owner === 'master').length + costumesItems.filter(item => item.owner === 'master').length + posesItems.filter(item => item.owner === 'master').length} 件
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="Zap" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">道具</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{propsItems.filter(item => item.owner === 'master').length} 件道具</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {propsItems.filter(item => item.owner === 'master').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {propsItems.filter(item => item.owner === 'master').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{propsItems.filter(item => item.owner === 'master').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>

                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="Shirt" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">服装</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{costumesItems.filter(item => item.owner === 'master').length} 套服装</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {costumesItems.filter(item => item.owner === 'master').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {costumesItems.filter(item => item.owner === 'master').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{costumesItems.filter(item => item.owner === 'master').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>

                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="User" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">姿势</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{posesItems.filter(item => item.owner === 'master').length} 个姿势</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {posesItems.filter(item => item.owner === 'master').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {posesItems.filter(item => item.owner === 'master').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{posesItems.filter(item => item.owner === 'master').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>
                        </div>
                    </VisualContainer>

                    {/* 女王用军械 */}
                    <VisualContainer level="secondary" variant="card">
                        <div className="flex items-center mb-6">
                            <SmartIcon name="Sparkles" level="accent" context="content" className="mr-3" />
                            <VisualHeading level="primary" size="xl">女王用军械</VisualHeading>
                            <div className="ml-auto text-sm text-tg-text-secondary">
                                总计 {propsItems.filter(item => item.owner === 'queen').length + costumesItems.filter(item => item.owner === 'queen').length + posesItems.filter(item => item.owner === 'queen').length} 件
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="Zap" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">道具</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{propsItems.filter(item => item.owner === 'queen').length} 件道具</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {propsItems.filter(item => item.owner === 'queen').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {propsItems.filter(item => item.owner === 'queen').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{propsItems.filter(item => item.owner === 'queen').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>

                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="Shirt" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">服装</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{costumesItems.filter(item => item.owner === 'queen').length} 套服装</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {costumesItems.filter(item => item.owner === 'queen').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {costumesItems.filter(item => item.owner === 'queen').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{costumesItems.filter(item => item.owner === 'queen').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>

                            <VisualContainer level="tertiary" variant="panel" className="text-center">
                                <SmartIcon name="User" level="accent" context="content" className="mx-auto mb-3" />
                                <h4 className="font-medium text-tg-text-primary mb-2">姿势</h4>
                                <p className="text-sm text-tg-text-secondary mb-4">{posesItems.filter(item => item.owner === 'queen').length} 个姿势</p>
                                <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {posesItems.filter(item => item.owner === 'queen').slice(0, 3).map(item => (
                                        <div key={item.id} className="text-xs text-tg-text-secondary bg-tg-bg-tertiary rounded px-2 py-1">
                                            {item.name}
                                        </div>
                                    ))}
                                    {posesItems.filter(item => item.owner === 'queen').length > 3 && (
                                        <div className="text-xs text-tg-text-placeholder">
                                            +{posesItems.filter(item => item.owner === 'queen').length - 3} 更多...
                                        </div>
                                    )}
                                </div>
                            </VisualContainer>
                        </div>
                    </VisualContainer>

                    {/* 管理按钮 */}
                    <div className="text-center space-y-4">
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <button
                                onClick={() => setShowCMSModal(true)}
                                className="px-8 py-3 bg-gradient-primary text-white rounded-xl hover:bg-gradient-secondary transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
                            >
                                <SmartIcon name="Settings" level="secondary" context="action" className="mr-2" />
                                打开军械库管理
                            </button>

                            <button
                                onClick={() => setShowAssetManagementModal(true)}
                                className="px-8 py-3 bg-teal-600 text-white rounded-xl hover:bg-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
                            >
                                <SmartIcon name="Shield" level="secondary" context="action" className="mr-2" />
                                资产管理中心
                            </button>

                            <button
                                onClick={handleRunDiagnostics}
                                disabled={isRunningDiagnostics}
                                className="px-8 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <SmartIcon name={isRunningDiagnostics ? "Loader" : "Stethoscope"} level="secondary" context="action" className={`mr-2 ${isRunningDiagnostics ? 'animate-spin' : ''}`} />
                                {isRunningDiagnostics ? '诊断中...' : '训练室诊断'}
                            </button>
                        </div>
                    </div>

                    <div className="bg-tg-bg-tertiary rounded-lg p-4 border border-tg-border-primary">
                        <h4 className="font-medium text-tg-text-primary mb-2 flex items-center">
                            <SmartIcon name="Info" level="secondary" context="content" className="mr-2" />
                            军械库说明
                        </h4>
                        <ul className="text-sm text-tg-text-secondary space-y-1">
                            <li>• 同样的道具/姿势，不同角色使用时对应不同的提示词工程</li>
                            <li>• 主人用：侧重于主导性和控制感的表达</li>
                            <li>• 女王用：侧重于优雅和权威感的表达</li>
                            <li>• 每个资产都有独特的 LLM 提示词配置</li>
                        </ul>
                    </div>

                    {/* 诊断报告 */}
                    {diagnosticsReport && (
                        <div className="bg-tg-bg-tertiary rounded-lg p-4 border border-tg-border-primary">
                            <h4 className="font-medium text-tg-text-primary mb-2 flex items-center">
                                <SmartIcon name="Stethoscope" level="secondary" context="content" className="mr-2" />
                                训练室诊断报告
                                <button
                                    onClick={() => setDiagnosticsReport(null)}
                                    className="ml-auto text-tg-text-placeholder hover:text-tg-text-secondary"
                                >
                                    <SmartIcon name="X" level="secondary" context="action" />
                                </button>
                            </h4>
                            <pre className="text-xs text-tg-text-secondary whitespace-pre-wrap font-mono bg-tg-bg-secondary p-3 rounded border max-h-96 overflow-y-auto">
                                {diagnosticsReport}
                            </pre>
                        </div>
                    )}
                </div>
            </SettingSection>
        )}

        {activeTab === 'achievements' && (
            <SettingSection title="成就殿堂" iconName="Trophy" description="查看您在绝对领域中解锁的各种成就。">
                <div className="space-y-6">
                    <div className="text-center">
                        <VisualContainer level="accent" variant="card" className="inline-block">
                            <SmartIcon name="Trophy" level="accent" context="decoration" className="mx-auto mb-4" animate="float" />
                            <VisualHeading level="primary" size="xl" className="mb-2">成就系统</VisualHeading>
                            <p className="text-tg-text-secondary mb-4">记录您与林珞姐姐的美好时光</p>
                            <button
                                onClick={() => setShowAchievementModal(true)}
                                className="px-6 py-3 bg-gradient-primary text-white rounded-xl hover:bg-gradient-secondary transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
                            >
                                <SmartIcon name="Trophy" level="secondary" context="action" className="mr-2" />
                                查看成就殿堂
                            </button>
                        </VisualContainer>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <VisualContainer level="tertiary" variant="card" className="text-center">
                            <SmartIcon name="Heart" level="accent" context="content" className="mx-auto mb-3" />
                            <h4 className="font-medium text-tg-text-primary mb-2">互动成就</h4>
                            <p className="text-sm text-tg-text-secondary">记录与林珞的互动里程碑</p>
                        </VisualContainer>

                        <VisualContainer level="tertiary" variant="card" className="text-center">
                            <SmartIcon name="TrendingUp" level="accent" context="content" className="mx-auto mb-3" />
                            <h4 className="font-medium text-tg-text-primary mb-2">发展成就</h4>
                            <p className="text-sm text-tg-text-secondary">见证林珞的成长历程</p>
                        </VisualContainer>

                        <VisualContainer level="tertiary" variant="card" className="text-center">
                            <SmartIcon name="Star" level="accent" context="content" className="mx-auto mb-3" />
                            <h4 className="font-medium text-tg-text-primary mb-2">隐藏成就</h4>
                            <p className="text-sm text-tg-text-secondary">神秘的特殊成就</p>
                        </VisualContainer>
                    </div>

                    <div className="bg-tg-bg-tertiary rounded-lg p-4 border border-tg-border-primary">
                        <h4 className="font-medium text-tg-text-primary mb-2 flex items-center">
                            <SmartIcon name="Info" level="secondary" context="content" className="mr-2" />
                            成就系统说明
                        </h4>
                        <ul className="text-sm text-tg-text-secondary space-y-1">
                            <li>• 成就会根据您与林珞的互动自动解锁</li>
                            <li>• 不同类型的成就有不同的解锁条件</li>
                            <li>• 隐藏成就需要特殊的触发条件</li>
                            <li>• 成就记录会永久保存在您的档案中</li>
                        </ul>
                    </div>
                </div>
            </SettingSection>
        )}
        </div>
      </main>

      {/* 模态框 */}
      {editingCoreSetting && (
        <GenericModal
            isOpen={!!editingCoreSetting}
            onClose={() => setEditingCoreSetting(null)}
            title={`编辑: ${editingCoreSetting.label}`}
            size="xl"
            footerContent={
                <>
                <button onClick={() => setEditingCoreSetting(null)} className={getButtonClasses('secondary', undefined)}>取消</button>
                <button 
                    onClick={async () => {
                        const editorTextarea = document.getElementById(`agent-core-editor-${editingCoreSetting.id}`) as HTMLTextAreaElement;
                        if (editorTextarea) {
                            await handleSaveAgentCoreSetting(editingCoreSetting.id, editorTextarea.value);
                            setEditingCoreSetting(null); 
                        }
                    }} 
                    className={getButtonClasses('primary', undefined)}
                >
                    保存并关闭
                </button>
                </>
            }
            >
            <AgentCoreSettingEditor
                settingId={editingCoreSetting.id}
                label={editingCoreSetting.label}
                placeholder={editingCoreSetting.placeholder}
                initialContent={agentCoreSettings[editingCoreSetting.id]?.content}
                lastUpdated={agentCoreSettings[editingCoreSetting.id]?.last_updated_at}
                onSave={handleSaveAgentCoreSetting} 
                onClose={() => setEditingCoreSetting(null)} 
            />
        </GenericModal>
      )}

      {/* 资产管理模态框 */}
      {showCMSModal && (
        <CMSManagementModal
          isOpen={showCMSModal}
          onClose={() => setShowCMSModal(false)}
          fetchCMSItems={fetchAssetsData}
          initialPropsItems={propsItems}
          initialCostumesItems={costumesItems}
          initialPosesItems={posesItems}
          initialRoleCards={roleCards}
        />
      )}

      {/* 成就殿堂模态框 */}
      {showAchievementModal && linLuoStatus && (
        <AchievementHallModal
          isOpen={showAchievementModal}
          onClose={() => setShowAchievementModal(false)}
          bodyDevelopmentData={bodyDevelopmentData}
          linLuoCurrentStatus={linLuoStatus}
        />
      )}

      {/* 资产管理模态框 */}
      {showAssetManagementModal && (
        <AssetManagementModal
          isOpen={showAssetManagementModal}
          onClose={() => setShowAssetManagementModal(false)}
          onRefresh={fetchAssetsData}
        />
      )}
    </div>
  );
};
