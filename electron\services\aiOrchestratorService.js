// electron/services/aiOrchestrationService.js
console.log('AI_ORCHESTRATION_SERVICE_JS: File execution started (Refactored from aiService.js).');

import * as aiKernelService from './aiKernelService.js';
import * as promptBuilderService from './promptBuilderService.js';
import * as memoryAugmentationService from './memoryAugmentationService.js';
import * as memoryPersistenceService from './memoryPersistenceService.js';
import * as dbService from './databaseService.js';
import * as universalAssetService from './universalAssetService.js';
import * as fileSystemService from './fileSystemService.js'; // For AIDE project analysis AND Bridge file operations
import { GEMINI_TEXT_MODEL } from '../../src/config/globalConfig.js'; // Direct import for default model


export function initializeAIAgent(apiKey) {
    return aiKernelService.initializeSharedAIAgent(apiKey);
}

export async function getAvailableModels() {
    return [
        { id: GEMINI_TEXT_MODEL, name: GEMINI_TEXT_MODEL },
    ];
}

export async function executeRawLLMQuery(contents, config, modelNameFromArgs, apiKeyFromArgs) {
    console.log("AI_ORCHESTRATION_SERVICE: executeRawLLMQuery called.");
    return aiKernelService.generateContentInternal(contents, config, modelNameFromArgs, apiKeyFromArgs);
}


export async function callAI(userRawPrompt, personaId, context = {}) {
    console.log(`AI_ORCHESTRATION_SERVICE: callAI for persona ${personaId}, contextType: ${context.contextType}, prompt: "${userRawPrompt ? userRawPrompt.substring(0,50) : 'N/A'}..."`);

    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) {
        const errorMsg = "AI service not initialized or API key missing for callAI.";
        return { text: errorMsg, status: null, scriptChoices: null, newSceneCardId: null, json: (personaId === 'OrchestratorPersona' || personaId === 'AideProjectAnalyzerPersona') ? { command: 'error', parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
    }
    
    const currentApiKey = aiKernelService.getCurrentApiKey();

    // --- Script Mode Handling ---
    if (context.scriptModeInfo && context.scriptModeInfo.scriptId) {
        // ... (Script mode logic remains the same as in the original aiService.js)
        const { scriptId, currentSceneId, userChoiceText } = context.scriptModeInfo;
        const scriptAsset = universalAssetService.getAssetById(scriptId);
        if (!scriptAsset || scriptAsset.type !== 'script') return { text: "错误：未能找到指定的剧本。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
        let currentScene = scriptAsset.scenes.find(s => s.id === currentSceneId);
        if (!currentScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到场景 ID: ${currentSceneId}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
        // ... (rest of script logic) ...
         let nextScene = null;
        if (userChoiceText && currentScene.user_choices) {
            const choiceMade = currentScene.user_choices.find(c => c.text === userChoiceText);
            if (choiceMade) {
                if (choiceMade.next_scene_id) {
                    nextScene = scriptAsset.scenes.find(s => s.id === choiceMade.next_scene_id);
                     if (!nextScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到由选项 '${userChoiceText}' 指向的下一场景 ID: ${choiceMade.next_scene_id}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
                } else if (choiceMade.action) {
                    console.log(`AI_ORCHESTRATION_SERVICE: Script action triggered:`, choiceMade.action);
                    if (choiceMade.action.end_script) return { text: choiceMade.action.end_message || "剧本结束。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
                }
            } else return { text: `错误：在场景 ${currentSceneId} 中未找到选项 '${userChoiceText}'。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
        }
        const sceneToDisplay = nextScene || currentScene;
        let newSceneCardId = sceneToDisplay.scenario_card_id !== undefined ? sceneToDisplay.scenario_card_id : (scriptAsset.scenario_card_id !== undefined ? scriptAsset.scenario_card_id : (context.selectedSceneCardId !== undefined ? context.selectedSceneCardId : null));
        return { text: sceneToDisplay.plot_text, status: context.currentLinLuoDetailedStatus, scriptChoices: sceneToDisplay.user_choices || null, newSceneCardId: newSceneCardId, json: null };
    }

    // --- Orchestrated AI Call Logic ---
    try {
        const personaTargetForMemory = personaId.toLowerCase();
        let modelConfigOverride = {};
         if (personaId === 'OrchestratorPersona' || personaId === 'AideProjectAnalyzerPersona') {
            modelConfigOverride = { responseMimeType: "application/json" };
        }

        let memoryContextString = "";
        if (personaId !== 'ClassifierPersona' && personaId !== 'SummarizerPersona' && personaId !== 'TaskResourceSuggesterPersona' && personaId !== 'OrchestratorPersona' && personaId !== 'AideProjectAnalyzerPersona' && currentApiKey && context.contextType !== 'task_resource_suggestion') {
            const queryTextForRag = userRawPrompt || (context.history && context.history.length > 0 ? context.history[context.history.length-1].parts[0].text : "");
            if (queryTextForRag) {
                memoryContextString = await memoryAugmentationService.retrieveAndFormatMemories(queryTextForRag, personaTargetForMemory, context.projectId, context.contextType, context.keywords, context.desiredMemoryTypes, currentApiKey, GEMINI_EMBEDDING_MODEL, dbService, aiKernelService);
            }
        }
        
        const systemInstruction = await promptBuilderService.buildSystemInstruction(personaId, context.selectedRoleCardId, context.currentLinLuoDetailedStatus);
        
        let finalUserPromptForLLM = userRawPrompt;
        if (personaId === 'OrchestratorPersona') { // File system orchestration persona
             // The prompt is already constructed by the persona template, userRawPrompt is the direct input
             finalUserPromptForLLM = userRawPrompt;
        } else if (personaId === 'AideProjectAnalyzerPersona') {
            finalUserPromptForLLM = userRawPrompt; // UserRawPrompt here is the pre-formatted prompt including file list
        }

        const requestContents = promptBuilderService.prepareUserHistoryForAI(context.history || [], memoryContextString, finalUserPromptForLLM);
        
        const rawAiText = await aiKernelService.generateContentInternal(requestContents, { systemInstruction, ...modelConfigOverride }, GEMINI_TEXT_MODEL, currentApiKey);

        if (typeof rawAiText !== 'string') {
            console.error(`AI_ORCHESTRATION_SERVICE: LLM call did not return a string. Response:`, rawAiText);
            const errorMsg = `AI核心返回了非文本响应。 (Persona: ${personaId})`;
            return { text: errorMsg, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: (personaId === 'OrchestratorPersona' || personaId === 'AideProjectAnalyzerPersona') ? { command: 'error', parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
        }

        const { cleanedText, actionResults } = await memoryPersistenceService.processResponseForActions(rawAiText, context.projectId, dbService);
        // TODO: Handle actionResults if needed

        if (personaId === 'OrchestratorPersona') { // This is the file system operation orchestrator
            let jsonStr = cleanedText.trim();
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const match = jsonStr.match(fenceRegex);
            if (match && match[2]) jsonStr = match[2].trim();
            try {
                const parsedJson = JSON.parse(jsonStr);
                // Now execute the file system command based on parsedJson
                return await executeFileSystemCommand(parsedJson);
            } catch (e) { /* ... error handling as before ... */ 
                 const errorMsg = `文件操作指令AI返回的JSON格式无效 (Persona: ${personaId}): ${e.message}`;
                 console.error(`AI_ORCHESTRATION_SERVICE (OrchestratorPersona): Failed to parse JSON:`, e, jsonStr);
                 return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { command: 'error', parameters: { originalInput: userRawPrompt, error: errorMsg } } };
            }
        } else if (personaId === 'AideProjectAnalyzerPersona') {
            // Similar JSON parsing as OrchestratorPersona
            let jsonStr = cleanedText.trim(); /* ... fence removal ... */
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const match = jsonStr.match(fenceRegex);
            if (match && match[2]) jsonStr = match[2].trim();
            try {
                const parsedJson = JSON.parse(jsonStr);
                return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: parsedJson };
            } catch (e) { /* ... error handling ... */
                const errorMsg = `AIDE分析AI返回的JSON格式无效: ${e.message}`;
                return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { error: errorMsg }};
            }
        }
        
        let statusOutput = null;
        let textForDisplay = cleanedText;
        const statusRegex = /\[\[STATUS:({.*?})\]\]/s;
        const statusMatch = cleanedText.match(statusRegex);
        if (statusMatch && statusMatch[1]) {
            try { statusOutput = JSON.parse(statusMatch[1]); textForDisplay = textForDisplay.replace(statusRegex, '').trim(); } 
            catch (e) { console.error("AI_ORCHESTRATION_SERVICE: Failed to parse status JSON:", e, statusMatch[1]); }
        }
        
        return { text: textForDisplay, status: statusOutput, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null };

    } catch (error) {
        console.error(`AI_ORCHESTRATION_SERVICE_ERROR (callAI with persona ${personaId}):`, error);
        return { text: `调用AI时发生错误: ${error.message}`, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null };
    }
}

async function executeFileSystemCommand(parsedCommand) {
    const { command, parameters } = parsedCommand;
    console.log(`AI_ORCHESTRATION_SERVICE: Executing file system command: ${command}, Parameters:`, parameters);
    let resultText = "";

    switch (command) {
      case 'listFiles':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '列出文件' 指令缺少有效的 'path' 参数。"; break;}
        const listResult = await fileSystemService.listFiles(parameters.path, parameters.recursive, parameters.depth);
        if (listResult.error) { resultText = `错误: 列出文件失败 - ${listResult.error}`; break; }
        if (Array.isArray(listResult) && listResult.length === 0) { resultText = `目录 "${parameters.path}" 为空或不包含文件。`; break; }
        const formatNode = (node, indent = "") => { let str = `${indent}- ${node.name} ${node.type === 'directory' ? '(目录)' : '(文件)'}\n`; if (node.children && node.children.length > 0) node.children.forEach(child => { str += formatNode(child, indent + "  "); }); return str; };
        resultText = `目录 "${parameters.path}" 下的文件列表:\n${Array.isArray(listResult) ? listResult.map(node => formatNode(node)).join('') : "无法格式化文件列表。"}`;
        break;
      case 'writeFile':
        if (!parameters || typeof parameters.path !== 'string' || typeof parameters.content !== 'string') { resultText = "错误: '写入文件' 指令缺少有效的 'path' 或 'content' 参数。"; break; }
        const writeResult = await fileSystemService.writeFile(parameters.path, parameters.content);
        resultText = writeResult.success ? `文件 "${parameters.path}" 已成功写入！` : `错误: 写入文件 "${parameters.path}" 失败 - ${writeResult.error}`;
        break;
      case 'readFile':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '读取文件' 指令缺少有效的 'path' 参数。"; break; }
        const readResult = await fileSystemService.readFile(parameters.path);
        if (readResult.error) { resultText = `错误: 读取文件失败 - ${readResult.error}`; break; }
        let fileContentDisplay = typeof readResult === 'string' ? readResult : JSON.stringify(readResult);
        if (fileContentDisplay.length > 2000) fileContentDisplay = fileContentDisplay.substring(0, 2000) + "\n\n... (文件内容过长，已截断)";
        resultText = `文件 "${parameters.path}" 内容:\n\`\`\`\n${fileContentDisplay}\n\`\`\``;
        break;
      case 'isDirectoryEmpty':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '检查目录是否为空' 指令缺少有效的 'path' 参数。"; break; }
        const emptyResult = await fileSystemService.isDirectoryEmpty(parameters.path);
        if (typeof emptyResult === 'object' && emptyResult.error) { resultText = `错误: 检查目录失败 - ${emptyResult.error}`; break; }
        resultText = `目录 "${parameters.path}" ${emptyResult ? '是空的' : '不是空的'}。`;
        break;
      case 'copyDirectoryContents':
         if (!parameters || typeof parameters.sourceDir !== 'string' || typeof parameters.targetDir !== 'string') { resultText = "错误: '复制目录内容' 指令缺少有效的 'sourceDir' 或 'targetDir' 参数。"; break; }
        const copyResult = await fileSystemService.copyDirectoryContents(parameters.sourceDir, parameters.targetDir);
        resultText = copyResult.success ? `已成功将 "${parameters.sourceDir}" 的内容复制到 "${parameters.targetDir}"。` : `错误: 复制目录内容失败 - ${copyResult.error}`;
        break;
      case 'unknown':
      case 'error': // Handle error commands from AI as well
        resultText = `抱歉，小岚无法识别或执行您的文件操作指令: "${parameters?.originalInput || '未知输入'}" ${parameters?.error ? `(AI错误: ${parameters.error})` : ''}`;
        break;
      default:
        resultText = `错误: AI返回了未知的指令类型 "${command}"。`;
    }
    // The Bridge AI panel expects a string response, not the complex AIResponseWithStatus.
    // So we return the resultText directly if it was a file system command.
    // We need to ensure the return type of callAI matches what the Bridge expecting.
    // For now, let's wrap this in the AIResponseWithStatus structure to be consistent,
    // and the Bridge panel can extract .text or .json.
    // Actually, the Bridge panel's `processIntent` call expects a string directly.
    // This means callAI needs to differentiate its return type or the IPC handler needs to adapt.
    // Given current IPC structure, `bridgeAi:processIntent` expects a string.
    // So this function should return a string.
    return { text: resultText, status: null, scriptChoices: null, newSceneCardId: null, json: parsedCommand }; // Return full structure for consistency if other parts of callAI use it
}


export async function summarizeConversation(historyChunk) {
    // ... (logic remains same as before, but uses aiKernelService.generateContentInternal)
    console.log("AI_ORCHESTRATION_SERVICE: summarizeConversation called.");
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return "系统错误: AI服务未初始化或API Key缺失，无法进行摘要。";
    const summarizerPersonaSetting = await dbService.getAgentCoreSetting('summarizer_persona');
    const systemInstruction = summarizerPersonaSetting?.content || 'You are a helpful AI assistant that summarizes conversations concisely.';
    const conversationText = historyChunk.map(msg => `${msg.senderName || msg.sender}: ${msg.text.replace(/<[^>]+>/g, '')}`).join('\n');
    try {
        return await aiKernelService.generateContentInternal(
            [{role: "user", parts: [{text: `Please summarize the following conversation snippet:\n\n${conversationText}`}]}],
            { systemInstruction }, GEMINI_TEXT_MODEL, aiKernelService.getCurrentApiKey()
        );
    } catch (error) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (summarizeConversation):`, error); return `系统错误: 摘要生成失败 - ${error.message}`; }
}

export async function suggestResourcesForTask(taskTitle, taskDescription, projectId) {
    // ... (logic remains same)
    console.log(`AI_ORCHESTRATION_SERVICE: suggestResourcesForTask called (simulated) for project ${projectId}, title: ${taskTitle}`);
    // Placeholder: In a real scenario, this would involve a more complex RAG + LLM call via orchestrator
    return [];
}


export async function decomposeRequirementToTasks(requirementText, projectId) {
    // ... (logic remains same, but uses aiKernelService for LLM call)
    console.log(`AI_ORCHESTRATION_SERVICE: Decomposing requirement for project ${projectId}: "${requirementText.substring(0, 100)}..."`);
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
    const systemInstruction = (await dbService.getAgentCoreSetting('TaskDecomposerPersona'))?.content || `You are an AI assistant for task decomposition. Output valid JSON array of tasks: {"title": string, "description": string, "priority": 0|1|2|3, "status": "todo"}. Max 5-7 tasks. Empty array [] if vague. No extra text.`;
    const prompt = `Requirement Text:\n"""\n${requirementText}\n"""\n\nJSON Array of Tasks:`;
    try {
        const rawResponse = await aiKernelService.generateContentInternal([{ role: "user", parts: [{ text: prompt }] }], { systemInstruction, responseMimeType: "application/json" }, GEMINI_TEXT_MODEL, aiKernelService.getCurrentApiKey());
        if (typeof rawResponse !== 'string') throw new Error("AI返回了非文本响应。");
        let jsonStr = rawResponse.trim(); /* ... fence removal ... */
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s; const match = jsonStr.match(fenceRegex); if (match && match[2]) jsonStr = match[2].trim();
        const parsedTasks = JSON.parse(jsonStr);
        if (!Array.isArray(parsedTasks)) throw new Error("AI response was not a JSON array.");
        const validatedTasks = parsedTasks.map(task => ({ title: task.title || "未命名任务", description: task.description || "", priority: typeof task.priority === 'number' && task.priority >=0 && task.priority <=3 ? task.priority : 2, status: 'todo' }));
        return { success: true, tasks: validatedTasks };
    } catch (error) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (decomposeRequirementToTasks):`, error); return { success: false, error: `任务拆解失败: ${error.message}`, tasks: [] }; }
}

export async function analyzeAndDecomposeAideProject(projectId, projectPath) {
    // ... (logic remains same, uses the orchestrated callAI)
    console.log(`AI_ORCHESTRATION_SERVICE: Analyzing AIDE project ${projectId} at ${projectPath}.`);
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
    let fileListString = "无法获取文件列表。"; /* ... file listing logic ... */
    try { const files = await fileSystemService.listFiles(projectPath, true, 2); if (Array.isArray(files)) { const formatNode = (node, indent = "") => { let str = `${indent}- ${node.name} ${node.type === 'directory' ? '(目录)' : '(文件)'}\n`; if (node.children && node.children.length > 0) str += node.children.map(child => formatNode(child, indent + "  ")).join(''); return str; }; fileListString = files.map(node => formatNode(node)).join(''); if (fileListString.length > 2000) fileListString = fileListString.substring(0, 2000) + "\n... (截断)"; } else if (files.error) fileListString = `获取文件列表时出错: ${files.error}`; } catch (fsError) { fileListString = `扫描项目文件时发生错误: ${fsError.message}`; }
    const userPromptForAide = `项目文件结构 (部分):\n${fileListString}\n\n请基于此结构，反向推导一组已完成的开发任务列表。`;
    try {
        const aiResponse = await callAI(userPromptForAide, 'AideProjectAnalyzerPersona', { projectId, contextType: 'aide_project_analysis', aideProjectPath: projectPath, aideFileStructure: fileListString });
        if (aiResponse.json && Array.isArray(aiResponse.json)) { const tasks = aiResponse.json.map(task => ({ title: task.title || "AI生成的任务", description: task.description || "由AI根据项目结构逆向工程生成。", priority: typeof task.priority === 'number' ? task.priority : 2, status: 'done' })); return { success: true, tasks }; } 
        else { const errorMsg = aiResponse.text || "AI未能解析项目结构或返回了无效的JSON。"; return { success: false, error: errorMsg, tasks: [] }; }
    } catch (error) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (analyzeAndDecomposeAideProject):`, error); return { success: false, error: `AIDE项目任务逆向工程失败: ${error.message}`, tasks: [] }; }
}

export async function analyzeErrorLogFromService(logContent) {
    // ... (logic remains same, uses aiKernelService.generateContentInternal)
    console.log("AI_ORCHESTRATION_SERVICE: analyzeErrorLogFromService called.");
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return "错误：API Key未配置，无法分析日志。";
    const prompt = `你是一位经验丰富的AI系统分析员。请分析以下日志内容...`; // Full prompt as before
    try {
        return await aiKernelService.generateContentInternal(
            [{ role: 'user', parts: [{ text: prompt }] }], { temperature: 0.5 }, GEMINI_TEXT_MODEL, aiKernelService.getCurrentApiKey()
        );
    } catch (error) { console.error("AI_ORCHESTRATION_SERVICE_ERROR (analyzeErrorLogFromService):", error); return `AI分析日志时发生错误: ${error.message}`; }
}


console.log('AI_ORCHESTRATION_SERVICE_JS: File execution finished. Exports configured.');
