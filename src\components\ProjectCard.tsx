
// src/components/ProjectCard.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
// useSettings hook import removed
import type { Project, AppSettings } from '../types';
import { Icon } from '@/components/common/Icon';
import { SmartIcon, VisualContainer } from '@/components/common/VisualUtils';
import { APP_TITLE } from '@/config/globalConfig';
import { PROJECT_CARD_ID_NEW_PROJECT, PROJECT_CARD_ID_TRAINING_ROOM } from '@/features/projects/projectConstants';

interface ProjectCardProps {
  project: Project;
  onDelete: (projectId: string) => Promise<void>;
  onRename: (project: Project) => void;
  onDuplicate: (projectId: string) => Promise<Project | undefined>;
  onSetCover: (project: Project) => Promise<void>;
  onClickOverride?: () => void;
  isSpecialCard?: boolean;
  settings: AppSettings; // Settings now come as a prop
}

const DefaultCoverPattern: React.FC<{id: string, isSpecialCardType?: 'new' | 'training', settings: AppSettings}> = ({id, isSpecialCardType, settings}) => {
  const seed = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  let c1, c2;
  if (settings.currentTheme === 'theme-light') {
    c1 = `hsl(${(seed * 2) % 360}, 30%, 85%)`;
    c2 = `hsl(${(seed * 2 + 45) % 360}, 25%, 95%)`;
  } else {
    c1 = `hsl(${seed % 360}, 20%, 25%)`;
    c2 = `hsl(${(seed + 30) % 360}, 20%, 20%)`;
  }

  let patternStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    opacity: 0.3,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  if (isSpecialCardType === 'new') {
    return (
      <div style={{...patternStyle, backgroundColor: 'hsl(205, 50%, 30%)', opacity: 1}}>
        <Icon name="Plus" className="w-16 h-16 text-white/70" />
      </div>
    );
  }
  if (isSpecialCardType === 'training') {
    return (
      <div style={{...patternStyle, backgroundColor: 'hsl(270, 40%, 25%)', opacity: 1}}>
        <Icon name="Lock" className="w-16 h-16 text-white/70" />
      </div>
    );
  }

  if (settings.currentTheme === 'theme-light') {
     patternStyle = {
        ...patternStyle,
        backgroundImage: `
            linear-gradient(45deg, ${c1} 25%, transparent 25%, transparent 75%, ${c1} 75%),
            linear-gradient(45deg, ${c1} 25%, transparent 25%, transparent 75%, ${c1} 75%)
        `,
        backgroundSize: '30px 30px',
        backgroundPosition: '0 0, 15px 15px',
        backgroundColor: c2,
        opacity: 0.2,
    };
  } else {
     patternStyle = {
        ...patternStyle,
        backgroundImage: `
            linear-gradient(45deg, ${c1} 25%, transparent 25%),
            linear-gradient(-45deg, ${c1} 25%, transparent 25%),
            linear-gradient(45deg, transparent 75%, ${c1} 75%),
            linear-gradient(-45deg, transparent 75%, ${c1} 75%)`,
        backgroundSize: '20px 20px',
        backgroundColor: c2,
    };
  }
  return <div style={patternStyle}></div>;
}


export const ProjectCard: React.FC<ProjectCardProps> = ({
    project, onDelete, onRename, onDuplicate, onSetCover,
    onClickOverride, isSpecialCard, settings 
}) => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const menuRef = React.useRef<HTMLDivElement>(null);

  const handleClick = (e: React.MouseEvent) => {
    if (menuRef.current && menuRef.current.contains(e.target as Node)) {
      return;
    }
    if ((e.target as HTMLElement).closest('[data-menu-button="true"]')) {
        return;
    }
    if (onClickOverride) {
      onClickOverride();
    } else {
      navigate(`/project/${project.id}`);
    }
  };

  const handleMenuToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(prev => !prev);
  };

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && !(event.target as HTMLElement).closest('[data-menu-button="true"]')) {
        setIsMenuOpen(false);
      }
    };
    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);


  let coverImageUrlToUse = project.coverImageUrl;
  if (coverImageUrlToUse && !coverImageUrlToUse.startsWith('data:') && !coverImageUrlToUse.startsWith('http')) {
    coverImageUrlToUse = `app-avatar://${project.coverImageUrl.startsWith('project_covers/') ? project.coverImageUrl : `project_covers/${project.id}/${project.coverImageUrl.split('/').pop()}`}`;
  } else if (!coverImageUrlToUse && settings.defaultCover && settings.defaultCover.startsWith('default_covers/')) {
    coverImageUrlToUse = `app-avatar://${settings.defaultCover}`;
  }

  const lastModifiedTimestamp = project.lastModifiedAt ? new Date(project.lastModifiedAt).getTime() : new Date(project.createdAt).getTime();

  const coverStyle: React.CSSProperties = {
    backgroundImage: coverImageUrlToUse ? `url("${coverImageUrlToUse}?t=${lastModifiedTimestamp}")` : undefined,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    position: 'relative',
  };

  const cardShadowStyle = {
    boxShadow: '0 4px 6px -1px rgba(var(--color-shadow-rgb), 0.1), 0 2px 4px -2px rgba(var(--color-shadow-rgb), 0.1)'
  };
  const cardHoverShadowStyle = {
    boxShadow: '0 10px 15px -3px rgba(var(--color-shadow-rgb), 0.1), 0 4px 6px -4px rgba(var(--color-shadow-rgb), 0.1)'
  };

  const specialCardType = project.id === PROJECT_CARD_ID_NEW_PROJECT ? 'new' : project.id === PROJECT_CARD_ID_TRAINING_ROOM ? 'training' : undefined;

  return (
    <div
      onClick={handleClick}
      className="rounded-2xl overflow-hidden cursor-pointer transition-all duration-300 group bg-gradient-card border border-tg-border-primary flex flex-col card-enhanced glow-on-hover backdrop-blur-sm"
      style={cardShadowStyle}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = cardHoverShadowStyle.boxShadow;
        e.currentTarget.style.transform = 'translateY(-4px) scale(1.02)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = cardShadowStyle.boxShadow;
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
      }}
      title={isSpecialCard ? project.name : `打开项目: ${project.name}`}
      role="listitem"
      aria-label={`项目卡片：${project.name}`}
    >
      <div className="h-32 sm:h-40 relative" style={coverStyle}>
        {!coverImageUrlToUse && <DefaultCoverPattern id={project.id} isSpecialCardType={specialCardType} settings={settings} />}
        {!isSpecialCard && (
         <button
            data-menu-button="true"
            onClick={handleMenuToggle}
            className="absolute top-2 right-2 p-1.5 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors z-10"
            title="更多操作"
            aria-label="项目更多操作"
            aria-haspopup="true"
            aria-expanded={isMenuOpen}
        >
            <Icon name="Ellipsis" className="w-5 h-5" />
        </button>
        )}
        {isMenuOpen && !isSpecialCard && (
            <div
                ref={menuRef}
                className="absolute top-10 right-2 z-20 bg-tg-bg-secondary border border-tg-border-primary rounded-md shadow-lg py-1 w-40 text-sm"
                role="menu"
            >
                <button
                    onClick={(e) => { e.stopPropagation(); onRename(project); setIsMenuOpen(false); }}
                    className="w-full text-left px-3 py-1.5 hover:bg-tg-bg-hover text-tg-text-primary flex items-center"
                    role="menuitem"
                >
                   <Icon name="Pencil" className="w-4 h-4 mr-2"/> 重命名
                </button>
                <button
                    onClick={(e) => { e.stopPropagation(); onSetCover(project); setIsMenuOpen(false); }}
                    className="w-full text-left px-3 py-1.5 hover:bg-tg-bg-hover text-tg-text-primary flex items-center"
                    role="menuitem"
                >
                    <Icon name="Image" className="w-4 h-4 mr-2"/> 设置封面
                </button>
                <button
                    onClick={(e) => { e.stopPropagation(); onDuplicate(project.id); setIsMenuOpen(false); }}
                    className="w-full text-left px-3 py-1.5 hover:bg-tg-bg-hover text-tg-text-primary flex items-center"
                    role="menuitem"
                >
                    <Icon name="Copy" className="w-4 h-4 mr-2"/> 复制项目
                </button>
                 <div className="my-1 border-t border-tg-border-primary"></div>
                <button
                    onClick={(e) => { e.stopPropagation(); onDelete(project.id); setIsMenuOpen(false); }}
                    className="w-full text-left px-3 py-1.5 hover:bg-tg-danger-hover text-tg-danger flex items-center"
                    role="menuitem"
                >
                   <Icon name="Trash2" className="w-4 h-4 mr-2"/> 删除项目
                </button>
            </div>
        )}
      </div>
      <div className="p-4 flex-grow flex flex-col justify-between">
        <div>
          <h3
            className={`text-lg font-semibold mb-1 truncate transition-colors ${isSpecialCard ? 'text-tg-text-primary group-hover:text-tg-accent-primary' : 'text-tg-accent-primary group-hover:text-tg-accent-primary-hover'}`}
          >
            {project.name}
          </h3>
          {!isSpecialCard && (
            <>
              <p className="text-xs text-tg-text-secondary">
                创建于: {new Date(project.createdAt).toLocaleDateString()}
              </p>
              <p className="text-xs text-tg-text-secondary">
                修改于: {new Date(project.lastModifiedAt).toLocaleDateString()}
              </p>
            </>
          )}
        </div>
         {isSpecialCard && project.id === PROJECT_CARD_ID_TRAINING_ROOM && (
          <p className="text-xs text-purple-400 mt-1">进入私密互动空间</p>
        )}
        {isSpecialCard && project.id === PROJECT_CARD_ID_NEW_PROJECT && (
          <p className="text-xs text-blue-400 mt-1">开始您的下一个创作</p>
        )}
      </div>
    </div>
  );
};
