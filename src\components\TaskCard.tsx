// src/components/TaskCard.tsx
import React from 'react';
import type { Task, TaskPriority } from '@/types';
import { Icon } from '@/components/common/Icon'; 


interface TaskCardProps {
  task: Task;
  onClick: () => void; 
  onEnterCockpit: (e: React.MouseEvent) => void; 
  onDragStart: (e: React.DragEvent<HTMLDivElement>, task: Task) => void;
}

const getPriorityStyles = (priority: TaskPriority): {bgColor: string, textColor: string, label: string, borderColor: string} => { // priority is now number
  switch (priority) {
    case 0: return { bgColor: 'bg-red-600', textColor: 'text-white', label: '十万火急', borderColor: 'border-red-500' };
    case 1: return { bgColor: 'bg-yellow-500', textColor: 'text-yellow-900', label: '优先处理', borderColor: 'border-yellow-400' };
    case 2: return { bgColor: 'bg-sky-500', textColor: 'text-white', label: '常规', borderColor: 'border-sky-400' };
    case 3: return { bgColor: 'bg-gray-500', textColor: 'text-white', label: '低', borderColor: 'border-gray-400' };
    default: return { bgColor: 'bg-gray-400', textColor: 'text-gray-800', label: '未知', borderColor: 'border-gray-300' };
  }
};

export const TaskCard: React.FC<TaskCardProps> = ({ task, onClick, onEnterCockpit, onDragStart }) => {
  const priorityStyles = getPriorityStyles(task.priority);

  return (
    <div
      draggable
      onDragStart={(e) => onDragStart(e, task)}
      className={`p-2.5 rounded-md shadow-sm cursor-pointer transition-all duration-150 ease-in-out bg-tg-bg-tertiary border hover:shadow-md hover:border-tg-accent-primary focus:outline-none focus:ring-1 focus:ring-tg-accent-primary`}
      style={{borderColor: priorityStyles.borderColor, minHeight: '100px'}} // Reduced padding, min-height ensures some blockiness
      role="listitem"
      aria-label={`任务: ${task.title}, 优先级: ${priorityStyles.label}`}
    >
      <div onClick={onClick} className="cursor-pointer flex flex-col h-full"> 
        <h4 className="text-sm font-semibold mb-1 text-tg-text-primary truncate line-clamp-2" title={task.title}>{task.title}</h4>
        
        <div className="flex items-center justify-between text-[11px] text-tg-text-secondary mb-1.5">
          <span 
            className={`px-1.5 py-0.5 rounded-full text-[9px] font-medium ${priorityStyles.bgColor} ${priorityStyles.textColor}`}
            title={`优先级: ${priorityStyles.label}`}
          >
            {priorityStyles.label}
          </span>
          {task.due_date && (
            <span className="flex items-center" title={`截止日期: ${new Date(task.due_date).toLocaleDateString()}`}>
              <Icon name="Clock" className="w-2.5 h-2.5 mr-0.5"/>
              {new Date(task.due_date).toLocaleDateString()}
            </span>
          )}
        </div>

        {task.assignee_id && ( 
          <div className="flex items-center text-[11px] text-tg-text-placeholder mt-auto" title={`负责人: ${task.assignee_id}`}>
            <Icon name="CircleUser" className="w-3 h-3 mr-1"/>
            <span>{task.assignee_id}</span>
          </div>
        )}
      </div>
      <button
        onClick={onEnterCockpit}
        className="mt-1.5 w-full flex items-center justify-center px-2 py-1 text-[11px] font-medium rounded-md bg-green-700/80 text-white hover:bg-green-600 transition-colors shadow-sm"
        title="进入任务驾驶舱开始处理此任务"
      >
        <Icon name="Play" className="w-3 h-3 mr-1"/>
        驾驶舱
      </button>
    </div>
  );
};
