// src/components/roundtable/ParticipantsPanel.tsx
import React from 'react';
import type { Character, RoundtableParticipant, Post, Assignment } from '@/types';
import { Icon } from '@/components/common/Icon';

// Redefined ParticipantsPanelProps to explicitly include all fields
// This replaces:
// import type { ..., ParticipantsPanelProps as OriginalParticipantsPanelProps, ... } from '@/types';
// interface ParticipantsPanelProps extends OriginalParticipantsPanelProps { ... }
interface ParticipantsPanelProps {
  // Fields from the original ParticipantsPanelProps (from src/types/roundtableTypes.ts)
  allCharacters: Character[];
  activeParticipantIds: string[];
  onParticipantToggle: (characterId: string) => void;
  discussionRounds: number;
  onDiscussionRoundsChange: (rounds: number) => void;
  onStartMeeting: () => void; // This prop might not be used directly by the panel if meeting start is handled by parent.
  isMeetingActive: boolean;
  isLoading: boolean;

  // Fields added in the local extension
  allPosts: Post[];
  allAssignments: Assignment[];
}


export const ParticipantsPanel: React.FC<ParticipantsPanelProps> = ({
  allCharacters,
  allPosts, 
  allAssignments, 
  activeParticipantIds,
  onParticipantToggle,
  discussionRounds,
  onDiscussionRoundsChange,
  isMeetingActive,
  isLoading,
}) => {
  return (
    <div className="p-3 bg-tg-bg-tertiary rounded-lg shadow-md h-full flex flex-col">
      <h3 className="text-md font-semibold text-tg-accent-secondary mb-3 flex items-center">
        <Icon name="Users" className="w-5 h-5 mr-2" />
        作战参谋部
      </h3>

      <div className="mb-4">
        <label htmlFor="discussion-rounds" className="block text-xs text-tg-text-secondary mb-1">
          讨论轮数 (每位参会者发言次数):
        </label>
        <input
          type="number"
          id="discussion-rounds"
          value={discussionRounds}
          onChange={(e) => onDiscussionRoundsChange(Math.max(1, parseInt(e.target.value, 10)))}
          min="1"
          max="10"
          className="w-full p-2 bg-tg-bg-primary border border-tg-border-primary rounded-md text-sm focus:border-tg-accent-primary disabled:opacity-50"
          disabled={isMeetingActive || isLoading}
        />
      </div>

      <div className="mb-4 flex-grow overflow-y-auto custom-scrollbar pr-1">
        <p className="text-xs text-tg-text-secondary mb-1.5">选择与会AI角色:</p>
        {allCharacters.length === 0 && !isLoading && (
          <p className="text-xs text-tg-text-placeholder italic">暂无可用AI角色。</p>
        )}
        {isLoading && allCharacters.length === 0 && (
            <p className="text-xs text-tg-text-placeholder italic">加载角色中...</p>
        )}
        <ul className="space-y-2">
          {allCharacters.map((character) => {
            const participant = character as RoundtableParticipant; 
            const isActive = activeParticipantIds.includes(participant.id);
            const assignment = allAssignments.find(a => a.character_id === character.id);
            const post = assignment ? allPosts.find(p => p.id === assignment.post_id) : null;
            const displayName = post ? `[${post.name}] - ${character.name}` : character.name;
            
            return (
              <li key={participant.id} className="flex items-center justify-between p-2 bg-tg-bg-primary rounded-md border border-tg-border-primary/50">
                <div className="flex items-center overflow-hidden">
                  {participant.avatarPath ? (
                    <img src={`app-avatar://${participant.avatarPath}`} alt={character.name} className="w-7 h-7 rounded-full mr-2 object-cover flex-shrink-0 transition-all duration-300 hover:scale-110"/>
                  ) : (
                    <Icon name="CircleUser" className="w-7 h-7 mr-2 text-tg-text-placeholder flex-shrink-0" />
                  )}
                  <span className="text-sm text-tg-text-primary truncate" title={displayName}>{displayName}</span>
                </div>
                <button
                  onClick={() => onParticipantToggle(participant.id)}
                  className={`p-1.5 rounded-full transition-colors flex-shrink-0 ${
                    isActive ? 'bg-green-500 hover:bg-green-600' : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                  title={isActive ? '移出会议' : '加入会议'}
                  disabled={isMeetingActive || isLoading}
                  aria-pressed={isActive}
                >
                  <Icon name={isActive ? "CheckCircle" : "PlusCircle"} className="w-4 h-4 text-white" />
                </button>
              </li>
            );
          })}
        </ul>
      </div>
      
       {activeParticipantIds.length === 0 && !isMeetingActive && (
         <p className="text-xs text-tg-text-placeholder text-center mt-auto py-1">
            请至少选择一位AI角色以开始对话。
         </p>
       )}
       {activeParticipantIds.length === 1 && !isMeetingActive && (
         <p className="text-xs text-tg-text-placeholder text-center mt-auto py-1">
            当前为与【{allCharacters.find(c=>c.id === activeParticipantIds[0])?.name || '选中角色'}】单人对话模式。
         </p>
       )}
       {activeParticipantIds.length >= 2 && !isMeetingActive && (
         <p className="text-xs text-tg-text-placeholder text-center mt-auto py-1">
            已选择多位参会者。在主聊天区发送消息以发起会议。
         </p>
       )}
    </div>
  );
};