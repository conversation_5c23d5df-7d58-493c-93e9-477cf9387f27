import React from 'react';
import type { Editor } from '@tiptap/core'; // Corrected import
// import { Icon } from '@/components/common/Icon'; // Import Icon if visual icons are used

interface EditorToolbarProps {
  editor: Editor | null;
}

const MANDALA_RED = '#DC143C'; 
const HIGHLIGHT_YELLOW = '#FEF9C3'; 

export const EditorToolbar: React.FC<EditorToolbarProps> = ({ editor }) => {
  if (!editor) {
    return null;
  }

  const commonButtonClass = "p-2 rounded text-sm transition-colors disabled:opacity-50";
  const activeClass = "bg-tg-accent-primary text-white";
  const inactiveClass = "bg-tg-bg-tertiary hover:bg-tg-bg-hover text-tg-text-secondary";

  return (
    <div className="flex items-center space-x-1 p-1.5 mb-1.5 bg-tg-bg-secondary border border-tg-border-primary rounded-md shadow">
      <button
        onClick={() => editor.chain().focus().toggleMark('bold').run()}
        disabled={!editor.can().toggleMark('bold')}
        className={`${commonButtonClass} ${editor.isActive('bold') ? activeClass : inactiveClass}`}
        title="加粗 (Bold)"
      >
        {/* <Icon name="Bold" className="w-4 h-4" /> Example if using Lucide's 'Bold' icon */}
        <span className="font-bold">B</span>
      </button>
      <button
        onClick={() => editor.chain().focus().toggleMark('highlight', { color: HIGHLIGHT_YELLOW }).run()}
        disabled={!editor.can().toggleMark('highlight', {color: HIGHLIGHT_YELLOW})}
        className={`${commonButtonClass} ${editor.isActive('highlight', { color: HIGHLIGHT_YELLOW }) ? activeClass : inactiveClass}`}
        title="高亮 (Highlight Yellow)"
      >
        {/* <Icon name="Highlighter" className="w-4 h-4" /> Example if using Lucide's 'Highlighter' icon */}
        <span style={{ backgroundColor: HIGHLIGHT_YELLOW, padding: '0 2px', borderRadius:'2px', color: '#713F12' }}>H</span>
      </button>
      <button
        onClick={() => editor.chain().focus().setMark('textStyle', { color: MANDALA_RED }).run()}
        disabled={!editor.can().setMark('textStyle')}
        className={`${commonButtonClass} ${editor.isActive('textStyle', { color: MANDALA_RED }) ? activeClass : inactiveClass}`}
        title="文字颜色 (Mandala Red)"
      >
         {/* <Icon name="Palette" className="w-4 h-4" /> Example if using Lucide's 'Palette' icon */}
        <span style={{ color: MANDALA_RED }}>A</span>
      </button>
      {/* Add more buttons here as needed */}
    </div>
  );
};