// electron/services/projectDbService.js
console.log('PROJECT_DB_SERVICE_JS: File execution started.');

import { db, parseJsonArray, mapChatMessage, mapNoteItem, mapDevelopmentTask, crypto } from './databaseCore';

export function getAllProjects() {
 if (!db) { console.error("PROJECT_DB_ERROR: getAllProjects - db not available."); return []; }
 try {
   const projectRows = db.prepare('SELECT * FROM projects ORDER BY createdAt DESC').all();
   const projects = projectRows.map(p => {
     const messages = db.prepare('SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp ASC').all(p.id);
     const inspirationNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'inspiration' ORDER BY createdAt DESC").all(p.id);
     const bugMemoNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'bugs' ORDER BY createdAt DESC").all(p.id);
     const quickCommandsNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'commands' ORDER BY createdAt DESC").all(p.id);
     const mindNodes = db.prepare('SELECT * FROM mindworkshop_nodes WHERE projectId = ?').all(p.id);
     const mindConnections = db.prepare('SELECT * FROM mindworkshop_connections WHERE projectId = ?').all(p.id);
     const projectKnowledgeTomesRaw = db.prepare('SELECT * FROM project_knowledge_tomes WHERE projectId = ? ORDER BY lastModifiedAt DESC').all(p.id);
     const projectKnowledgeTomes = projectKnowledgeTomesRaw.map(t => ({...t, tags: parseJsonArray(t.tags)}));
     const categoryRows = db.prepare('SELECT categoryName FROM project_knowledge_categories WHERE projectId = ? ORDER BY categoryName ASC').all(p.id);
     const projectKnowledgeCategories = categoryRows.map(r => r.categoryName);
     const developmentTasksRaw = db.prepare('SELECT * FROM development_tasks WHERE projectId = ? ORDER BY createdAt DESC').all(p.id);
     const developmentTasks = developmentTasksRaw.map(mapDevelopmentTask);

     return {
       ...p,
       sourceCodePath: p.sourceCodePath || null,
       discussionMessages: messages.map(mapChatMessage),
       inspirationNotes: inspirationNotes.map(mapNoteItem),
       bugMemoNotes: bugMemoNotes.map(mapNoteItem),
       quickCommandsNotes: quickCommandsNotes.map(mapNoteItem),
       mindNodes,
       mindConnections,
       projectKnowledgeTomes,
       projectKnowledgeCategories,
       developmentTasks,
       isDeleted: p.isDeleted === 1, // Ensure boolean conversion
     };
   });
   console.log(`PROJECT_DB: Retrieved ${projects.length} projects.`);
   return projects;
 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error getting all projects:', error);
   return [];
 }
}

export function getProjectById(projectId) {
 if (!db) { console.error(`PROJECT_DB_ERROR: getProjectById(${projectId}) - db not available.`); return null; }
 try {
   const p = db.prepare('SELECT * FROM projects WHERE id = ?').get(projectId);
   if (!p) {
     console.log(`PROJECT_DB: Project with ID ${projectId} not found.`);
     return null;
   }
   // Logic for fetching related data (messages, notes, etc.) as in original getAllProjects but for a single project
   const messages = db.prepare('SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp ASC').all(p.id);
   const inspirationNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'inspiration' ORDER BY createdAt DESC").all(p.id);
   const bugMemoNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'bugs' ORDER BY createdAt DESC").all(p.id);
   const quickCommandsNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'commands' ORDER BY createdAt DESC").all(p.id);
   const mindNodes = db.prepare('SELECT * FROM mindworkshop_nodes WHERE projectId = ?').all(p.id);
   const mindConnections = db.prepare('SELECT * FROM mindworkshop_connections WHERE projectId = ?').all(p.id);
   const projectKnowledgeTomesRaw = db.prepare('SELECT * FROM project_knowledge_tomes WHERE projectId = ? ORDER BY lastModifiedAt DESC').all(p.id);
   const projectKnowledgeTomes = projectKnowledgeTomesRaw.map(t => ({...t, tags: parseJsonArray(t.tags)}));
   const categoryRows = db.prepare('SELECT categoryName FROM project_knowledge_categories WHERE projectId = ? ORDER BY categoryName ASC').all(p.id);
   const projectKnowledgeCategories = categoryRows.map(r => r.categoryName);
   const developmentTasksRaw = db.prepare('SELECT * FROM development_tasks WHERE projectId = ? ORDER BY createdAt DESC').all(p.id);
   const developmentTasks = developmentTasksRaw.map(mapDevelopmentTask);

   console.log(`PROJECT_DB: Retrieved project by ID ${projectId}.`);
   return {
     ...p,
     sourceCodePath: p.sourceCodePath || null,
     discussionMessages: messages.map(mapChatMessage),
     inspirationNotes: inspirationNotes.map(mapNoteItem),
     bugMemoNotes: bugMemoNotes.map(mapNoteItem),
     quickCommandsNotes: quickCommandsNotes.map(mapNoteItem),
     mindNodes,
     mindConnections,
     projectKnowledgeTomes,
     projectKnowledgeCategories,
     developmentTasks,
     isDeleted: p.isDeleted === 1, // Ensure boolean conversion
   };
 } catch (error) {
   console.error(`PROJECT_DB_ERROR: Error getting project by ID ${projectId}:`, error);
   return null;
 }
}

export function addProject(projectData) {
  console.log('[PROJECT_DB_SERVICE][addProject] Attempting to add project with data:', JSON.stringify(projectData, null, 2));
  if (!db) {
    const errorMsg = "PROJECT_DB_ERROR: addProject - Database not initialized.";
    console.error(errorMsg);
    throw new Error(errorMsg);
  }
  try {
    const transaction = db.transaction((data) => {
      console.log('[PROJECT_DB_SERVICE][addProject] Inside transaction. Data to insert:', JSON.stringify(data, null, 2));
      const stmt = db.prepare('INSERT INTO projects (id, name, coverImageUrl, createdAt, sourceCodePath, lastModifiedAt, isDeleted) VALUES (?, ?, ?, ?, ?, ?, ?)');
      stmt.run(
        data.id, 
        data.name, 
        data.coverImageUrl || null,
        data.createdAt, 
        data.sourceCodePath || null,
        data.lastModifiedAt, 
        data.isDeleted ? 1 : 0
      );
      console.log(`[PROJECT_DB_SERVICE][addProject] Project insert successful for ID: ${data.id}`);
      
      const fullProjectObject = { 
          ...data,
          coverImageUrl: data.coverImageUrl || null,
          sourceCodePath: data.sourceCodePath || null,
          isDeleted: !!data.isDeleted,
          discussionMessages: [], 
          inspirationNotes: [], 
          bugMemoNotes: [], 
          quickCommandsNotes: [],
          mindNodes: [], 
          mindConnections: [], 
          projectKnowledgeTomes: [], 
          projectKnowledgeCategories: [], 
          developmentTasks: []
      };
      console.log('[PROJECT_DB_SERVICE][addProject] Returning full project object from transaction:', JSON.stringify(fullProjectObject, null, 2));
      return fullProjectObject;
    });
    
    const result = transaction(projectData);
    console.log(`[PROJECT_DB_SERVICE][addProject] Project added and transaction committed. ID: ${result.id}.`);
    return result;
  } catch (dbError) {
      console.error('[PROJECT_DB_SERVICE][addProject] Database error during addProject transaction:', dbError.message, dbError.stack);
      throw dbError;
  }
}


export function updateProject(project) {
 if (!db) {
   console.error("PROJECT_DB_ERROR: updateProject - Database not initialized.");
   throw new Error("Database not initialized for updateProject.");
 }
 const transaction = db.transaction((proj) => {
     const projectStmt = db.prepare('UPDATE projects SET name = ?, coverImageUrl = ?, sourceCodePath = ?, lastModifiedAt = ?, isDeleted = ? WHERE id = ?');
     projectStmt.run(proj.name, proj.coverImageUrl, proj.sourceCodePath || null, proj.lastModifiedAt, proj.isDeleted ? 1 : 0, proj.id);
     
     db.prepare('DELETE FROM chat_messages WHERE projectId = ?').run(proj.id);
     const chatMsgStmt = db.prepare('INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
     for (const msg of proj.discussionMessages || []) {
         chatMsgStmt.run(msg.id, proj.id, msg.sender, msg.senderName, msg.text, msg.timestamp, msg.isEditing ? 1 : 0, msg.isStarred ? 1 : 0, msg.isPinned ? 1 : 0, msg.theme, msg.replyToMessageId, msg.triggeringUserMessageId);
     }
     
     db.prepare('DELETE FROM wisdom_pouch_notes WHERE projectId = ?').run(proj.id);
     const noteStmt = db.prepare('INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)');
     for (const note of proj.inspirationNotes || []) {
         noteStmt.run(note.id, proj.id, 'inspiration', note.text, note.createdAt, note.lastModifiedAt, note.importance);
     }
     for (const note of proj.bugMemoNotes || []) {
         noteStmt.run(note.id, proj.id, 'bugs', note.text, note.createdAt, note.lastModifiedAt, note.importance);
     }
     for (const note of proj.quickCommandsNotes || []) {
         noteStmt.run(note.id, proj.id, 'commands', note.text, note.createdAt, note.lastModifiedAt, note.importance);
     }

     db.prepare('DELETE FROM mindworkshop_nodes WHERE projectId = ?').run(proj.id);
     db.prepare('DELETE FROM mindworkshop_connections WHERE projectId = ?').run(proj.id);
     const mindNodeStmt = db.prepare('INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)');
     for (const node of proj.mindNodes || []) {
         mindNodeStmt.run(node.id, proj.id, node.text, node.x, node.y, node.width, node.height);
     }
     const mindConnStmt = db.prepare('INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)');
     for (const conn of proj.mindConnections || []) {
         mindConnStmt.run(conn.id, proj.id, conn.fromNodeId, conn.toNodeId);
     }

     db.prepare('DELETE FROM project_knowledge_tomes WHERE projectId = ?').run(proj.id);
     const pkTomeStmt = db.prepare('INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
     for (const tome of proj.projectKnowledgeTomes || []) {
         pkTomeStmt.run(tome.id, proj.id, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.createdAt, tome.lastModifiedAt);
     }
     db.prepare('DELETE FROM project_knowledge_categories WHERE projectId = ?').run(proj.id);
     const pkCatStmt = db.prepare('INSERT INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)');
     for (const category of proj.projectKnowledgeCategories || []) {
         pkCatStmt.run(proj.id, category);
     }
     
     db.prepare('DELETE FROM development_tasks WHERE projectId = ?').run(proj.id);
     const devTaskStmt = db.prepare('INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)');
     for (const task of proj.developmentTasks || []) {
         devTaskStmt.run(task.id, proj.id, task.title, task.status, task.createdAt, JSON.stringify(task.context_files || []), task.generated_code);
     }

     return { success: true, project: {...proj, sourceCodePath: proj.sourceCodePath || null, isDeleted: !!proj.isDeleted} };
 });
 const result = transaction(project);
 console.log(`PROJECT_DB: Updated project with ID ${project.id}.`);
 return result;
}

export function deleteProject(projectId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: deleteProject - Database not initialized.");
    return { success: false, error: "Database not initialized." };
  }
  try {
    const stmt = db.prepare('UPDATE projects SET isDeleted = 1, lastModifiedAt = ? WHERE id = ?');
    const info = stmt.run(new Date().toISOString(), projectId);
    if (info.changes > 0) {
      console.log(`PROJECT_DB: Marked project ${projectId} as deleted.`);
      return { success: true };
    } else {
      console.warn(`PROJECT_DB_WARN: Project ${projectId} not found for deletion marking.`);
      return { success: false, error: "Project not found." };
    }
  } catch (error) {
    console.error(`PROJECT_DB_ERROR: Error marking project ${projectId} as deleted:`, error);
    return { success: false, error: error.message };
  }
}

export function duplicateProject(sourceProjectId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: duplicateProject - Database not initialized.");
    return null;
  }
  const originalProject = getProjectById(sourceProjectId);
  if (!originalProject) {
    console.warn(`PROJECT_DB_WARN: Project ${sourceProjectId} not found for duplication.`);
    return null;
  }

  const newProjectId = crypto.randomUUID();
  const newProjectName = `${originalProject.name} (副本)`;
  const now = new Date().toISOString();

  const newProjectCoreData = {
    id: newProjectId,
    name: newProjectName,
    coverImageUrl: originalProject.coverImageUrl,
    createdAt: now,
    lastModifiedAt: now,
    sourceCodePath: originalProject.sourceCodePath,
    isDeleted: 0,
  };
  
  let duplicatedFullProject;

  const duplicationTransaction = db.transaction(() => {
    // 1. Insert the new project shell
    const projectInsertStmt = db.prepare('INSERT INTO projects (id, name, coverImageUrl, createdAt, lastModifiedAt, sourceCodePath, isDeleted) VALUES (?, ?, ?, ?, ?, ?, ?)');
    projectInsertStmt.run(newProjectCoreData.id, newProjectCoreData.name, newProjectCoreData.coverImageUrl, newProjectCoreData.createdAt, newProjectCoreData.lastModifiedAt, newProjectCoreData.sourceCodePath, newProjectCoreData.isDeleted);

    // 2. Duplicate Chat Messages
    if (originalProject.discussionMessages && originalProject.discussionMessages.length > 0) {
      const chatMsgStmt = db.prepare('INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
      for (const msg of originalProject.discussionMessages) {
        chatMsgStmt.run(crypto.randomUUID(), newProjectId, msg.sender, msg.senderName, msg.text, msg.timestamp, msg.isEditing ? 1 : 0, msg.isStarred ? 1 : 0, msg.isPinned ? 1 : 0, msg.theme, msg.replyToMessageId, msg.triggeringUserMessageId);
      }
    }

    // 3. Duplicate Wisdom Pouch Notes
    const noteStmt = db.prepare('INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)');
    for (const note of originalProject.inspirationNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, 'inspiration', note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of originalProject.bugMemoNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, 'bugs', note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of originalProject.quickCommandsNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, 'commands', note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    
    // 4. Duplicate Mind Map Nodes & Connections
    const mindNodeIdMap = new Map();
    if (originalProject.mindNodes && originalProject.mindNodes.length > 0) {
        const mindNodeStmt = db.prepare('INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)');
        for (const node of originalProject.mindNodes) {
            const newNodeId = crypto.randomUUID();
            mindNodeIdMap.set(node.id, newNodeId);
            mindNodeStmt.run(newNodeId, newProjectId, node.text, node.x, node.y, node.width, node.height);
        }
    }
    if (originalProject.mindConnections && originalProject.mindConnections.length > 0) {
        const mindConnStmt = db.prepare('INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)');
        for (const conn of originalProject.mindConnections) {
            const newFromNodeId = mindNodeIdMap.get(conn.fromNodeId);
            const newToNodeId = mindNodeIdMap.get(conn.toNodeId);
            if (newFromNodeId && newToNodeId) { // Only insert if both nodes were successfully mapped
                mindConnStmt.run(crypto.randomUUID(), newProjectId, newFromNodeId, newToNodeId);
            } else {
                console.warn(`PROJECT_DB_DUPLICATE: Skipping mind connection ${conn.id} due to missing node mapping.`);
            }
        }
    }

    // 5. Duplicate Project Knowledge Tomes
    if (originalProject.projectKnowledgeTomes && originalProject.projectKnowledgeTomes.length > 0) {
        const pkTomeStmt = db.prepare('INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        for (const tome of originalProject.projectKnowledgeTomes) {
            pkTomeStmt.run(crypto.randomUUID(), newProjectId, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), now, now);
        }
    }
    
    // 6. Duplicate Project Knowledge Categories
    if (originalProject.projectKnowledgeCategories && originalProject.projectKnowledgeCategories.length > 0) {
        const pkCatStmt = db.prepare('INSERT INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)');
        for (const category of originalProject.projectKnowledgeCategories) {
            pkCatStmt.run(newProjectId, category);
        }
    }

    // 7. Duplicate Development Tasks
    if (originalProject.developmentTasks && originalProject.developmentTasks.length > 0) {
        const devTaskStmt = db.prepare('INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)');
        for (const task of originalProject.developmentTasks) {
            devTaskStmt.run(crypto.randomUUID(), newProjectId, task.title, task.status, now, JSON.stringify(task.context_files || []), task.generated_code);
        }
    }
    // After all insertions, fetch the complete new project to return it
    duplicatedFullProject = getProjectById(newProjectId); 
  });

  try {
    duplicationTransaction();
    console.log(`PROJECT_DB: Duplicated project ${sourceProjectId} to ${newProjectId} successfully.`);
    return duplicatedFullProject; // This will be the full project object
  } catch (error) {
    console.error(`PROJECT_DB_ERROR: Error in duplicationTransaction for project ${sourceProjectId}:`, error);
    return null; // Return null on transaction error
  }
}


export function addNoteToPouch(projectId, pouchType, noteData) {
    if (!db) { console.error("PROJECT_DB_ERROR: addNoteToPouch - db not available."); return null; }
    const stmt = db.prepare('INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)');
    try {
        stmt.run(noteData.id, projectId, pouchType, noteData.text, noteData.createdAt, noteData.lastModifiedAt, noteData.importance);
        console.log(`PROJECT_DB: Added note ${noteData.id} to pouch ${pouchType} for project ${projectId}.`);
        return { ...noteData, isEditing: false }; // isEditing is UI state
    } catch (error) {
        console.error('PROJECT_DB_ERROR: Error adding note to pouch:', error);
        return null;
    }
}

export function updateNoteInPouch(pouchType, noteData) { // projectId is implicit via noteData.id context if needed, but not for query
    if (!db) { console.error("PROJECT_DB_ERROR: updateNoteInPouch - db not available."); return null; }
    const stmt = db.prepare('UPDATE wisdom_pouch_notes SET text = ?, lastModifiedAt = ?, importance = ? WHERE id = ? AND pouchType = ?');
    try {
        // Assuming noteData contains id
        stmt.run(noteData.text, noteData.lastModifiedAt, noteData.importance, noteData.id, pouchType);
        console.log(`PROJECT_DB: Updated note ${noteData.id} in pouch ${pouchType}.`);
        return { ...noteData, isEditing: false };
    } catch (error) {
        console.error('PROJECT_DB_ERROR: Error updating note in pouch:', error);
        return null;
    }
}

export function deleteNoteFromPouch(pouchType, noteId) { // projectId is implicit
    if (!db) { console.error("PROJECT_DB_ERROR: deleteNoteFromPouch - db not available."); return { success: false, error: "Database not available."}; }
    const stmt = db.prepare('DELETE FROM wisdom_pouch_notes WHERE id = ? AND pouchType = ?');
    try {
        stmt.run(noteId, pouchType);
        console.log(`PROJECT_DB: Deleted note ${noteId} from pouch ${pouchType}.`);
        return { success: true, id: noteId };
    } catch (error) {
        console.error('PROJECT_DB_ERROR: Error deleting note from pouch:', error);
        return { success: false, error: error.message };
    }
}

export function updateProjectMindMap(projectId, nodes, connections) {
 if (!db) {
   console.error("PROJECT_DB_ERROR: updateProjectMindMap - Database not initialized.");
   throw new Error("Database not initialized for updateProjectMindMap.");
 }
 const transaction = db.transaction((pid, nodeList, connList) => {
   db.prepare('DELETE FROM mindworkshop_nodes WHERE projectId = ?').run(pid);
   db.prepare('DELETE FROM mindworkshop_connections WHERE projectId = ?').run(pid);

   const nodeStmt = db.prepare('INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)');
   for (const node of nodeList) {
       nodeStmt.run(node.id, pid, node.text, node.x, node.y, node.width, node.height);
   }
   const connStmt = db.prepare('INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)');
   for (const conn of connList) {
       connStmt.run(conn.id, pid, conn.fromNodeId, conn.toNodeId);
   }
   return { success: true };
 });
 const result = transaction(projectId, nodes, connections);
 console.log(`PROJECT_DB: Updated mind map for project ${projectId}.`);
 return result;
}

export function addProjectKnowledgeTome(projectId, tome) {
    if (!db) { console.error("PROJECT_DB_ERROR: addProjectKnowledgeTome - db not available."); return null; }
    const stmt = db.prepare('INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    try {
        stmt.run(tome.id, projectId, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.createdAt, tome.lastModifiedAt);
        console.log(`PROJECT_DB: Added project knowledge tome ${tome.id} for project ${projectId}.`);
        return tome;
    } catch (e) {
        console.error('PROJECT_DB_ERROR: adding project tome',e);
        return null;
    }
}

export function updateProjectKnowledgeTome(projectId, tome) { 
    if (!db) { console.error("PROJECT_DB_ERROR: updateProjectKnowledgeTome - db not available."); return null; }
    const stmt = db.prepare('UPDATE project_knowledge_tomes SET title = ?, content = ?, projectCategory = ?, tags = ?, lastModifiedAt = ? WHERE id = ? AND projectId = ?');
    try {
        stmt.run(tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.lastModifiedAt, tome.id, projectId);
        console.log(`PROJECT_DB: Updated project knowledge tome ${tome.id} for project ${projectId}.`);
        return tome;
    } catch (e) {
        console.error('PROJECT_DB_ERROR: updating project tome',e);
        return null;
    }
}

export function deleteProjectKnowledgeTome(projectId, tomeId) {
    if (!db) { console.error("PROJECT_DB_ERROR: deleteProjectKnowledgeTome - db not available."); return { success: false, error: "Database not available." }; }
    const stmt = db.prepare('DELETE FROM project_knowledge_tomes WHERE id = ? AND projectId = ?');
    try {
        stmt.run(tomeId, projectId);
        console.log(`PROJECT_DB: Deleted project knowledge tome ${tomeId} for project ${projectId}.`);
        return { success: true };
    } catch (e) {
        console.error('PROJECT_DB_ERROR: deleting project tome',e);
        return { success: false, error: e.message };
    }
}

export function addProjectKnowledgeCategory(projectId, categoryName) {
    if (!db) { console.error("PROJECT_DB_ERROR: addProjectKnowledgeCategory - db not available."); return undefined; }
    const stmt = db.prepare('INSERT OR IGNORE INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)');
    try {
        const info = stmt.run(projectId, categoryName);
        if(info.changes > 0) console.log(`PROJECT_DB: Added category '${categoryName}' for project ${projectId}.`);
        return info.changes > 0 ? categoryName : undefined; // Return categoryName if added, undefined otherwise
    } catch (e) {
        console.error('PROJECT_DB_ERROR: adding project category',e);
        return undefined;
    }
}

export function removeProjectKnowledgeCategory(projectId, categoryName) {
 if (!db) {
   console.error("PROJECT_DB_ERROR: removeProjectKnowledgeCategory - Database not initialized.");
   throw new Error("Database not initialized for removeProjectKnowledgeCategory.");
 }
 const transaction = db.transaction((pid, catName) => {
   // Update tomes in this category to '未分类'
   db.prepare('UPDATE project_knowledge_tomes SET projectCategory = ? WHERE projectId = ? AND projectCategory = ?')
     .run('未分类', pid, catName);
   // Delete the category itself
   db.prepare('DELETE FROM project_knowledge_categories WHERE projectId = ? AND categoryName = ?')
     .run(pid, catName);
   return { success: true };
 });
 try {
    const result = transaction(projectId, categoryName);
    console.log(`PROJECT_DB: Removed category '${categoryName}' for project ${projectId}. Tomes reassigned to '未分类'.`);
    return result;
 } catch (e) {
    console.error(`PROJECT_DB_ERROR: removing project category ${categoryName}`, e);
    return { success: false, error: e.message };
 }
}

export function getAllDevelopmentTasks() {
 if (!db) { console.error("PROJECT_DB_ERROR: getAllDevelopmentTasks - db not available."); return []; }
 try {
   const tasksRaw = db.prepare('SELECT * FROM development_tasks ORDER BY createdAt DESC').all();
   const tasks = tasksRaw.map(mapDevelopmentTask);
   console.log(`PROJECT_DB: Retrieved ${tasks.length} development tasks.`);
   return tasks;
 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error getting all development tasks:', error);
   return [];
 }
}

export function addDevelopmentTask(projectId, title) {
 if (!db) { console.error("PROJECT_DB_ERROR: addDevelopmentTask - db not available."); return null; }
 const newTask = {
   id: crypto.randomUUID(),
   projectId,
   title, // This existing function only takes title
   status: 'todo',
   createdAt: new Date().toISOString(),
   context_files: [], 
   generated_code: null
 };
 const stmt = db.prepare('INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)');
 try {
   stmt.run(newTask.id, newTask.projectId, newTask.title, newTask.status, newTask.createdAt, JSON.stringify(newTask.context_files), newTask.generated_code);
   console.log(`PROJECT_DB: Added development task ${newTask.id} for project ${projectId}.`);
   return newTask;
 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error adding development task:', error);
   return null;
 }
}

export function createDevelopmentTaskFromChat(projectId, title, description) {
  if (!db) { console.error("PROJECT_DB_ERROR: createDevelopmentTaskFromChat - db not available."); return null; }
  
  let combinedTitle = title;
  if (description && description.trim() !== "") {
    combinedTitle += `\n\n--- 描述 ---\n${description.trim()}`;
  }

  const newTask = {
    id: crypto.randomUUID(),
    projectId,
    title: combinedTitle, 
    status: 'todo',
    createdAt: new Date().toISOString(),
    context_files: [], 
    generated_code: null
  };
  const stmt = db.prepare('INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)');
  try {
    stmt.run(newTask.id, newTask.projectId, newTask.title, newTask.status, newTask.createdAt, JSON.stringify(newTask.context_files), newTask.generated_code);
    console.log(`PROJECT_DB: Created development task from chat ${newTask.id} for project ${projectId}.`);
    return newTask;
  } catch (error) {
    console.error('PROJECT_DB_ERROR: Error creating development task from chat:', error);
    return null;
  }
}


export function deleteDevelopmentTask(taskId) {
 if (!db) { console.error("PROJECT_DB_ERROR: deleteDevelopmentTask - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare('DELETE FROM development_tasks WHERE id = ?');
 try {
   const info = stmt.run(taskId);
   if (info.changes > 0) {
     console.log(`PROJECT_DB: Deleted development task ${taskId}.`);
     return { success: true };
   }
   console.log(`PROJECT_DB: Development task ${taskId} not found for deletion.`);
   return { success: false, error: "Task not found." };
 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error deleting development task:', error);
   return { success: false, error: error.message };
 }
}

export function updateDevelopmentTaskContextFiles(taskId, contextFiles) {
 if (!db) { console.error("PROJECT_DB_ERROR: updateDevelopmentTaskContextFiles - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare('UPDATE development_tasks SET context_files = ? WHERE id = ?');
 try {
   const info = stmt.run(JSON.stringify(contextFiles || []), taskId);
   if (info.changes > 0) {
     console.log(`PROJECT_DB: Updated context_files for development task ${taskId}.`);
     return { success: true };
   }
   // Check if the task exists to differentiate "not found" from "no change needed"
   const taskExists = db.prepare('SELECT id FROM development_tasks WHERE id = ?').get(taskId);
    if (!taskExists) {
       console.log(`PROJECT_DB: Development task ${taskId} not found for context_files update.`);
       return { success: false, error: "Task not found." };
   }
   // If task exists but info.changes is 0, it means data was identical
   console.log(`PROJECT_DB: No change in context_files for development task ${taskId} (data might be identical).`);
   return { success: true }; // Still success as the state is as requested

 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error updating context_files for development task:', error);
   return { success: false, error: error.message };
 }
}

export function updateDevelopmentTaskGeneratedCode(taskId, generatedCode) {
 if (!db) { console.error("PROJECT_DB_ERROR: updateDevelopmentTaskGeneratedCode - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare('UPDATE development_tasks SET generated_code = ? WHERE id = ?');
 try {
   const info = stmt.run(generatedCode, taskId);
   if (info.changes > 0) {
     console.log(`PROJECT_DB: Updated generated_code for development task ${taskId}.`);
     return { success: true };
   }
   const taskExists = db.prepare('SELECT id FROM development_tasks WHERE id = ?').get(taskId);
    if (!taskExists) {
       console.log(`PROJECT_DB: Development task ${taskId} not found for generated_code update.`);
       return { success: false, error: "Task not found." };
   }
   console.log(`PROJECT_DB: No change in generated_code for development task ${taskId} (data might be identical).`);
   return { success: true };
 } catch (error) {
   console.error('PROJECT_DB_ERROR: Error updating generated_code for development task:', error);
   return { success: false, error: error.message };
 }
}

console.log('PROJECT_DB_SERVICE_JS: File execution finished. Exports configured.');