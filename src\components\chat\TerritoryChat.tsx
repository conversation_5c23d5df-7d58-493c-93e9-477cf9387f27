
// src/components/chat/TerritoryChat.tsx
import React, { useState, useEffect, useRef, useCallback, useImperativeHandle, forwardRef, useLayoutEffect, memo, useMemo } from 'react';
import type {
    ChatMessage,
    GlobalQuickCommandItem,
    ChatDiscussionAreaRef,
    AIResponseWithStatus,
    AiCallContext,
    WisdomPouchType,
    AppSettings,
    Character,
    XPHypothesisProposalData,
    ScriptChoice,
    LinLuoDetailedStatus,
    LinLuoSystemUpdatePayload,
} from '@/types';
import { Icon } from '@/components/common/Icon';
import { themeDefinitions, parseMessageTextForTheme, summaryBlockRegex } from '@/chatConstants';
import { ChatDiscussionMessageItem } from '@/components/chat/ChatDiscussionMessageItem';
import { GenericModal } from '@/components/GenericModal';

// Props for TerritoryChat, derived from original ChatDiscussionAreaProps but AT-specific
interface TerritoryChatProps {
  messages: ChatMessage[];
  onSaveUserMessage: (message: ChatMessage) => Promise<void>;
  onUpdateUserMessage: (message: ChatMessage) => Promise<void>; // For editing
  // onCallAI prop removed, TerritoryChat will handle its IPC call internally
  aiTaskStatus: any; 
  currentUserName: string;
  settings: AppSettings; 
  globalQuickCommands: GlobalQuickCommandItem[];
  isAIServiceReady: boolean;
  onClearHistory?: () => Promise<{success: boolean, error?: string} | undefined>;
  onReplayCg?: (cgPath: string) => void;
  onAddToCoreMemory?: (message: ChatMessage) => void;
  onHypothesisFeedback?: (hypothesisId: string, choice: string, modification?: string, originalElements?: XPHypothesisProposalData['elements']) => Promise<void>;
  scriptChoices?: ScriptChoice[] | null;
  onScriptChoiceMade?: (choice: ScriptChoice) => void;
  onStatusUpdate?: (status: Partial<LinLuoDetailedStatus>) => void;
  onSystemUpdate?: (systemUpdate: LinLuoSystemUpdatePayload) => void;
  isLoadingATInitial?: boolean;
  isLoadingOlderAT?: boolean;
  canLoadOlderAT?: boolean;
  onLoadOlderATMessages?: () => Promise<void>;
  onOpenWisdomPouch?: () => void; 
}


const escapeHTML = (str: string) => {
  const p = document.createElement("p");
  p.appendChild(document.createTextNode(str));
  return p.innerHTML;
};

const getCleanText = (html: string): string => {
    if (typeof DOMParser === 'undefined') {
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return (doc.body.textContent || "").trim();
    } catch (e) {
        console.error("Error in getCleanText:", e);
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
};

const TerritoryChatWithRef: React.ForwardRefRenderFunction<ChatDiscussionAreaRef, TerritoryChatProps> = (
  props,
  ref
): React.ReactElement | null => {
  const {
    messages: initialMessagesFromProps, 
    onSaveUserMessage, onUpdateUserMessage, aiTaskStatus, currentUserName,
    settings: appSettings, globalQuickCommands, isAIServiceReady,
    onClearHistory, onReplayCg, onAddToCoreMemory, onHypothesisFeedback,
    scriptChoices, onScriptChoiceMade,
    isLoadingATInitial, isLoadingOlderAT, canLoadOlderAT, onLoadOlderATMessages,
    onOpenWisdomPouch
  } = props;

  if (!appSettings) {
    return <div className="p-4 text-red-500">错误：应用配置丢失，聊天区域无法渲染。</div>;
  }
  
  const apiKeyAvailable = !!appSettings.apiKey && appSettings.apiKey.trim() !== "";
  const [inputValue, setInputValue] = useState('');
  const inputTextRef = useRef<HTMLTextAreaElement>(null);
  const [richEditingId, setRichEditingId] = useState<string | null>(null);
  const [displayedMessages, setDisplayedMessages] = useState<ChatMessage[]>(initialMessagesFromProps || []);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevScrollHeightRef = useRef<number | null>(null);
  const isUserAtBottomRef = useRef(true);
  const [showMoreActions, setShowMoreActions] = useState(false);
  const moreActionsButtonRef = useRef<HTMLButtonElement>(null);
  const moreActionsDropdownRef = useRef<HTMLDivElement>(null);
  const [showSaveChatModal, setShowSaveChatModal] = useState(false);
  const [saveChatFormat, setSaveChatFormat] = useState<'md' | 'html'>('md');
  const [showGlobalCommandsModal, setShowGlobalCommandsModal] = useState(false);
  const [isAttachmentMenuOpen, setIsAttachmentMenuOpen] = useState(false); 
  const attachmentButtonRef = useRef<HTMLButtonElement>(null);
  const [isWisdomPouchMenuOpen, setIsWisdomPouchMenuOpen] = useState(false);
  const wisdomPouchButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const isAiProcessing = aiTaskStatus !== 'idle';

  const insertTextAtFocus = useCallback((textToInsert: string) => {
    if (inputTextRef.current) {
      const { selectionStart, selectionEnd, value } = inputTextRef.current;
      setInputValue(value.substring(0, selectionStart) + textToInsert + value.substring(selectionEnd));
      setTimeout(() => { if (inputTextRef.current) { inputTextRef.current.selectionStart = inputTextRef.current.selectionEnd = selectionStart + textToInsert.length; inputTextRef.current.focus(); }}, 0);
    } else setInputValue(prev => prev + textToInsert);
  }, []);

  useImperativeHandle(ref, () => ({ insertTextAtFocus, scrollToBottom: (behavior: ScrollBehavior | undefined = "smooth") => messagesEndRef.current?.scrollIntoView({ behavior }), focusTextarea: () => inputTextRef.current?.focus() }), [insertTextAtFocus]);
  const scrollToBottom = useCallback((behavior: ScrollBehavior = "smooth") => { messagesEndRef.current?.scrollIntoView({ behavior }); }, []);
  
  const handleSaveMessageForDisplayAndBackend = useCallback(async (message: ChatMessage) => {
    setDisplayedMessages(prev => [...prev, message]); 
    try {
      if (typeof onSaveUserMessage === 'function') await onSaveUserMessage(message);
      else console.error("TerritoryChat Error: No suitable save message callback found.");
    } catch (error) { console.error("TerritoryChat Error: Failed to save message via callback:", error); }
  }, [onSaveUserMessage]);

  useEffect(() => {
    setDisplayedMessages(initialMessagesFromProps || []); 
  }, [initialMessagesFromProps]);


  useLayoutEffect(() => {
    if (isLoadingOlderAT === false && prevScrollHeightRef.current !== null && scrollContainerRef.current) {
      const currentScrollHeight = scrollContainerRef.current.scrollHeight;
      scrollContainerRef.current.scrollTop += currentScrollHeight - prevScrollHeightRef.current;
      prevScrollHeightRef.current = null;
    }
  }, [displayedMessages, isLoadingOlderAT]);

   useEffect(() => {
    const container = scrollContainerRef.current;
    const handleScroll = () => { if (container) isUserAtBottomRef.current = container.scrollHeight - container.scrollTop <= container.clientHeight + 50; };
    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => { if (scrollContainerRef.current && !isLoadingOlderAT && !isLoadingATInitial && isUserAtBottomRef.current) scrollToBottom("smooth"); }, [displayedMessages, scrollToBottom, isLoadingOlderAT, isLoadingATInitial]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moreActionsDropdownRef.current && !moreActionsDropdownRef.current.contains(event.target as Node) && moreActionsButtonRef.current && !moreActionsButtonRef.current.contains(event.target as Node)) setShowMoreActions(false);
      if (isAttachmentMenuOpen && attachmentButtonRef.current && !attachmentButtonRef.current.contains(event.target as Node)) { const menuElement = document.querySelector('[data-attachment-menu="true"]'); if (menuElement && !menuElement.contains(event.target as Node)) setIsAttachmentMenuOpen(false); }
      if (isWisdomPouchMenuOpen && wisdomPouchButtonRef.current && !wisdomPouchButtonRef.current.contains(event.target as Node)) { const menuElement = document.querySelector('[data-wisdom-pouch-menu="true"]'); if (menuElement && !menuElement.contains(event.target as Node)) setIsWisdomPouchMenuOpen(false); }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMoreActions, isAttachmentMenuOpen, isWisdomPouchMenuOpen]);
  
  useEffect(() => { 
    if (inputTextRef.current) {
        const textarea = inputTextRef.current; textarea.style.height = 'auto'; 
        const newHeight = Math.min(textarea.scrollHeight, parseFloat(getComputedStyle(textarea).maxHeight || '9rem'));
        textarea.style.height = `${Math.max(newHeight, parseFloat(getComputedStyle(textarea).lineHeight || '1.5rem') * 3)}px`;
        textarea.style.overflowY = newHeight >= parseFloat(getComputedStyle(textarea).maxHeight || '9rem') ? 'auto' : 'hidden';
    }
  }, [inputValue]);


  const handleInternalAICall = useCallback(async (prompt?: string, history?: ChatMessage[], ragActive?: boolean, context?: AiCallContext) => {
    if (!window.api?.ai?.invokeTerritoryRequest) { 
      await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统严重错误', text: 'AI调用服务接口(Territory)丢失。', timestamp: new Date().toISOString(), isError: true }); return; 
    }
    const aiResponse = await window.api.ai.invokeTerritoryRequest({ 
        prompt: prompt || "...", 
        history: history || [], 
        persona: context?.explicitPersona || 'LinLuo', // Default to LinLuo for AT
        otherTerritoryContext: context 
    });
    if (aiResponse.text !== null) {
      const { theme: aiTheme, finalText: aiFinalText } = parseMessageTextForTheme(aiResponse.text);
      const aiChatMessage: ChatMessage = { 
        id: window.crypto.randomUUID(), 
        sender: 'ai', 
        senderName: '林珞姐姐', // Default for AT
        characterId: 'linluo', // Default for AT
        text: aiFinalText, 
        timestamp: new Date().toISOString(), 
        theme: aiTheme, 
        avatarPath: appSettings.linluo_avatar_path, 
        isHypothesisProposal: !!aiResponse.json?.hypothesisId, 
        hypothesisData: aiResponse.json?.hypothesisId ? aiResponse.json : undefined, 
      };
      await handleSaveMessageForDisplayAndBackend(aiChatMessage);
    }
    // 处理状态更新 - 这是关键的状态更新逻辑
    if (aiResponse.status) {
        console.log('🔄 AI 响应包含状态更新:', aiResponse.status);
        // 通过 props 回调通知父组件更新状态
        if (props.onStatusUpdate) {
            props.onStatusUpdate(aiResponse.status);
        }
    }

    // 处理系统更新（身体发展、成就等）
    if (aiResponse.systemUpdate) {
        console.log('🎯 AI 响应包含系统更新:', aiResponse.systemUpdate);
        if (props.onSystemUpdate) {
            props.onSystemUpdate(aiResponse.systemUpdate);
        }
    }

    if (aiResponse.scriptChoices && props.onScriptChoiceMade) {
        // Handle script choices update
    }

  }, [handleSaveMessageForDisplayAndBackend, appSettings.linluo_avatar_path, props.onHypothesisFeedback, props.onScriptChoiceMade]);


  const handleSendMessage = async () => {
    const textToSend = inputValue.trim();
    // For TerritoryChat, sending an empty message can be a way to prompt the AI
    if (textToSend === '') { 
      if (!isAIServiceReady || !apiKeyAvailable || aiTaskStatus !== 'idle') return; 
      await handleInternalAICall(undefined, displayedMessages, false, {}); 
      setInputValue(''); 
      return; 
    }

    const newMessage: ChatMessage = { id: window.crypto.randomUUID(), sender: 'user', senderName: currentUserName, text: escapeHTML(textToSend), timestamp: new Date().toISOString(), avatarPath: appSettings.user_avatar_path || null };
    await handleSaveMessageForDisplayAndBackend(newMessage);
    if (!isAIServiceReady) console.warn("AI service became unready after user message saved.");
    else if (apiKeyAvailable) await handleInternalAICall(textToSend || "...", [...displayedMessages, newMessage], false, {});
    else console.warn("API Key not available.");
    setInputValue('');
  };

  const handleRequestRichEdit = (messageId: string | null) => setRichEditingId(messageId);
  const handleUpdateMessageFromItem = (update: { messageId: string; newHtml: string }) => {
    const { messageId, newHtml } = update; const targetMessages = displayedMessages; 
    const messageToUpdate = targetMessages.find(m => m.id === messageId);
    if (messageToUpdate && typeof onUpdateUserMessage === 'function') {
      const updatedMsgData = { ...messageToUpdate, text: newHtml, timestamp: new Date().toISOString() };
      onUpdateUserMessage(updatedMsgData);
      setDisplayedMessages(prev => prev.map(m => m.id === messageId ? updatedMsgData : m));
    }
  };
  const handleCopyMessageText = async (textToCopy: string) => { try { await navigator.clipboard.writeText(textToCopy.replace(summaryBlockRegex, '').replace(/<[^>]+>/g, '')); } catch (err) { console.error('Failed to copy text: ', err); } };
  const handleScrollToTopForLoad = () => { if (onLoadOlderATMessages) onLoadOlderATMessages(); };
  const handleManualSummarize = async () => {
    setShowMoreActions(false); if (!isAIServiceReady || !apiKeyAvailable || aiTaskStatus !== 'idle') { window.alert("AI服务不可用或未配置API Key。"); return; }
    if (typeof window.api?.ai?.summarizeConversation !== 'function') { window.alert("摘要功能所需接口未准备就绪。"); return; }
    const messagesToSummarize = displayedMessages.filter(m => m.sender !== 'system_summary' && m.sender !== 'system').slice(-30);
    if (messagesToSummarize.length < 5) { window.alert("对话内容过少，无需手动摘要。"); return; }
    try { const summaryTextResult = await window.api.ai.summarizeConversation(messagesToSummarize); await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system_summary', senderName: '手动摘要', text: summaryTextResult, timestamp: new Date().toISOString() }); } catch (e:any) { window.alert(`手动摘要失败: ${e.message}`); }
  };
  const handleSaveChatAction = async () => {
    setShowSaveChatModal(false); setShowMoreActions(false); if (!window.api?.fs?.exportChatHistory) return;
    try { const exportName = "绝对领域记录"; const result = await window.api.fs.exportChatHistory(displayedMessages, saveChatFormat, exportName); if (result.success && result.path) window.alert(`聊天记录已成功导出到: ${result.path}`); else window.alert(`导出失败: ${result.error || "未知错误"}`); } catch (exportError: any) { console.error("Failed to export chat history:", exportError); }
  };
  
  const commonMessageListProps = { 
    onUpdateMessage: handleUpdateMessageFromItem, 
    onToggleStar: () => {}, 
    onTogglePin: () => {}, 
    onCopy: handleCopyMessageText, 
    onRequestRichEdit: handleRequestRichEdit, 
    settings: appSettings, 
    currentUserName, 
    onAddToCoreMemory: onAddToCoreMemory, 
    isWorkspaceContext: false, // This is AT, not workspace
    onReplayCg: onReplayCg,
    onHypothesisFeedback: onHypothesisFeedback,
  };
  
  return (
    <div ref={containerRef} className="flex flex-col h-full bg-black/50 rounded-lg shadow-xl border border-purple-500 overflow-hidden">
      <div className="p-2.5 border-b border-purple-700 flex justify-between items-center bg-purple-900/50">
        <h3 className="text-base font-semibold text-purple-300">
          绝对领域・训练室
        </h3>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button ref={moreActionsButtonRef} onClick={() => setShowMoreActions(!showMoreActions)} className="p-1.5 rounded-md text-purple-300 hover:bg-purple-700/50 hover:text-white transition-colors" title="更多操作"><Icon name="EllipsisVertical" className="w-5 h-5"/></button>
            {showMoreActions && (<div ref={moreActionsDropdownRef} className="absolute right-0 mt-1 w-48 bg-gray-800 border border-purple-700 rounded-md shadow-lg z-20 py-1"> <button onClick={() => { setShowSaveChatModal(true); setShowMoreActions(false); }} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 flex items-center"> <Icon name="Save" className="w-4 h-4 mr-2"/>保存当前记录</button> <button onClick={handleManualSummarize} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 flex items-center"> <Icon name="FileText" className="w-4 h-4 mr-2"/>手动摘要当前对话</button> {onClearHistory && <button onClick={() => { if(onClearHistory) onClearHistory(); setShowMoreActions(false); }} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-red-400 flex items-center"><Icon name="Trash2" className="w-4 h-4 mr-2"/>焚毁所有私密记录</button>} <button onClick={() => { setShowGlobalCommandsModal(true); setShowMoreActions(false);}} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 flex items-center"><Icon name="Terminal" className="w-4 h-4 mr-2"/>查看全局快捷指令</button> </div>)}
          </div>
        </div>
      </div>
      <div ref={scrollContainerRef} className="flex-grow p-3 space-y-3 overflow-y-auto custom-scrollbar bg-black/30">
        {(isLoadingATInitial) && ( <div className="text-center py-4 text-gray-400"><Icon name="Loader2" className="w-6 h-6 animate-spin mx-auto mb-2" /> 加载记录中...</div> )}
        {canLoadOlderAT && ( <div className="text-center"><button onClick={handleScrollToTopForLoad} disabled={isLoadingOlderAT} className="text-xs text-purple-400 hover:underline disabled:opacity-50">{isLoadingOlderAT ? "加载中..." : "加载更早的记录"}</button></div> )}
        {displayedMessages.map(msg => ( <ChatDiscussionMessageItem key={msg.id} message={msg} isRichEditingActive={richEditingId === msg.id} {...commonMessageListProps} /> ))}
        <div ref={messagesEndRef} />
      </div>
      {scriptChoices && scriptChoices.length > 0 && (
        <div className="p-2 border-t border-purple-700 bg-purple-900/50 flex flex-wrap gap-2 justify-center items-center">
            {scriptChoices.map(choice => (
                <button key={choice.id} onClick={() => onScriptChoiceMade && onScriptChoiceMade(choice)} className="px-3 py-1.5 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded-md shadow-sm transition-colors">
                    {choice.text}
                </button>
            ))}
        </div>
      )}
      <div className="p-3 border-t border-purple-700 bg-purple-900/50 flex-shrink-0">
         <div className="flex items-end space-x-2">
            <div className="relative flex-shrink-0"> <button ref={attachmentButtonRef} onClick={() => setIsAttachmentMenuOpen(prev => !prev)} className="p-3 rounded-lg transition-colors text-purple-300 hover:bg-purple-700/50" title="添加附件" disabled={isAiProcessing}><Icon name="Paperclip" className="w-5 h-5"/></button> {isAttachmentMenuOpen && ( <div data-attachment-menu="true" className="absolute bottom-full left-0 mb-1 w-52 bg-gray-800 border border-purple-700 rounded-md shadow-lg py-1 z-30"> <button className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 disabled:opacity-50" disabled>从本地硬盘上传 (待实现)</button> </div> )} </div>
            <textarea ref={inputTextRef} value={inputValue} onChange={(e) => setInputValue(e.target.value)} placeholder={aiTaskStatus === 'idle' ? "与姐姐大人私密互动..." : "AI思考中，请稍候..."} className="flex-grow p-2.5 bg-gray-800/70 text-gray-100 border border-purple-700 rounded-lg resize-none outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500/50 text-sm" style={{ minHeight: 'calc(1.5rem * 3 + 1.25rem)', maxHeight: 'calc(1.5rem * 10 + 1.25rem)', lineHeight: '1.5rem', overflowY: 'hidden' }} onKeyPress={(e) => { if (e.key === 'Enter' && !e.shiftKey && aiTaskStatus === 'idle') { e.preventDefault(); handleSendMessage(); }}} disabled={isAiProcessing} rows={3} aria-label="聊天输入框" />
            <div className="flex flex-col space-y-1.5 flex-shrink-0"> {onOpenWisdomPouch && ( <button ref={wisdomPouchButtonRef} onClick={() => setIsWisdomPouchMenuOpen(prev => !prev)} className="p-3 rounded-lg transition-colors text-purple-300 hover:bg-purple-700/50" title="快捷指令/百宝袋" disabled={isAiProcessing}><Icon name="Package" className="w-5 h-5"/></button> )} <button onClick={handleSendMessage} disabled={isAiProcessing || (!inputValue.trim() && !(isAIServiceReady && apiKeyAvailable)) } className="p-3 rounded-lg transition-colors text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:text-gray-400 disabled:cursor-not-allowed flex-grow" title="发送消息"> {isAiProcessing ? <Icon name="Loader2" className="w-5 h-5 animate-spin"/> : <Icon name="SendHorizontal" className="w-5 h-5" />} </button> </div>
             {isWisdomPouchMenuOpen && onOpenWisdomPouch && ( <div data-wisdom-pouch-menu="true" className="absolute bottom-full right-0 mb-1 mr-14 w-48 bg-gray-800 border border-purple-700 rounded-md shadow-lg py-1 z-30"> <button onClick={() => { if(onOpenWisdomPouch) onOpenWisdomPouch(); setIsWisdomPouchMenuOpen(false); }} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 flex items-center"> <Icon name="BookOpen" className="w-4 h-4 mr-1.5"/> 管理智慧锦囊 </button> <button onClick={() => { setShowGlobalCommandsModal(true); setIsWisdomPouchMenuOpen(false);}} className="w-full text-left px-3 py-1.5 text-xs hover:bg-purple-700/50 text-gray-200 flex items-center"> <Icon name="Terminal" className="w-4 h-4 mr-1.5"/> 全局快捷指令 </button> </div> )}
        </div>
      </div>
      <GenericModal isOpen={showGlobalCommandsModal} onClose={() => setShowGlobalCommandsModal(false)} title="快捷指令参考 (AT)" size="lg"> {globalQuickCommands.length > 0 ? ( <ul className="space-y-2 max-h-96 overflow-y-auto"> {globalQuickCommands.map(cmd => ( <li key={cmd.id} className="p-2 bg-gray-700/50 rounded-md"> <button onClick={() => {insertTextAtFocus(cmd.commandText); setShowGlobalCommandsModal(false);}} className="w-full text-left text-gray-200 hover:text-purple-300"> <p className="font-semibold text-sm">{cmd.title}</p> <p className="text-xs text-gray-400 truncate mt-0.5">{cmd.commandText}</p> </button> </li> ))} </ul> ) : ( <p className="text-gray-500 text-center py-4">暂无全局快捷指令。请在“天工阁设置”中添加。</p> )} </GenericModal>
    </div>
  );
};
export const TerritoryChat = memo(forwardRef(TerritoryChatWithRef));
