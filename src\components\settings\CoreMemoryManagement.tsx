// src/components/settings/CoreMemoryManagement.tsx
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { CoreMemory, CoreMemoryImportance, CoreMemoryPersonaTarget, CoreMemoryStatus, PaginationOptions } from '@/types';
import { CoreMemoryModal } from '@/components/settings/CoreMemoryModal';
import { Icon } from '@/components/common/Icon';

const ITEMS_PER_PAGE = 10;

interface CoreMemoryManagementProps {
  onAddCoreMemoryProp: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => Promise<CoreMemory | null>;
}

const initialFilters = {
    persona_target: 'all' as CoreMemoryPersonaTarget | 'all',
    memory_type_query: '',
    importance: 'all' as CoreMemoryImportance | 'all',
    keywords_query: '',
    project_context_id: '',
    status: 'all' as CoreMemoryStatus | 'all',
    memory_content_query: '',
};

type FilterKeys = keyof typeof initialFilters;


export const CoreMemoryManagement: React.FC<CoreMemoryManagementProps> = ({ onAddCoreMemoryProp }) => {
    const [memories, setMemories] = useState<CoreMemory[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingMemory, setEditingMemory] = useState<CoreMemory | null>(null);

    const [filters, setFilters] = useState(initialFilters);
    
    const [sort, setSort] = useState<{ field: keyof CoreMemory | 'default'; order: 'ASC' | 'DESC' }>({ field: 'created_at', order: 'DESC' });
    const [pagination, setPagination] = useState<PaginationOptions>({ limit: ITEMS_PER_PAGE, offset: 0 });
    const [totalMemories, setTotalMemories] = useState(0);

    const personaTargetOptions: {value: CoreMemoryPersonaTarget | 'all', label: string}[] = [
        {value: 'all', label: '所有目标人格'},
        {value: 'shared', label: '共享记忆'},
        {value: 'LinLuo', label: '林珞专属'},
        {value: 'XiaoLan', label: '小岚专属'},
        {value: 'user', label: '用户专属'},
    ];
    const importanceOptions: {value: CoreMemoryImportance | 'all', label: string}[] = [
        {value: 'all', label: '所有重要性'},
        {value: 'high', label: '高重要性'},
        {value: 'medium', label: '中等重要性'},
        {value: 'low', label: '低重要性'},
    ];
     const statusOptions: {value: CoreMemoryStatus | 'all', label: string}[] = [
        {value: 'all', label: '所有状态'},
        {value: 'active', label: '活跃'},
        {value: 'archived', label: '归档'},
    ];

    const fetchMemories = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const apiFilters: any = {};
            if (filters.persona_target && filters.persona_target !== 'all') apiFilters.persona_target = filters.persona_target;
            if (filters.memory_type_query?.trim()) apiFilters.memory_type_query = filters.memory_type_query.trim();
            if (filters.importance && filters.importance !== 'all') apiFilters.importance = filters.importance;
            if (filters.keywords_query?.trim()) apiFilters.keywords_query = filters.keywords_query.trim();
            if (filters.project_context_id?.trim()) apiFilters.project_context_id = filters.project_context_id.trim();
            if (filters.status && filters.status !== 'all') apiFilters.status = filters.status;
            if (filters.memory_content_query?.trim()) apiFilters.memory_content_query = filters.memory_content_query.trim();

            const apiSort: any = {};
            if (sort.field !== 'default') {
                apiSort.field = sort.field;
                apiSort.order = sort.order;
            }
            
            const fetchedData = await window.api.database.getAllCoreMemories(apiFilters, apiSort, pagination);
            setMemories(fetchedData || []);
            
            // Simple total count for now. For real pagination, backend should provide total.
            if (fetchedData.length < (pagination.limit || ITEMS_PER_PAGE) && pagination.offset === 0) {
                 setTotalMemories(fetchedData.length);
            } else if (pagination.offset === 0 && fetchedData.length === (pagination.limit || ITEMS_PER_PAGE) ) {
                // This is a rough estimate, might need a dedicated count endpoint
                setTotalMemories(fetchedData.length * 2); // Guess there's more
            } else if (fetchedData.length < (pagination.limit || ITEMS_PER_PAGE)) {
                setTotalMemories((pagination.offset || 0) + fetchedData.length);
            }


        } catch (err: any) {
            setError(`加载核心记忆失败: ${err.message}`);
        } finally {
            setIsLoading(false);
        }
    }, [filters, sort, pagination]);

    useEffect(() => {
        fetchMemories();
    }, [fetchMemories]);

    const handleSaveMemory = async (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>, editingId?: string) => {
        try {
            let savedMemory;
            if (editingId) {
                savedMemory = await window.api.database.updateCoreMemory({ ...memoryData, id: editingId } as CoreMemory);
            } else {
                savedMemory = await onAddCoreMemoryProp(memoryData);
            }
            if (savedMemory) {
                fetchMemories(); // Refresh list
                return { success: true, memory: savedMemory };
            }
            return { success: false, error: "保存核心记忆失败，未返回有效数据。" };
        } catch (e: any) {
            return { success: false, error: `保存核心记忆时出错: ${e.message}` };
        }
    };

    const handleDeleteMemory = async (memoryId: string) => {
        if (window.confirm("确定要永久删除此条核心记忆吗？")) {
            try {
                const result = await window.api.database.deleteCoreMemory(memoryId);
                if (result.success) {
                    fetchMemories(); // Refresh list
                } else {
                    setError(result.error || "删除核心记忆失败。");
                }
            } catch (e: any) {
                setError(`删除核心记忆时发生意外错误: ${e.message}`);
            }
        }
    };
    
    const handlePageChange = (newPageOffset: number) => {
        setPagination(prev => ({ ...prev, offset: newPageOffset }));
    };

    const totalPages = Math.ceil(totalMemories / (pagination.limit || ITEMS_PER_PAGE));
    const currentPage = Math.floor((pagination.offset || 0) / (pagination.limit || ITEMS_PER_PAGE)) + 1;


    const renderFilterInput = (id: FilterKeys, label: string, type: 'text' | 'select' = 'text', options?: {value: string, label: string}[]) => (
        <div>
            <label htmlFor={`filter-${id}`} className="block text-xs text-tg-text-secondary mb-0.5">{label}</label>
            {type === 'select' && options ? (
                <select id={`filter-${id}`} value={filters[id] || ''} onChange={e => setFilters(f => ({...f, [id]: e.target.value === 'all' ? 'all' : e.target.value}))}
                    className="w-full p-1.5 text-xs bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
                    {options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                </select>
            ) : (
                <input id={`filter-${id}`} type="text" value={String(filters[id] || '')} onChange={e => setFilters(f => ({...f, [id]: e.target.value}))}
                    className="w-full p-1.5 text-xs bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
            )}
        </div>
    );
    
    const handleSort = (field: keyof CoreMemory | 'default') => {
        setSort(prev => ({
            field,
            order: prev.field === field && prev.order === 'ASC' ? 'DESC' : 'ASC'
        }));
    };
    
    const getSortIcon = (field: keyof CoreMemory | 'default') => {
        if (sort.field !== field) return <Icon name="ChevronsUpDown" className="w-3 h-3 opacity-30"/>;
        return sort.order === 'ASC' ? <Icon name="ChevronUp" className="w-3 h-3"/> : <Icon name="ChevronDown" className="w-3 h-3"/>;
    };


    return (
        <div className="p-4 bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary text-sm">
            <div className="flex justify-between items-center mb-3">
                <h3 className="text-lg font-semibold text-tg-accent-secondary flex items-center">
                    <Icon name="List" className="w-5 h-5 mr-2"/>核心记忆管理
                </h3>
                <button onClick={() => { setEditingMemory(null); setIsModalOpen(true); }} className="px-3 py-1.5 text-xs bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover flex items-center">
                    <Icon name="Plus" className="w-4 h-4 mr-1"/> 新增记忆
                </button>
            </div>

            {error && <p className="my-2 p-2 text-xs text-red-400 bg-red-900/30 rounded flex items-center"><Icon name="AlertTriangle" className="w-4 h-4 mr-1.5"/>{error}</p>}
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 mb-3 p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded-md">
                {renderFilterInput('persona_target', '目标人格', 'select', personaTargetOptions)}
                {renderFilterInput('memory_type_query', '记忆类型')}
                {renderFilterInput('importance', '重要性', 'select', importanceOptions)}
                {renderFilterInput('keywords_query', '关键词')}
                {renderFilterInput('project_context_id', '项目ID')}
                {renderFilterInput('status', '状态', 'select', statusOptions)}
                {renderFilterInput('memory_content_query', '内容包含')}
            </div>


            {isLoading ? (
                <p className="text-center text-tg-text-placeholder py-5"><Icon name="Loader2" className="w-6 h-6 animate-spin mx-auto"/> 加载记忆中...</p>
            ) : memories.length === 0 ? (
                <p className="text-center text-tg-text-placeholder py-5">暂无核心记忆，或无匹配筛选结果。</p>
            ) : (
                <div className="overflow-x-auto custom-scrollbar">
                    <table className="min-w-full text-xs">
                        <thead className="bg-tg-bg-tertiary">
                            <tr>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('memory_content')}><span className="flex items-center cursor-pointer">内容 {getSortIcon('memory_content')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('persona_target')}><span className="flex items-center cursor-pointer">目标 {getSortIcon('persona_target')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('memory_type')}><span className="flex items-center cursor-pointer">类型 {getSortIcon('memory_type')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('importance')}><span className="flex items-center cursor-pointer">重要性 {getSortIcon('importance')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('status')}><span className="flex items-center cursor-pointer">状态 {getSortIcon('status')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary" onClick={() => handleSort('last_accessed_at')}><span className="flex items-center cursor-pointer">最近访问 {getSortIcon('last_accessed_at')}</span></th>
                                <th className="p-2 text-left text-tg-text-secondary">操作</th>
                            </tr>
                        </thead>
                        <tbody className="divide-y divide-tg-border-primary">
                            {memories.map(memory => (
                                <tr key={memory.id} className="hover:bg-tg-bg-tertiary/50">
                                    <td className="p-2 text-tg-text-primary max-w-xs truncate" title={memory.memory_content}>{memory.memory_content}</td>
                                    <td className="p-2 text-tg-text-primary">{memory.persona_target}</td>
                                    <td className="p-2 text-tg-text-primary">{memory.memory_type}</td>
                                    <td className="p-2 text-tg-text-primary">{memory.importance}</td>
                                    <td className="p-2 text-tg-text-primary">{memory.status}</td>
                                    <td className="p-2 text-tg-text-primary">{memory.last_accessed_at ? new Date(memory.last_accessed_at).toLocaleDateString() : '-'}</td>
                                    <td className="p-2 space-x-1.5 whitespace-nowrap">
                                        <button onClick={() => { setEditingMemory(memory); setIsModalOpen(true); }} className="text-tg-accent-primary hover:underline" title="编辑"><Icon name="Pencil" className="w-4 h-4"/></button>
                                        <button onClick={() => handleDeleteMemory(memory.id)} className="text-tg-danger hover:underline" title="删除"><Icon name="Trash2" className="w-4 h-4"/></button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
            
             <div className="mt-3 flex justify-between items-center text-xs text-tg-text-secondary">
                <span>第 {currentPage} / {totalPages} 页 (共 {totalMemories} 条)</span>
                <div className="space-x-1.5">
                    <button onClick={() => handlePageChange(Math.max(0, (pagination.offset || 0) - (pagination.limit || ITEMS_PER_PAGE)))} disabled={currentPage <= 1} className="px-2 py-1 bg-tg-bg-tertiary rounded hover:bg-tg-bg-hover disabled:opacity-50"><Icon name="ChevronLeft" className="w-4 h-4"/></button>
                    <button onClick={() => handlePageChange((pagination.offset || 0) + (pagination.limit || ITEMS_PER_PAGE))} disabled={currentPage >= totalPages} className="px-2 py-1 bg-tg-bg-tertiary rounded hover:bg-tg-bg-hover disabled:opacity-50"><Icon name="ChevronRight" className="w-4 h-4"/></button>
                </div>
            </div>

            {isModalOpen && (
                <CoreMemoryModal 
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    onSave={handleSaveMemory}
                    existingMemory={editingMemory}
                />
            )}
        </div>
    );
};