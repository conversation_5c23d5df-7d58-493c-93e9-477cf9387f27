
import type { AppSettings, AppTheme } from '@/types';

export const APP_TITLE = "天工阁·创世框架";

export const GEMINI_TEXT_MODEL = "gemini-2.5-flash-preview-04-17";
export const GEMINI_IMAGE_MODEL = "imagen-3.0-generate-002";
export const GEMINI_EMBEDDING_MODEL = "text-embedding-004";

export const AVAILABLE_CHAT_MODELS = [
  "gemini-2.5-flash-preview-04-17",
];

export const AVAILABLE_EMBEDDING_MODELS = [
  "text-embedding-004",
];

export const DEFAULT_SETTINGS: AppSettings = {
  apiKey: "",
  chatModel: AVAILABLE_CHAT_MODELS[0], 
  embeddingModel: AVAILABLE_EMBEDDING_MODELS[0],
  backupPath: "/天工阁/备份/", 
  defaultCover: null,
  currentTheme: 'theme-default' as AppTheme,
  absolute_territory_password: null,
  training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false },
  user_avatar_path: null,
  linluo_avatar_path: null,
  xiaolan_avatar_path: null,
};
