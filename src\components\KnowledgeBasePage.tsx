
// src/components/KnowledgeBasePage.tsx
import React, { useState, useMemo, useEffect } from 'react';
// useSettings hook import removed
import type { Project, KnowledgeTome, KnowledgeTomeCategory, AppSettings, RetrievedChunk } from '@/types';
import { GeneralKnowledgeBaseView } from '@/components/GeneralKnowledgeBaseView';
import { Icon } from '@/components/common/Icon';
import { SearchResultsDisplay } from '@/components/SearchResultsDisplay';

interface KnowledgeBasePageProps {
  projects: Project[];
  globalKnowledgeTomes: KnowledgeTome[];
  onAddTome: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<KnowledgeTome | undefined>;
  onUpdateTome: (updatedTome: KnowledgeTome) => void;
  onDeleteTome: (tomeId: string) => void;
  settings: AppSettings; // Settings now come as a prop
}

const SourceCodeRepositoryView = ({ projects }: { projects: Project[] }): React.ReactElement | null => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  return (
    <div className="p-4 md:p-6">
      <h3 className="text-xl font-semibold text-tg-text-primary mb-4 flex items-center">
        <Icon name="CodeSquare" className="w-6 h-6 mr-2 text-tg-accent-secondary" />
        各项目源码洞天入口导览
      </h3>
      <p className="text-sm text-tg-text-secondary mb-4">
        此区域汇集所有“天工阁”内项目的源码及相关资源之“导览索引”。点击项目名称可查看简要说明。
        项目的详细源码和文件结构，请通过其专属“项目工作区”内的“源码洞天”（若已实现）功能进行查阅和管理。
        “万象书海”的此导览区旨在提供一个统一的入口概览，并非直接存储或管理源码文件。
      </p>
      {projects.length === 0 ? (
        <p className="text-tg-text-placeholder">暂无项目可供展示导览。</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ul className="space-y-2 md:col-span-1 max-h-[400px] overflow-y-auto bg-tg-bg-tertiary p-3 rounded-md border border-tg-border-primary">
            {projects.map(p => (
              <li key={p.id}>
                <button
                  onClick={() => setSelectedProject(p)}
                  className={`w-full text-left p-2 rounded-md text-sm transition-colors ${
                    selectedProject?.id === p.id
                      ? 'bg-tg-accent-primary text-white'
                      : 'text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary'
                  }`}
                >
                  {p.name}
                </button>
              </li>
            ))}
          </ul>
          <div className="md:col-span-2 bg-tg-bg-tertiary p-4 rounded-md border border-tg-border-primary min-h-[200px]">
            {selectedProject ? (
              <div>
                <h4 className="text-lg font-medium text-tg-text-primary mb-2">{selectedProject.name}</h4>
                <p className="text-sm text-tg-text-secondary">
                  关于「{selectedProject.name}」项目的详细源码和文件结构，请导航至该项目的专属工作区进行查阅与管理。
                </p>
                 <p className="text-xs text-tg-text-placeholder mt-3">
                  (当前版本提示：具体源码管理功能可能仍在各项目内部署或规划中。)
                </p>
              </div>
            ) : (
              <p className="text-tg-text-placeholder">请从左侧选择一个项目以查看导览说明。</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};


export const KnowledgeBasePage: React.FC<KnowledgeBasePageProps> = ({
  projects,
  globalKnowledgeTomes,
  onAddTome,
  onUpdateTome,
  onDeleteTome,
  settings, // Use settings from props
}) => {
  const [activeTab, setActiveTab] = useState<'source_code' | 'general_knowledge'>('general_knowledge');

  const [searchQueryKB, setSearchQueryKB] = useState('');
  const [selectedProjectIdForSearch, setSelectedProjectIdForSearch] = useState<string>('');
  const [searchResultsKB, setSearchResultsKB] = useState<RetrievedChunk[] | null>(null);
  const [isSearchLoadingKB, setIsSearchLoadingKB] = useState(false);
  const [searchErrorKB, setSearchErrorKB] = useState<string | null>(null);
  const [showSearchResultsModalKB, setShowSearchResultsModalKB] = useState(false);

  useEffect(() => {
    if (projects.length > 0 && !selectedProjectIdForSearch) {
      setSelectedProjectIdForSearch(projects[0].id);
    }
  }, [projects, selectedProjectIdForSearch]);

  const handleSearchKB = async () => {
    if (!searchQueryKB.trim()) return;
    if (!settings.apiKey) {
      setSearchErrorKB("请先在天工阁设置中配置您的Gemini API Key。");
      setShowSearchResultsModalKB(true);
      return;
    }
    if (!selectedProjectIdForSearch) {
      setSearchErrorKB("请选择一个项目以进行知识索引检索。");
      setShowSearchResultsModalKB(true);
      return;
    }

    setIsSearchLoadingKB(true);
    setSearchErrorKB(null);
    setSearchResultsKB(null);
    setShowSearchResultsModalKB(true);

    try {
      const response = await window.api.rag.retrieveRelevantChunks(
        searchQueryKB,
        selectedProjectIdForSearch,
        settings.apiKey,
        settings.embeddingModel
      );
      if (response.success && response.results) {
        setSearchResultsKB(response.results);
      } else {
        setSearchErrorKB(response.error || "未能检索到相关内容。");
      }
    } catch (e: any) {
      setSearchErrorKB(`检索时发生错误: ${e.message || '未知错误'}`);
    } finally {
      setIsSearchLoadingKB(false);
    }
  };

  const handleCopyText = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text from Knowledge Base search: ', err);
    }
  };

  const getTabClassName = (tabName: 'source_code' | 'general_knowledge') => {
    return `py-2.5 px-5 rounded-t-lg text-sm font-medium transition-colors flex items-center focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-tg-accent-primary focus:ring-offset-tg-bg-primary ${
      activeTab === tabName
        ? 'bg-tg-bg-secondary text-tg-accent-primary shadow-md'
        : 'text-tg-text-secondary hover:bg-tg-bg-tertiary hover:text-tg-text-primary border-b-2 border-transparent'
    } whitespace-nowrap`;
  };

  const apiKeyAvailable = !!settings.apiKey && settings.apiKey.trim() !== "";

  return (
    <div className="p-4 md:p-6 min-h-full bg-tg-bg-primary text-tg-text-primary flex flex-col">
      <header className="mb-6">
        <h2 className="text-3xl font-bold text-tg-text-primary mb-2 flex items-center">
            <Icon name="Archive" className="w-8 h-8 mr-3 text-tg-accent-primary"/>
            万象书海 - 天工阁知识库
        </h2>
        <p className="text-tg-text-secondary">集中管理您的项目源码索引与通用知识文档。</p>
      </header>

      <div className="mb-6 p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary shadow">
        <h4 className="text-lg font-semibold text-tg-text-primary mb-2 flex items-center">
          <Icon name="Search" className="w-5 h-5 mr-2 text-tg-accent-secondary"/>
          全局项目知识检索
        </h4>
        <div className="flex flex-col sm:flex-row items-stretch gap-2">
          <input
            type="text"
            placeholder="在选定项目的知识索引库中搜索..."
            className="bg-tg-bg-primary text-base flex-grow outline-none text-tg-text-primary placeholder-tg-text-placeholder p-2.5 rounded-md border border-tg-border-primary focus:border-tg-accent-primary disabled:opacity-50 disabled:cursor-not-allowed"
            value={searchQueryKB}
            onChange={(e) => setSearchQueryKB(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearchKB()}
            aria-label="知识库项目内搜索"
            disabled={!apiKeyAvailable || projects.length === 0}
          />
          <select
            value={selectedProjectIdForSearch}
            onChange={(e) => setSelectedProjectIdForSearch(e.target.value)}
            className="bg-tg-bg-primary text-base text-tg-text-primary p-2.5 rounded-md border border-tg-border-primary focus:border-tg-accent-primary min-w-[180px] disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="选择要搜索的项目"
            disabled={!apiKeyAvailable || projects.length === 0}
          >
            {projects.length === 0 && <option value="">无可用项目</option>}
            {projects.map(p => (
              <option key={p.id} value={p.id}>{p.name}</option>
            ))}
          </select>
          <button
            onClick={handleSearchKB}
            disabled={isSearchLoadingKB || !searchQueryKB.trim() || !selectedProjectIdForSearch || !apiKeyAvailable}
            className="px-4 py-2.5 text-sm font-medium rounded-md bg-tg-accent-secondary text-white hover:brightness-110 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            title={!apiKeyAvailable ? "请配置API Key" : (!projects.length ? "请先创建项目" : "执行检索")}
          >
            {isSearchLoadingKB ? <Icon name="Loader2" className="w-4 h-4 animate-spin inline-block mr-1.5"/> : <Icon name="Search" className="w-4 h-4 inline-block mr-1.5"/>}
            搜索
          </button>
        </div>
        {!apiKeyAvailable &&
            <p className="text-xs text-tg-warning mt-2 flex items-center">
                <Icon name="AlertTriangle" className="w-3.5 h-3.5 mr-1 flex-shrink-0"/>
                提示: 请先在“天工阁设置”中配置API Key以启用知识检索功能。
            </p>
        }
        {apiKeyAvailable && projects.length === 0 &&
            <p className="text-xs text-tg-text-placeholder mt-2">
                提示: 请先创建项目并建立知识索引以使用检索功能。
            </p>
        }
         <p className="text-xs text-tg-text-placeholder mt-1.5">此搜索功能将从选定项目的已索引源码中检索信息。</p>
      </div>

      <nav className="flex border-b border-tg-border-primary mb-px">
        <button onClick={() => setActiveTab('general_knowledge')} className={getTabClassName('general_knowledge')}>
          <Icon name="BookOpen" className="w-5 h-5 mr-2 flex-shrink-0" />
          全局·通用智库 (卷宗管理)
        </button>
        <button onClick={() => setActiveTab('source_code')} className={getTabClassName('source_code')}>
          <Icon name="CodeSquare" className="w-5 h-5 mr-2 flex-shrink-0" />
          源码资源总汇 (导览)
        </button>
      </nav>

      <div className="flex-grow bg-tg-bg-secondary rounded-b-lg shadow-inner overflow-y-auto">
        {activeTab === 'source_code' && (
          <SourceCodeRepositoryView projects={projects} />
        )}
        {activeTab === 'general_knowledge' && (
          <GeneralKnowledgeBaseView
            globalKnowledgeTomes={globalKnowledgeTomes}
            onAddTome={onAddTome}
            onUpdateTome={onUpdateTome}
            onDeleteTome={onDeleteTome}
          />
        )}
      </div>
      <SearchResultsDisplay
        isVisible={showSearchResultsModalKB}
        query={searchQueryKB}
        results={searchResultsKB}
        isLoading={isSearchLoadingKB}
        error={searchErrorKB}
        onClose={() => setShowSearchResultsModalKB(false)}
        onCopy={handleCopyText}
      />
    </div>
  );
};
