
// electron/services/bodyDevelopmentDbService.js
console.log('BODY_DEVELOPMENT_DB_SERVICE_JS: File execution started.');

import { db } from './databaseCore.js';

export function getBodyDevelopment(zone_id) {
    if (!db) { console.error(`BODY_DEV_DB_ERROR: getBodyDevelopment(${zone_id}) - db not available.`); return null; }
    try {
        const row = db.prepare('SELECT * FROM linluo_body_development WHERE zone_id = ?').get(zone_id);
        console.log(`BODY_DEV_DB: Retrieved body development for zone: ${zone_id}. Found: ${!!row}`);
        return row || null;
    } catch (error) {
        console.error(`BODY_DEV_DB_ERROR: Error getting body development for zone ${zone_id}:`, error);
        return null;
    }
}

export function getAllBodyDevelopment() {
    if (!db) { console.error("BODY_DEV_DB_ERROR: getAllBodyDevelopment - db not available."); return []; }
    try {
        const rows = db.prepare('SELECT * FROM linluo_body_development ORDER BY zone_id ASC').all();
        console.log(`BODY_DEV_DB: Retrieved ${rows.length} body development zones.`);
        return rows;
    } catch (error) {
        console.error('BODY_DEV_DB_ERROR: Error getting all body development zones:', error);
        return [];
    }
}

export function updateBodyDevelopment(zone_id, pointsToAdd) {
    if (!db) { console.error(`BODY_DEV_DB_ERROR: updateBodyDevelopment(${zone_id}) - db not available.`); return { success: false, error: "Database not available." }; }
    
    const transaction = db.transaction(() => {
        const currentData = db.prepare('SELECT development_points FROM linluo_body_development WHERE zone_id = ?').get(zone_id);
        
        let oldPoints = 0;
        if (currentData) {
            oldPoints = currentData.development_points;
        } else {
            // If zone doesn't exist, create it. This is important for initialization.
            db.prepare('INSERT INTO linluo_body_development (zone_id, development_points, last_developed_at) VALUES (?, 0, ?)').run(zone_id, new Date().toISOString());
        }

        const newTotalPoints = oldPoints + pointsToAdd;
        const now = new Date().toISOString();

        const stmt = db.prepare('UPDATE linluo_body_development SET development_points = ?, last_developed_at = ? WHERE zone_id = ?');
        const info = stmt.run(newTotalPoints, now, zone_id);

        if (info.changes > 0) {
            console.log(`BODY_DEV_DB: Updated body development for zone ${zone_id}. Added ${pointsToAdd} points. New total: ${newTotalPoints}.`);
            return { success: true, zone_id, newPoints: newTotalPoints, oldPoints };
        } else {
            // This case should ideally not happen if we insert if not exists.
            console.warn(`BODY_DEV_DB_WARN: updateBodyDevelopment - No rows updated for zone ${zone_id}. This might indicate an issue.`);
            return { success: false, error: `Zone ${zone_id} not found or update failed.` };
        }
    });

    try {
        return transaction();
    } catch (error) {
        console.error(`BODY_DEV_DB_ERROR: Error updating body development for zone ${zone_id}:`, error);
        return { success: false, error: error.message };
    }
}

console.log('BODY_DEVELOPMENT_DB_SERVICE_JS: File execution finished. Exports configured.');
