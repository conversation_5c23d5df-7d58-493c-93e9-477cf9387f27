
import React, { useState, useEffect, useMemo } from 'react';
import type { CoreMemory, CoreMemoryPersonaTarget, CoreMemoryImportance, CoreMemoryStatus } from '@/types';
import { GenericModal } from '@/components/GenericModal'; 
import { Icon } from '@/components/common/Icon'; 

interface CoreMemoryModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>, editingId?: string) => Promise<{success: boolean, error?: string, memory?: CoreMemory | null}>;
    existingMemory: CoreMemory | null;
}

export const CoreMemoryModal: React.FC<CoreMemoryModalProps> = ({ isOpen, onClose, onSave, existingMemory }) => {
    const [content, setContent] = useState('');
    const [personaTarget, setPersonaTarget] = useState<CoreMemoryPersonaTarget | string>('shared');
    const [memoryType, setMemoryType] = useState('');
    const [importance, setImportance] = useState<CoreMemoryImportance>('medium');
    const [projectContextId, setProjectContextId] = useState<string | null>(null);
    const [keywordsString, setKeywordsString] = useState('');
    const [contextAffinityTagsString, setContextAffinityTagsString] = useState('');
    const [status, setStatus] = useState<CoreMemoryStatus>('active');
    const [valueScore, setValueScore] = useState<number>(0.5);

    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            setContent(existingMemory?.memory_content || '');
            setPersonaTarget(existingMemory?.persona_target || 'shared');
            setMemoryType(existingMemory?.memory_type || '');
            setImportance(existingMemory?.importance || 'medium');
            setProjectContextId(existingMemory?.project_context_id || null);
            setKeywordsString(existingMemory?.keywords?.split(',').filter(k => k).join(', ') || '');
            setContextAffinityTagsString(existingMemory?.context_affinity_tags?.split(',').filter(t => t).map(t => t.startsWith('#') ? t.substring(1) : t).join(', ') || '');
            setStatus(existingMemory?.status || 'active');
            setValueScore(existingMemory?.value_score === undefined ? 0.5 : existingMemory.value_score);
            
            setError(null);
            setIsSaving(false);
        }
    }, [existingMemory, isOpen]);

    const handleSubmit = async () => {
        if (!content.trim()) {
            setError("记忆内容不能为空。");
            return;
        }
        setIsSaving(true);
        setError(null);

        const keywords = keywordsString.split(',').map(k => k.trim()).filter(Boolean);
        const contextAffinityTags = contextAffinityTagsString.split(',').map(t => t.trim()).filter(Boolean);

        const memoryDataToSave: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'> = {
            memory_content: content.trim(),
            persona_target: personaTarget as CoreMemoryPersonaTarget, 
            memory_type: memoryType.trim(),
            importance,
            project_context_id: projectContextId?.trim() || null,
            keywords: keywords.length > 0 ? keywords.join(',') : undefined, 
            context_affinity_tags: contextAffinityTags.length > 0 ? contextAffinityTags.join(',') : undefined,
            status,
            value_score: Number(valueScore)
        };
        
        try {
            const result = await onSave(memoryDataToSave, existingMemory?.id);
            if (result.success) {
                onClose();
            } else {
                setError(result.error || "保存核心记忆失败。");
            }
        } catch (err: any) {
            setError(`保存核心记忆时发生意外: ${err.message}`);
        } finally {
            setIsSaving(false);
        }
    };
    
    const personaTargetOptions: {value: CoreMemoryPersonaTarget | string, label: string}[] = [
        {value: 'shared', label: '共享记忆'},
        {value: 'LinLuo', label: '林珞专属'},
        {value: 'XiaoLan', label: '小岚专属'},
        {value: 'user', label: '用户专属'},
    ];
    const importanceOptions: {value: CoreMemoryImportance, label: string}[] = [
        {value: 'high', label: '高重要性'},
        {value: 'medium', label: '中等重要性'},
        {value: 'low', label: '低重要性'},
    ];
     const statusOptions: {value: CoreMemoryStatus, label: string}[] = [
        {value: 'active', label: '活跃'},
        {value: 'archived', label: '归档'},
    ];


    return (
        <GenericModal
            isOpen={isOpen}
            onClose={onClose}
            title={existingMemory ? "编辑核心记忆" : "新增核心记忆"}
            size="xl"
            footerContent={
                <>
                    <button onClick={onClose} className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary" disabled={isSaving}>取消</button>
                    <button onClick={handleSubmit} className="py-2 px-4 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover" disabled={isSaving}>
                        {isSaving ? <Icon name="Loader2" className="w-4 h-4 mr-1.5 animate-spin inline-block"/> : null}
                        {isSaving ? "保存中..." : (existingMemory ? "保存更改" : "新增记忆")}
                    </button>
                </>
            }
        >
            {error && <p className="mb-3 p-2 text-sm text-red-400 bg-red-900/30 rounded flex items-center"><Icon name="AlertTriangle" className="w-4 h-4 mr-1.5"/>{error}</p>}
            <div className="space-y-3 text-sm max-h-[70vh] overflow-y-auto pr-2 custom-scrollbar">
                <div>
                    <label htmlFor="memory-content" className="block text-xs text-tg-text-secondary mb-1">记忆内容*</label>
                    <textarea id="memory-content" value={content} onChange={e => setContent(e.target.value)} rows={5} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary resize-y min-h-[100px]"/>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <label htmlFor="persona-target" className="block text-xs text-tg-text-secondary mb-1">目标人格</label>
                        <select id="persona-target" value={personaTarget} onChange={e => setPersonaTarget(e.target.value as CoreMemoryPersonaTarget)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
                            {personaTargetOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                        </select>
                    </div>
                    <div>
                        <label htmlFor="memory-type" className="block text-xs text-tg-text-secondary mb-1">记忆类型</label>
                        <input id="memory-type" type="text" value={memoryType} onChange={e => setMemoryType(e.target.value)} placeholder="例如: 技术规范, 用户偏好..." className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
                    </div>
                    <div>
                        <label htmlFor="importance" className="block text-xs text-tg-text-secondary mb-1">重要性</label>
                        <select id="importance" value={importance} onChange={e => setImportance(e.target.value as CoreMemoryImportance)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
                             {importanceOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                        </select>
                    </div>
                     <div>
                        <label htmlFor="status" className="block text-xs text-tg-text-secondary mb-1">状态</label>
                        <select id="status" value={status} onChange={e => setStatus(e.target.value as CoreMemoryStatus)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
                            {statusOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                        </select>
                    </div>
                    <div>
                        <label htmlFor="project-context-id" className="block text-xs text-tg-text-secondary mb-1">项目上下文ID (留空为全局)</label>
                        <input id="project-context-id" type="text" value={projectContextId || ''} onChange={e => setProjectContextId(e.target.value || null)} placeholder="项目ID或'global'" className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
                    </div>
                     <div>
                        <label htmlFor="value-score" className="block text-xs text-tg-text-secondary mb-1">基础价值评分 (0-1)</label>
                        <input id="value-score" type="number" value={valueScore} onChange={e => setValueScore(parseFloat(e.target.value))} step="0.01" min="0" max="1" className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
                    </div>
                </div>
                <div>
                    <label htmlFor="keywords" className="block text-xs text-tg-text-secondary mb-1">关键词 (逗号分隔)</label>
                    <input id="keywords" type="text" value={keywordsString} onChange={e => setKeywordsString(e.target.value)} placeholder="例如: api, 认证, 数据库..." className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
                </div>
                <div>
                    <label htmlFor="context-affinity-tags" className="block text-xs text-tg-text-secondary mb-1">上下文关联标签 (逗号分隔, 无#)</label>
                    <input id="context-affinity-tags" type="text" value={contextAffinityTagsString} onChange={e => setContextAffinityTagsString(e.target.value)} placeholder="例如: 登录流程, 用户界面, 性能优化..." className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
                </div>
            </div>
        </GenericModal>
    );
};