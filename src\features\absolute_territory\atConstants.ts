// src/features/absolute_territory/atConstants.ts
import type { BodyZoneKey } from '@/types/commonTypes'; // Import from the new central location

export const BODY_ZONE_DISPLAY_NAMES: Record<BodyZoneKey, string> = {
  mouth: '小嘴',
  breasts: '双峰',
  vagina: '花穴',
  clitoris: '蜜蕊',
  anus: '幽谷', // Changed from 后庭 to 幽谷 based on previous output by user.
  feet: '玉足',
  skin: '肌肤',
  mind: '心智',
};
// BodyZoneKey and ALL_BODY_ZONES are now exported from src/types/commonTypes.ts
