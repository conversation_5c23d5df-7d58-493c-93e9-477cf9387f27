// src/components/PlaceholderPanel.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon'; // Use the common Icon component
import type { IconProps } from '@/components/common/Icon';


interface PlaceholderPanelProps {
  title: string;
  message?: string;
  iconNode?: React.ReactNode; 
  iconName?: IconProps['name']; 
  iconClassName?: string; 
}

export const PlaceholderPanel: React.FC<PlaceholderPanelProps> = ({ 
  title, 
  message, 
  iconNode, 
  iconName = "AlertTriangle", 
  iconClassName = "w-12 h-12 mb-4 text-tg-text-placeholder opacity-50"
}) => {
  return (
    <div 
      className="flex-grow flex flex-col items-center justify-center text-center p-8 rounded-lg shadow-inner h-full bg-tg-bg-secondary"
      role="region"
      aria-label={title}
    >
      {iconNode ? (
        <div className="mb-4">{iconNode}</div>
      ) : (
        <Icon name={iconName} className={iconClassName} /> 
      )}
      <h3 className="text-2xl font-semibold mb-3 text-tg-text-secondary">{title}</h3>
      <p className="text-tg-text-placeholder max-w-md">
        {message || "此功能模块正在规划建设中，或所需资源暂时无法加载。敬请期待或检查您的配置！"}
      </p>
      <div 
        className="mt-6 rounded-md opacity-20 w-[200px] h-[150px] bg-tg-bg-tertiary border border-tg-border-primary flex items-center justify-center"
        aria-hidden="true"
      >
        <span className="text-tg-text-placeholder text-lg">建设中...</span>
      </div>
    </div>
  );
};