// src/utils/trainingRoomDiagnostics.ts
// 训练室功能诊断工具

export interface DiagnosticResult {
  category: string;
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export class TrainingRoomDiagnostics {
  private results: DiagnosticResult[] = [];

  async runFullDiagnostics(): Promise<DiagnosticResult[]> {
    this.results = [];
    
    console.log('🔍 开始训练室功能诊断...');
    
    await this.checkAPIConfiguration();
    await this.checkAssetData();
    await this.checkDatabaseConnection();
    await this.checkAIService();
    
    console.log('✅ 诊断完成！结果：', this.results);
    return this.results;
  }

  private addResult(category: string, test: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
  }

  private async checkAPIConfiguration() {
    const category = 'API 配置';
    
    try {
      // 检查 window.api 是否可用
      if (!window.api) {
        this.addResult(category, 'API 接口', 'fail', 'window.api 不可用');
        return;
      }
      
      // 检查各个 API 模块
      const requiredAPIs = [
        'ai.invokeTerritoryRequest',
        'cms.getCMSItems', 
        'cms.addCMSItem',
        'cms.getRolePlayingCards',
        'assets.getLoadedAssets',
        'linluo.getDetailedStatus',
        'linluo.getBodyDevelopment'
      ];
      
      for (const apiPath of requiredAPIs) {
        const parts = apiPath.split('.');
        let current: any = window.api;
        
        for (const part of parts) {
          current = current?.[part];
        }
        
        if (typeof current === 'function') {
          this.addResult(category, `API ${apiPath}`, 'pass', '可用');
        } else {
          this.addResult(category, `API ${apiPath}`, 'fail', '不可用或类型错误');
        }
      }
      
    } catch (error) {
      this.addResult(category, 'API 检查', 'fail', `检查失败: ${error.message}`);
    }
  }

  private async checkAssetData() {
    const category = '资产数据';
    
    try {
      // 检查角色卡
      const roleCards = await window.api.cms.getRolePlayingCards();
      this.addResult(category, '角色卡数量', roleCards.length > 0 ? 'pass' : 'warning', 
        `共 ${roleCards.length} 个角色卡`, roleCards.map(c => c.name));
      
      // 检查特定角色卡
      const kirishimaCard = roleCards.find(c => c.id === 'SC001');
      const linluoCard = roleCards.find(c => c.id === 'Luo_Personality_NaturalSeductiveSoul_v2.2.1');
      
      this.addResult(category, '雾岛玲奈角色卡', kirishimaCard ? 'pass' : 'warning', 
        kirishimaCard ? '已导入' : '未找到');
      this.addResult(category, '林珞自然骚魂版', linluoCard ? 'pass' : 'warning', 
        linluoCard ? '已导入' : '未找到');
      
      // 检查道具
      const props = await window.api.cms.getCMSItems('props');
      this.addResult(category, '道具数量', props.length > 0 ? 'pass' : 'warning', 
        `共 ${props.length} 个道具`);
      
      // 检查特定道具
      const kirishimaProps = props.filter(p => p.id.startsWith('prop_kr_'));
      const linluoProps = props.filter(p => p.id.startsWith('prop_ln_'));

      this.addResult(category, '雾岛玲奈道具', kirishimaProps.length > 0 ? 'pass' : 'warning',
        `${kirishimaProps.length} 个专属道具`, kirishimaProps.map(p => p.name));
      this.addResult(category, '林珞自然骚魂版道具', linluoProps.length > 0 ? 'pass' : 'warning',
        `${linluoProps.length} 个专属道具`, linluoProps.map(p => p.name));

      // 检查是否为最新的大胆版道具
      const latestPropNames = [
        // 雾岛玲奈道具
        '情趣内衣试穿箱', '身体测量绳', '挑逗羽毛棒', '催情玫瑰',
        '遥控震动内裤', '乳夹震动器', '调教项圈', '肛塞尾巴',
        // 林珞自然骚魂版道具
        '震动按摩棒', 'SM束缚绳套', '情趣跳蛋', '双头情趣棒',
        '自动抽插机', '电击调教器', '强制高潮腰带', '感官剥夺套装'
      ];

      const hasLatestProps = props.some(p => latestPropNames.includes(p.name));
      const oldPropNames = ['高级产品展示箱', '专业软尺', '古典羽毛笔', '芬芳鲜花', '灵感激发器', '温柔的枷锁', '情感共鸣石', '深度探索工具'];
      const hasOldProps = props.some(p => oldPropNames.includes(p.name));

      if (hasLatestProps && !hasOldProps) {
        this.addResult(category, '道具版本', 'pass', '已升级为最新大胆版道具');
      } else if (hasOldProps) {
        this.addResult(category, '道具版本', 'warning', '检测到旧版本道具，建议清理');
      } else {
        this.addResult(category, '道具版本', 'warning', '未检测到标准道具');
      }
      
      // 检查服装和姿势
      const costumes = await window.api.cms.getCMSItems('costumes');
      const poses = await window.api.cms.getCMSItems('poses');
      
      this.addResult(category, '服装数量', costumes.length > 0 ? 'pass' : 'warning', 
        `共 ${costumes.length} 套服装`);
      this.addResult(category, '姿势数量', poses.length > 0 ? 'pass' : 'warning', 
        `共 ${poses.length} 个姿势`);
      
    } catch (error) {
      this.addResult(category, '资产数据检查', 'fail', `检查失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkDatabaseConnection() {
    const category = '数据库连接';
    
    try {
      // 检查林珞状态
      if ((window.api as any).linluo?.getDetailedStatus) {
        const status = await (window.api as any).linluo.getDetailedStatus();
        this.addResult(category, '林珞状态', status ? 'pass' : 'fail',
          status ? '状态数据正常' : '无法获取状态', status);
      } else {
        this.addResult(category, '林珞状态', 'warning', 'API 方法不可用');
      }

      // 检查身体发展数据
      if ((window.api as any).linluo?.getBodyDevelopment) {
        const bodyDev = await (window.api as any).linluo.getBodyDevelopment();
        this.addResult(category, '身体发展数据', bodyDev && bodyDev.length > 0 ? 'pass' : 'warning',
          `${bodyDev?.length || 0} 个身体部位数据`);
      } else {
        this.addResult(category, '身体发展数据', 'warning', 'API 方法不可用');
      }

    } catch (error) {
      this.addResult(category, '数据库连接', 'fail', `连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkAIService() {
    const category = 'AI 服务';
    
    try {
      // 简单的 AI 测试调用
      const testResponse = await window.api.ai.invokeTerritoryRequest({
        prompt: '测试连接',
        history: [],
        persona: 'LinLuo',
        otherTerritoryContext: { contextType: 'training_room' }
      });
      
      if (testResponse && testResponse.text) {
        this.addResult(category, 'AI 响应', 'pass', 'AI 服务正常响应');
        
        // 检查状态解析
        if (testResponse.status) {
          this.addResult(category, '状态解析', 'pass', '状态更新功能正常');
        } else {
          this.addResult(category, '状态解析', 'warning', '本次测试未返回状态更新');
        }
        
      } else {
        this.addResult(category, 'AI 响应', 'fail', 'AI 服务无响应或响应格式错误');
      }
      
    } catch (error) {
      this.addResult(category, 'AI 服务', 'fail', `AI 调用失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  generateReport(): string {
    const passCount = this.results.filter(r => r.status === 'pass').length;
    const failCount = this.results.filter(r => r.status === 'fail').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    
    let report = `
🔍 训练室功能诊断报告
========================

📊 总体状况：
✅ 通过：${passCount} 项
⚠️  警告：${warningCount} 项  
❌ 失败：${failCount} 项

📋 详细结果：
`;
    
    const categories = [...new Set(this.results.map(r => r.category))];
    
    for (const category of categories) {
      report += `\n## ${category}\n`;
      const categoryResults = this.results.filter(r => r.category === category);
      
      for (const result of categoryResults) {
        const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        report += `${icon} ${result.test}: ${result.message}\n`;
      }
    }
    
    report += `\n🎯 建议：\n`;
    
    if (failCount > 0) {
      report += `- 优先解决 ${failCount} 个失败项\n`;
    }
    if (warningCount > 0) {
      report += `- 检查 ${warningCount} 个警告项\n`;
    }
    if (passCount === this.results.length) {
      report += `- 所有功能正常，可以开始使用训练室！\n`;
    }
    
    return report;
  }
}

// 全局诊断函数
export async function runTrainingRoomDiagnostics(): Promise<string> {
  const diagnostics = new TrainingRoomDiagnostics();
  await diagnostics.runFullDiagnostics();
  return diagnostics.generateReport();
}

// 在开发模式下自动运行诊断
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，避免影响应用启动
  setTimeout(async () => {
    try {
      const report = await runTrainingRoomDiagnostics();
      console.log(report);
    } catch (error) {
      console.error('诊断工具执行失败:', error);
    }
  }, 3000);
}
