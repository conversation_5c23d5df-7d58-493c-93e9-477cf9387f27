// src/types/aiTypes.ts
import type { LinLuoDetailedStatus, TaskResourceLink, ChatMessage, TaskStatus, TaskPriority, Task, ScriptChoice } from './'; // Import from main index

export type AITaskStatus =
  | 'idle'
  | 'retrieving_rag'
  | 'generating_llm'
  | 'summarizing'
  | 'classifying_intent'
  | 'extracting_keywords'
  | 'suggesting_resources'
  | 'decomposing_requirement'
  | 'analyzing_log'
  | 'roundtable_processing';

export type AiCallContextType =
  | 'workspace'
  | 'training_room'
  | 'general'
  | 'settings'
  | 'code_assistance'
  | 'task_resource_suggestion'
  | 'file_orchestration'
  | 'aide_project_analysis'
  | 'command_log_analysis'
  | 'task_cockpit_command'
  | 'roundtable_meeting'; // Added roundtable

export interface XPHypothesisFeedbackData { // Keep if needed, though not directly in aiTypes
  hypothesisId: string;
  choice: string;
  modification?: string;
  originalElements: any; // Consider defining XPHypothesisElements if not already available
}

export interface LinLuoSystemUpdatePayload { // Keep if needed for AIResponseWithStatus
  bodyZoneUpdate?: { zone: any; pointsAdded: number; newTotalPoints?: number }; // Consider using BodyZoneKey
  triggeredAchievementId?: string;
  unlockedCmsItem?: { type: any; id: string; name: string }; // Consider using CMSType/AssetType
}

export interface AIResponseWithStatus {
  text: string | null;
  status?: Partial<LinLuoDetailedStatus> | null;
  systemUpdate?: LinLuoSystemUpdatePayload;
  suggestedResources?: Partial<TaskResourceLink>[];
  scriptChoices?: ScriptChoice[] | null;
  newSceneCardId?: string | null;
  json?: any;
  characterId?: string; // For roundtable AI responses
}

export interface ScriptModeInfo {
  scriptId: string;
  currentSceneId: string;
  userChoiceText?: string;
}

export interface AiCallContext {
  projectId?: string;
  history?: any[];
  contextType?: AiCallContextType;
  requiredPostId?: string;
  filePath?: string;
  triggeringUserMessageId?: string;
  action?: 'REQUEST_XP_HYPOTHESIS' | 'SUBMIT_XP_HYPOTHESIS_FEEDBACK' | 'SCENE_OPENING_DIALOGUE' | string;
  feedbackData?: XPHypothesisFeedbackData;
  userId?: string;
  currentLinLuoDetailedStatus?: LinLuoDetailedStatus;
  selectedRoleCardId?: string | null;
  selectedSceneCardId?: string | null;
  explicitPersona?: 'LinLuo' | 'XiaoLan' | 'YuJing';
  taskTitle?: string;
  taskDescription?: string;
  scriptModeInfo?: ScriptModeInfo;
  aideProjectPath?: string;
  aideFileStructure?: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
  desiredMemoryTypes?: string[]; // Added desiredMemoryTypes
  taskId?: string;
  currentFileInRightPanePath?: string;
  selectedTextInRightPaneEditor?: string;
  currentRightPaneView?: 'code_editor' | 'log_viewer' | 'knowledge_viewer' | null;
  workspaceRoot?: string;
  roundtable?: { // Context for roundtable meeting
      meetingId: string;
      currentSpeakerCharacterId: string;
      participantCharacterIds: string[];
      turnNumber: number;
      topic: string;
      fullChatHistory?: ChatMessage[]; // Entire history of the roundtable discussion
  };
  // Properties for TaskCockpitIntentRouterPersona
  userRawPromptForCockpit?: string;
  cockpitFullContext?: any;
  classifiedIntent?: string; // Added classifiedIntent
}

export interface AiCallContextInfoForMemory {
  personaTarget: string;
  projectContextId?: string | null;
  contextType: AiCallContextType;
  currentTopicKeywords: string[];
  desiredMemoryTypes?: string[];
}

export type AgentCoreSettingId =
  | 'user_profile'
  | 'linluo_persona'
  | 'xiaolan_persona'
  | 'yujing_persona'
  | 'summarizer_persona'
  | 'classifier_persona'
  | 'task_resource_suggester_persona'
  | 'OrchestratorPersona'
  | 'AideProjectAnalyzerPersona'
  | 'TaskCockpitIntentRouterPersona'
  | 'RoundtableFacilitatorPersona'
  | 'IntentClassifierPersona'; // Added IntentClassifierPersona

export interface AgentCoreSetting {
  setting_id: AgentCoreSettingId;
  content: string;
  last_updated_at: string;
}

export type CoreMemoryPersonaTarget = 'LinLuo' | 'XiaoLan' | 'YuJing' | 'shared' | 'user';
export type CoreMemoryImportance = 'high' | 'medium' | 'low';
export type CoreMemoryStatus = 'active' | 'archived' | 'pending_deletion';

export interface CoreMemory {
  id: string;
  persona_target: CoreMemoryPersonaTarget | string;
  memory_type: string;
  memory_content: string;
  importance: CoreMemoryImportance;
  created_at: string;
  last_accessed_at?: string | null;
  project_context_id: string | null;
  access_count?: number;
  keywords?: string | null;
  embedding?: string | null;
  source_message_id?: string | null;
  context_affinity_tags?: string | null;
  user_feedback_score?: number;
  status?: CoreMemoryStatus;
  value_score?: number;
  similarityScore?: number;
  contextAffinityScore?: number;
  timelinessScore?: number;
  totalScore?: number;
  retrieval_source?: 'semantic' | 'tag_associated';
}

export interface RelevantMemoriesSet {
  direct: CoreMemory[];
  associated: CoreMemory[];
}

export type AILearningLogTaskType =
  | 'manual_memory_storage_success'
  | 'rag_assisted_response_success'
  | 'user_positive_feedback_simple'
  | 'at_sensual_response_high_arousal'
  | 'code_generation_success'
  | 'xp_hypothesis_proposed'
  | 'xp_hypothesis_accepted'
  | 'xp_hypothesis_rejected'
  | 'xp_hypothesis_modified_accepted'
  | 'task_resource_suggestion_provided'
  | 'code_modification_applied'
  | 'command_log_analysis_requested'
  | 'command_execution_error'
  | 'other_significant_event';

export interface AILearningLog {
  log_id: string;
  timestamp: string;
  ai_persona: 'linluo' | 'xiaolan' | 'yujing';
  task_type: AILearningLogTaskType | string;
  triggering_input_summary: string;
  context_snapshot: any;
  ai_processing_summary: any;
  ai_generated_output_summary: string;
  user_feedback_explicit?: string | null;
  user_feedback_implicit_flags?: string | null;
  success_metric_value?: number | null;
  notes?: string | null;
  file_path?: string | null;
}

// AI Code Assist specific types
export interface GenerateCodeContext {
  currentSelection?: string;
  surroundingCode?: string;
  language: string;
  filePath?: string;
  projectId?: string;
  userInstruction: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
}
export interface ExplainCodeContext {
  codeToExplain: string;
  language: string;
  filePath?: string;
  projectId?: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
}
export interface GenerateDocContext {
  codeToDoc: string;
  language: string;
  filePath?: string;
  projectId?: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
}
export interface ReviewCodeContext {
  codeToReview: string;
  language: string;
  filePath?: string;
  projectId?: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
}
export interface AnalyzeErrorContext {
  errorLog: string;
  language?: string;
  filePath?: string;
  projectId?: string;
  surroundingCode?: string;
  queryForMemory?: string;
  keywordsForMemory?: string[];
}

export interface AIInteractionHistoryItem {
  id: string;
  type: 'generate' | 'explain' | 'document' | 'review' | 'analyze_log' | 'god_hand_modify';
  timestamp: string;
  instruction?: string;
  codeContext?: string;
  response: string;
  error?: string | null;
  associatedTaskId?: string | null;
  suggestedTaskStatus?: TaskStatus | null;
  filePath?: string | null;
}

export interface RouteUserIntentResponse {
  type: 'chat' | 'code_diff' | 'task_update' | 'file_content' | 'log_stream_started' | 'unknown_intent' | 'error';
  interactionId?: string;
  data: any;
  originalCommand?: string;
  aiPersona?: 'LinLuo' | 'XiaoLan' | 'System';
}

export interface AICommandAnalysisResult {
  analysis: string;
  possibleCauses: string[];
  suggestedSolutions: string[];
}

export interface InvokeSandboxRequestArgs {
  prompt: string;
  history?: ChatMessage[];
  projectId: string;
  persona?: string;
  otherSandboxContext?: Partial<AiCallContext>;
}

export interface InvokeTerritoryRequestArgs {
  prompt: string;
  history?: ChatMessage[];
  persona?: string;
  otherTerritoryContext?: Partial<AiCallContext>;
}
