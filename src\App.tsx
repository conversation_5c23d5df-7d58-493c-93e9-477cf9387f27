// src/App.tsx
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Routes, Route, useNavigate, useLocation, Link, Navigate, Outlet } from 'react-router-dom';
import { MainHallPage } from '@/components/MainHallPage';
import { ProjectWorkspacePage } from '@/components/ProjectWorkspacePage';
import { SettingsPage } from '@/components/SettingsPage';
import { KnowledgeBasePage } from '@/components/KnowledgeBasePage';
import { AbsoluteTerritoryPage } from '@/components/AbsoluteTerritoryPage';
import { TaskBoardPage } from '@/pages/TaskBoardPage';
import { SandboxChat } from '@/components/chat/SandboxChat';
import { ProjectKnowledgeBaseView } from '@/components/ProjectKnowledgeBaseView';
import { SourceCodeViewer } from '@/components/SourceCodeViewer';
import { MindWorkshopCanvas } from '@/components/MindWorkshopCanvas';
import { ProjectTimelinePage } from '@/pages/project/ProjectTimelinePage';
import { TaskCockpitPage } from '@/pages/TaskCockpitPage';
import { GlazedWorkshopPage } from '@/pages/GlazedWorkshopPage';
import { BridgePage } from '@/pages/BridgePage';


import type {
  Project, AppSettings as AppSettingsType, NoteItem, ImportanceLevel, MindNode, MindConnection,
  ChatMessage, KnowledgeTome, GlobalQuickCommandItem, ProjectKnowledgeTome,
  ModelOption, SummarizeAndReplaceResult, AudioPlaybackState,
  AiThinkingState, CoreMemory, NewProjectDataForApi, Task, TaskStatus, ActiveMainTabType,
  TaskCreationData, Character, Post, Assignment, ProjectWorkspacePageOutletContext,
  ChatDiscussionAreaRef
} from '@/types';
import { WisdomPouchType } from '@/types';
import { DEFAULT_SETTINGS, APP_TITLE, AVAILABLE_CHAT_MODELS } from '@/config/globalConfig';
import { Icon } from '@/components/common/Icon';
import { SmartIcon, VisualHeading } from '@/components/common/VisualUtils';

type AppSettings = AppSettingsType;

const App: React.FC = (): React.ReactElement | null => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [aiThinkingState, setAiThinkingState] = useState<AiThinkingState>('idle');

  const [settings, setSettings] = useState<AppSettings>(() => ({
    ...DEFAULT_SETTINGS,
    training_room_audio_state: { ...DEFAULT_SETTINGS.training_room_audio_state },
    currentTheme: DEFAULT_SETTINGS.currentTheme || 'theme-default',
    onAiThinkingStateChange: (newState: AiThinkingState) => setAiThinkingState(newState)
  }));

  const [globalKnowledgeTomes, setGlobalKnowledgeTomes] = useState<KnowledgeTome[]>([]);
  const [globalQuickCommands, setGlobalQuickCommands] = useState<GlobalQuickCommandItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiChatModels, setApiChatModels] = useState<ModelOption[]>(() =>
    AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id }))
  );
  const [isAIServiceReadyForApp, setIsAIServiceReadyForApp] = useState(false);
  const [taskToOpenInEditor, setTaskToOpenInEditor] = useState<Task | null>(null);
  
  const [allCharacters, setAllCharacters] = useState<Character[]>([]);
  const [allPosts, setAllPosts] = useState<Post[]>([]);
  const [allAssignments, setAllAssignments] = useState<Assignment[]>([]);

  const navigate = useNavigate();
  const location = useLocation();
  const chatAreaRef = useRef<ChatDiscussionAreaRef>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        if (!window.api || !window.api.database || !window.api.organization) {
          throw new Error("Core API services (database, organization) not available.");
        }
        let loadedSettingsFromDB = await window.api.database.getSettings();
        const baseDefaultAudioState: AudioPlaybackState = { ...DEFAULT_SETTINGS.training_room_audio_state };
        let finalSettings: AppSettings = {
          ...DEFAULT_SETTINGS, ...loadedSettingsFromDB,
          training_room_audio_state: { ...baseDefaultAudioState, ...(loadedSettingsFromDB?.training_room_audio_state || {}) },
          user_avatar_path: loadedSettingsFromDB?.user_avatar_path ?? null,
          linluo_avatar_path: loadedSettingsFromDB?.linluo_avatar_path ?? null,
          xiaolan_avatar_path: loadedSettingsFromDB?.xiaolan_avatar_path ?? null,
          currentTheme: loadedSettingsFromDB?.currentTheme || DEFAULT_SETTINGS.currentTheme || 'theme-default',
          defaultCover: loadedSettingsFromDB?.defaultCover === "" ? null : loadedSettingsFromDB?.defaultCover,
        };
        if (finalSettings.apiKey?.trim()) {
          if (typeof window.api?.ai?.getAvailableModels === 'function') {
            const fetchedModels = await window.api.ai.getAvailableModels();
            if (fetchedModels?.length) {
              setApiChatModels(fetchedModels);
              if (!fetchedModels.some(m => m.id === finalSettings.chatModel)) finalSettings.chatModel = fetchedModels[0].id;
            }
          }
        } else setIsAIServiceReadyForApp(false);
        setSettings({...finalSettings, onAiThinkingStateChange: (newState) => setAiThinkingState(newState) });

        const [loadedProjects, loadedGlobalTomes, loadedGlobalCmds, fetchedCharacters, fetchedPosts, fetchedAssignments] = await Promise.all([
          window.api.database.getAllProjects(), window.api.database.getAllGlobalKnowledgeTomes(),
          window.api.database.getAllGlobalQuickCommands(), window.api.organization.getCharacters(),
          window.api.organization.getPosts(), window.api.organization.getAssignments(),
        ]);
        setProjects((loadedProjects || []).filter(p => !p.isDeleted).map(p => ({...p, tasks: p.tasks || []})));
        setGlobalKnowledgeTomes(loadedGlobalTomes || []);
        setGlobalQuickCommands(loadedGlobalCmds || []);
        setAllCharacters(fetchedCharacters || []);
        setAllPosts(fetchedPosts || []);
        setAllAssignments(fetchedAssignments || []);
      } catch (err: any) { setError(`Data loading failed: ${err.message}`); } finally { setIsLoading(false); }
    };
    loadData();
    const handleAIServiceReady = () => setIsAIServiceReadyForApp(true);
    if (window.api?.ipc?.onAIServiceReady) window.api.ipc.onAIServiceReady(handleAIServiceReady);
    return () => { if (window.api?.ipc?.removeAIServiceReadyListener) window.api.ipc.removeAIServiceReadyListener(handleAIServiceReady); };
  }, []);

  useEffect(() => { document.documentElement.setAttribute('data-theme', settings.currentTheme || 'theme-default'); }, [settings.currentTheme]);

  const handleSaveSettings = useCallback(async (newSettingsOrCallback: AppSettings | ((prevState: AppSettings) => AppSettings)) => {
    let newSettingsToSave = typeof newSettingsOrCallback === 'function' ? newSettingsOrCallback(settings) : newSettingsOrCallback;
    newSettingsToSave.training_room_audio_state = { ...DEFAULT_SETTINGS.training_room_audio_state, ...(newSettingsToSave.training_room_audio_state || {}) };
    ['user_avatar_path', 'linluo_avatar_path', 'xiaolan_avatar_path', 'defaultCover'].forEach(key => {
        (newSettingsToSave as any)[key] = (newSettingsToSave as any)[key] === "" ? null : ((newSettingsToSave as any)[key] !== undefined ? (newSettingsToSave as any)[key] : (settings as any)[key]);
    });
    newSettingsToSave.currentTheme = newSettingsToSave.currentTheme || settings.currentTheme || 'theme-default';
    if (typeof newSettingsToSave.onAiThinkingStateChange !== 'function') newSettingsToSave.onAiThinkingStateChange = (newState) => setAiThinkingState(newState);
    
    const oldApiKey = settings.apiKey;
    const { onAiThinkingStateChange, ...serializableSettingsForIPC } = newSettingsToSave;
    const result = await window.api.database.saveSettings(serializableSettingsForIPC);
    if (result.success) {
      setSettings(newSettingsToSave);
      const newApiKeyProvided = newSettingsToSave.apiKey?.trim();
      if (newApiKeyProvided && newSettingsToSave.apiKey !== oldApiKey) {
        setIsAIServiceReadyForApp(false); 
        if (typeof window.api?.ai?.getAvailableModels === 'function') {
          const fetchedModels = await window.api.ai.getAvailableModels();
          if (fetchedModels?.length) {
            setApiChatModels(fetchedModels);
            if (!fetchedModels.some(m => m.id === newSettingsToSave.chatModel)) {
              const updatedChatModelSettings = { ...newSettingsToSave, chatModel: fetchedModels[0].id };
              const { onAiThinkingStateChange: _cb2, ...serializableChatModelSettings } = updatedChatModelSettings;
              await window.api.database.saveSettings(serializableChatModelSettings);
              setSettings(prev => ({...prev, ...updatedChatModelSettings }));
            }
          }
        }
      } else if (!newApiKeyProvided) setIsAIServiceReadyForApp(false);
    } else { setError(`Settings save failed: ${result.error || 'Unknown error'}`); }
  }, [settings]);

  const handleAddProject = useCallback(async (projectName: string): Promise<Project | null> => {
    const projectDataForApi: NewProjectDataForApi = { id: crypto.randomUUID(), name: projectName, createdAt: new Date().toISOString(), lastModifiedAt: new Date().toISOString(), isDeleted: false, sourceCodePath: null, coverImageUrl: undefined };
    const result = await window.api.database.addProject(projectDataForApi);
    if (result?.success && result.project) {
      const newProjectFull: Project = { ...result.project, discussionMessages: [], inspirationNotes: [], bugMemoNotes: [], quickCommandsNotes: [], mindNodes: [], mindConnections: [], projectKnowledgeTomes: [], projectKnowledgeCategories: [], developmentTasks: [], tasks: [] };
      setProjects(prev => [newProjectFull, ...prev].filter(p => p && !p.isDeleted)); return newProjectFull;
    } else { setError(`New project failed: ${result?.error || 'Backend error'}`); return null; }
  }, []);

  const handleUpdateProject = useCallback(async (updatedProject: Project) => {
    const result = await window.api.database.updateProject({...updatedProject, lastModifiedAt: new Date().toISOString()});
    if (result.success && result.project) setProjects(prev => prev.map(p => p.id === result.project!.id ? {...result.project!, tasks: result.project!.tasks || []} : p).filter(p => !p.isDeleted));
    else setError(`Project update failed: ${result.error || 'Unknown error'}`);
  }, []);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    const result = await window.api.database.deleteProject(projectId);
    if (result.success) setProjects(prev => prev.filter(p => p.id !== projectId));
    else setError(`Project delete failed: ${result.error || 'Unknown error'}`);
  }, []);

  const handleDuplicateProject = useCallback(async (projectId: string): Promise<Project | null> => {
    const duplicatedProject = await window.api.database.duplicateProject(projectId);
    if (duplicatedProject) {
      const fullDuplicatedProject: Project = { ...duplicatedProject, discussionMessages: [], inspirationNotes: [], bugMemoNotes: [], quickCommandsNotes: [], mindNodes: [], mindConnections: [], projectKnowledgeTomes: [], projectKnowledgeCategories: [], developmentTasks: [], tasks: duplicatedProject.tasks || [] };
      setProjects(prev => [fullDuplicatedProject, ...prev].filter(p => p && !p.isDeleted)); return fullDuplicatedProject;
    } else { setError("Duplicate project failed."); return null; }
  }, []);

  const handleAddNoteToProjectPouch = useCallback(async (projectId: string, pouchType: WisdomPouchType, text: string, importance?: ImportanceLevel): Promise<NoteItem | null> => {
    const newNote: NoteItem = { id: crypto.randomUUID(), text, createdAt: new Date().toISOString(), lastModifiedAt: new Date().toISOString(), importance: importance || 'medium' };
    const savedNote = await window.api.database.addNoteToProject(projectId, pouchType, newNote);
    if (savedNote) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, [pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes']: [...(p[pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes'] || []), savedNote], lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    return savedNote;
  }, []);

  const handleUpdateNoteInProject = ((projectId: string, pouchType: WisdomPouchType, updatedNote: NoteItem): void => {
     window.api.database.updateNoteInProject(pouchType, { ...updatedNote, lastModifiedAt: new Date().toISOString() }).then(savedNote => {
      if (savedNote) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, [pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes']: (p[pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes'] || []).map(n => n.id === savedNote.id ? savedNote : n), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    });
  });

  const handleDeleteNoteFromProject = ((projectId: string, pouchType: WisdomPouchType, noteId: string): void => {
     window.api.database.deleteNoteFromProject(pouchType, noteId).then(result => {
      if (result.success) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, [pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes']: (p[pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes'] || []).filter(n => n.id !== noteId), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    });
  });

  const handleUpdateProjectMindMap = useCallback((projectId: string, nodes: MindNode[], connections: MindConnection[]) => {
    window.api.database.updateProjectMindMap(projectId, nodes, connections).then(result => {
      if (result.success) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, mindNodes: nodes, mindConnections: connections, lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    });
  }, []);
  
  const handleAddGlobalTome = useCallback(async (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>): Promise<KnowledgeTome | undefined> => {
    const newTome: KnowledgeTome = { id: crypto.randomUUID(), ...tomeData, createdAt: new Date().toISOString(), lastModifiedAt: new Date().toISOString() };
    const savedTome = await window.api.database.addGlobalKnowledgeTome(newTome);
    if (savedTome) setGlobalKnowledgeTomes(prev => [savedTome, ...prev]); return savedTome;
  }, []);

  const handleUpdateGlobalTome = useCallback((updatedTome: KnowledgeTome) => {
    window.api.database.updateGlobalKnowledgeTome({...updatedTome, lastModifiedAt: new Date().toISOString() }).then(savedTome => { if (savedTome) setGlobalKnowledgeTomes(prev => prev.map(t => t.id === savedTome.id ? savedTome : t)); });
  }, []);

  const handleDeleteGlobalTome = useCallback((tomeId: string) => {
    window.api.database.deleteGlobalKnowledgeTome(tomeId).then(result => { if (result.success) setGlobalKnowledgeTomes(prev => prev.filter(t => t.id !== tomeId)); });
  }, []);

  const handleAddProjectTome = useCallback(async (projectId: string, tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>): Promise<ProjectKnowledgeTome | undefined> => {
    const newTome: ProjectKnowledgeTome = { id: crypto.randomUUID(), ...tomeData, createdAt: new Date().toISOString(), lastModifiedAt: new Date().toISOString() };
    const savedTome = await window.api.database.addProjectKnowledgeTome(projectId, newTome);
    if (savedTome) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, projectKnowledgeTomes: [savedTome, ...(p.projectKnowledgeTomes || [])], lastModifiedAt: new Date().toISOString() } : p ).filter(p => !p.isDeleted)); return savedTome;
  }, []);

  const handleUpdateProjectTome = useCallback((projectId: string, updatedTome: ProjectKnowledgeTome) => {
    window.api.database.updateProjectKnowledgeTome(projectId, {...updatedTome, lastModifiedAt: new Date().toISOString() }).then(savedTome => {
      if (savedTome) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, projectKnowledgeTomes: (p.projectKnowledgeTomes || []).map(t => t.id === savedTome.id ? savedTome : t), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    });
  }, []);

  const handleDeleteProjectTome = useCallback((projectId: string, tomeId: string) => {
    window.api.database.deleteProjectKnowledgeTome(projectId, tomeId).then(result => {
      if (result.success) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, projectKnowledgeTomes: (p.projectKnowledgeTomes || []).filter(t => t.id !== tomeId), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    });
  }, []);
  
  const handleAddProjectCategory = useCallback(async (projectId: string, category: string): Promise<string | undefined> => {
    const addedCategory = await window.api.database.addProjectKnowledgeCategory(projectId, category);
    if (addedCategory) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, projectKnowledgeCategories: [...(p.projectKnowledgeCategories || []).filter(c => c !== addedCategory), addedCategory].sort(), lastModifiedAt: new Date().toISOString() } : p ).filter(p => !p.isDeleted)); return addedCategory;
  }, []);

  const handleRemoveProjectCategory = useCallback(async (projectId: string, categoryName: string) => {
    const result = await window.api.database.removeProjectKnowledgeCategory(projectId, categoryName);
    if (result.success) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, projectKnowledgeCategories: (p.projectKnowledgeCategories || []).filter(c => c !== categoryName), projectKnowledgeTomes: (p.projectKnowledgeTomes || []).map(tome => tome.projectCategory === categoryName ? { ...tome, projectCategory: '未分类' } : tome), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    else setError(`Remove category failed: ${result.error || 'Unknown error'}`);
  }, []);
  
  const handleSaveNewChatMessage = useCallback(async (projectId: string, message: ChatMessage) => {
    if (!projectId) return; const savedMessage = await window.api.database.saveChatMessage(projectId, message);
    if (savedMessage) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, discussionMessages: [...(p.discussionMessages || []), savedMessage], lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
  }, []);

  const handleUpdateExistingChatMessageCallback = useCallback(async (updatedMessage: ChatMessage) => {
    if (!updatedMessage.projectId) return;
    const updatedMsgFromDb = await window.api.database.updateChatMessage(updatedMessage);
    if (updatedMsgFromDb) setProjects(prev => prev.map(p => p.id === updatedMessage.projectId ? { ...p, discussionMessages: (p.discussionMessages || []).map(m => m.id === updatedMsgFromDb.id ? updatedMsgFromDb : m), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
  }, []);

  const handleMessagesReplacedBySummary = useCallback((projectId: string, newSummaryMessage: ChatMessage, replacedMessageIds: string[]) => {
    setProjects(prev => prev.map(p => p.id === projectId ? { ...p, discussionMessages: [...(p.discussionMessages || []).filter(m => !replacedMessageIds.includes(m.id)), newSummaryMessage].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
  }, []);

  const handleAddCoreMemory = useCallback(async (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => {
    return await window.api.database.addCoreMemory(memoryData);
  }, []);

  const handleUpdateTaskStatusInApp = useCallback(async (taskId: string, newStatus: TaskStatus, projectIdToUpdate: string): Promise<Task | null> => {
    const updatedTask = await window.api.tasks.updateTask(taskId, { status: newStatus });
    if (updatedTask) setProjects(prev => prev.map(p => p.id === projectIdToUpdate ? { ...p, tasks: (p.tasks || []).map(t => t.task_id === taskId ? updatedTask : t), lastModifiedAt: new Date().toISOString() } : p).filter(p => !p.isDeleted));
    return updatedTask;
  }, []);

  const handleAideProjectImported = useCallback(async (projectId: string, newTasksData: TaskCreationData[]) => {
    const createdTasks: Task[] = [];
    for (const taskData of newTasksData) { const created = await window.api.tasks.addTask({ ...taskData, project_id: projectId }); if (created) createdTasks.push(created); }
    if (createdTasks.length > 0) setProjects(prev => prev.map(p => p.id === projectId ? { ...p, tasks: [...(p.tasks || []), ...createdTasks], lastModifiedAt: new Date().toISOString() } : p));
  }, []);

  const handleTaskEditorOpen = useCallback((task: Task, targetTab?: ActiveMainTabType) => { setTaskToOpenInEditor(task); navigate(`/project/${task.project_id}/${targetTab || 'cockpit'}`); }, [navigate]);
  const handleTaskEditorOpened = useCallback(() => { setTaskToOpenInEditor(null); }, []);

  const currentPath = location.pathname;
  const [pageTitle, setPageTitle] = useState(APP_TITLE);
  useEffect(() => { let title = APP_TITLE; const currentProjectIdFromPath = location.pathname.split('/')[2]; const currentProj = projects.find(p => p.id === currentProjectIdFromPath); if (currentPath === '/') title = `主殿 - ${APP_TITLE}`; else if (currentPath.startsWith('/project/') && currentProj) { const subRoute = location.pathname.split('/')[3]; let subRouteName = ""; switch(subRoute) { case 'discussion': subRouteName = '战略沙盘'; break; case 'task-board': subRouteName = '神谕罗盘'; break; case 'cockpit': subRouteName = '任务驾驶舱'; break; case 'source-code': subRouteName = '源码洞天'; break; case 'mind-workshop': subRouteName = '思路工坊'; break; case 'timeline': subRouteName = '项目时间轴'; break; case 'glazed-workshop': subRouteName = '琉璃坊'; break; case 'project-knowledge-base': subRouteName = '项目知库'; break; default: subRouteName = '工作区'; } title = `${subRouteName} - ${currentProj.name} - ${APP_TITLE}`; } else if (currentPath === '/settings') title = `天工阁设置 - ${APP_TITLE}`; else if (currentPath === '/knowledge-base') title = `万象书海 - ${APP_TITLE}`; else if (currentPath === '/absolute-territory') title = `训练室 - ${APP_TITLE}`; else if (currentPath === '/bridge') title = `指挥舰桥 - ${APP_TITLE}`; setPageTitle(title); }, [currentPath, projects, location.pathname]);
  useEffect(() => { document.title = pageTitle; }, [pageTitle]);

  const currentProjectForOutlet = useMemo(() => projects.find(p => p.id === location.pathname.split('/')[2]) || null, [projects, location.pathname]);

  const projectWorkspaceOutletContextValue: ProjectWorkspacePageOutletContext = useMemo(() => ({
    project: currentProjectForOutlet, projects, settings, globalQuickCommands, isAIServiceReady: isAIServiceReadyForApp,
    onAddCoreMemory: handleAddCoreMemory, onUpdateTaskStatusInApp: handleUpdateTaskStatusInApp, onAideProjectImported: handleAideProjectImported,
    handleDecomposeRequirement: async (req, projId, msgId) => { /* Placeholder */ }, handleConvertToTask: (msg) => { /* Placeholder */ },
    onSaveNewChatMessage: handleSaveNewChatMessage, onUpdateExistingChatMessage: (msg) => handleUpdateExistingChatMessageCallback(msg as ChatMessage), // Cast needed for now
    onMessagesReplacedBySummary: handleMessagesReplacedBySummary, chatAreaRef, addNoteToProject: handleAddNoteToProjectPouch,
    updateNoteInProject: handleUpdateNoteInProject, deleteNoteFromProject: handleDeleteNoteFromProject, updateProjectMindMap: handleUpdateProjectMindMap,
    addProjectKnowledgeTome: handleAddProjectTome, updateProjectKnowledgeTome: handleUpdateProjectTome, deleteProjectKnowledgeTome: handleDeleteProjectTome,
    addProjectKnowledgeCategory: handleAddProjectCategory, removeProjectKnowledgeCategory: handleRemoveProjectCategory,
    updateProjectGeneral: handleUpdateProject, taskToOpenInEditor, onEditorOpenedTask: handleTaskEditorOpened,
    onStartCraftingFromTask: handleTaskEditorOpen, aiTaskStatus: aiThinkingState,
    onOpenWisdomPouch: () => { const trigger = document.getElementById('project-workspace-page-wisdom-pouch-trigger'); if (trigger) trigger.click(); },
    allCharacters, allPosts, allAssignments, 
    onCallAI: async () => Promise.resolve() // Placeholder as specific pages handle their AI calls
  }), [
    currentProjectForOutlet, projects, settings, globalQuickCommands, isAIServiceReadyForApp, handleAddCoreMemory, handleUpdateTaskStatusInApp,
    handleAideProjectImported, handleSaveNewChatMessage, handleUpdateExistingChatMessageCallback, handleMessagesReplacedBySummary,
    chatAreaRef, handleAddNoteToProjectPouch, handleUpdateNoteInProject, handleDeleteNoteFromProject, handleUpdateProjectMindMap,
    handleAddProjectTome, handleUpdateProjectTome, handleDeleteProjectTome, handleAddProjectCategory,
    handleRemoveProjectCategory, handleUpdateProject, taskToOpenInEditor, handleTaskEditorOpened, handleTaskEditorOpen,
    aiThinkingState, allCharacters, allPosts, allAssignments
  ]);


  if (isLoading && !error) return (
    <div className="flex flex-col items-center justify-center h-screen bg-gradient-dark text-tg-text-primary">
      <div className="glass-effect p-8 rounded-3xl shadow-2xl animate-scale-in">
        <Icon name="Loader" className="w-16 h-16 animate-spin text-tg-accent-primary mx-auto mb-4 glow-on-hover" />
        <p className="text-xl font-medium text-gradient text-center">天工阁启动中，请稍候...</p>
        <div className="mt-4 w-48 h-1 bg-tg-bg-tertiary rounded-full overflow-hidden">
          <div className="h-full bg-gradient-primary animate-pulse rounded-full"></div>
        </div>
      </div>
    </div>
  );
  if (error) return ( <div className="flex flex-col items-center justify-center h-screen bg-tg-bg-primary text-tg-text-primary p-4"> <h2 className="text-2xl font-bold text-red-400 mb-4">天工阁启动失败</h2> <p className="text-tg-text-secondary mb-2">加载核心数据时遇到问题。</p> <p className="text-xs text-tg-text-placeholder bg-tg-bg-secondary p-3 rounded-md">{error}</p> <button onClick={() => window.location.reload()} className="mt-6 px-4 py-2 bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover">尝试重新加载</button> </div> );

  const apiKeyIsSet = settings.apiKey?.trim(); let apiStatusIndicatorColor = 'bg-red-500'; let apiStatusTooltip = "Gemini AI 服务未配置。";
  if (apiKeyIsSet) { if (isAIServiceReadyForApp) { apiStatusIndicatorColor = 'bg-green-500'; apiStatusTooltip = "Gemini AI 服务已连接。"; } else { apiStatusIndicatorColor = 'bg-yellow-500 animate-pulse'; apiStatusTooltip = "Gemini AI 服务连接中..."; }}
  const navLinkClass = (path: string) => `text-sm font-medium transition-all duration-300 flex items-center space-x-2 ${currentPath.startsWith(path) && (path !== '/' || currentPath === '/') ? 'bg-tg-accent-primary text-white shadow-lg' : 'text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary'}`;
  const nonDeletedProjects = projects.filter(p => !p.isDeleted);

  return (
    <div className="flex flex-col h-screen bg-gradient-dark text-tg-text-primary relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 bg-tg-accent-primary rounded-full blur-3xl animate-float"></div>
        <div className="absolute top-1/3 right-20 w-24 h-24 bg-tg-accent-secondary rounded-full blur-2xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-purple-500 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>
      <nav className="glass-effect shadow-xl px-6 py-3 flex items-center justify-between flex-shrink-0 backdrop-blur-xl border-b border-tg-border-primary">
        <div className="flex items-center space-x-3">
          <VisualHeading level="accent" size="2xl" gradient className="animate-glow-pulse">天工阁</VisualHeading>
          <div className="w-2 h-2 bg-tg-accent-primary rounded-full animate-pulse"></div>
        </div>
        <div className="flex items-center space-x-4">
          <Link to="/" title="主殿" className={`${navLinkClass('/')} card-hover rounded-xl px-4 py-2`}>
            <SmartIcon
              name="House"
              level="primary"
              context="navigation"
              className={currentPath === '/' ? '!text-white' : ''}
            />
            <span className="font-medium">主殿</span>
          </Link>
          <Link to="/bridge" title="指挥舰桥" className={`${navLinkClass('/bridge')} card-hover rounded-xl px-4 py-2`}>
            <SmartIcon
              name="Anchor"
              level="primary"
              context="navigation"
              className={currentPath.startsWith('/bridge') ? '!text-white' : ''}
            />
            <span className="font-medium">舰桥</span>
          </Link>
          <Link to="/knowledge-base" title="万象书海" className={`${navLinkClass('/knowledge-base')} card-hover rounded-xl px-4 py-2`}>
            <SmartIcon
              name="Archive"
              level="primary"
              context="navigation"
              className={currentPath.startsWith('/knowledge-base') ? '!text-white' : ''}
            />
            <span className="font-medium">万象书海</span>
          </Link>
          <Link to="/settings" title="天工阁设置" className={`${navLinkClass('/settings')} card-hover rounded-xl px-4 py-2`}>
            <SmartIcon
              name="Settings"
              level="primary"
              context="navigation"
              className={currentPath.startsWith('/settings') ? '!text-white' : ''}
            />
            <span className="font-medium">设置</span>
          </Link>
        </div>
      </nav>
      <main className="flex-grow overflow-auto relative z-10">
        {aiThinkingState !== 'idle' && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 glass-effect text-white px-4 py-2 text-sm rounded-2xl shadow-2xl z-50 animate-bounce-subtle backdrop-blur-md border border-purple-400">
            <div className="flex items-center space-x-2">
              <Icon name="Loader" className="w-4 h-4 animate-spin" />
              <span>{aiThinkingState === 'linluo_thinking' ? "林珞姐姐思考中..." : (aiThinkingState === 'xiaolan_thinking' ? "小岚炼器中..." : "AI 运转中...")}</span>
            </div>
          </div>
        )}
        <Routes>
          <Route path="/" element={<MainHallPage projects={nonDeletedProjects} addProject={handleAddProject} deleteProject={handleDeleteProject} updateProject={handleUpdateProject} duplicateProject={handleDuplicateProject} settings={settings} />} />
          <Route path="/bridge" element={<BridgePage projects={nonDeletedProjects} settings={settings} />} />
          <Route path="/settings" element={<SettingsPage settings={settings} setSettings={handleSaveSettings} projects={nonDeletedProjects} globalKnowledgeTomes={globalKnowledgeTomes} availableChatModels={apiChatModels} onAddCoreMemory={handleAddCoreMemory} />} />
          <Route path="/knowledge-base" element={<KnowledgeBasePage projects={nonDeletedProjects} globalKnowledgeTomes={globalKnowledgeTomes} onAddTome={handleAddGlobalTome} onUpdateTome={handleUpdateGlobalTome} onDeleteTome={handleDeleteGlobalTome} settings={settings} />} />
          <Route path="/absolute-territory" element={<AbsoluteTerritoryPage settings={settings} globalQuickCommands={globalQuickCommands} isAIServiceReady={isAIServiceReadyForApp} onAddCoreMemory={handleAddCoreMemory} />} />
          
          <Route element={<Outlet context={projectWorkspaceOutletContextValue} />}>
            <Route path="/project/:projectId/*" element={<ProjectWorkspacePage />}>
              <Route index element={<Navigate to="discussion" replace />} />
              <Route path="discussion" element={<SandboxChat />} />
              <Route path="task-board" element={<TaskBoardPage />} />
              <Route path="cockpit/:taskId?" element={<TaskCockpitPage />} />
              <Route path="source-code" element={<SourceCodeViewer updateProjectSettings={handleUpdateProject} settings={settings} initialTaskForCodeAssist={taskToOpenInEditor} onUpdateTaskStatus={handleUpdateTaskStatusInApp} onAideProjectImported={handleAideProjectImported} />} />
              <Route path="mind-workshop" element={<MindWorkshopCanvas initialNodes={currentProjectForOutlet?.mindNodes || []} initialConnections={currentProjectForOutlet?.mindConnections || []} onSave={(nodes, connections) => currentProjectForOutlet && handleUpdateProjectMindMap(currentProjectForOutlet.id, nodes, connections)} />} />
              <Route path="timeline" element={<ProjectTimelinePage />} />
              <Route path="glazed-workshop" element={<GlazedWorkshopPage />} />
              <Route path="project-knowledge-base" element={<ProjectKnowledgeBaseView projectTomes={currentProjectForOutlet?.projectKnowledgeTomes || []} projectCategories={currentProjectForOutlet?.projectKnowledgeCategories || []} onAddTome={(tomeData) => currentProjectForOutlet ? handleAddProjectTome(currentProjectForOutlet.id, tomeData) : Promise.resolve(undefined)} onUpdateTome={(tome) => currentProjectForOutlet && handleUpdateProjectTome(currentProjectForOutlet.id, tome)} onDeleteTome={(tomeId) => currentProjectForOutlet && handleDeleteProjectTome(currentProjectForOutlet.id, tomeId)} onAddCategory={(category) => currentProjectForOutlet ? handleAddProjectCategory(currentProjectForOutlet.id, category) : Promise.resolve(undefined)} onRemoveCategory={(category) => currentProjectForOutlet && handleRemoveProjectCategory(currentProjectForOutlet.id, category)} projectName={currentProjectForOutlet?.name || ""} />} />
              <Route path="*" element={<Navigate to="discussion" replace />} /> 
            </Route>
          </Route>

          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </main>
      <footer className="bg-tg-bg-secondary px-4 py-1.5 border-t border-tg-border-primary text-xs text-tg-text-secondary flex items-center justify-between flex-shrink-0">
        <span>天工阁·创世框架 v3.5.0</span>
        <div className="flex items-center space-x-2"> <span>AI服务状态:</span> <div title={apiStatusTooltip} aria-label={`Gemini API Status: ${apiStatusTooltip}`} className="p-1 relative cursor-pointer" onClick={() => navigate('/settings')} > <Icon name="Sparkles" className={`w-4 h-4 ${apiKeyIsSet && isAIServiceReadyForApp ? 'text-green-400' : (apiKeyIsSet ? 'text-yellow-400' : 'text-red-400')}`} /> <span className={`absolute top-0.5 right-0.5 block w-2 h-2 rounded-full border border-tg-bg-secondary ${apiStatusIndicatorColor}`}></span> </div> </div>
      </footer>
    </div>
  );
};
export default App;