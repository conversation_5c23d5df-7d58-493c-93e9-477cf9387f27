
import React, { useState, useEffect } from 'react';
import type { KnowledgeTome, ProjectKnowledgeTome } from '@/types';
import { GenericModal } from '@/components/GenericModal';
import { Icon } from '@/components/icons/IconComponents';

export interface SaveToKnowledgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTitle: string;
  initialContent: string;
  projectId?: string;
  projectName?: string; 
  projectCategories: string[]; 
  onSaveGlobal: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<void>;
  onSaveProject: (tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt' | 'projectId'>) => Promise<void>;
}

export const SaveToKnowledgeModal: React.FC<SaveToKnowledgeModalProps> = ({
  isOpen,
  onClose,
  initialTitle,
  initialContent,
  projectId,
  projectName,
  projectCategories,
  onSaveGlobal,
  onSaveProject,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [saveTarget, setSaveTarget] = useState<'global' | 'project'>(projectId ? 'project' : 'global');
  const [category, setCategory] = useState('');
  const [tagsString, setTagsString] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      setTitle(initialTitle.substring(0, 100)); // Limit title length
      setContent(initialContent);
      setSaveTarget(projectId ? 'project' : 'global');
      // Set default category based on save target
      if (projectId && projectCategories && projectCategories.length > 0) {
        setCategory(projectCategories.includes('AI 生成内容') ? 'AI 生成内容' : projectCategories[0]);
      } else {
        setCategory('AI 生成内容'); // Default global category or if project has no categories
      }
      setTagsString('AI代码助手, 代码片段'); // Default tags
      setError(null);
      setIsSaving(false);
    }
  }, [isOpen, initialTitle, initialContent, projectId, projectCategories]);

  const handleSave = async () => {
    if (!title.trim()) {
      setError("标题不能为空。");
      return;
    }
    if (!content.trim()) {
      setError("内容不能为空。");
      return;
    }
    if (!category.trim()) {
        setError("分类不能为空。");
        return;
    }

    setError(null);
    setIsSaving(true);

    const tags = tagsString.split(',').map(tag => tag.trim()).filter(Boolean);

    try {
      if (saveTarget === 'global') {
        await onSaveGlobal({ title, content, category, tags });
      } else if (projectId) {
        await onSaveProject({ title, content, projectCategory: category, tags });
      }
      onClose();
    } catch (e: any) {
      setError(`保存失败: ${e.message || '未知错误'}`);
    } finally {
      setIsSaving(false);
    }
  };
  
  const availableProjectCategories = ['AI 生成内容', ...projectCategories.filter(c => c !== 'AI 生成内容')];


  return (
    <GenericModal
      isOpen={isOpen}
      onClose={onClose}
      title="存入万象星尘"
      size="xl"
      footerContent={
        <>
          <button onClick={onClose} className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary" disabled={isSaving}>
            取消
          </button>
          <button onClick={handleSave} className="py-2 px-4 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover" disabled={isSaving}>
            {isSaving ? '保存中...' : '确认保存'}
          </button>
        </>
      }
    >
      {error && <p className="mb-3 p-2 text-sm text-red-400 bg-red-900/30 rounded">{error}</p>}
      <div className="space-y-4 text-sm">
        <div>
          <label htmlFor="save-tome-title" className="block text-xs text-tg-text-secondary mb-1">标题</label>
          <input
            id="save-tome-title"
            type="text"
            value={title}
            onChange={e => setTitle(e.target.value)}
            className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded"
            placeholder="卷宗标题..."
          />
        </div>
        <div>
          <label htmlFor="save-tome-content" className="block text-xs text-tg-text-secondary mb-1">内容</label>
          <textarea
            id="save-tome-content"
            value={content}
            onChange={e => setContent(e.target.value)}
            rows={8}
            className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded min-h-[150px] max-h-[40vh] resize-y"
            placeholder="卷宗内容..."
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label className="block text-xs text-tg-text-secondary mb-1">保存至</label>
                <div className="flex space-x-3">
                    <label className="flex items-center space-x-1.5 cursor-pointer">
                        <input type="radio" name="saveTarget" value="global" checked={saveTarget === 'global'} onChange={() => setSaveTarget('global')} className="form-radio text-tg-accent-primary bg-tg-bg-tertiary border-tg-border-primary focus:ring-tg-accent-primary"/>
                        <Icon name="archive-box" className="w-4 h-4 text-tg-accent-secondary"/>
                        <span>全局智库</span>
                    </label>
                    {projectId && (
                    <label className="flex items-center space-x-1.5 cursor-pointer">
                        <input type="radio" name="saveTarget" value="project" checked={saveTarget === 'project'} onChange={() => setSaveTarget('project')} disabled={!projectId} className="form-radio text-tg-accent-primary bg-tg-bg-tertiary border-tg-border-primary focus:ring-tg-accent-primary"/>
                        <Icon name="circle-stack" className="w-4 h-4 text-tg-accent-primary"/>
                        <span>当前项目 ({projectName?.substring(0,10) || projectId.substring(0,6)}...)</span>
                    </label>
                    )}
                </div>
            </div>
            <div>
                <label htmlFor="save-tome-category" className="block text-xs text-tg-text-secondary mb-1">
                    分类
                    {saveTarget === 'project' && projectCategories.length > 0 && " (选择或输入新分类)"}
                    {saveTarget === 'global' && " (输入新分类)"}
                </label>
                {saveTarget === 'project' && projectCategories.length > 0 ? (
                     <select
                        id="save-tome-category"
                        value={category}
                        onChange={(e) => setCategory(e.target.value)}
                        className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded mb-1"
                    >
                        {availableProjectCategories.map((cat) => (
                            <option key={cat} value={cat}>{cat}</option>
                        ))}
                    </select>
                ) : null}
                <input
                    type="text"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    placeholder={saveTarget === 'project' && projectCategories.length > 0 ? "或输入新项目分类..." : "输入分类名 (如: AI代码片段)..."}
                    className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded"
                />
            </div>
        </div>
        <div>
          <label htmlFor="save-tome-tags" className="block text-xs text-tg-text-secondary mb-1">
            <Icon name="tag" className="w-3.5 h-3.5 inline mr-1"/>
            关键词签 (逗号分隔)
          </label>
          <input
            id="save-tome-tags"
            type="text"
            value={tagsString}
            onChange={e => setTagsString(e.target.value)}
            className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded"
            placeholder="例如: javascript, 排序算法, AI辅助"
          />
        </div>
      </div>
    </GenericModal>
  );
};
