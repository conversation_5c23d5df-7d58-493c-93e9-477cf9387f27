// src/types/commonTypes.ts
import type { IconProps as OriginalIconProps } from '@/components/icons/iconTypes';

export type ImportanceLevel = 'high' | 'medium' | 'low';
export type IconProps = OriginalIconProps; // This refers to the generic IconProps for SVG attributes

export interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  error?: string;
  isDirty?: boolean;
}

export interface ModelOption {
  id: string;
  name: string;
}

export interface PaginationOptions {
  limit?: number;
  offset?: number;
}

export interface FileVersion {
  id: string;
  file_path: string;
  original_code: string;
  user_instruction?: string;
  archived_at: string;
}

// Moved BodyZoneKey here as it's used by multiple features (CMS, Achievements, etc.)
export type BodyZoneKey = 'mouth' | 'breasts' | 'vagina' | 'clitoris' | 'anus' | 'feet' | 'skin' | 'mind';
export const ALL_BODY_ZONES: BodyZoneKey[] = ['mouth', 'breasts', 'vagina', 'clitoris', 'anus', 'feet', 'skin', 'mind'];
