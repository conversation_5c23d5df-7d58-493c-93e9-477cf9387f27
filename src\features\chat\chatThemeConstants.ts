
import type { MessageThemeType } from '@/types';

export interface ThemeDefinition {
  prefixes: string[];
  icon: string;
  label: string;
  defaultStatus?: string;
  bubbleClass: string; 
  headerClass: string;
  textColorClass: string; 
  borderColorClass: string; 
  inputHintClass: string;
}

export const themeDefinitions: Record<MessageThemeType, ThemeDefinition> = {
  idea: { 
    prefixes: ['/idea', '/构想'], 
    icon: '🐉', 
    label: '龙之构想',
    bubbleClass: 'bg-yellow-700/30 border border-yellow-600 text-yellow-100',
    headerClass: 'text-yellow-300',
    textColorClass: 'text-yellow-100',
    borderColorClass: 'border-yellow-600',
    inputHintClass: 'border-yellow-500 text-yellow-400',
  },
  instruction: { 
    prefixes: ['/instruct', '/指令'], 
    icon: '📜', 
    label: '珞之指令书', 
    defaultStatus: '[状态：待执行]',
    bubbleClass: 'bg-sky-700/30 border border-sky-600 text-sky-100',
    headerClass: 'text-sky-300',
    textColorClass: 'text-sky-100',
    borderColorClass: 'border-sky-600',
    inputHintClass: 'border-sky-500 text-sky-400',
  },
  report: { 
    prefixes: ['/report', '/报告'], 
    icon: '📊', 
    label: '岚之炼器报表', 
    defaultStatus: '[状态：待审阅]',
    bubbleClass: 'bg-slate-600/30 border border-slate-500 text-slate-100',
    headerClass: 'text-slate-300',
    textColorClass: 'text-slate-100',
    borderColorClass: 'border-slate-500',
    inputHintClass: 'border-slate-400 text-slate-300',
  },
  review: { 
    prefixes: ['/review', '/审阅'], 
    icon: '🔖', 
    label: '珞之审阅笺',
    bubbleClass: 'bg-lime-700/30 border border-lime-600 text-lime-100',
    headerClass: 'text-lime-300',
    textColorClass: 'text-lime-100',
    borderColorClass: 'border-lime-600',
    inputHintClass: 'border-lime-500 text-lime-400',
  },
};

export function parseMessageTextForTheme(text: string): { theme?: MessageThemeType, finalText: string, rawContent: string } {
  let theme: MessageThemeType | undefined = undefined;
  let finalText = text;
  let rawContent = text;

  for (const [themeKey, def] of Object.entries(themeDefinitions)) {
    for (const prefix of def.prefixes) {
      const prefixWithOptionalSpace = prefix + " ";
      const prefixWithOptionalNewline = prefix + "\n";

      if (text.startsWith(prefixWithOptionalSpace)) {
        theme = themeKey as MessageThemeType;
        rawContent = text.substring(prefixWithOptionalSpace.length);
      } else if (text.startsWith(prefixWithOptionalNewline)) {
        theme = themeKey as MessageThemeType;
        rawContent = text.substring(prefixWithOptionalNewline.length);
      } else if (text.startsWith(prefix) && (text.length === prefix.length || !/\w/.test(text.charAt(prefix.length)))) {
        theme = themeKey as MessageThemeType;
        rawContent = text.substring(prefix.length);
      }

      if (theme) {
        if (def.defaultStatus) {
          if (rawContent.length > 0 && !rawContent.startsWith('\n') && !rawContent.startsWith('<strong>')) {
            finalText = `<strong>${def.defaultStatus}</strong>\n${rawContent}`;
          } else if (!rawContent.startsWith('<strong>')) {
            finalText = `<strong>${def.defaultStatus}</strong>${rawContent}`;
          } else {
            finalText = rawContent;
          }
        } else {
          finalText = rawContent;
        }
        break; 
      }
    }
    if (theme) break;
  }
  return { theme, finalText, rawContent };
}
