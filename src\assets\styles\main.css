/* src/assets/styles/main.css */

/* You can add global styles here or Tailwind @ directives */
/* For example:
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

/* Basic body reset if not handled by Tailwind base in index.html */
body {
  margin: 0;
  font-family: 'Inter', sans-serif; /* Ensure Inter is primary */
  background-color: var(--color-bg-primary); /* Use CSS var from index.html */
  color: var(--color-text-primary); /* Use CSS var from index.html */
  line-height: 1.6;
}

/* Ensure root takes full height if needed by App structure */
#root {
  height: 100vh;
  display: flex; /* If App.tsx's top div is a flex container */
  flex-direction: column; /* If App.tsx's top div is a flex container */
}
