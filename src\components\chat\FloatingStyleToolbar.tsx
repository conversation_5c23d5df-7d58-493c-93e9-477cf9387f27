// src/components/chat/FloatingStyleToolbar.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon'; 
import type { FloatingStyleToolbarProps } from '@/types'; 

const MANDALA_RED = '#DC143C'; 
const HIGHLIGHT_YELLOW = '#FEF9C3'; 

export const FloatingStyleToolbar: React.FC<FloatingStyleToolbarProps> = ({ isVisible, top, left, onApplyStyle, onClose }) => {
  if (!isVisible) return null;

  return (
    <div
      data-floating-toolbar="true" 
      className="absolute z-20 bg-tg-bg-tertiary border border-tg-border-primary rounded-md shadow-xl p-1.5 flex space-x-1"
      style={{ top, left }}
    >
      <button
        onMouseDown={e => e.preventDefault()} 
        onClick={() => onApplyStyle('bold')}
        className="p-1.5 hover:bg-tg-bg-hover rounded text-tg-text-primary"
        title="加粗 (Bold)"
      >
        <Icon name="Bold" className="w-4 h-4" />
        
      </button>
      <button
        onMouseDown={e => e.preventDefault()} 
        onClick={() => onApplyStyle('backColor', HIGHLIGHT_YELLOW)}
        className="p-1.5 hover:bg-tg-bg-hover rounded text-tg-text-primary"
        title="背景高亮 (Highlight Yellow)"
      >
        <Icon name="Highlighter" className="w-4 h-4" />
        
      </button>
      <button
        onMouseDown={e => e.preventDefault()} 
        onClick={() => onApplyStyle('foreColor', MANDALA_RED)}
        className="p-1.5 hover:bg-tg-bg-hover rounded text-tg-text-primary"
        title="文字颜色 (Mandala Red)"
      >
        <Icon name="Palette" className="w-4 h-4" />
        
      </button>
      <button
        onMouseDown={e => e.preventDefault()} 
        onClick={onClose}
        className="p-1.5 hover:bg-tg-bg-hover rounded text-tg-text-secondary"
        title="关闭 (Close)"
      >
        <Icon name="X" className="w-4 h-4" />
      </button>
    </div>
  );
};
