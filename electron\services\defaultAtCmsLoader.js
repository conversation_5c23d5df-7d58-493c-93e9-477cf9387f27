// electron/services/defaultAtCmsLoader.js
console.log('DEFAULT_AT_CMS_LOADER_JS: File execution started.');

import fs from 'node:fs/promises';
import path from 'node:path';
import { app } from 'electron';
import { parseTgcJson } from '../utils/jsonParser'; // Corrected import

const SYSTEM_RESOURCES_PATH_SEGMENT = 'system';
const DEFAULTS_PATH_SEGMENT = 'defaults';
const AT_CMS_DEFINITIONS_DIR = 'at_cms_definitions'; // New directory for definitions

function getAtCmsDefinitionsBasePath() {
  const baseAppPath = app.getAppPath();
  const effectiveBasePath = baseAppPath.includes('app.asar')
    ? path.join(baseAppPath, '..') 
    : baseAppPath;
  return path.resolve(effectiveBasePath, 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, AT_CMS_DEFINITIONS_DIR);
}

async function loadAtCmsItemsFromFileSystem(itemType) {
  const itemsDir = path.join(getAtCmsDefinitionsBasePath(), itemType); // e.g., .../at_cms_definitions/props
  console.log(`DEFAULT_AT_CMS_LOADER: Attempting to load ${itemType} from ${itemsDir}`);
  const loadedItems = [];

  try {
    await fs.access(itemsDir);
    const files = await fs.readdir(itemsDir);
    console.log(`DEFAULT_AT_CMS_LOADER: Found ${files.length} files in ${itemsDir}. Filtering for '.json'...`);
    
    for (const file of files) {
      if (file.endsWith(`.${itemType.slice(0, -1)}.json`)) { // e.g., .prop.json
        const filePath = path.join(itemsDir, file);
        console.log(`DEFAULT_AT_CMS_LOADER: Reading ${itemType} file: ${filePath}`);
        try {
          const fileContent = await fs.readFile(filePath, 'utf-8');
          const itemData = parseTgcJson(fileContent);
          
          if (itemData && typeof itemData === 'object' && itemData.id && itemData.name && itemData.type === itemType.slice(0,-1)) {
            loadedItems.push(itemData);
          } else {
            console.warn(`DEFAULT_AT_CMS_LOADER: Invalid or incomplete ${itemType} data in ${file}. Skipping. Expected type: ${itemType.slice(0,-1)}, Got: ${itemData?.type}`);
          }
        } catch (err) {
          console.error(`DEFAULT_AT_CMS_LOADER: Failed to load or parse ${itemType} file ${filePath}. Reason: ${err.message}. Skipping.`);
        }
      }
    }
  } catch (err) {
    console.error(`DEFAULT_AT_CMS_LOADER: Failed to access or read ${itemType} directory ${itemsDir}. Reason: ${err.message}. Returning empty array for ${itemType}.`);
    return [];
  }
  console.log(`DEFAULT_AT_CMS_LOADER: Successfully loaded ${loadedItems.length} default AT CMS ${itemType}.`);
  return loadedItems;
}

export async function loadDefaultAtCmsProps() {
  return loadAtCmsItemsFromFileSystem('props');
}

export async function loadDefaultAtCmsCostumes() {
  return loadAtCmsItemsFromFileSystem('costumes');
}

export async function loadDefaultAtCmsPoses() {
  return loadAtCmsItemsFromFileSystem('poses');
}

console.log('DEFAULT_AT_CMS_LOADER_JS: File execution finished. Service functions defined.');