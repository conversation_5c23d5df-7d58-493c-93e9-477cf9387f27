// src/components/roundtable/ToDoList.tsx
import React from 'react';
import type { ToDoListProps, ToDoItem } from '@/types';
import { ToDoListItem } from './ToDoListItem'; 
import { Icon } from '@/components/common/Icon';

export const ToDoList: React.FC<ToDoListProps> = ({
  items,
  onUpdateItemText,
  onDeleteItem,
  onPublishItem,
  onPublishAll,
  onAddItem,
  isMeetingActive,
}) => {
  return (
    <div className="flex flex-col h-full p-1">
      {items.length === 0 && (
        <div className="flex-grow flex flex-col items-center justify-center text-center text-tg-text-placeholder p-4">
          <Icon name="ListChecks" className="w-10 h-10 mb-2 opacity-50"/>
          <p className="text-sm">暂无待办事项。</p>
          {isMeetingActive && <p className="text-xs mt-1">点击下方按钮添加新的待办。</p>}
        </div>
      )}
      {items.length > 0 && (
        <ul className="space-y-2 overflow-y-auto flex-grow mb-2 custom-scrollbar pr-1">
          {items.map((item) => (
            <ToDoListItem
              key={item.id}
              item={item}
              onUpdateText={(newText) => onUpdateItemText(item.id, newText)}
              onDelete={() => onDeleteItem(item.id)}
              onPublish={() => onPublishItem(item.id)}
              isMeetingActive={isMeetingActive}
            />
          ))}
        </ul>
      )}
      {isMeetingActive && (
        <div className="mt-auto pt-2 border-t border-tg-border-primary flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 justify-end">
          <button
            onClick={onAddItem}
            className="px-3 py-1.5 text-xs bg-tg-accent-secondary text-white rounded-md hover:brightness-110 flex items-center justify-center"
            title="添加新的待办事项"
          >
            <Icon name="Plus" className="w-4 h-4 mr-1"/> 添加待办
          </button>
          <button
            onClick={onPublishAll}
            disabled={items.every(item => item.isPublished) || items.length === 0}
            className="px-3 py-1.5 text-xs bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center disabled:opacity-50"
            title="将所有未发布的待办事项转化为正式任务"
          >
            <Icon name="UploadCloud" className="w-4 h-4 mr-1"/> 全部发布
          </button>
        </div>
      )}
    </div>
  );
};