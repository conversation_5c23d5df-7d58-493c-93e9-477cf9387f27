// src/components/at_sidebar/MagicMirrorDisplay.tsx
import React from 'react';
import type { RolePlayingCard, TGCRoleCardAsset, LinLuoDetailedStatus, AppSettings } from '@/types';
import { Icon } from '@/components/common/Icon'; 

interface MagicMirrorDisplayProps {
  selectedRoleCard: RolePlayingCard | TGCRoleCardAsset | null;
  linLuoStatus: LinLuoDetailedStatus;
  settings: AppSettings; 
}

const AHEGAO_SANITY_THRESHOLD = 30;
const AHEGAO_AROUSAL_THRESHOLD = 80;
const AHEGAO_ORGASM_THRESHOLD = 80;

export const MagicMirrorDisplay: React.FC<MagicMirrorDisplayProps> = ({ selectedRoleCard, linLuoStatus, settings }) => {
  // 使用 useMemo 来避免不必要的重新计算和闪烁
  const baseImageSrc = React.useMemo(() => {
    let src = settings.linluo_avatar_path
      ? `app-avatar://${settings.linluo_avatar_path}`
      : "./placeholders/linluo_default.png";

    if (selectedRoleCard) {
      if (selectedRoleCard.base_avatar_clothing_key) {
        if (selectedRoleCard.base_avatar_clothing_key.startsWith('tgc-asset://') || selectedRoleCard.base_avatar_clothing_key.startsWith('at-asset://')) {
          src = selectedRoleCard.base_avatar_clothing_key;
        } else {
          if (selectedRoleCard.icon_path && (selectedRoleCard.icon_path.startsWith('tgc-asset://') || selectedRoleCard.icon_path.startsWith('at-asset://'))) {
            src = selectedRoleCard.icon_path;
          }
        }
      } else if (selectedRoleCard.icon_path) {
        if (selectedRoleCard.icon_path.startsWith('tgc-asset://') || selectedRoleCard.icon_path.startsWith('at-asset://')) {
          src = selectedRoleCard.icon_path;
        }
      }
    }

    return src;
  }, [settings.linluo_avatar_path, selectedRoleCard]);


  
  const isAhegao = (linLuoStatus.sanityPoints < AHEGAO_SANITY_THRESHOLD) && 
                   (((linLuoStatus.arousal || 0) > AHEGAO_AROUSAL_THRESHOLD) || ((linLuoStatus.orgasmProximity || 0) > AHEGAO_ORGASM_THRESHOLD));

  let expressionText = "";
  if (isAhegao) {
    expressionText = "此刻姐姐大人露出了心神迷醉的阿黑颜...";
  } else if ((linLuoStatus.shameLevel || 0) > 70) {
    expressionText = "姐姐大人面色绯红，眼神羞涩地低垂着...";
  } else if ((linLuoStatus.shameLevel || 0) > 40) {
    expressionText = "姐姐大人脸颊泛红，眼神有些躲闪...";
  } else if ((linLuoStatus.arousal || 0) > 80) {
    expressionText = "姐姐大人媚眼如丝，呼吸略显急促...";
  } else if ((linLuoStatus.arousal || 0) > 50) {
    expressionText = "姐姐大人眼神迷离，似乎有些动情...";
  }


  // 添加图片加载状态管理
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [imageError, setImageError] = React.useState(false);

  // 当图片源改变时重置状态
  React.useEffect(() => {
    setImageLoaded(false);
    setImageError(false);
  }, [baseImageSrc]);

  return (
    <div className="w-full h-full glass-effect border border-purple-700/50 rounded-2xl shadow-2xl flex flex-col items-center justify-center p-6 overflow-hidden relative backdrop-blur-md">
      <div className="relative w-full h-[70%] flex items-center justify-center">
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Icon name="Loader" className="w-8 h-8 animate-spin text-purple-400" />
          </div>
        )}
        <img
          src={baseImageSrc}
          alt={selectedRoleCard ? `${selectedRoleCard.name} - 立绘` : "林珞姐姐 - 立绘"}
          className={`max-w-full max-h-full object-contain rounded-xl shadow-2xl border-2 border-purple-500/30 transition-all duration-300 ${
            imageLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}
          onLoad={() => {
            setImageLoaded(true);
            setImageError(false);
          }}
          onError={(e) => {
            e.currentTarget.src = "./placeholders/linluo_default.png";
            e.currentTarget.alt = "默认立绘";
            setImageError(true);
            setImageLoaded(true);
          }}
        />
      </div>
      <div className="text-center mt-4 space-y-2">
        <h3 className="text-lg text-gradient font-bold">{selectedRoleCard ? selectedRoleCard.name : "林珞姐姐"}</h3>
        {expressionText && (
          <div className="glass-effect rounded-xl p-3 backdrop-blur-sm">
            <p className="text-sm text-pink-300 italic leading-relaxed">{expressionText}</p>
          </div>
        )}
        {!expressionText && linLuoStatus.mood && (
          <div className="glass-effect rounded-xl p-2 backdrop-blur-sm">
            <p className="text-xs text-gray-300">当前心情: <span className="text-purple-300 font-medium">{linLuoStatus.mood}</span></p>
          </div>
        )}
      </div>
      <div className="absolute top-3 right-3 p-2 glass-effect rounded-full backdrop-blur-md">
        <Icon name="Camera" className="w-5 h-5 text-purple-400 animate-pulse" title="姐姐的魔镜"/>
      </div>
    </div>
  );
};