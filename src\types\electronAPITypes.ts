// src/types/electronAPITypes.ts

// Import all necessary types from './types' (which is src/types/index.ts)
import type {
    Project, AppSettings, NoteItem, ImportanceLevel, MindNode, MindConnection,
    ChatMessage, KnowledgeTome, GlobalQuickCommandItem, ProjectKnowledgeTome,
    FileNode, RetrievedChunk, ModelOption, SummarizeAndReplaceResult, WisdomPouchType,
    DevelopmentTask, AgentCoreSettingId, AgentCoreSetting, CoreMemory, CoreMemoryPersonaTarget, CoreMemoryStatus,
    AiCallContext, AIResponseWithStatus, AudioPlaybackState, SoundName,
    AiCallContextInfoForMemory, RelevantMemoriesSet,
    CMSItemBase, CMSType, RolePlayingCard, NewProjectDataForApi, AILearningLog, PaginationOptions,
    LinLuoBodyDevelopment, BodyZoneKey, Achievement, UserAchievement,
    Task, TaskStatus, TaskPriority, ResourceType, TaskResourceLink, AITaskStatus, TaskCreationData,
    GenerateCodeContext, ExplainCodeContext, GenerateDocContext, ReviewCodeContext, AnalyzeErrorContext, AIInteractionHistoryItem,
    AiCallContextType, FileVersion, AICommandAnalysisResult, RouteUserIntentResponse,
    DevelopmentTaskCreationPayload, Post, Character, Assignment,
    AnyLoadedAsset,
    RoundtableParticipant, ToDoItem,
    ScriptChoice,
    InvokeSandboxRequestArgs, InvokeTerritoryRequestArgs 
} from './'; // Import from main index
import type { OpenDialogOptions, IpcRendererEvent } from 'electron';

// Define CommandExecutionEvent locally and export it
export interface CommandExecutionEvent {
  internalPid: string;
  systemPid?: number;
  type: 'stdout' | 'stderr' | 'exit' | 'error_event';
  data?: string;
  code?: number | null;
  signal?: string | null;
  error?: string;
}


// Define the structure of the API exposed by preload.ts
export interface IElectronAPI {
  testPreload: () => void;
  database: {
    getSettings: () => Promise<AppSettings>;
    saveSettings: (settings: Omit<AppSettings, 'onAiThinkingStateChange'>) => Promise<{ success: boolean; error?: string }>;
    getAllProjects: () => Promise<Project[]>;
    addProject: (projectData: NewProjectDataForApi) => Promise<{success: boolean, project?: Project, error?: string}>;
    getProjectById: (projectId: string) => Promise<Project | null>;
    updateProject: (projectData: Project) => Promise<{success: boolean, project?: Project, error?: string}>;
    deleteProject: (projectId: string) => Promise<{ success: boolean; error?: string }>;
    duplicateProject: (projectId: string) => Promise<Project | null>;
    addNoteToProject: (projectId: string, pouchType: WisdomPouchType, noteData: NoteItem) => Promise<NoteItem | null>;
    updateNoteInProject: (pouchType: WisdomPouchType, noteData: NoteItem) => Promise<NoteItem | null>;
    deleteNoteFromProject: (pouchType: WisdomPouchType, noteId: string) => Promise<{ success: boolean; error?: string, id?:string }>;
    updateProjectMindMap: (projectId: string, nodes: MindNode[], connections: MindConnection[]) => Promise<{ success: boolean; error?: string }>;
    getAllGlobalKnowledgeTomes: () => Promise<KnowledgeTome[]>;
    addGlobalKnowledgeTome: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<KnowledgeTome | null>;
    updateGlobalKnowledgeTome: (tomeData: KnowledgeTome) => Promise<KnowledgeTome | null>;
    deleteGlobalKnowledgeTome: (tomeId: string) => Promise<{ success: boolean; error?: string }>;
    addProjectKnowledgeTome: (projectId: string, tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<ProjectKnowledgeTome | null>;
    updateProjectKnowledgeTome: (projectId: string, tomeData: ProjectKnowledgeTome) => Promise<ProjectKnowledgeTome | null>;
    deleteProjectKnowledgeTome: (projectId: string, tomeId: string) => Promise<{ success: boolean; error?: string }>;
    addProjectKnowledgeCategory: (projectId: string, categoryName: string) => Promise<string | undefined>;
    removeProjectKnowledgeCategory: (projectId: string, categoryName: string) => Promise<{ success: boolean; error?: string }>;
    getAllDevelopmentTasks: () => Promise<DevelopmentTask[]>;
    addDevelopmentTask: (projectId: string, title: string) => Promise<DevelopmentTask | null>;
    createDevelopmentTaskFromChat: (payload: DevelopmentTaskCreationPayload) => Promise<DevelopmentTask | null>; 
    deleteDevelopmentTask: (taskId: string) => Promise<{ success: boolean; error?: string }>;
    updateDevelopmentTaskContextFiles: (taskId: string, contextFiles: string[]) => Promise<{ success: boolean; error?: string }>;
    updateDevelopmentTaskGeneratedCode: (taskId: string, generatedCode: string) => Promise<{ success: boolean; error?: string }>;
    getAgentCoreSetting: (settingId: AgentCoreSettingId) => Promise<AgentCoreSetting | null>;
    getAllAgentCoreSettings: () => Promise<AgentCoreSetting[]>;
    saveAgentCoreSetting: (settingId: AgentCoreSettingId, content: string) => Promise<{ success: boolean; error?: string }>;
    findRelevantMemories: (queryEmbedding: number[], contextInfo: AiCallContextInfoForMemory, limit: number) => Promise<RelevantMemoriesSet>;
    getCoreMemories: (personaTarget: CoreMemoryPersonaTarget, projectContextId: string | null, limit: number, importanceThreshold: string | null, keywords: string[]) => Promise<CoreMemory[]>;
    getAllCoreMemories: (filters: any, sort: any, pagination: PaginationOptions) => Promise<CoreMemory[]>;
    addCoreMemory: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => Promise<CoreMemory | null>;
    addCoreMemoryFromChat: (messageText: string, personaTarget: CoreMemoryPersonaTarget, projectId: string | null) => Promise<CoreMemory | null>;
    updateCoreMemory: (memoryData: CoreMemory) => Promise<CoreMemory | null>;
    deleteCoreMemory: (memoryId: string) => Promise<{ success: boolean; error?: string }>;
    getCoreMemoryById: (memoryId: string) => Promise<CoreMemory | null>;
    getAbsoluteTerritoryMessages: (limit: number, beforeTimestamp?: string) => Promise<ChatMessage[]>;
    addAbsoluteTerritoryMessage: (message: ChatMessage) => Promise<ChatMessage | null>;
    clearAbsoluteTerritoryHistory: () => Promise<{ success: boolean; error?: string }>;
    addLearningLog: (logData: Omit<AILearningLog, 'log_id' | 'timestamp'>) => Promise<AILearningLog | null>;
    getLearningLogs: (filters: any, limit: number, offset: number) => Promise<AILearningLog[]>;
    getBodyDevelopment: (zone_id: BodyZoneKey) => Promise<LinLuoBodyDevelopment | null>;
    getAllBodyDevelopment: () => Promise<LinLuoBodyDevelopment[]>;
    updateBodyDevelopment: (zone_id: BodyZoneKey, pointsToAdd: number) => Promise<{ success: boolean; error?: string, zone_id?: BodyZoneKey, newPoints?: number, oldPoints?: number }>;
    getAllAchievements: () => Promise<Achievement[]>;
    getUserAchievements: (userId: string) => Promise<UserAchievement[]>;
    unlockAchievement: (userId: string, achievementId: string) => Promise<{ success: boolean; error?: string, message?: string }>;
    getAchievementById: (achievementId: string) => Promise<Achievement | null>;
    // Chat Message specific functions
    saveChatMessage: (projectId: string, message: ChatMessage) => Promise<ChatMessage | null>;
    updateChatMessage: (message: ChatMessage) => Promise<ChatMessage | null>;
    getInitialChatMessages: (projectId: string, limit: number) => Promise<ChatMessage[]>;
    getOlderChatMessages: (projectId: string, beforeTimestamp: string, limit: number) => Promise<ChatMessage[]>;
    summarizeAndReplaceMessages: (projectId: string, messageIds: string[], summaryMessage: ChatMessage) => Promise<SummarizeAndReplaceResult>;
    // Global Quick Command specific functions
    getAllGlobalQuickCommands: () => Promise<GlobalQuickCommandItem[]>;
    addGlobalQuickCommand: (commandData: Omit<GlobalQuickCommandItem, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<GlobalQuickCommandItem | null>;
    updateGlobalQuickCommand: (commandData: GlobalQuickCommandItem) => Promise<GlobalQuickCommandItem | null>;
    deleteGlobalQuickCommand: (commandId: string) => Promise<{ success: boolean; error?: string }>;
  };
  settings: {
    getAbsoluteTerritoryPassword: () => Promise<string | null>;
    setAbsoluteTerritoryPassword: (password: string | null) => Promise<{ success: boolean, error?: string }>;
    verifyAbsoluteTerritoryPassword: (password: string) => Promise<{ isValid: boolean, isFirstTime: boolean }>;
  };
  tasks: {
    getTaskById: (taskId: string) => Promise<Task | null>;
    getTasksByProjectId: (projectId: string, filters?: any, sortOptions?: any) => Promise<Task[]>;
    getTasksByStatus: (projectId: string, statusArray: TaskStatus[]) => Promise<Task[]>;
    addTask: (taskData: TaskCreationData) => Promise<Task | null>;
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<Task | null>;
    deleteTask: (taskId: string) => Promise<{ success: boolean; error?: string }>;
    addResourceLinkToTask: (taskId: string, resourceData: Omit<TaskResourceLink, 'link_id' | 'task_id'>) => Promise<TaskResourceLink | null>;
    getResourceLinksForTask: (taskId: string) => Promise<TaskResourceLink[]>;
    removeResourceLinkFromTask: (linkId: string) => Promise<{ success: boolean; error?: string }>;
    suggestResourcesForTask: (taskId: string, taskTitle: string, taskDescription: string | undefined, projectId: string) => Promise<Partial<TaskResourceLink>[]>;
  };
  ai: {
    invokeSandboxRequest: (args: InvokeSandboxRequestArgs) => Promise<AIResponseWithStatus>;
    invokeTerritoryRequest: (args: InvokeTerritoryRequestArgs) => Promise<AIResponseWithStatus>;
    getAvailableModels: () => Promise<ModelOption[]>;
    callAI: (prompt: string, persona: string, context: AiCallContext) => Promise<AIResponseWithStatus>;
    discussWithAI: (contents: any, config: any, modelName: string, apiKey?: string) => Promise<string>;
    summarizeConversation: (historyChunk: ChatMessage[]) => Promise<string>;
    decomposeRequirementToTasks: (requirementText: string, projectId: string) => Promise<{ success: boolean; error?: string; tasks: TaskCreationData[] }>;
    analyzeAndDecomposeAideProject: (projectId: string, projectPath: string) => Promise<{ success: boolean; error?: string; tasks: TaskCreationData[] }>;
    routeUserIntent: (userInputText: string, context: AiCallContext) => Promise<RouteUserIntentResponse>;
    startRoundtableMeeting: (initialPrompt: string, participantCharacterIds: string[], turnLimit: number, projectId: string, initialHistory: ChatMessage[]) => Promise<{ success: boolean; error?: string; history?: ChatMessage[], meetingId?: string }>;
    assist: {
        generateCode: (context: GenerateCodeContext) => Promise<string>;
        explainCode: (context: ExplainCodeContext) => Promise<string>;
        generateDocForCode: (context: GenerateDocContext) => Promise<string>;
        reviewCode: (context: ReviewCodeContext) => Promise<string>;
        analyzeErrorLog: (context: AnalyzeErrorContext) => Promise<string>;
        modifyCode: (context: { filePath: string, userInstruction: string, originalCode: string }) => Promise<{ newCode?: string, error?: string }>;
    };
  };
  bridgeAi: {
    processIntent: (userInputText: string) => Promise<string>;
  };
  fs: {
    openDirectoryDialog: () => Promise<string | null>;
    readDirectory: (dirPath: string) => Promise<FileNode[] | { error: string }>;
    readFileContent: (filePath: string) => Promise<string | { error: string }>;
    saveFileContent: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
    openFileDialog: (options?: OpenDialogOptions) => Promise<string | null>;
    openMultipleFilesDialog: (extensions: string[]) => Promise<string[] | null>;
    copyFileToUserData: (sourcePath: string, targetSubdir: string, targetFilename?: string) => Promise<string | { error: string }>;
    exportChatHistory: (messages: ChatMessage[], format: 'md' | 'html', defaultFileName: string) => Promise<{ success: boolean; path?: string; error?: string }>;
    listFiles: (directoryPath: string, recursive?: boolean, depth?: number) => Promise<FileNode[] | {error: string}>;
    readFile: (filePath: string) => Promise<string | {error: string}>;
    writeFile: (filePath: string, content: string) => Promise<{success: boolean, error?: string}>;
    archiveFileVersion: (filePath: string, originalCode: string, userInstruction: string) => Promise<{success: boolean, error?: string}>;
    isDirectoryEmpty: (directoryPath: string) => Promise<boolean | { error: string }>;
    copyDirectoryContents: (sourceDir: string, targetDir: string) => Promise<{ success: boolean; error?: string }>;
  };
  file: {
    readPackageJsonScripts: (projectPath: string) => Promise<Record<string, string> | { error: string }>;
  };
  rag: {
    runProjectIndexing: (projectId: string, projectPath: string, options: { apiKey: string; embeddingModelName: string }) => Promise<{ success: boolean; error?: string; message?: string }>;
    retrieveRelevantChunks: (queryText: string, projectId: string, apiKey: string, embeddingModelName: string, topK?: number) => Promise<{ success: boolean; error?: string; results?: RetrievedChunk[]; message?: string }>;
    indexFileContent: (projectId: string, filePath: string, fileContent: string, options: { apiKey: string; embeddingModelName: string }) => Promise<{ success: boolean; error?: string; message?: string, chunksCreated?: number }>;
  };
  audio: {
    playSound: (soundName: SoundName, loop: boolean) => Promise<void>;
    stopSound: (soundName?: SoundName) => Promise<void>;
    setVolume: (volume: number) => Promise<void>;
    getPlaybackState: () => Promise<AudioPlaybackState>;
    onPlaybackStateChanged: (callback: (newState: AudioPlaybackState) => void) => () => void;
  };
  electronUtils: {
    getPlatform: () => Promise<string>;
  };
  utils: {
    combinePaths: (basePath: string, relativePath: string) => Promise<string>;
  };
  assets: {
    getLoadedAssets: () => Promise<{ props: AnyLoadedAsset[], costumes: AnyLoadedAsset[], poses: AnyLoadedAsset[], scene_cards: AnyLoadedAsset[], achievements: AnyLoadedAsset[], scripts: AnyLoadedAsset[], role_cards: AnyLoadedAsset[] }>;
    refreshAssetPacks: () => Promise<{success: boolean, error?: string}>;
    createAssetPack: (fileName: string, assetPackData: any) => Promise<{success: boolean, filePath?: string, error?: string}>;
    deleteAssetPack: (fileName: string) => Promise<{success: boolean, error?: string}>;
  };
  command: {
    execute: (params: { commandString: string; args: string[]; cwd: string }) => Promise<{ internalPid: string; error?: string }>;
    kill: (internalPid: string) => Promise<{ success: boolean; error?: string }>;
    onEvent: (callback: (event: CommandExecutionEvent) => void) => () => void; 
    analyzeLog: (logContent: string) => Promise<string>;
  };
  cms: { 
    getCMSItems: (type: CMSType) => Promise<CMSItemBase[]>;
    addCMSItem: (type: CMSType, itemData: any, iconBase64?: string | null, cgBase64?: string | null, statusEffectsJson?: string, developmentEffectsJson?: string, unlockRequirementsJson?: string) => Promise<CMSItemBase | null>;
    updateCMSItem: (type: CMSType, itemId: string, itemData: any, iconBase64?: string | null, cgBase64?: string | null, statusEffectsJson?: string, developmentEffectsJson?: string, unlockRequirementsJson?: string) => Promise<CMSItemBase | null>;
    deleteCMSItem: (type: CMSType, itemId: string) => Promise<{ success: boolean, error?: string }>;
    triggerHuntingTime: () => Promise<string | null>;
    getRolePlayingCards: () => Promise<RolePlayingCard[]>;
    getRolePlayingCardById: (cardId: string) => Promise<RolePlayingCard | null>;
    addRolePlayingCard: (cardData: Partial<RolePlayingCard> & { icon_base64?: string | null }) => Promise<RolePlayingCard | null>;
    updateRolePlayingCard: (cardData: Partial<RolePlayingCard> & { icon_base64?: string | null | undefined }) => Promise<RolePlayingCard | null>;
    deleteRolePlayingCard: (cardId: string) => Promise<{ success: boolean, error?: string }>;
  };
  organization: { 
    getPosts: () => Promise<Post[]>;
    getCharacters: () => Promise<Character[]>;
    getAssignments: () => Promise<Assignment[]>;
    setAssignment: (postId: string, characterId: string) => Promise<{ success: boolean; assignment?: Assignment; error?: string }>;
    getAssignmentByPostId: (postId: string) => Promise<string | null>; 
  };
  ipc: {
    onAIServiceReady: (callback: () => void) => () => void; 
    removeAIServiceReadyListener: (callback: () => void) => void; 
    onRoundtableMessage: (callback: (message: ChatMessage) => void) => () => void; 
  };
}

declare global {
  interface Window {
    api: IElectronAPI;
  }
}

export {};
