// electron/services/cmsDbService.js
console.log('CMS_DB_SERVICE_JS: File execution started.');

import { db, crypto } from './databaseCore';
import fs from 'node:fs/promises';
import path from 'node:path';
import { app } from 'electron'; // To get userData path

const CMS_ASSETS_DIR_NAME = 'at_cms_assets';

// Helper to save/update an image asset and return its relative DB path
// subfolder should be 'icons' or 'cgs'
async function saveOrUpdateImageAsset(type, subfolder, imageBase64ToSave, existingDbRelativePath) {
    const userDataPath = app.getPath('userData');

    async function deleteAsset(relativePathToDelete) {
        if (relativePathToDelete) {
            // The relativePathToDelete already includes type/subfolder/filename.ext
            const absolutePathToDelete = path.join(userDataPath, CMS_ASSETS_DIR_NAME, relativePathToDelete);
            try {
                await fs.access(absolutePathToDelete);
                await fs.unlink(absolutePathToDelete);
                console.log(`CMS_DB: Deleted asset ${absolutePathToDelete}`);
            } catch (accessOrDeleteError) {
                if (accessOrDeleteError.code === 'ENOENT') {
                    console.log(`CMS_DB_INFO: Asset ${absolutePathToDelete} not found, skipping deletion.`);
                } else {
                    console.warn(`CMS_DB_WARN: Failed to delete asset ${relativePathToDelete}`, accessOrDeleteError);
                }
            }
        }
    }
    
    // If imageBase64ToSave is null or empty, it means "clear this image"
    if (imageBase64ToSave === null || imageBase64ToSave === "") {
        if (existingDbRelativePath) {
            await deleteAsset(existingDbRelativePath);
        }
        return null; // Return null to clear the path in DB
    }

    // If imageBase64ToSave is undefined, it means "don't change this image"
    if (imageBase64ToSave === undefined) {
        return existingDbRelativePath; // Return existing path, no change
    }

    // If we reach here, imageBase64ToSave contains new data.
    const fullAssetDir = path.join(userDataPath, CMS_ASSETS_DIR_NAME, type, subfolder);
    try {
        await fs.mkdir(fullAssetDir, { recursive: true });
    } catch (mkdirError) {
        console.error(`CMS_DB_ERROR: Could not create directory ${fullAssetDir}`, mkdirError);
        throw new Error(`Failed to create asset directory for ${type}/${subfolder}.`);
    }

    let extension = '.png';
    const mimeMatch = imageBase64ToSave.match(/^data:image\/(\w+);base64,/);
    if (mimeMatch && mimeMatch[1]) {
        const imgType = mimeMatch[1].toLowerCase();
        if (['jpeg', 'jpg'].includes(imgType)) extension = '.jpg';
        else if (imgType === 'webp') extension = '.webp';
        else if (imgType === 'gif') extension = '.gif';
    }

    const base64Data = imageBase64ToSave.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, 'base64');
    const newFilename = `${crypto.randomUUID()}${extension}`;
    // Path stored in DB will be like: props/icons/uuid.png or props/cgs/uuid.png
    const newDbRelativePath = path.join(type, subfolder, newFilename).replace(/\\/g, '/'); 
    const absoluteSavePath = path.join(fullAssetDir, newFilename);

    try {
        await fs.writeFile(absoluteSavePath, buffer);
        console.log(`CMS_DB: Saved new image asset to ${absoluteSavePath}`);
        if (existingDbRelativePath && existingDbRelativePath !== newDbRelativePath) {
            await deleteAsset(existingDbRelativePath);
        }
        return newDbRelativePath;
    } catch (writeError) {
        console.error(`CMS_DB_ERROR: Failed to write image asset to ${absoluteSavePath}`, writeError);
        throw new Error(`Failed to save image asset for ${type}/${subfolder}.`);
    }
}


function getTableName(type) {
    switch (type) {
        case 'props': return 'at_cms_props';
        case 'costumes': return 'at_cms_costumes';
        case 'poses': return 'at_cms_poses';
        case 'role_cards': return 'at_role_playing_cards';
        default: throw new Error(`Invalid CMS type: ${type}`);
    }
}

export async function getCMSItems(type) {
    if (!db) { console.error(`CMS_DB_ERROR: getCMSItems(${type}) - db not available.`); return []; }
    const tableName = getTableName(type);
    try {
        let items;
        if (type === 'role_cards') {
             items = db.prepare(`SELECT id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt FROM ${tableName} ORDER BY sort_order ASC, name ASC`).all();
        } else {
             items = db.prepare(`SELECT id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt FROM ${tableName} ORDER BY name ASC`).all();
        }
        console.log(`CMS_DB: Retrieved ${items.length} items for type ${type}.`);
        // 为 CMS 数据添加 type 字段以兼容资产系统
        const typeMapping = {
            'props': 'prop',
            'costumes': 'costume',
            'poses': 'pose',
            'role_cards': 'role_card'
        };

        return items.map(item => ({
            ...item,
            type: typeMapping[type], // 添加 type 字段
            status_effects_json: item.status_effects_json,
            development_effects_json: item.development_effects_json,
            unlock_requirements_json: item.unlock_requirements_json,
        }));
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error getting CMS items for type ${type}:`, e);
        return [];
    }
}

export async function addCMSItem(type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) {
    if (!db) { console.error(`CMS_DB_ERROR: addCMSItem(${type}) - db not available.`); return null; }
    const tableName = getTableName(type);
    
    let finalIconPath = null;
    let finalCgPath = null;

    try {
        if (iconBase64) finalIconPath = await saveOrUpdateImageAsset(type, 'icons', iconBase64, null);
        if (cgBase64) finalCgPath = await saveOrUpdateImageAsset(type, 'cgs', cgBase64, null);
    } catch (imgError) {
        console.error(`CMS_DB_ERROR: Failed to save image(s) for new ${type} item:`, imgError.message);
    }

    const newItem = {
        id: crypto.randomUUID(),
        name: itemData.name,
        owner: itemData.owner,
        icon_path: finalIconPath,
        cg_image_path: finalCgPath,
        prompt_for_linluo: itemData.prompt_for_linluo,
        prompt_for_master: itemData.prompt_for_master,
        status_effects_json: statusEffectsJson || '{}',
        development_effects_json: developmentEffectsJson || '[]',
        unlock_requirements_json: unlockRequirementsJson || '[]',
        createdAt: new Date().toISOString(),
        lastModifiedAt: new Date().toISOString(),
    };

    const stmt = db.prepare(`
        INSERT INTO ${tableName} (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    try {
        stmt.run(newItem.id, newItem.name, newItem.owner, newItem.icon_path, newItem.cg_image_path, newItem.prompt_for_linluo, newItem.prompt_for_master, newItem.status_effects_json, newItem.development_effects_json, newItem.unlock_requirements_json, newItem.createdAt, newItem.lastModifiedAt);
        console.log(`CMS_DB: Added CMS item ${newItem.id} of type ${type}.`);
        return newItem;
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error adding CMS item of type ${type}:`, e);
        return null;
    }
}

export async function updateCMSItem(type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) {
    if (!db) { console.error(`CMS_DB_ERROR: updateCMSItem(${type}, ${itemId}) - db not available.`); return null; }
    const tableName = getTableName(type);

    const existingItemRow = db.prepare(`SELECT icon_path, cg_image_path FROM ${tableName} WHERE id = ?`).get(itemId);
    if (!existingItemRow) {
        console.error(`CMS_DB_ERROR: Item ${itemId} of type ${type} not found for update.`);
        return null;
    }
    
    let finalIconPath = existingItemRow.icon_path;
    let finalCgPath = existingItemRow.cg_image_path;
    let iconProcessed = false;
    let cgProcessed = false;

    try {
        if (iconBase64 !== undefined) { 
            finalIconPath = await saveOrUpdateImageAsset(type, 'icons', iconBase64, existingItemRow.icon_path);
            iconProcessed = true;
        }
        if (cgBase64 !== undefined) {
            finalCgPath = await saveOrUpdateImageAsset(type, 'cgs', cgBase64, existingItemRow.cg_image_path);
            cgProcessed = true;
        }
    } catch (imgError) {
        console.error(`CMS_DB_ERROR: Failed to process image(s) during update for ${type} item ${itemId}:`, imgError.message);
        if (iconBase64 !== undefined && iconProcessed && finalIconPath === null && imgError.message.includes('icons')) finalIconPath = existingItemRow.icon_path; 
        if (cgBase64 !== undefined && cgProcessed && finalCgPath === null && imgError.message.includes('cgs')) finalCgPath = existingItemRow.cg_image_path; 
    }


    const fieldsToUpdate = [];
    const valuesToUpdate = [];

    if (itemData.name !== undefined) { fieldsToUpdate.push("name = ?"); valuesToUpdate.push(itemData.name); }
    if (itemData.owner !== undefined) { fieldsToUpdate.push("owner = ?"); valuesToUpdate.push(itemData.owner); }
    
    if (iconProcessed) { fieldsToUpdate.push("icon_path = ?"); valuesToUpdate.push(finalIconPath); }
    if (cgProcessed) { fieldsToUpdate.push("cg_image_path = ?"); valuesToUpdate.push(finalCgPath); }
    
    if (itemData.prompt_for_linluo !== undefined) { fieldsToUpdate.push("prompt_for_linluo = ?"); valuesToUpdate.push(itemData.prompt_for_linluo); }
    if (itemData.prompt_for_master !== undefined) { fieldsToUpdate.push("prompt_for_master = ?"); valuesToUpdate.push(itemData.prompt_for_master); }
    
    if (statusEffectsJson !== undefined) { fieldsToUpdate.push("status_effects_json = ?"); valuesToUpdate.push(statusEffectsJson === null ? '{}' : statusEffectsJson); }
    if (developmentEffectsJson !== undefined) { fieldsToUpdate.push("development_effects_json = ?"); valuesToUpdate.push(developmentEffectsJson === null ? '[]' : developmentEffectsJson); }
    if (unlockRequirementsJson !== undefined) { fieldsToUpdate.push("unlock_requirements_json = ?"); valuesToUpdate.push(unlockRequirementsJson === null ? '[]' : unlockRequirementsJson); }
    
    if (fieldsToUpdate.length === 0) {
        console.log(`CMS_DB: No data fields to update for item ${itemId} of type ${type}.`);
        return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(itemId);
    }

    fieldsToUpdate.push("lastModifiedAt = ?");
    valuesToUpdate.push(new Date().toISOString());
    valuesToUpdate.push(itemId);

    const stmt = db.prepare(`UPDATE ${tableName} SET ${fieldsToUpdate.join(', ')} WHERE id = ?`);
    try {
        stmt.run(...valuesToUpdate);
        console.log(`CMS_DB: Updated CMS item ${itemId} of type ${type}.`);
        const updatedItem = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(itemId);
        return {
            ...updatedItem,
            status_effects_json: updatedItem.status_effects_json,
            development_effects_json: updatedItem.development_effects_json,
            unlock_requirements_json: updatedItem.unlock_requirements_json,
        };
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error updating CMS item ${itemId} of type ${type}:`, e);
        return null;
    }
}

export async function deleteCMSItem(type, itemId) {
    if (!db) { console.error(`CMS_DB_ERROR: deleteCMSItem(${type}, ${itemId}) - db not available.`); return { success: false, error: "Database not available." }; }
    const tableName = getTableName(type);
    const userDataPath = app.getPath('userData');

    const itemToDelete = db.prepare(`SELECT icon_path, cg_image_path FROM ${tableName} WHERE id = ?`).get(itemId);

    const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
    try {
        const result = stmt.run(itemId);
        if (result.changes > 0) {
            console.log(`CMS_DB: Deleted CMS item ${itemId} of type ${type}.`);
            if (itemToDelete) {
                const pathsToDelete = [itemToDelete.icon_path, itemToDelete.cg_image_path].filter(Boolean);
                for (const relativeAssetPath of pathsToDelete) {
                    const absoluteAssetPath = path.join(userDataPath, CMS_ASSETS_DIR_NAME, relativeAssetPath);
                    try {
                        await fs.access(absoluteAssetPath);
                        await fs.unlink(absoluteAssetPath);
                        console.log(`CMS_DB: Deleted asset ${relativeAssetPath} for item ${itemId}.`);
                    } catch (deleteError) {
                        if (deleteError.code === 'ENOENT') {
                            console.log(`CMS_DB_INFO: Asset ${relativeAssetPath} not found, no need to delete.`);
                        } else {
                           console.warn(`CMS_DB_WARN: Failed to delete asset ${relativeAssetPath} for deleted item ${itemId}.`, deleteError);
                        }
                    }
                }
            }
            return { success: true };
        } else {
            console.warn(`CMS_DB_WARN: Item ${itemId} of type ${type} not found for deletion.`);
            return { success: false, error: "Item not found." };
        }
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error deleting CMS item ${itemId} of type ${type}:`, e);
        return { success: false, error: e.message };
    }
}

export function triggerHuntingTime() {
    console.log("CMS_DB_SERVICE: Hunting Time triggered!");
    return "狩猎时刻已激活！林珞女王的目光扫向了她的猎物...";
}


// --- Role Playing Cards CRUD ---
export async function getRolePlayingCards() {
    if (!db) { console.error("CMS_DB_ERROR: getRolePlayingCards - db not available."); return []; }
    const tableName = getTableName('role_cards');
    try {
        const cards = db.prepare(`SELECT id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt FROM ${tableName} ORDER BY sort_order ASC, name ASC`).all();
        console.log(`CMS_DB: Retrieved ${cards.length} role playing cards.`);
        return cards;
    } catch (e) {
        console.error("CMS_DB_ERROR: Error getting role playing cards:", e);
        return [];
    }
}

export async function getRolePlayingCardById(cardId) {
    if (!db) { console.error(`CMS_DB_ERROR: getRolePlayingCardById(${cardId}) - db not available.`); return null; }
    const tableName = getTableName('role_cards');
    try {
        const card = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardId);
        if (card) {
            console.log(`CMS_DB: Retrieved role playing card by ID ${cardId}.`);
        } else {
            console.warn(`CMS_DB_WARN: Role playing card with ID ${cardId} not found.`);
        }
        return card || null;
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error getting role playing card by ID ${cardId}:`, e);
        return null;
    }
}

export async function addRolePlayingCard(cardData) {
    if (!db) { console.error("CMS_DB_ERROR: addRolePlayingCard - db not available."); return null; }
    const tableName = getTableName('role_cards');
    
    let finalIconPath = null;
    if (cardData.icon_base64) { 
        try {
            finalIconPath = await saveOrUpdateImageAsset('role_cards', 'icons', cardData.icon_base64, null);
        } catch (imgError) {
            console.error(`CMS_DB_ERROR: Failed to save icon for new role card:`, imgError.message);
        }
    }
    
    const newCard = {
        id: crypto.randomUUID(),
        name: cardData.name,
        description: cardData.description || '',
        icon_path: finalIconPath,
        initial_status_override_json: cardData.initial_status_override_json || '{}',
        persona_snippet_override: cardData.persona_snippet_override || '',
        sort_order: cardData.sort_order || 0,
        createdAt: new Date().toISOString(),
        lastModifiedAt: new Date().toISOString(),
    };
    const stmt = db.prepare(`
        INSERT INTO ${tableName} (id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    try {
        stmt.run(newCard.id, newCard.name, newCard.description, newCard.icon_path, newCard.initial_status_override_json, newCard.persona_snippet_override, newCard.sort_order, newCard.createdAt, newCard.lastModifiedAt);
        console.log(`CMS_DB: Added role playing card ${newCard.id}.`);
        return newCard;
    } catch (e) {
        console.error("CMS_DB_ERROR: Error adding role playing card:", e);
        return null;
    }
}

export async function updateRolePlayingCard(cardData) {
    if (!db) { console.error(`CMS_DB_ERROR: updateRolePlayingCard(${cardData.id}) - db not available.`); return null; }
    const tableName = getTableName('role_cards');
    
    const existingCard = db.prepare(`SELECT icon_path FROM ${tableName} WHERE id = ?`).get(cardData.id);
    if (!existingCard) {
        console.error(`CMS_DB_ERROR: Role playing card ${cardData.id} not found for update.`);
        return null;
    }

    let finalIconPath = existingCard.icon_path;
    let iconProcessed = false;
    if (cardData.icon_base64 !== undefined) { 
        try {
            finalIconPath = await saveOrUpdateImageAsset('role_cards', 'icons', cardData.icon_base64, existingCard.icon_path);
            iconProcessed = true;
        } catch (imgError) {
             console.error(`CMS_DB_ERROR: Failed to update icon for role card ${cardData.id}:`, imgError.message);
        }
    }

    const fieldsToUpdate = [];
    const valuesToUpdate = [];

    if (cardData.name !== undefined) { fieldsToUpdate.push("name = ?"); valuesToUpdate.push(cardData.name); }
    if (cardData.description !== undefined) { fieldsToUpdate.push("description = ?"); valuesToUpdate.push(cardData.description); }
    if (iconProcessed) { fieldsToUpdate.push("icon_path = ?"); valuesToUpdate.push(finalIconPath); } 
    if (cardData.initial_status_override_json !== undefined) { fieldsToUpdate.push("initial_status_override_json = ?"); valuesToUpdate.push(cardData.initial_status_override_json); }
    if (cardData.persona_snippet_override !== undefined) { fieldsToUpdate.push("persona_snippet_override = ?"); valuesToUpdate.push(cardData.persona_snippet_override); }
    if (cardData.sort_order !== undefined) { fieldsToUpdate.push("sort_order = ?"); valuesToUpdate.push(cardData.sort_order); }

    if (fieldsToUpdate.length === 0) {
        console.log(`CMS_DB: No data fields to update for role card ${cardData.id}.`);
        return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardData.id);
    }

    fieldsToUpdate.push("lastModifiedAt = ?");
    valuesToUpdate.push(new Date().toISOString());
    valuesToUpdate.push(cardData.id);

    const stmt = db.prepare(`UPDATE ${tableName} SET ${fieldsToUpdate.join(', ')} WHERE id = ?`);
    try {
        stmt.run(...valuesToUpdate);
        console.log(`CMS_DB: Updated role playing card ${cardData.id}.`);
        return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardData.id);
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error updating role playing card ${cardData.id}:`, e);
        return null;
    }
}

export async function deleteRolePlayingCard(cardId) {
    if (!db) { console.error(`CMS_DB_ERROR: deleteRolePlayingCard(${cardId}) - db not available.`); return { success: false, error: "Database not available." }; }
    const tableName = getTableName('role_cards');
    const userDataPath = app.getPath('userData');
    const cardToDelete = db.prepare(`SELECT icon_path FROM ${tableName} WHERE id = ?`).get(cardId);

    const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
    try {
        const result = stmt.run(cardId);
        if (result.changes > 0) {
            console.log(`CMS_DB: Deleted role playing card ${cardId}.`);
            if (cardToDelete && cardToDelete.icon_path) {
                const absoluteAssetPath = path.join(userDataPath, CMS_ASSETS_DIR_NAME, cardToDelete.icon_path);
                try {
                    await fs.access(absoluteAssetPath);
                    await fs.unlink(absoluteAssetPath);
                    console.log(`CMS_DB: Deleted asset ${cardToDelete.icon_path} for card ${cardId}.`);
                } catch (deleteError) {
                     if (deleteError.code === 'ENOENT') { console.log(`CMS_DB_INFO: Asset ${cardToDelete.icon_path} not found, no need to delete.`);}
                     else { console.warn(`CMS_DB_WARN: Failed to delete asset ${cardToDelete.icon_path} for deleted card ${cardId}.`, deleteError); }
                }
            }
            return { success: true };
        } else {
            console.warn(`CMS_DB_WARN: Role playing card ${cardId} not found for deletion.`);
            return { success: false, error: "Card not found." };
        }
    } catch (e) {
        console.error(`CMS_DB_ERROR: Error deleting role playing card ${cardId}:`, e);
        return { success: false, error: e.message };
    }
}


console.log('CMS_DB_SERVICE_JS: File execution finished. Exports configured.');