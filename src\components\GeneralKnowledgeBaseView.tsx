import React, { useState, useMemo, useCallback } from 'react';
import type { KnowledgeTome } from '@/types';
import { KNOWLEDGE_TOME_CATEGORIES_MAP, KnowledgeTomeCategory } from '@/features/knowledge_tomes/knowledgeConstants'; 
import { Icon } from '@/components/common/Icon';

interface GeneralKnowledgeBaseViewProps {
  globalKnowledgeTomes: KnowledgeTome[];
  onAddTome: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<KnowledgeTome | undefined>;
  onUpdateTome: (updatedTome: KnowledgeTome) => void;
  onDeleteTome: (tomeId: string) => void;
}

interface CreateEditTomeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>, editingId?: string) => Promise<void>;
  existingTome?: KnowledgeTome | null;
}

const CreateEditTomeModal: React.FC<CreateEditTomeModalProps> = ({ isOpen, onClose, onSave, existingTome }) => {
  const [title, setTitle] = useState(existingTome?.title || '');
  const [content, setContent] = useState(existingTome?.content || '');
  const [category, setCategory] = useState<KnowledgeTomeCategory>(existingTome?.category || 'miscellaneous');
  const [tagsString, setTagsString] = useState(existingTome?.tags?.join(', ') || '');

  React.useEffect(() => {
    if (isOpen) {
      setTitle(existingTome?.title || '');
      setContent(existingTome?.content || '');
      setCategory(existingTome?.category || 'miscellaneous');
      setTagsString(existingTome?.tags?.join(', ') || '');
    }
  }, [isOpen, existingTome]);

  const handleSave = async () => {
    if (!title.trim()) {
      alert("“卷名”不能为空！");
      return;
    }
    const tags = tagsString.split(/[,;\s]+/).map(tag => tag.trim()).filter(tag => tag.length > 0);
    await onSave({ title, content, category, tags }, existingTome?.id);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-tg-bg-secondary p-6 rounded-lg shadow-xl w-full max-w-2xl border border-tg-border-primary max-h-[90vh] flex flex-col">
        <h3 className="text-2xl font-semibold mb-5 text-tg-text-primary">{existingTome ? '编辑卷宗' : '创建新卷宗'}</h3>
        <div className="space-y-4 overflow-y-auto pr-2 flex-grow">
          <div>
            <label htmlFor="tome-title" className="block text-sm font-medium text-tg-text-secondary mb-1">卷名 (Title)</label>
            <input
              id="tome-title"
              type="text"
              value={title}
              onChange={e => setTitle(e.target.value)}
              className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
              placeholder="输入卷宗标题..."
            />
          </div>
          <div>
            <label htmlFor="tome-category" className="block text-sm font-medium text-tg-text-secondary mb-1">所属书架 (Category)</label>
            <select
              id="tome-category"
              value={category}
              onChange={e => setCategory(e.target.value as KnowledgeTomeCategory)}
              className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
            >
              {(Object.entries(KNOWLEDGE_TOME_CATEGORIES_MAP) as [KnowledgeTomeCategory, string][]).map(([key, name]) => (
                <option key={key} value={key}>{name}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="tome-tags" className="block text-sm font-medium text-tg-text-secondary mb-1">关键词签 (Tags)</label>
            <input
              id="tome-tags"
              type="text"
              value={tagsString}
              onChange={e => setTagsString(e.target.value)}
              className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
              placeholder="输入关键词，以逗号或空格分隔..."
            />
          </div>
          <div>
            <label htmlFor="tome-content" className="block text-sm font-medium text-tg-text-secondary mb-1">正文 (Content)</label>
            <textarea
              id="tome-content"
              value={content}
              onChange={e => setContent(e.target.value)}
              className="w-full p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30 min-h-[200px] max-h-[40vh] resize-y"
              placeholder="输入卷宗内容 (纯文本)..."
              rows={10}
            />
          </div>
        </div>
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-tg-border-primary">
          <button
            onClick={onClose}
            className="py-2 px-4 rounded-md transition-colors text-sm bg-tg-bg-tertiary text-tg-text-secondary border border-tg-border-primary hover:bg-tg-bg-hover"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="py-2 px-4 rounded-md transition-colors font-semibold text-sm bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover"
          >
            {existingTome ? '保存更改' : '创建卷宗'}
          </button>
        </div>
      </div>
    </div>
  );
};

interface ViewTomeModalProps {
  isOpen: boolean;
  onClose: () => void;
  tome: KnowledgeTome | null;
}

const ViewTomeModal: React.FC<ViewTomeModalProps> = ({ isOpen, onClose, tome }) => {
  if (!isOpen || !tome) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-tg-bg-secondary p-6 rounded-lg shadow-xl w-full max-w-3xl border border-tg-border-primary max-h-[90vh] flex flex-col">
        <h3 className="text-2xl font-semibold mb-3 text-tg-accent-primary truncate" title={tome.title}>{tome.title}</h3>
        <div className="mb-4 text-sm text-tg-text-secondary border-b border-tg-border-primary pb-3">
          <p><strong>书架:</strong> {KNOWLEDGE_TOME_CATEGORIES_MAP[tome.category]}</p>
          {tome.tags.length > 0 && <p className="mt-1"><strong>关键词签:</strong> {tome.tags.join(', ')}</p>}
          <p className="mt-1"><strong>创建于:</strong> {new Date(tome.createdAt).toLocaleString()}</p>
          <p className="mt-1"><strong>最后修改:</strong> {new Date(tome.lastModifiedAt).toLocaleString()}</p>
        </div>
        <div className="overflow-y-auto flex-grow pr-2 mb-4">
          <pre className="whitespace-pre-wrap break-words text-tg-text-primary text-base leading-relaxed">{tome.content}</pre>
        </div>
        <div className="flex justify-end mt-2">
          <button
            onClick={onClose}
            className="py-2 px-4 rounded-md transition-colors text-sm bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};


export const GeneralKnowledgeBaseView: React.FC<GeneralKnowledgeBaseViewProps> = ({
  globalKnowledgeTomes,
  onAddTome,
  onUpdateTome,
  onDeleteTome,
}) => {
  const [isCreateEditModalOpen, setIsCreateEditModalOpen] = useState(false);
  const [editingTome, setEditingTome] = useState<KnowledgeTome | null>(null);
  const [viewingTome, setViewingTome] = useState<KnowledgeTome | null>(null);
  const [filterCategory, setFilterCategory] = useState<KnowledgeTomeCategory | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [importStatus, setImportStatus] = useState<string | null>(null);


  const handleOpenCreateModal = () => {
    setEditingTome(null);
    setIsCreateEditModalOpen(true);
  };

  const handleOpenEditModal = (tome: KnowledgeTome) => {
    setEditingTome(tome);
    setIsCreateEditModalOpen(true);
  };

  const handleSaveTome = async (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>, editingId?: string) => {
    if (editingId && editingTome) { 
      onUpdateTome({ ...tomeData, id: editingId, createdAt: editingTome.createdAt, lastModifiedAt: new Date().toISOString() });
    } else {
      await onAddTome(tomeData);
    }
  };

  const handleDeleteTome = (tomeId: string) => {
    if (window.confirm("确定要永久删除此卷宗吗？此操作无法撤销。")) {
      onDeleteTome(tomeId);
    }
  };

  const handleImportFiles = useCallback(async () => {
    setIsImporting(true);
    setImportStatus("正在选择文件...");
    try {
      const filePaths = await window.api.fs.openMultipleFilesDialog(['txt', 'md']);
      if (filePaths && filePaths.length > 0) {
        setImportStatus(`正在导入 ${filePaths.length} 个文件...`);
        let successCount = 0;
        let errorCount = 0;
        for (const filePath of filePaths) {
          try {
            const content = await window.api.fs.readFileContent(filePath);
            if (typeof content === 'string') {
              const fileName = filePath.split(/[\\/]/).pop()?.replace(/\.(txt|md)$/i, '') || '导入的文件';
              await onAddTome({
                title: fileName,
                content: content,
                category: 'miscellaneous', 
                tags: ['imported'], 
              });
              successCount++;
            } else {
              console.error(`Failed to read content of ${filePath}:`, content.error);
              errorCount++;
            }
            setImportStatus(`已导入 ${successCount}/${filePaths.length}，失败 ${errorCount}...`);
          } catch (fileReadError) {
            console.error(`Error processing file ${filePath}:`, fileReadError);
            errorCount++;
             setImportStatus(`已导入 ${successCount}/${filePaths.length}，失败 ${errorCount}...`);
          }
        }
        setImportStatus(`导入完成：成功 ${successCount} 个，失败 ${errorCount} 个。`);
      } else {
        setImportStatus(null); 
      }
    } catch (error) {
      console.error("Error during file import process:", error);
      setImportStatus("文件导入过程中发生错误。");
    } finally {
      setIsImporting(false);
      setTimeout(() => setImportStatus(null), 5000); 
    }
  }, [onAddTome]);

  const filteredTomes = useMemo(() => {
    return globalKnowledgeTomes
      .filter(tome => filterCategory === 'all' || tome.category === filterCategory)
      .filter(tome => {
        if (!searchTerm.trim()) return true;
        const lowerSearchTerm = searchTerm.toLowerCase();
        return (
          tome.title.toLowerCase().includes(lowerSearchTerm) ||
          tome.content.toLowerCase().includes(lowerSearchTerm) || 
          tome.tags.some(tag => tag.toLowerCase().includes(lowerSearchTerm))
        );
      })
      .sort((a,b) => new Date(b.lastModifiedAt).getTime() - new Date(a.lastModifiedAt).getTime());
  }, [globalKnowledgeTomes, filterCategory, searchTerm]);

  return (
    <div className="p-4 md:p-6 h-full flex flex-col">
      <div className="mb-4 flex flex-col sm:flex-row justify-between items-center gap-3">
        <h3 className="text-xl font-semibold text-tg-text-primary flex items-center">
          <Icon name="BookOpen" className="w-6 h-6 mr-2 text-tg-accent-primary" />
          全局·通用智库卷宗
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={handleImportFiles}
            disabled={isImporting}
            className="px-4 py-2 rounded-md text-sm font-medium flex items-center bg-tg-accent-secondary text-white hover:brightness-110 transition-colors shadow-sm disabled:opacity-60"
            title="导入 .txt 或 .md 文件作为新卷宗"
          >
            <Icon name={isImporting ? "Loader" : "UploadCloud"} className={`w-5 h-5 mr-2 ${isImporting ? 'animate-spin' : ''}`} />
            {isImporting ? "导入中..." : "导入文件"}
          </button>
          <button
            onClick={handleOpenCreateModal}
            disabled={isImporting}
            className="px-4 py-2 rounded-md text-sm font-medium flex items-center bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover transition-colors shadow-sm disabled:opacity-60"
          >
            <Icon name="PlusCircle" className="w-5 h-5 mr-2" /> 创建新卷宗
          </button>
        </div>
      </div>
      {importStatus && (
        <div className={`mb-3 p-2 text-xs rounded-md ${importStatus.includes("失败") || importStatus.includes("错误") ? 'bg-red-700/30 text-red-300' : 'bg-blue-700/30 text-blue-300'}`}>
          {importStatus}
        </div>
      )}

      <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4 p-3 bg-tg-bg-tertiary rounded-md border border-tg-border-primary">
        <div>
          <label htmlFor="category-filter" className="block text-xs font-medium text-tg-text-secondary mb-1">筛选书架:</label>
          <select
            id="category-filter"
            value={filterCategory}
            onChange={e => setFilterCategory(e.target.value as KnowledgeTomeCategory | 'all')}
            className="w-full p-2 text-sm bg-tg-bg-primary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
          >
            <option value="all">所有书架</option>
            {(Object.entries(KNOWLEDGE_TOME_CATEGORIES_MAP) as [KnowledgeTomeCategory, string][]).map(([key, name]) => (
              <option key={key} value={key}>{name}</option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="search-filter" className="block text-xs font-medium text-tg-text-secondary mb-1">搜索卷宗 (标题/标签/内容):</label>
          <input
            id="search-filter"
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="输入搜索词..."
            className="w-full p-2 text-sm bg-tg-bg-primary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
          />
        </div>
      </div>

      {filteredTomes.length === 0 ? (
        <div className="text-center py-10 text-tg-text-placeholder">
          <p>此书架暂无卷宗，或无匹配搜索结果。</p>
          <p>尝试调整筛选条件或创建新的卷宗。</p>
        </div>
      ) : (
        <div className="overflow-auto flex-grow pr-1 space-y-3">
          {filteredTomes.map(tome => (
            <div key={tome.id} className="bg-tg-bg-tertiary p-4 rounded-lg shadow-md border border-tg-border-primary hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h4 
                    className="text-lg font-semibold text-tg-accent-primary hover:text-tg-accent-primary-hover cursor-pointer truncate" 
                    onClick={() => setViewingTome(tome)}
                    title={`查看: ${tome.title}`}
                >
                    {tome.title}
                </h4>
                <span className="text-xs text-tg-text-secondary whitespace-nowrap bg-tg-bg-primary px-2 py-1 rounded-full border border-tg-border-primary">
                  {KNOWLEDGE_TOME_CATEGORIES_MAP[tome.category]}
                </span>
              </div>
              {tome.tags.length > 0 && (
                <div className="mb-2 flex flex-wrap gap-1.5 items-center">
                  <Icon name="Tag" className="w-3.5 h-3.5 text-tg-text-placeholder mr-1" />
                  {tome.tags.slice(0, 5).map(tag => (
                    <span key={tag} className="text-xs bg-tg-bg-primary text-tg-text-secondary px-2 py-0.5 rounded-full border border-tg-border-primary">{tag}</span>
                  ))}
                  {tome.tags.length > 5 && <span className="text-xs text-tg-text-placeholder">...</span>}
                </div>
              )}
              <p className="text-xs text-tg-text-placeholder mb-3">
                最后修改: {new Date(tome.lastModifiedAt).toLocaleDateString()}
              </p>
              <div className="flex space-x-2">
                <button onClick={() => setViewingTome(tome)} className="p-1.5 text-sm text-tg-accent-secondary hover:text-tg-accent-primary-hover transition-colors flex items-center" title="查看详情">
                  <Icon name="Eye" className="w-4 h-4 mr-1" /> 查看
                </button>
                <button onClick={() => handleOpenEditModal(tome)} className="p-1.5 text-sm text-tg-warning hover:brightness-125 transition-colors flex items-center" title="编辑卷宗">
                  <Icon name="Pencil" className="w-4 h-4 mr-1" /> 编辑
                </button>
                <button onClick={() => handleDeleteTome(tome.id)} className="p-1.5 text-sm text-tg-danger hover:brightness-125 transition-colors flex items-center" title="删除卷宗">
                  <Icon name="Trash2" className="w-4 h-4 mr-1" /> 删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      <CreateEditTomeModal
        isOpen={isCreateEditModalOpen}
        onClose={() => setIsCreateEditModalOpen(false)}
        onSave={handleSaveTome}
        existingTome={editingTome}
      />
      <ViewTomeModal
        isOpen={!!viewingTome}
        onClose={() => setViewingTome(null)}
        tome={viewingTome}
      />
    </div>
  );
};
