// electron/services/taskDbService.js
console.log('TASK_DB_SERVICE_JS: File execution started.');

import { db, crypto } from './databaseCore.js';

export function getTaskById(taskId) {
    if (!db) { console.error(`TASK_DB_ERROR: getTaskById(${taskId}) - db not available.`); return null; }
    try {
        const task = db.prepare('SELECT * FROM tasks WHERE task_id = ?').get(taskId);
        if (task) {
            task.resource_links = getResourceLinksForTask(taskId); // Populate resource links
            console.log(`TASK_DB: Retrieved task by ID ${taskId}.`);
            return task;
        }
        console.log(`TASK_DB: Task with ID ${taskId} not found.`);
        return null;
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error getting task by ID ${taskId}:`, error);
        return null;
    }
}

export function getTasksByProjectId(projectId, filters = {}, sortOptions = { field: 'created_at', order: 'DESC' }) {
    if (!db) { console.error(`TASK_DB_ERROR: getTasksByProjectId(${projectId}) - db not available.`); return []; }
    try {
        let query = 'SELECT * FROM tasks WHERE project_id = ?';
        const params = [projectId];

        if (filters.status) {
            query += ' AND status = ?';
            params.push(filters.status);
        }
        if (filters.priority !== undefined) {
            query += ' AND priority = ?';
            params.push(filters.priority);
        }
        // Add more filters as needed

        const validSortFields = ['created_at', 'updated_at', 'due_date', 'priority', 'title', 'status'];
        const sortField = validSortFields.includes(sortOptions.field) ? sortOptions.field : 'created_at';
        const sortOrder = sortOptions.order === 'ASC' ? 'ASC' : 'DESC';
        query += ` ORDER BY ${sortField} ${sortOrder}`;
        
        const tasks = db.prepare(query).all(...params);
        tasks.forEach(task => {
            task.resource_links = getResourceLinksForTask(task.task_id); // Populate resource links
        });
        console.log(`TASK_DB: Retrieved ${tasks.length} tasks for project ${projectId}.`);
        return tasks;
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error getting tasks for project ${projectId}:`, error);
        return [];
    }
}

export function getTasksByStatus(projectId, statusArray) {
    if (!db) { console.error(`TASK_DB_ERROR: getTasksByStatus(${projectId}, ${statusArray}) - db not available.`); return []; }
    if (!Array.isArray(statusArray) || statusArray.length === 0) {
        console.warn(`TASK_DB_WARN: getTasksByStatus called with empty or invalid statusArray for project ${projectId}.`);
        return [];
    }
    try {
        const placeholders = statusArray.map(() => '?').join(',');
        const query = `SELECT * FROM tasks WHERE project_id = ? AND status IN (${placeholders}) ORDER BY priority ASC, created_at DESC`;
        const params = [projectId, ...statusArray];
        
        const tasks = db.prepare(query).all(...params);
        tasks.forEach(task => {
            task.resource_links = getResourceLinksForTask(task.task_id);
        });
        console.log(`TASK_DB: Retrieved ${tasks.length} tasks for project ${projectId} with statuses [${statusArray.join(', ')}].`);
        return tasks;
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error getting tasks by status for project ${projectId}:`, error);
        return [];
    }
}

export function addTask(taskData) {
    if (!db) { console.error("TASK_DB_ERROR: addTask - db not available."); return null; }
    const now = new Date().toISOString();
    const newTask = {
        task_id: crypto.randomUUID(),
        project_id: taskData.project_id,
        title: taskData.title,
        description: taskData.description || null,
        status: taskData.status || 'todo',
        priority: taskData.priority === undefined ? 1 : taskData.priority,
        assignee_id: taskData.assignee_id || null,
        created_at: now,
        updated_at: now,
        due_date: taskData.due_date || null,
        parent_task_id: taskData.parent_task_id || null,
    };
    const stmt = db.prepare('INSERT INTO tasks (task_id, project_id, title, description, status, priority, assignee_id, created_at, updated_at, due_date, parent_task_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
    try {
        stmt.run(newTask.task_id, newTask.project_id, newTask.title, newTask.description, newTask.status, newTask.priority, newTask.assignee_id, newTask.created_at, newTask.updated_at, newTask.due_date, newTask.parent_task_id);
        console.log(`TASK_DB: Added task ${newTask.task_id} for project ${newTask.project_id}.`);
        return { ...newTask, resource_links: [] }; // Return with empty links, can be populated if needed
    } catch (error) {
        console.error('TASK_DB_ERROR: Error adding task:', error);
        return null;
    }
}

export function updateTask(taskId, updates) {
    if (!db) { console.error(`TASK_DB_ERROR: updateTask(${taskId}) - db not available.`); return null; }
    const allowedFields = ['title', 'description', 'status', 'priority', 'assignee_id', 'due_date', 'parent_task_id'];
    const fieldsToUpdate = [];
    const valuesToUpdate = [];

    for (const field of allowedFields) {
        if (updates[field] !== undefined) {
            fieldsToUpdate.push(`${field} = ?`);
            valuesToUpdate.push(updates[field]);
        }
    }
    if (fieldsToUpdate.length === 0) {
        console.log(`TASK_DB: No valid fields to update for task ${taskId}.`);
        return getTaskById(taskId); // Return current task if no updates
    }

    fieldsToUpdate.push('updated_at = ?');
    valuesToUpdate.push(new Date().toISOString());
    valuesToUpdate.push(taskId);

    const stmt = db.prepare(`UPDATE tasks SET ${fieldsToUpdate.join(', ')} WHERE task_id = ?`);
    try {
        const info = stmt.run(...valuesToUpdate);
        if (info.changes > 0) {
            console.log(`TASK_DB: Updated task ${taskId}.`);
            return getTaskById(taskId); // Fetch and return the updated task
        }
        console.warn(`TASK_DB_WARN: Task ${taskId} not found for update or no changes made.`);
        return null;
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error updating task ${taskId}:`, error);
        return null;
    }
}

export function deleteTask(taskId) {
    if (!db) { console.error(`TASK_DB_ERROR: deleteTask(${taskId}) - db not available.`); return { success: false, error: "Database not available." }; }
    const transaction = db.transaction(() => {
        // Delete associated resource links first due to ON DELETE CASCADE on task_resource_links.task_id
        // Although CASCADE should handle it, explicit deletion can be clearer or used if CASCADE wasn't set.
        // db.prepare('DELETE FROM task_resource_links WHERE task_id = ?').run(taskId);
        
        const info = db.prepare('DELETE FROM tasks WHERE task_id = ?').run(taskId);
        if (info.changes > 0) {
            console.log(`TASK_DB: Deleted task ${taskId} and its resource links (via CASCADE).`);
            return { success: true };
        }
        console.warn(`TASK_DB_WARN: Task ${taskId} not found for deletion.`);
        return { success: false, error: "Task not found." };
    });
    try {
        return transaction();
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error deleting task ${taskId}:`, error);
        return { success: false, error: error.message };
    }
}

// --- TaskResourceLinks Management ---

export function addResourceLinkToTask(taskId, resourceData) {
    if (!db) { console.error("TASK_DB_ERROR: addResourceLinkToTask - db not available."); return null; }
    const newLink = {
        link_id: crypto.randomUUID(),
        task_id: taskId,
        resource_type: resourceData.resource_type,
        resource_identifier: resourceData.resource_identifier,
        resource_name: resourceData.resource_name, // Added
        description: resourceData.description, // Added
    };
    const stmt = db.prepare('INSERT INTO task_resource_links (link_id, task_id, resource_type, resource_identifier, resource_name, description) VALUES (?, ?, ?, ?, ?, ?)');
    try {
        stmt.run(newLink.link_id, newLink.task_id, newLink.resource_type, newLink.resource_identifier, newLink.resource_name, newLink.description);
        console.log(`TASK_DB: Added resource link ${newLink.link_id} to task ${taskId}.`);
        return newLink;
    } catch (error) {
        console.error('TASK_DB_ERROR: Error adding resource link to task:', error);
        return null;
    }
}

export function getResourceLinksForTask(taskId) {
    if (!db) { console.error(`TASK_DB_ERROR: getResourceLinksForTask(${taskId}) - db not available.`); return []; }
    try {
        const links = db.prepare('SELECT * FROM task_resource_links WHERE task_id = ?').all(taskId);
        // console.log(`TASK_DB: Retrieved ${links.length} resource links for task ${taskId}.`); // Can be too verbose
        return links;
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error getting resource links for task ${taskId}:`, error);
        return [];
    }
}

export function removeResourceLinkFromTask(linkId) {
    if (!db) { console.error(`TASK_DB_ERROR: removeResourceLinkFromTask(${linkId}) - db not available.`); return { success: false, error: "Database not available." }; }
    const stmt = db.prepare('DELETE FROM task_resource_links WHERE link_id = ?');
    try {
        const info = stmt.run(linkId);
        if (info.changes > 0) {
            console.log(`TASK_DB: Removed resource link ${linkId}.`);
            return { success: true };
        }
        console.warn(`TASK_DB_WARN: Resource link ${linkId} not found for deletion.`);
        return { success: false, error: "Resource link not found." };
    } catch (error) {
        console.error(`TASK_DB_ERROR: Error removing resource link ${linkId}:`, error);
        return { success: false, error: error.message };
    }
}

console.log('TASK_DB_SERVICE_JS: File execution finished. Exports configured.');