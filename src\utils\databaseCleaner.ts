// src/utils/databaseCleaner.ts
// 数据库清理工具 - 删除重复的资产

export class DatabaseCleaner {
  /**
   * 清理重复的 CMS 资产
   */
  static async cleanDuplicateAssets(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      console.log('🧹 开始清理重复资产...');

      const results = {
        props: { before: 0, after: 0, removed: 0 },
        costumes: { before: 0, after: 0, removed: 0 },
        poses: { before: 0, after: 0, removed: 0 }
      };

      // 清理道具
      console.log('🧹 清理重复道具...');
      const propsResult = await this.cleanDuplicateItemsByType('props');
      results.props = propsResult;

      // 清理服装
      console.log('🧹 清理重复服装...');
      const costumesResult = await this.cleanDuplicateItemsByType('costumes');
      results.costumes = costumesResult;

      // 清理姿势
      console.log('🧹 清理重复姿势...');
      const posesResult = await this.cleanDuplicateItemsByType('poses');
      results.poses = posesResult;

      const totalRemoved = results.props.removed + results.costumes.removed + results.poses.removed;

      console.log('🧹 清理完成！', results);

      return {
        success: true,
        message: `清理完成！删除了 ${totalRemoved} 个重复资产`,
        details: results
      };
    } catch (error) {
      console.error('❌ 清理失败:', error);
      return {
        success: false,
        message: `清理失败: ${error.message}`
      };
    }
  }

  /**
   * 清理所有旧版本资产（保留最新的道具名称）
   */
  static async cleanAllOldAssets(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      console.log('🧹 开始清理所有旧版本资产...');

      // 定义最新的道具名称
      const latestPropNames = [
        // 雾岛玲奈道具
        '情趣内衣试穿箱',
        '身体测量绳',
        '挑逗羽毛棒',
        '催情玫瑰',
        '遥控震动内裤',
        '乳夹震动器',
        '调教项圈',
        '肛塞尾巴',
        // 林珞自然骚魂版道具
        '震动按摩棒',
        'SM束缚绳套',
        '情趣跳蛋',
        '双头情趣棒',
        '自动抽插机',
        '电击调教器',
        '强制高潮腰带',
        '感官剥夺套装'
      ];

      const results = {
        props: { before: 0, after: 0, removed: 0 },
        costumes: { before: 0, after: 0, removed: 0 },
        poses: { before: 0, after: 0, removed: 0 }
      };

      // 清理道具 - 只保留最新名称的道具
      const allProps = await window.api.cms.getCMSItems('props');
      results.props.before = allProps.length;

      let removedProps = 0;
      for (const prop of allProps) {
        if (!latestPropNames.includes(prop.name)) {
          console.log(`🗑️ 删除旧道具: ${prop.name} (${prop.id})`);
          try {
            const deleteResult = await window.api.cms.deleteCMSItem('props', prop.id);
            if (deleteResult.success) {
              removedProps++;
            }
          } catch (error) {
            console.error(`❌ 删除失败:`, error);
          }
        }
      }

      // 然后清理剩余的重复项
      const remainingProps = await window.api.cms.getCMSItems('props');
      const duplicateCleanResult = await this.cleanDuplicateItemsByType('props');

      results.props.removed = removedProps + duplicateCleanResult.removed;
      results.props.after = duplicateCleanResult.after;

      // 清理服装和姿势的重复项
      results.costumes = await this.cleanDuplicateItemsByType('costumes');
      results.poses = await this.cleanDuplicateItemsByType('poses');

      const totalRemoved = results.props.removed + results.costumes.removed + results.poses.removed;

      console.log('🧹 清理完成！', results);

      return {
        success: true,
        message: `清理完成！删除了 ${totalRemoved} 个旧版本和重复资产`,
        details: results
      };
    } catch (error) {
      console.error('❌ 清理失败:', error);
      return {
        success: false,
        message: `清理失败: ${error.message}`
      };
    }
  }

  /**
   * 按类型清理重复资产
   */
  private static async cleanDuplicateItemsByType(type: 'props' | 'costumes' | 'poses'): Promise<{
    before: number;
    after: number;
    removed: number;
  }> {
    // 获取所有资产
    const allItems = await window.api.cms.getCMSItems(type);
    const beforeCount = allItems.length;

    console.log(`📊 ${type} 清理前: ${beforeCount} 个`);
    console.log(`📋 所有 ${type} 项目:`, allItems.map(item => ({ id: item.id, name: item.name, createdAt: item.createdAt })));

    // 按 name 分组，找出重复项
    const itemGroups = new Map<string, any[]>();

    allItems.forEach(item => {
      const key = item.name;
      if (!itemGroups.has(key)) {
        itemGroups.set(key, []);
      }
      itemGroups.get(key)!.push(item);
    });

    console.log(`🔍 分组结果:`, Array.from(itemGroups.entries()).map(([name, items]) => ({ name, count: items.length })));

    let removedCount = 0;

    // 对于每个重复组，保留最新的一个，删除其他的
    for (const [name, items] of itemGroups) {
      if (items.length > 1) {
        console.log(`🔍 发现重复的 ${type}: "${name}" (${items.length} 个)`);
        console.log(`📝 重复项详情:`, items.map(item => ({ id: item.id, createdAt: item.createdAt })));

        // 按创建时间排序，保留最新的
        items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        const itemsToKeep = items.slice(0, 1); // 保留最新的 1 个
        const itemsToRemove = items.slice(1); // 删除其余的

        console.log(`📌 保留: ${itemsToKeep[0].id} (${itemsToKeep[0].createdAt})`);
        console.log(`🗑️ 将删除: ${itemsToRemove.length} 个项目`);

        // 删除重复项
        for (const item of itemsToRemove) {
          try {
            console.log(`🗑️ 正在删除: ${item.id} "${item.name}" (${item.createdAt})`);
            const deleteResult = await window.api.cms.deleteCMSItem(type, item.id);
            console.log(`✅ 删除结果:`, deleteResult);

            if (deleteResult.success) {
              removedCount++;
            } else {
              console.error(`❌ 删除失败 ${item.id}: ${deleteResult.error}`);
            }
          } catch (error) {
            console.error(`❌ 删除异常 ${item.id}:`, error);
          }
        }
      } else {
        console.log(`✅ ${type} "${name}" 无重复`);
      }
    }

    // 获取清理后的数量
    const remainingItems = await window.api.cms.getCMSItems(type);
    const afterCount = remainingItems.length;

    console.log(`📊 ${type} 清理后: ${afterCount} 个 (删除了 ${removedCount} 个)`);
    console.log(`📋 剩余 ${type} 项目:`, remainingItems.map(item => ({ id: item.id, name: item.name, createdAt: item.createdAt })));

    return {
      before: beforeCount,
      after: afterCount,
      removed: removedCount
    };
  }

  /**
   * 获取重复资产统计
   */
  static async getDuplicateStats(): Promise<{
    props: { total: number; duplicates: number };
    costumes: { total: number; duplicates: number };
    poses: { total: number; duplicates: number };
  }> {
    const stats = {
      props: { total: 0, duplicates: 0 },
      costumes: { total: 0, duplicates: 0 },
      poses: { total: 0, duplicates: 0 }
    };

    for (const type of ['props', 'costumes', 'poses'] as const) {
      const items = await window.api.cms.getCMSItems(type);
      stats[type].total = items.length;
      
      // 计算重复项
      const nameGroups = new Map<string, number>();
      items.forEach(item => {
        const count = nameGroups.get(item.name) || 0;
        nameGroups.set(item.name, count + 1);
      });
      
      let duplicateCount = 0;
      nameGroups.forEach(count => {
        if (count > 1) {
          duplicateCount += count - 1; // 每组保留1个，其余为重复
        }
      });
      
      stats[type].duplicates = duplicateCount;
    }

    return stats;
  }
}
