
// src/components/chat/SandboxChat.tsx
import React, { useState, useEffect, useRef, useCallback, useImperativeHandle, forwardRef, useLayoutEffect, memo, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import type {
    ChatMessage,
    GlobalQuickCommandItem,
    ChatDiscussionAreaRef,
    AIResponseWithStatus,
    DevelopmentTaskCreationPayload,
    DevelopmentTask,
    Character,
    RoundtableParticipant,
    ToDoItem,
    AiCallContext,
    WisdomPouchType,
    ProjectWorkspacePageOutletContext,
    AppSettings,
    RetrievedChunk,
    Post, 
    Assignment 
} from '@/types';
import { Icon } from '@/components/common/Icon';
import { SmartIcon, VisualHeading, VisualContainer } from '@/components/common/VisualUtils';
import { themeDefinitions, parseMessageTextForTheme, summaryBlockRegex } from '@/chatConstants';
import { ChatDiscussionMessageItem } from '@/components/chat/ChatDiscussionMessageItem';
import { SearchResultsDisplay } from '@/components/SearchResultsDisplay';
import { GenericModal } from '@/components/GenericModal';
import { CreateTaskFromChatMessageModal } from '@/components/chat/CreateTaskFromChatMessageModal';
import { ParticipantsPanel } from '@/components/roundtable/ParticipantsPanel';
import { RoundtableDraftPanel } from '@/components/roundtable/RoundtableDraftPanel';
import { SharedDraftCanvas } from '@/components/common/SharedDraftCanvas';


const escapeHTML = (str: string) => {
  const p = document.createElement("p");
  p.appendChild(document.createTextNode(str));
  return p.innerHTML;
};

const INITIAL_MESSAGES_LOAD_COUNT = 50;
const OLDER_MESSAGES_LOAD_COUNT = 30;

const getCleanText = (html: string): string => {
    if (typeof DOMParser === 'undefined') {
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return (doc.body.textContent || "").trim();
    } catch (e) {
        console.error("Error in getCleanText:", e);
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
};

const ResizablePanel: React.FC<{ initialWidth?: string; minWidth?: string; maxWidth?: string; children: React.ReactNode; className?: string; onResize?: (newWidth: number) => void; isVisible?: boolean }> = ({ initialWidth = '33.33%', minWidth = '200px', maxWidth = '50%', children, className, onResize, isVisible = true }) => {
    const panelRef = useRef<HTMLDivElement>(null);
    if (!isVisible) return null; 
    return <div ref={panelRef} className={`flex-shrink-0 overflow-hidden ${className}`} style={{ flexBasis: initialWidth, minWidth, maxWidth }}>{children}</div>;
};


const SandboxChatWithRef: React.ForwardRefRenderFunction<ChatDiscussionAreaRef> = (
  props, 
  ref
): React.ReactElement | null => {
  const outletContext = useOutletContext<ProjectWorkspacePageOutletContext | null>();
  
  if (!outletContext) {
    console.error("SandboxChat Critical Error: Outlet context is not available.");
    return <div className="p-4 text-red-500">错误：项目上下文丢失，聊天区域无法渲染。</div>;
  }

  const {
    project, 
    settings: appSettings, 
    globalQuickCommands, 
    isAIServiceReady, 
    aiTaskStatus,
    onAddCoreMemory, 
    handleDecomposeRequirement, 
    handleConvertToTask, 
    onSaveNewChatMessage: onSaveNewChatMessageForProject, 
    onUpdateExistingChatMessage: onUpdateExistingChatMessageCallback, 
    onMessagesReplacedBySummary, 
    chatAreaRef: contextChatAreaRef, 
    onOpenWisdomPouch, 
    allCharacters: allAvailableCharactersFromContext, 
    allPosts: allPostsFromContext, 
    allAssignments: allAssignmentsFromContext,
  } = outletContext;

  const [allAvailableCharacters, setAllAvailableCharacters] = useState<Character[]>(allAvailableCharactersFromContext || []);
  const [allPosts, setAllPosts] = useState<Post[]>(allPostsFromContext || []);
  const [allAssignments, setAllAssignments] = useState<Assignment[]>(allAssignmentsFromContext || []);

  useEffect(() => {
    // Always use three-column layout. Filter characters.
    const personaCharacterIds = ['linluo', 'xiaolan', 'yujing'];
    setAllAvailableCharacters((allAvailableCharactersFromContext || []).filter(char => personaCharacterIds.includes(char.id)));
  }, [allAvailableCharactersFromContext]);

  useEffect(() => {
    setAllPosts(allPostsFromContext || []);
  }, [allPostsFromContext]);
  
  useEffect(() => {
    setAllAssignments(allAssignmentsFromContext || []);
  }, [allAssignmentsFromContext]);


  if (!appSettings || !project) {
    return <div className="p-4 text-red-500">错误：应用配置或项目数据丢失，聊天区域无法渲染。</div>;
  }
  
  const projectId = project.id;
  const projectName = project.name;
  const initialMessagesFromContext = project.discussionMessages || [];

  const apiKeyAvailable = !!appSettings.apiKey && appSettings.apiKey.trim() !== "";
  const [inputText, setInputText] = useState('');
  const inputTextRef = useRef<HTMLTextAreaElement>(null);
  const [richEditingId, setRichEditingId] = useState<string | null>(null);
  const [displayedMessages, setDisplayedMessages] = useState<ChatMessage[]>(initialMessagesFromContext || []);
  const [isLoadingInternalInitialMessages, setIsLoadingInternalInitialMessages] = useState(!!projectId);
  const [isLoadingOlder, setIsLoadingOlder] = useState(false);
  const [canLoadOlderInternal, setCanLoadOlderInternal] = useState(!!projectId);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevScrollHeightRef = useRef<number | null>(null);
  const isUserAtBottomRef = useRef(true);
  const [isRagEnabled, setIsRagEnabled] = useState(true);
  const [ragSearchQuery, setRagSearchQuery] = useState('');
  const [ragSearchResults, setRagSearchResults] = useState<RetrievedChunk[] | null>(null);
  const [isRagSearchLoading, setIsRagSearchLoading] = useState(false);
  const [ragSearchError, setRagSearchError] = useState<string | null>(null);
  const [showRagResultsModal, setShowRagResultsModal] = useState(false);
  const [showMoreActions, setShowMoreActions] = useState(false);
  const moreActionsButtonRef = useRef<HTMLButtonElement>(null);
  const moreActionsDropdownRef = useRef<HTMLDivElement>(null);
  const [showSaveChatModal, setShowSaveChatModal] = useState(false);
  const [saveChatFormat, setSaveChatFormat] = useState<'md' | 'html'>('md');
  const [showGlobalCommandsModal, setShowGlobalCommandsModal] = useState(false);
  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [taskModalInitialData, setTaskModalInitialData] = useState<{ title: string; description?: string, originalMessageId?: string }>({ title: '' });
  const [isAttachmentMenuOpen, setIsAttachmentMenuOpen] = useState(false);
  const attachmentButtonRef = useRef<HTMLButtonElement>(null);
  const [isWisdomPouchMenuOpen, setIsWisdomPouchMenuOpen] = useState(false);
  const wisdomPouchButtonRef = useRef<HTMLButtonElement>(null);
  const [roundtableParticipants, setRoundtableParticipants] = useState<RoundtableParticipant[]>([]);
  const [roundtableDiscussionRounds, setRoundtableDiscussionRounds] = useState<number>(3);
  const [isMeetingInProgress, setIsMeetingInProgress] = useState(false);
  const [currentRoundtableTurn, setCurrentRoundtableTurn] = useState(0);
  const [roundtableChatHistory, setRoundtableChatHistory] = useState<ChatMessage[]>([]); 
  const [roundtableToDoItems, setRoundtableToDoItems] = useState<ToDoItem[]>([]);
  const [roundtableSharedDraft, setRoundtableSharedDraft] = useState<string>('');
  const [currentMeetingId, setCurrentMeetingId] = useState<string | null>(null);
  const [currentMeetingTopic, setCurrentMeetingTopic] = useState<string | null>(null);
  
  const isAiProcessing = aiTaskStatus !== 'idle';
  const activeRoundtableParticipantIds = useMemo(() => roundtableParticipants.filter(p => p.isActiveInMeeting).map(p => p.id), [roundtableParticipants]);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const leftPanelWidth = '20%'; 
  const rightPanelWidth = '25%'; 
  const currentUserName = appSettings.user_avatar_path ? "我" : "用户";

  useEffect(() => {
    // Always use three-column layout. Filter characters.
    const personaCharacterIds = ['linluo', 'xiaolan', 'yujing'];
    const filteredChars = (allAvailableCharactersFromContext || []).filter(char => personaCharacterIds.includes(char.id));

    if (filteredChars.length > 0) {
        const linLuoDefault = filteredChars.find(c => c.id === 'linluo');
        const participants = filteredChars.map(char => ({ ...char, isActiveInMeeting: char.id === (linLuoDefault?.id || filteredChars[0]?.id) }));
        if (participants.length > 0 && !participants.some(p => p.isActiveInMeeting)) participants[0].isActiveInMeeting = true;
        setRoundtableParticipants(participants);
    } else {
      setRoundtableParticipants([]);
    }
  }, [allAvailableCharactersFromContext]);

  const insertTextAtFocus = useCallback((textToInsert: string) => {
    if (inputTextRef.current) {
      const { selectionStart, selectionEnd, value } = inputTextRef.current;
      setInputText(value.substring(0, selectionStart) + textToInsert + value.substring(selectionEnd));
      setTimeout(() => { if (inputTextRef.current) { inputTextRef.current.selectionStart = inputTextRef.current.selectionEnd = selectionStart + textToInsert.length; inputTextRef.current.focus(); }}, 0);
    } else setInputText(prev => prev + textToInsert);
  }, []);

  const internalChatAreaRef = contextChatAreaRef || ref; 
  useImperativeHandle(internalChatAreaRef, () => ({ insertTextAtFocus, scrollToBottom: (behavior: ScrollBehavior | undefined = "smooth") => messagesEndRef.current?.scrollIntoView({ behavior }), focusTextarea: () => inputTextRef.current?.focus() }), [insertTextAtFocus]);
  const scrollToBottom = useCallback((behavior: ScrollBehavior = "smooth") => { messagesEndRef.current?.scrollIntoView({ behavior }); }, []);
  
  const handleSaveMessageForDisplayAndBackend = useCallback(async (message: ChatMessage) => {
    setDisplayedMessages(prev => { const newMessages = [...prev, message]; return newMessages; }); 
    try {
      if (projectId && typeof onSaveNewChatMessageForProject === 'function') {
        await onSaveNewChatMessageForProject(projectId, message);
      } else { console.error("SandboxChat Error: No suitable save message callback found or projectId missing for saving message."); const systemErrorMsg: ChatMessage = { id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `消息保存失败：回调或项目ID丢失 (消息ID: ${message.id})。`, timestamp: new Date().toISOString(), isError: true, projectId }; setDisplayedMessages(prev => [...prev, systemErrorMsg]); }
    } catch (error: any) {  console.error("SandboxChat Error: Failed to save message via onSaveNewChatMessageForProject callback:", error); const systemErrorMsg: ChatMessage = { id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `消息保存到数据库时发生错误 (消息ID: ${message.id})。详情请查看控制台: ${error.message}`, timestamp: new Date().toISOString(), isError: true, projectId }; setDisplayedMessages(prev => [...prev, systemErrorMsg]); }
  }, [onSaveNewChatMessageForProject, projectId]);

  useEffect(() => {
    if (isMeetingInProgress && window.api?.ipc?.onRoundtableMessage) {
      const handleNewRoundtableMsg = (message: ChatMessage) => { handleSaveMessageForDisplayAndBackend(message); setRoundtableChatHistory(prev => [...prev, message]); scrollToBottom("smooth"); };
      const unsubscribe = window.api.ipc.onRoundtableMessage(handleNewRoundtableMsg);
      return () => unsubscribe();
    }
  }, [isMeetingInProgress, scrollToBottom, handleSaveMessageForDisplayAndBackend]);

  useEffect(() => { setRoundtableSharedDraft(roundtableSharedDraft); }, [roundtableSharedDraft]);
  useEffect(() => { setRoundtableToDoItems(roundtableToDoItems); }, [roundtableToDoItems]);

  const loadInternalInitialMessages = useCallback(async () => {
    if (!projectId) { setIsLoadingInternalInitialMessages(false); setDisplayedMessages([]); setCanLoadOlderInternal(false); return; }
    setIsLoadingInternalInitialMessages(true); setCanLoadOlderInternal(true);
    try {
      if (typeof window.api?.database?.getInitialChatMessages !== 'function') { setDisplayedMessages([]); setCanLoadOlderInternal(false); setIsLoadingInternalInitialMessages(false); return; }
      const initialMsgs = await window.api.database.getInitialChatMessages(projectId, INITIAL_MESSAGES_LOAD_COUNT);
      setDisplayedMessages(initialMsgs || []);
      if ((initialMsgs || []).length < INITIAL_MESSAGES_LOAD_COUNT) setCanLoadOlderInternal(false);
    } catch (error) { setDisplayedMessages([]); setCanLoadOlderInternal(false); }
    finally { setIsLoadingInternalInitialMessages(false); setTimeout(() => scrollToBottom("auto"), 50); }
  }, [projectId, scrollToBottom]);

  useEffect(() => {
    if (projectId) {
        setDisplayedMessages(initialMessagesFromContext || []); 
        loadInternalInitialMessages();
    } else { setIsLoadingInternalInitialMessages(false); setDisplayedMessages([]); setCanLoadOlderInternal(false); }
  }, [projectId, initialMessagesFromContext, loadInternalInitialMessages]);


  const loadInternalOlderMessages = useCallback(async () => {
    if (!projectId || displayedMessages.length === 0 || !canLoadOlderInternal || isLoadingOlder) return;
    setIsLoadingOlder(true);
    if (scrollContainerRef.current) prevScrollHeightRef.current = scrollContainerRef.current.scrollHeight;
    try {
      if (typeof window.api?.database?.getOlderChatMessages !== 'function') { setCanLoadOlderInternal(false); setIsLoadingOlder(false); return; }
      const oldestTimestamp = displayedMessages[0].timestamp;
      const olderMsgs = await window.api.database.getOlderChatMessages(projectId, oldestTimestamp, OLDER_MESSAGES_LOAD_COUNT);
      if (olderMsgs.length > 0) setDisplayedMessages(prev => [...olderMsgs, ...prev]);
      if (!olderMsgs || olderMsgs.length < OLDER_MESSAGES_LOAD_COUNT) setCanLoadOlderInternal(false);
    } catch (error) { console.error("SandboxChat: Error loading older project messages:", error); }
    finally { setIsLoadingOlder(false); }
  }, [projectId, displayedMessages, canLoadOlderInternal, isLoadingOlder]);

  useLayoutEffect(() => {
    if (isLoadingOlder === false && prevScrollHeightRef.current !== null && scrollContainerRef.current) {
      const currentScrollHeight = scrollContainerRef.current.scrollHeight;
      scrollContainerRef.current.scrollTop += currentScrollHeight - prevScrollHeightRef.current;
      prevScrollHeightRef.current = null;
    }
  }, [displayedMessages, isLoadingOlder]);

   useEffect(() => {
    const container = scrollContainerRef.current;
    const handleScroll = () => { if (container) isUserAtBottomRef.current = container.scrollHeight - container.scrollTop <= container.clientHeight + 50; };
    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => { if (scrollContainerRef.current && !isLoadingOlder && !isLoadingInternalInitialMessages && isUserAtBottomRef.current) scrollToBottom("smooth"); }, [displayedMessages, scrollToBottom, isLoadingOlder, isLoadingInternalInitialMessages]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moreActionsDropdownRef.current && !moreActionsDropdownRef.current.contains(event.target as Node) && moreActionsButtonRef.current && !moreActionsButtonRef.current.contains(event.target as Node)) setShowMoreActions(false);
      if (isAttachmentMenuOpen && attachmentButtonRef.current && !attachmentButtonRef.current.contains(event.target as Node)) { const menuElement = document.querySelector('[data-attachment-menu="true"]'); if (menuElement && !menuElement.contains(event.target as Node)) setIsAttachmentMenuOpen(false); }
      if (isWisdomPouchMenuOpen && wisdomPouchButtonRef.current && !wisdomPouchButtonRef.current.contains(event.target as Node)) { const menuElement = document.querySelector('[data-wisdom-pouch-menu="true"]'); if (menuElement && !menuElement.contains(event.target as Node)) setIsWisdomPouchMenuOpen(false); }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMoreActions, isAttachmentMenuOpen, isWisdomPouchMenuOpen]);
  
  useEffect(() => { 
    if (inputTextRef.current) {
        const textarea = inputTextRef.current; textarea.style.height = 'auto'; 
        const newHeight = Math.min(textarea.scrollHeight, parseFloat(getComputedStyle(textarea).maxHeight || '9rem'));
        textarea.style.height = `${Math.max(newHeight, parseFloat(getComputedStyle(textarea).lineHeight || '1.5rem') * 3)}px`; // Ensure minHeight based on lineHeight
        textarea.style.overflowY = newHeight >= parseFloat(getComputedStyle(textarea).maxHeight || '9rem') ? 'auto' : 'hidden';
    }
  }, [inputText]);


  const startNewRoundtableMeeting = async (initialPromptText: string) => {
    if (!projectId) { console.error("SandboxChat: Cannot start roundtable, projectId is missing."); await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', text: '错误：项目ID丢失，无法启动战略沙盘。', timestamp: new Date().toISOString(), isError: true }); return; }
    if (activeRoundtableParticipantIds.length < 1) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', text: '提示：战略沙盘至少需要一位AI角色参与。', timestamp: new Date().toISOString(), projectId }); return; }
    if (roundtableDiscussionRounds <= 0) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', text: '提示：讨论轮数必须大于0。', timestamp: new Date().toISOString(), projectId }); return; }
    
    setCurrentMeetingId(`roundtable-${window.crypto.randomUUID()}`); setCurrentMeetingTopic(initialPromptText); setIsMeetingInProgress(true); setCurrentRoundtableTurn(0); setRoundtableChatHistory([]); 
    setRoundtableSharedDraft(''); setRoundtableToDoItems([]);
    const userMessage: ChatMessage = { id: window.crypto.randomUUID(), sender: 'user', senderName: currentUserName || "舰长", text: `发起战略沙盘议题：${initialPromptText}`, timestamp: new Date().toISOString(), avatarPath: appSettings.user_avatar_path, projectId: projectId };
    await handleSaveMessageForDisplayAndBackend(userMessage); setRoundtableChatHistory(prev => [...prev, userMessage]);
    
    if(typeof window.api?.ai?.startRoundtableMeeting !== 'function') {
        await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: '战略沙盘功能接口未准备就绪。', timestamp: new Date().toISOString(), projectId });
        setIsMeetingInProgress(false); return;
    }
    try {
      const result = await window.api.ai.startRoundtableMeeting(initialPromptText, activeRoundtableParticipantIds, roundtableDiscussionRounds, projectId, [userMessage] );
      const endMsgText = result.success ? "战略沙盘讨论结束。" : `战略沙盘启动或执行失败: ${result.error}`;
      await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: result.success ? '系统提示' : '系统错误', text: endMsgText, timestamp: new Date().toISOString(), projectId: projectId });
    } catch(e:any) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统严重错误', text: `战略沙盘启动时发生意外：${e.message}`, timestamp: new Date().toISOString(), projectId: projectId }); } 
    finally { setIsMeetingInProgress(false); }
  };

  const sendCaptainIntervention = async (interventionText: string) => {
    if (!isMeetingInProgress || !projectId || !currentMeetingId || !currentMeetingTopic) return;
    const userMessage: ChatMessage = { id: window.crypto.randomUUID(), sender: 'user', senderName: currentUserName || "舰长", text: `舰长干预：${interventionText}`, timestamp: new Date().toISOString(), avatarPath: appSettings.user_avatar_path, projectId: projectId };
    await handleSaveMessageForDisplayAndBackend(userMessage);
    
    if (typeof window.api?.ai?.startRoundtableMeeting !== 'function') {
        await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: '战略沙盘干预失败：功能接口未准备就绪。', timestamp: new Date().toISOString(), projectId });
        return;
    }
    try {
        const remainingRounds = Math.max(1, roundtableDiscussionRounds - currentRoundtableTurn); 
        const result = await window.api.ai.startRoundtableMeeting(interventionText, activeRoundtableParticipantIds, remainingRounds, projectId, displayedMessages );
        if (!result.success) await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `战略沙盘干预失败: ${result.error}`, timestamp: new Date().toISOString(), projectId: projectId });
    } catch (e: any) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统严重错误', text: `战略沙盘干预时发生意外：${e.message}`, timestamp: new Date().toISOString(), projectId: projectId }); }
  };

  const handleInternalAICall = useCallback(async (prompt?: string, history?: ChatMessage[], ragActive?: boolean, context?: AiCallContext) => {
    if (!projectId) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', text: '错误：项目ID丢失，无法与AI交互。', timestamp: new Date().toISOString(), isError: true }); return; }
    if (!window.api?.ai?.invokeSandboxRequest) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统严重错误', text: 'AI调用服务接口(Sandbox)丢失。', timestamp: new Date().toISOString(), isError: true, projectId }); return; }
    if (!apiKeyAvailable) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '配置错误', text: 'API Key未配置或无效，无法调用AI。请检查天工阁设置。', timestamp: new Date().toISOString(), isError: true, projectId }); return; }
    
    if (outletContext.settings.onAiThinkingStateChange) outletContext.settings.onAiThinkingStateChange('xiaolan_thinking'); 
    let aiResponse: AIResponseWithStatus;
    try {
      const requestPayload = { 
          prompt: prompt || "...", 
          history: history || [], 
          projectId: projectId!, 
          persona: context?.explicitPersona || (activeRoundtableParticipantIds.length === 1 ? activeRoundtableParticipantIds[0] : 'LinLuo'),
          otherSandboxContext: context 
      };
      aiResponse = await window.api.ai.invokeSandboxRequest(requestPayload);
    } catch (ipcError: any) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `与AI核心通信失败：${ipcError.message}`, timestamp: new Date().toISOString(), isError: true, projectId }); if (outletContext.settings.onAiThinkingStateChange) outletContext.settings.onAiThinkingStateChange('idle'); return; }

    if (aiResponse && aiResponse.text !== null && aiResponse.text !== undefined) {
      const { theme: aiTheme, finalText: aiFinalText } = parseMessageTextForTheme(aiResponse.text);
      const characterIdForDisplay = aiResponse.characterId || (context?.explicitPersona as Character['id']) || (activeRoundtableParticipantIds.length === 1 ? activeRoundtableParticipantIds[0] : 'linluo');
      const character = allAvailableCharacters.find(c => c.id === characterIdForDisplay);
      const assignment = character ? allAssignments.find(a => a.character_id === character.id) : undefined;
      const post = assignment ? allPosts.find(p => p.id === assignment.post_id) : undefined;
      
      let senderNameWithPost = character ? (post ? `[${post.name}] - ${character.name}` : character.name) : (characterIdForDisplay || 'AI助手');
      let avatarPathForDisplay = character?.avatar_path || (characterIdForDisplay === 'linluo' ? appSettings.linluo_avatar_path : (characterIdForDisplay === 'xiaolan' ? appSettings.xiaolan_avatar_path : null));
      
      const aiChatMessage: ChatMessage = { id: window.crypto.randomUUID(), sender: 'ai', senderName: senderNameWithPost, characterId: characterIdForDisplay, text: aiFinalText, timestamp: new Date().toISOString(), theme: aiTheme, avatarPath: avatarPathForDisplay, isHypothesisProposal: !!aiResponse.json?.hypothesisId, hypothesisData: aiResponse.json?.hypothesisId ? aiResponse.json : undefined, projectId: projectId || undefined };
      await handleSaveMessageForDisplayAndBackend(aiChatMessage);
    } else if (aiResponse?.json?.error) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: 'AI错误', text: `AI处理时发生错误： ${aiResponse.json.error}`, timestamp: new Date().toISOString(), isError: true, projectId }); }
     else if (!aiResponse) { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', senderName: 'AI错误', text: 'AI未能返回有效响应。', timestamp: new Date().toISOString(), isError: true, projectId }); }
    if (outletContext.settings.onAiThinkingStateChange) outletContext.settings.onAiThinkingStateChange('idle');
  }, [handleSaveMessageForDisplayAndBackend, allAvailableCharacters, allAssignments, allPosts, projectId, apiKeyAvailable, activeRoundtableParticipantIds, appSettings, outletContext.settings.onAiThinkingStateChange]);

  const handleSendMessage = async () => {
    const textToSend = inputText.trim();
    if (textToSend === '') return; 
    if (!projectId) { await handleSaveMessageForDisplayAndBackend({id: window.crypto.randomUUID(), sender:'system', text:'项目ID丢失，无法发送。', timestamp:new Date().toISOString(), isError: true}); return; }

    // Always use three-column logic now for sending
    if (activeRoundtableParticipantIds.length >=2) { 
      if (!isMeetingInProgress) { await startNewRoundtableMeeting(textToSend); } 
      else { await sendCaptainIntervention(textToSend); }
      setInputText(''); return;
    }
    
    let targetPersonaId: Character['id'] = 'linluo'; 
    if (textToSend.toLowerCase().startsWith("@小岚") || textToSend.toLowerCase().startsWith("@xiaolan")) targetPersonaId = 'xiaolan';
    else if (textToSend.toLowerCase().startsWith("@林珞") || textToSend.toLowerCase().startsWith("@linluo")) targetPersonaId = 'linluo';
    else if (textToSend.toLowerCase().startsWith("@语镜") || textToSend.toLowerCase().startsWith("@yujing")) targetPersonaId = 'yujing';
    else if (activeRoundtableParticipantIds.length === 1) targetPersonaId = activeRoundtableParticipantIds[0] as Character['id'];
    else { const linLuoIsActive = activeRoundtableParticipantIds.includes('linluo'); if (linLuoIsActive) targetPersonaId = 'linluo'; else if (activeRoundtableParticipantIds.length > 0) targetPersonaId = activeRoundtableParticipantIds[0] as Character['id']; }
    
    const actualPromptForAI = textToSend.replace(/^@(?:小岚|xiaolan|林珞|linluo|语镜|yujing)\s*/i, '').trim();
    const newMessage: ChatMessage = { id: window.crypto.randomUUID(), sender: 'user', senderName: currentUserName, text: escapeHTML(textToSend), timestamp: new Date().toISOString(), avatarPath: appSettings.user_avatar_path || null, projectId: projectId };
    await handleSaveMessageForDisplayAndBackend(newMessage);
    
    if (!isAIServiceReady) { await handleSaveMessageForDisplayAndBackend({id: window.crypto.randomUUID(), sender:'system', text:'AI服务尚未就绪，请稍后再试。', timestamp:new Date().toISOString(), isError: true, projectId}); } 
    else if (!apiKeyAvailable) { await handleSaveMessageForDisplayAndBackend({id: window.crypto.randomUUID(), sender:'system', text:'API Key未配置，无法与AI交互。请检查天工阁设置。', timestamp:new Date().toISOString(), isError: true, projectId}); } 
    else { await handleInternalAICall(actualPromptForAI || "...", [...displayedMessages, newMessage], isRagEnabled, {projectId: projectId, triggeringUserMessageId: newMessage.id, explicitPersona: targetPersonaId as any }); }
    setInputText('');
  };

  const handleRequestRichEdit = (messageId: string | null) => setRichEditingId(messageId);
  
  const handleUpdateMessageFromItem = (update: { messageId: string; newHtml: string }) => {
    const { messageId, newHtml } = update;
    const messageToUpdate = displayedMessages.find(m => m.id === messageId);
    if (messageToUpdate && projectId && typeof onUpdateExistingChatMessageCallback === 'function') {
      const updatedMsgData = { ...messageToUpdate, text: newHtml, timestamp: new Date().toISOString(), projectId }; 
      onUpdateExistingChatMessageCallback(updatedMsgData); 
      setDisplayedMessages(prev => prev.map(m => m.id === messageId ? updatedMsgData : m));
    }
  };

  const handleCopyMessageText = async (textToCopy: string) => { try { await navigator.clipboard.writeText(textToCopy.replace(summaryBlockRegex, '').replace(/<[^>]+>/g, '')); } catch (err) { console.error('Failed to copy text: ', err); } };
  const handleScrollToTopForLoad = () => { if (!isLoadingOlder) loadInternalOlderMessages(); };
  const handleManualSummarize = async () => {
    setShowMoreActions(false); if (!isAIServiceReady || !apiKeyAvailable || aiTaskStatus !== 'idle') { window.alert("AI服务不可用或未配置API Key。"); return; }
    if (typeof window.api?.ai?.summarizeConversation !== 'function') { window.alert("摘要功能所需接口未准备就绪。"); return; }
    const messagesToSummarize = displayedMessages.filter(m => m.sender !== 'system_summary' && m.sender !== 'system').slice(-30);
    if (messagesToSummarize.length < 5) { window.alert("对话内容过少，无需手动摘要。"); return; }
    try { const summaryTextResult = await window.api.ai.summarizeConversation(messagesToSummarize); await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system_summary', senderName: '手动摘要', text: summaryTextResult, timestamp: new Date().toISOString(), projectId: projectId }); } catch (e:any) { window.alert(`手动摘要失败: ${e.message}`); }
  };
  const handleSaveChatAction = async () => {
    if (!projectId) return; setShowSaveChatModal(false); setShowMoreActions(false); if (!window.api?.fs?.exportChatHistory) return;
    try { const exportName = (projectName || projectId || "沙盘记录"); const result = await window.api.fs.exportChatHistory(displayedMessages, saveChatFormat, exportName); if (result.success && result.path) window.alert(`聊天记录已成功导出到: ${result.path}`); else window.alert(`导出失败: ${result.error || "未知错误"}`); } catch (exportError: any) { console.error("Failed to export chat history:", exportError); }
  };
  
  const handleDecomposeRequirementInternal = async (text: string, originalMessageId: string) => {
    if (handleDecomposeRequirement && projectId) handleDecomposeRequirement(text, projectId, originalMessageId);
  };

  const handleConvertToTaskInternal = (message: ChatMessage) => {
    if (handleConvertToTask) handleConvertToTask(message);
  };

  const handleParticipantToggle = (characterId: string) => {
    setRoundtableParticipants(prev =>
      prev.map(p => p.id === characterId ? { ...p, isActiveInMeeting: !p.isActiveInMeeting } : p)
    );
  };
  const handleDiscussionRoundsChange = (rounds: number) => {
    setRoundtableDiscussionRounds(Math.max(1, rounds));
  };
  
  const commonMessageListProps = { 
    onUpdateMessage: handleUpdateMessageFromItem, 
    onToggleStar: () => {}, 
    onTogglePin: () => {}, 
    onCopy: handleCopyMessageText, 
    onRequestRichEdit: handleRequestRichEdit, 
    settings: appSettings, 
    currentUserName, 
    onAddToCoreMemory: onAddCoreMemory, 
    isWorkspaceContext: true, 
    onDecomposeRequirement: handleDecomposeRequirementInternal, 
    onConvertToTask: handleConvertToTaskInternal,
    allAvailableCharacters: allAvailableCharacters, 
    allPosts: allPosts,
    allAssignments: allAssignments,
    onSendToPouch: (message: ChatMessage, pouchType?: WisdomPouchType) => {
      if (onOpenWisdomPouch && pouchType) {
        onOpenWisdomPouch(); 
      } else if (onOpenWisdomPouch) {
        onOpenWisdomPouch();
      }
    }
  };
  
  return (
    <div ref={containerRef} className="flex h-full bg-tg-bg-primary text-tg-text-primary overflow-hidden">
      {/* Left Panel: ParticipantsPanel - Always visible */}
      <ResizablePanel className="h-full flex flex-col border-r border-tg-border-primary" initialWidth={leftPanelWidth}>
          <ParticipantsPanel 
              allCharacters={allAvailableCharacters} 
              allPosts={allPosts}
              allAssignments={allAssignments}
              activeParticipantIds={activeRoundtableParticipantIds} 
              onParticipantToggle={handleParticipantToggle} 
              discussionRounds={roundtableDiscussionRounds} 
              onDiscussionRoundsChange={handleDiscussionRoundsChange} 
              onStartMeeting={() => { /* No direct start button here, auto-starts on send */ }}
              isMeetingActive={isMeetingInProgress}
              isLoading={isAiProcessing}
          />
      </ResizablePanel>

      {/* Main Chat Area */}
      <div className="flex-grow flex flex-col overflow-hidden relative">
        {/* 背景装饰 */}
        <div className="absolute inset-0 opacity-5 pointer-events-none">
          <div className="absolute top-10 right-20 w-16 h-16 bg-blue-400 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-20 left-10 w-12 h-12 bg-purple-400 rounded-full blur-lg animate-float" style={{animationDelay: '2s'}}></div>
        </div>
        <div className="p-4 border-b border-tg-border-primary glass-effect backdrop-blur-xl flex justify-between items-center flex-shrink-0 shadow-lg relative z-10">
          <div className="flex items-center">
            <SmartIcon name="MessagesSquare" level="accent" context="navigation" className="mr-3" />
            <VisualHeading level="accent" size="lg" gradient>战略沙盘</VisualHeading>
            {projectName && <span className="text-sm text-tg-text-secondary ml-3 font-medium">- {projectName}</span>}
          </div>
           <div className="flex items-center space-x-2">
            {/* Removed Three-column Layout Toggle Button */}
            <div className="relative">
                <button ref={moreActionsButtonRef} onClick={() => setShowMoreActions(!showMoreActions)} className="p-1.5 rounded-md text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary transition-colors" title="更多操作"><Icon name="EllipsisVertical" className="w-5 h-5"/></button>
                {showMoreActions && (<div ref={moreActionsDropdownRef} className="absolute right-0 mt-1 w-48 bg-tg-bg-tertiary border border-tg-border-primary rounded-md shadow-lg z-20 py-1"> <button onClick={() => { setShowSaveChatModal(true); setShowMoreActions(false); }} className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"> <Icon name="Save" className="w-4 h-4 mr-2"/>保存当前沙盘记录</button> <button onClick={handleManualSummarize} className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"> <Icon name="FileText" className="w-4 h-4 mr-2"/>手动摘要当前对话</button> <button onClick={() => { setShowGlobalCommandsModal(true); setShowMoreActions(false);}} className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"><Icon name="Terminal" className="w-4 h-4 mr-2"/>查看全局快捷指令</button> </div>)}
            </div>
          </div>
        </div>
        <div ref={scrollContainerRef} className="flex-grow p-6 space-y-4 overflow-y-auto custom-scrollbar relative z-10 bg-gradient-to-b from-transparent to-tg-bg-primary/10">
          {(isLoadingInternalInitialMessages && displayedMessages.length === 0) && ( <div className="text-center py-4 text-tg-text-placeholder"><Icon name="Loader2" className="w-6 h-6 animate-spin mx-auto mb-2" /> 加载历史记录中...</div> )}
          {canLoadOlderInternal && ( <div className="text-center"><button onClick={handleScrollToTopForLoad} disabled={isLoadingOlder} className="text-xs text-tg-accent-primary hover:underline disabled:opacity-50">{isLoadingOlder ? "加载中..." : "加载更早的记录"}</button></div> )}
          {displayedMessages.map(msg => ( <ChatDiscussionMessageItem key={msg.id} message={msg} isRichEditingActive={richEditingId === msg.id} {...commonMessageListProps} /> ))}
          <div ref={messagesEndRef} />
        </div>
        <div className="p-4 border-t border-tg-border-primary glass-effect backdrop-blur-xl flex-shrink-0 shadow-lg relative z-10">
           <div className="flex items-end space-x-2">
              <div className="relative flex-shrink-0"> <button ref={attachmentButtonRef} onClick={() => setIsAttachmentMenuOpen(prev => !prev)} className="p-3 rounded-lg transition-colors text-tg-text-secondary hover:bg-tg-bg-hover" title="添加附件" disabled={isAiProcessing}><Icon name="Paperclip" className="w-5 h-5"/></button> {isAttachmentMenuOpen && ( <div data-attachment-menu="true" className="absolute bottom-full left-0 mb-1 w-52 bg-tg-bg-tertiary border border-tg-border-interactive rounded-md shadow-lg py-1 z-30"> <button className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary disabled:opacity-50" disabled>从本地硬盘上传 (待实现)</button> <button className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary disabled:opacity-50" disabled>从项目知识库选择 (待实现)</button> </div> )} </div>
              <textarea ref={inputTextRef} value={inputText} onChange={(e) => setInputText(e.target.value)} placeholder={aiTaskStatus === 'idle' ? "输入指令或与AI对话..." : "AI思考中，请稍候..."} className="flex-grow p-4 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-2xl resize-none outline-none focus:border-tg-accent-primary focus:ring-2 focus:ring-tg-accent-primary/30 focus:shadow-glow text-sm transition-all duration-300 backdrop-blur-sm" style={{ minHeight: 'calc(1.5rem * 3 + 2rem)', maxHeight: 'calc(1.5rem * 10 + 2rem)', lineHeight: '1.5rem', overflowY: 'hidden' }} onKeyPress={(e) => { if (e.key === 'Enter' && !e.shiftKey && aiTaskStatus === 'idle') { e.preventDefault(); handleSendMessage(); }}} disabled={isAiProcessing} rows={3} aria-label="聊天输入框" />
              <div className="flex flex-col space-y-1.5 flex-shrink-0"> {onOpenWisdomPouch && ( <button ref={wisdomPouchButtonRef} onClick={() => setIsWisdomPouchMenuOpen(prev => !prev)} className="p-3 rounded-lg transition-colors text-tg-text-secondary hover:bg-tg-bg-hover" title="快捷指令/百宝袋" disabled={isAiProcessing}><Icon name="Package" className="w-5 h-5"/></button> )} <button onClick={handleSendMessage} disabled={isAiProcessing || !inputText.trim()} className="p-3 rounded-lg transition-colors text-white flex-grow bg-tg-accent-primary hover:bg-tg-accent-primary-hover disabled:bg-tg-bg-tertiary disabled:text-tg-text-placeholder disabled:cursor-not-allowed" title="发送消息"> {isAiProcessing ? <Icon name="Loader2" className="w-5 h-5 animate-spin"/> : <Icon name="SendHorizontal" className="w-5 h-5" />} </button> </div>
               {isWisdomPouchMenuOpen && onOpenWisdomPouch && ( <div data-wisdom-pouch-menu="true" className="absolute bottom-full right-0 mb-1 mr-14 w-48 bg-tg-bg-tertiary border border-tg-border-interactive rounded-md shadow-lg py-1 z-30"> <button onClick={() => { if(onOpenWisdomPouch) onOpenWisdomPouch(); setIsWisdomPouchMenuOpen(false); }} className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"> <Icon name="BookOpen" className="w-4 h-4 mr-1.5"/> 打开智慧锦囊 </button> <button onClick={() => { setShowGlobalCommandsModal(true); setIsWisdomPouchMenuOpen(false);}} className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"> <Icon name="Terminal" className="w-4 h-4 mr-1.5"/> 全局快捷指令 </button> </div> )}
          </div>
        </div>
      </div>
      {/* Right Panel: RoundtableDraftPanel - Always visible */}
      <ResizablePanel className="h-full flex flex-col border-l border-tg-border-primary" initialWidth={rightPanelWidth}>
          <RoundtableDraftPanel 
              sharedDraftContent={roundtableSharedDraft}
              onSharedDraftChange={setRoundtableSharedDraft}
              toDoItems={roundtableToDoItems}
              onAddToDoItem={() => setRoundtableToDoItems(prev => [...prev, {id: window.crypto.randomUUID(), text: "新待办事项"}])}
              onUpdateToDoItemText={(id, text) => setRoundtableToDoItems(prev => prev.map(item => item.id === id ? {...item, text} : item))}
              onDeleteToDoItem={(id) => setRoundtableToDoItems(prev => prev.filter(item => item.id !== id))}
              onPublishToDoItem={async (id) => { const item = roundtableToDoItems.find(i => i.id === id); if(item && projectId) { const payload: DevelopmentTaskCreationPayload = { projectId, title: `[沙盘纪要] ${item.text}`, description: `来自战略沙盘会议 (ID: ${currentMeetingId || '未知'}) 的待办事项。`}; const task = await window.api.database.createDevelopmentTaskFromChat(payload); if(task) setRoundtableToDoItems(prev => prev.map(i => i.id === id ? {...i, isPublished: true} : i)); } }}
              onPublishAllToDoItems={async () => { if(!projectId) return; for(const item of roundtableToDoItems.filter(i => !i.isPublished)) { const payload: DevelopmentTaskCreationPayload = { projectId, title: `[沙盘纪要] ${item.text}`, description: `来自战略沙盘会议 (ID: ${currentMeetingId || '未知'}) 的待办事项。`}; await window.api.database.createDevelopmentTaskFromChat(payload); } setRoundtableToDoItems(prev => prev.map(i => ({...i, isPublished: true}))); }}
              isMeetingActive={isMeetingInProgress}
          />
      </ResizablePanel>
      {showRagResultsModal && ( <SearchResultsDisplay isVisible={showRagResultsModal} query={ragSearchQuery} results={ragSearchResults} isLoading={isRagSearchLoading} error={ragSearchError} onClose={() => setShowRagResultsModal(false)} onCopy={insertTextAtFocus}/> )}
      <GenericModal isOpen={showGlobalCommandsModal} onClose={() => setShowGlobalCommandsModal(false)} title="快捷指令参考 (沙盘)" size="lg"> {globalQuickCommands.length > 0 ? ( <ul className="space-y-2 max-h-96 overflow-y-auto"> {globalQuickCommands.map(cmd => ( <li key={cmd.id} className="p-2 bg-tg-bg-tertiary rounded-md"> <button onClick={() => {insertTextAtFocus(cmd.commandText); setShowGlobalCommandsModal(false);}} className="w-full text-left text-tg-text-primary hover:text-tg-accent-primary"> <p className="font-semibold text-sm">{cmd.title}</p> <p className="text-xs text-tg-text-secondary truncate mt-0.5">{cmd.commandText}</p> </button> </li> ))} </ul> ) : ( <p className="text-tg-text-placeholder text-center py-4">暂无全局快捷指令。请在“天工阁设置”中添加。</p> )} </GenericModal>
      {isCreateTaskModalOpen && projectId && ( <CreateTaskFromChatMessageModal isOpen={isCreateTaskModalOpen} onClose={() => setIsCreateTaskModalOpen(false)} initialTitle={taskModalInitialData.title} initialDescription={taskModalInitialData.description} projectId={projectId} onTaskCreated={async (createdTaskTitle) => { await handleSaveMessageForDisplayAndBackend({ id: window.crypto.randomUUID(), sender: 'system', text: `开发任务 "${createdTaskTitle}" 已从消息创建并发布至神谕罗盘。`, timestamp: new Date().toISOString(), projectId: projectId }); setIsCreateTaskModalOpen(false); }} /> )}
    </div>
  );
};

export const SandboxChat = memo(forwardRef(SandboxChatWithRef));
export default SandboxChat;
