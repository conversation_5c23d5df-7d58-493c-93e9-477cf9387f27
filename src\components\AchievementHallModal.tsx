// src/components/AchievementHallModal.tsx
import React, { useState, useEffect, useMemo } from 'react';
import type { Achievement, UserAchievement, BodyZoneKey, LinLuoBodyDevelopment, LinLuoDetailedStatus, AchievementCriteria, UnlockRequirement, AchievementTypeTGC } from '@/types';
import { ALL_BODY_ZONES } from '@/types';
import { GenericModal } from '@/components/GenericModal';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon
import { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';

interface AchievementHallModalProps {
  isOpen: boolean;
  onClose: () => void;
  bodyDevelopmentData: LinLuoBodyDevelopment[];
  linLuoCurrentStatus: LinLuoDetailedStatus;
}

const getCriteriaText = (criteria: AchievementCriteria, bodyDev: LinLuoBodyDevelopment[], linLuoStatus: LinLuoDetailedStatus): string => {
    let progressText = "";
    switch (criteria.type) {
        case 'body_zone_points':
            const zoneDev = bodyDev.find(z => z.zone_id === criteria.zone);
            const currentPoints = zoneDev?.development_points || 0;
            progressText = `(当前: ${currentPoints}/${criteria.points_required}点)`;
            return `【${BODY_ZONE_DISPLAY_NAMES[criteria.zone!] || criteria.zone}】开发度达到 ${criteria.points_required} 点 ${progressText}`;
        case 'any_zone_min_points':
            const maxPoints = Math.max(0, ...bodyDev.map(z => z.development_points));
            progressText = `(最高区域: ${maxPoints}/${criteria.points_required}点)`;
            return `任意身体区域开发度达到 ${criteria.points_required} 点 ${progressText}`;
        case 'all_zones_min_points':
            const metCount = ALL_BODY_ZONES.filter(zoneKey => (bodyDev.find(z => z.zone_id === zoneKey)?.development_points || 0) >= criteria.points_required!).length;
            progressText = `(已达成: ${metCount}/${ALL_BODY_ZONES.length}个区域)`;
            return `所有身体区域开发度均达到 ${criteria.points_required} 点 ${progressText}`;
        case 'obedience_action_count':
            const currentObedienceCount = linLuoStatus.obedienceActionCount || 0;
            progressText = `(当前: ${currentObedienceCount}/${criteria.count_required}次)`;
            return `成功让姐姐执行 ${criteria.count_required} 次服从性指令 ${progressText}`;
        case 'specific_interaction':
             return `触发特殊互动: ${criteria.action_name || "特定条件"}`; 
        case 'cms_combo_count':
            return `在单次互动中使用超过 ${criteria.count_required} 种不同的组合`; 
        default:
            return "满足特定条件";
    }
};

const getRewardText = (rewardJson: string): string => {
    try {
        const reward = JSON.parse(rewardJson);
        switch (reward.type) {
            case 'cg': return `解锁CG: ${reward.value.split('/').pop() || '专属画面'}`;
            case 'item': return `解锁道具: ${reward.item_name_to_unlock_or_grant || '神秘道具'}`;
            case 'title': return `获得称号: “${reward.value}”`;
            case 'none': return reward.description || "特殊嘉奖";
            default: return "神秘奖励";
        }
    } catch {
        return "神秘奖励";
    }
};

export const AchievementHallModal: React.FC<AchievementHallModalProps> = ({ isOpen, onClose, bodyDevelopmentData, linLuoCurrentStatus }) => {
    const [allAchievements, setAllAchievements] = useState<Achievement[]>([]);
    const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (isOpen) {
            setIsLoading(true);
            Promise.all([
                window.api.database.getAllAchievements(),
                window.api.database.getUserAchievements('default_user') 
            ]).then(([allAchData, userAchData]) => {
                setAllAchievements(allAchData || []);
                setUserAchievements(userAchData || []);
            }).catch(error => {
                console.error("Failed to load achievement data:", error);
            }).finally(() => {
                setIsLoading(false);
            });
        }
    }, [isOpen]);

    const categorizedAchievements = useMemo(() => {
        const dev: Achievement[] = [];
        const interact: Achievement[] = [];
        const hidden: Achievement[] = [];

        allAchievements.forEach(ach => {
            const userAch = userAchievements.find(ua => ua.achievement_id === ach.id);
            const achievementWithStatus = { ...ach, unlocked_at: userAch?.unlocked_at || null };
            if (ach.type === 'development') dev.push(achievementWithStatus);
            else if (ach.type === 'interaction') interact.push(achievementWithStatus);
            else if (ach.type === 'hidden') hidden.push(achievementWithStatus);
        });
        return { development: dev, interaction: interact, hidden: hidden };
    }, [allAchievements, userAchievements]);

    const renderAchievementCard = (ach: Achievement & { unlocked_at: string | null }) => {
        const isUnlocked = !!ach.unlocked_at;
        let criteriaText = "隐藏条件";
        if (ach.type !== 'hidden' || isUnlocked) {
           try {
               const criteriaObj = JSON.parse(ach.criteria_json);
               criteriaText = getCriteriaText(criteriaObj, bodyDevelopmentData, linLuoCurrentStatus);
           } catch {
               criteriaText = "查看条件失败";
           }
        } else { 
            criteriaText = ach.description || "解开谜题以揭示..."; 
        }


        return (
            <div key={ach.id} className={`p-3 rounded-md border-2 flex items-start space-x-3 transition-all
                ${isUnlocked ? 'bg-yellow-500/10 border-yellow-500 shadow-lg' : 'bg-gray-700/50 border-gray-600 opacity-70'}`}
            >
                <div className="flex-shrink-0 w-12 h-12 bg-gray-800 rounded-md flex items-center justify-center border border-gray-700">
                    {ach.icon_path && isUnlocked ? (
                        <img src={`at-asset://${ach.icon_path}?t=${Date.now()}`} alt={ach.title} className="w-full h-full object-contain p-1"/>
                    ) : isUnlocked ? (
                        <Icon name="Trophy" className="w-7 h-7 text-yellow-400"/>
                    ) : ach.type === 'hidden' ? (
                        <Icon name="EyeOff" className="w-7 h-7 text-gray-500"/>
                    ) : (
                        <Icon name="Lock" className="w-7 h-7 text-gray-500"/>
                    )}
                </div>
                <div className="flex-grow">
                    <h5 className={`font-semibold ${isUnlocked ? 'text-yellow-400' : 'text-gray-300'}`}>
                        {ach.type === 'hidden' && !isUnlocked ? "？？？" : ach.title}
                        {isUnlocked && <Icon name="CheckCircle" className="w-4 h-4 inline-block ml-1.5 text-green-400" title={`达成于: ${new Date(ach.unlocked_at!).toLocaleString()}`}/>}
                    </h5>
                    <p className="text-xs text-gray-400 mt-0.5 leading-relaxed">{criteriaText}</p>
                    <p className="text-xs text-purple-300 mt-1">奖励: {getRewardText(ach.reward_json)}</p>
                </div>
            </div>
        );
    };
    
    const getCategoryIconName = (type: AchievementTypeTGC): React.ComponentProps<typeof Icon>['name'] => {
        switch(type) {
            case 'development': return "Box"; 
            case 'interaction': return "Users";
            case 'hidden': return "Lightbulb"; 
            default: return "Trophy";
        }
    }

    return (
        <GenericModal
            isOpen={isOpen}
            onClose={onClose}
            title="姐姐的勋章墙 / 成就殿堂"
            size="xl"
            footerContent={<button onClick={onClose} className="py-2 px-4 bg-purple-600 text-white rounded hover:bg-purple-700">关闭</button>}
        >
            {isLoading ? (
                <p className="text-center text-gray-400 py-8">加载成就中...</p>
            ) : (
                <div className="space-y-6 max-h-[70vh] overflow-y-auto pr-2 text-sm">
                    {(Object.keys(categorizedAchievements) as AchievementTypeTGC[]).map(type => {
                         const achievementsOfType = categorizedAchievements[type as keyof typeof categorizedAchievements]; // Type assertion for keys
                         if (!achievementsOfType || achievementsOfType.length === 0) return null;
                         return (
                            <section key={type}>
                                <h4 className="text-md font-semibold text-purple-400 mb-2 pb-1 border-b border-purple-700 flex items-center">
                                   <Icon name={getCategoryIconName(type)} className="w-4 h-4 mr-1.5 text-purple-400"/>
                                   {type === 'development' ? '开发类成就' : type === 'interaction' ? '交互类成就' : '隐藏类成就'} ({achievementsOfType.filter(a => a.unlocked_at).length}/{achievementsOfType.length})
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {achievementsOfType.map(renderAchievementCard)}
                                </div>
                            </section>
                         );
                    })}
                     {allAchievements.length === 0 && <p className="text-center text-gray-500 py-6">暂无任何成就定义。</p>}
                </div>
            )}
        </GenericModal>
    );
};