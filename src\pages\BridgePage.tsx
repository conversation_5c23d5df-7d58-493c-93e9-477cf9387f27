// src/pages/BridgePage.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon'; // Assuming Icon is used

export const BridgePage: React.FC = () => {
  // useEffect can be added back if needed, once the export issue is resolved.
  // useEffect(() => {
  //   console.log("BridgePage mounted");
  //   return () => {
  //     console.log("BridgePage unmounted");
  //   };
  // }, []);

  return (
    <div className="p-6 bg-tg-bg-primary text-tg-text-primary h-full flex flex-col items-center justify-center">
      <Icon name="Anchor" className="w-16 h-16 text-tg-accent-primary mb-4" />
      <h1 className="text-3xl font-bold mb-3 text-tg-text-primary">指挥舰桥</h1>
      <p className="text-lg text-tg-text-secondary mb-6 text-center max-w-md">
        战略决策的核心中枢，连接并指挥天工阁的各项关键功能。
      </p>
      <div className="bg-tg-bg-secondary p-4 rounded-lg shadow-md border border-tg-border-primary">
        <p className="text-sm text-tg-text-secondary">
          当前模块正在紧张建设与调试中，敬请期待更多高级指挥与控制能力的集成！
        </p>
      </div>
    </div>
  );
};

// No default export, ensuring BridgePage is exported as named.
