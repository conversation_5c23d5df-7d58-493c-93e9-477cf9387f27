// src/components/chat/CreateTaskFromChatMessageModal.tsx
import React, { useState, useEffect } from 'react';
import type { CreateTaskFromChatMessageModalProps, DevelopmentTask, DevelopmentTaskCreationPayload } from '@/types';
import { GenericModal } from '@/components/GenericModal';
import { Icon } from '@/components/common/Icon';

export const CreateTaskFromChatMessageModal: React.FC<CreateTaskFromChatMessageModalProps> = ({
  isOpen,
  onClose,
  initialTitle,
  initialDescription = '',
  projectId,
  onTaskCreated,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [description, setDescription] = useState(initialDescription);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      setTitle(initialTitle);
      setDescription(initialDescription);
      setIsSaving(false);
      setError(null);
    }
  }, [isOpen, initialTitle, initialDescription]);

  const handleSubmit = async () => {
    if (!title.trim()) {
      setError("任务标题不能为空。");
      return;
    }
    if (!projectId) {
      setError("项目ID缺失，无法创建任务。");
      return;
    }

    setIsSaving(true);
    setError(null);

    const taskPayload: DevelopmentTaskCreationPayload = {
      projectId,
      title: title.trim(),
      description: description.trim() || undefined,
    };

    try {
      if (typeof window.api?.database?.createDevelopmentTaskFromChat !== 'function') {
        throw new Error("创建任务的后端接口 (createDevelopmentTaskFromChat) 未准备就绪。");
      }
      const createdTask: DevelopmentTask | null = await window.api.database.createDevelopmentTaskFromChat(taskPayload);
      
      if (createdTask) {
        onTaskCreated(createdTask.title); 
        onClose();
      } else {
        throw new Error("后端未能成功创建任务或未返回任务对象。");
      }
    } catch (err: any) {
      console.error("Error creating task from chat message:", err);
      setError(`创建任务失败: ${err.message || '未知错误'}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <GenericModal
      isOpen={isOpen}
      onClose={onClose}
      title="从消息创建新开发任务"
      size="md"
      footerContent={
        <>
          <button
            onClick={onClose}
            className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary"
            disabled={isSaving}
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="py-2 px-4 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover flex items-center"
            disabled={isSaving || !title.trim()}
          >
            {isSaving ? <Icon name="Loader2" className="w-4 h-4 mr-1.5 animate-spin" /> : <Icon name="Plus" className="w-4 h-4 mr-1.5" />}
            {isSaving ? "发布中..." : "发布到神谕罗盘"}
          </button>
        </>
      }
    >
      {error && <p className="mb-3 p-2 text-sm text-red-400 bg-red-900/30 rounded">{error}</p>}
      <div className="space-y-4 text-sm">
        <div>
          <label htmlFor="task-title-chat" className="block text-xs text-tg-text-secondary mb-1">
            任务标题*
          </label>
          <input
            id="task-title-chat"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full p-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
            placeholder="简明扼要的任务标题..."
          />
        </div>
        <div>
          <label htmlFor="task-description-chat" className="block text-xs text-tg-text-secondary mb-1">
            任务描述 (可选)
          </label>
          <textarea
            id="task-description-chat"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
            className="w-full p-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded min-h-[80px] resize-y focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
            placeholder="详细描述任务内容、目标或验收标准..."
          />
        </div>
      </div>
    </GenericModal>
  );
};