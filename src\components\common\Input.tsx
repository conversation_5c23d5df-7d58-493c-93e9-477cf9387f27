// src/components/common/Input.tsx
import React from 'react';
import { Icon, IconProps } from './Icon';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'enhanced' | 'glass' | 'bordered';
  inputSize?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  leftIcon?: IconProps['name'];
  rightIcon?: IconProps['name'];
  error?: boolean;
  success?: boolean;
  helperText?: string;
  label?: string;
  glow?: boolean;
}

export const Input: React.FC<InputProps> = ({
  className,
  variant = 'default',
  inputSize = 'md',
  rounded = 'lg',
  leftIcon,
  rightIcon,
  error = false,
  success = false,
  helperText,
  label,
  glow = false,
  ...props
}) => {
  const baseClasses = "w-full transition-all duration-300 focus:outline-none";
  
  const variantClasses = {
    default: "bg-tg-bg-tertiary border border-tg-border-primary text-tg-text-primary",
    enhanced: "input-enhanced",
    glass: "glass-effect backdrop-blur-md text-tg-text-primary border border-tg-border-primary",
    bordered: "bg-tg-bg-tertiary border-2 border-tg-accent-primary text-tg-text-primary"
  };

  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-5 py-3 text-base",
    xl: "px-6 py-4 text-lg"
  };

  const roundedClasses = {
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl", 
    '2xl': "rounded-2xl",
    full: "rounded-full"
  };

  const stateClasses = error 
    ? "border-tg-danger focus:border-tg-danger focus:ring-tg-danger"
    : success
    ? "border-tg-success focus:border-tg-success focus:ring-tg-success"
    : "focus:border-tg-accent-primary focus:ring-tg-accent-primary";

  const glowClass = glow ? "focus:shadow-glow" : "focus:shadow-md";

  const currentVariantClass = variantClasses[variant] || variantClasses.default;
  const currentSizeClass = sizeClasses[inputSize] || sizeClasses.md;
  const currentRoundedClass = roundedClasses[rounded] || roundedClasses.lg;

  const inputClasses = `${baseClasses} ${currentVariantClass} ${currentSizeClass} ${currentRoundedClass} ${stateClasses} ${glowClass}`;

  const iconSize = inputSize === 'sm' ? 'sm' : inputSize === 'lg' ? 'lg' : inputSize === 'xl' ? 'xl' : 'md';

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-tg-text-primary mb-2">
          {label}
        </label>
      )}
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon name={leftIcon} size={iconSize} className="text-tg-text-placeholder" />
          </div>
        )}
        <input
          className={`${inputClasses} ${leftIcon ? 'pl-10' : ''} ${rightIcon ? 'pr-10' : ''} ${className || ''}`}
          {...props}
        />
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <Icon name={rightIcon} size={iconSize} className="text-tg-text-placeholder" />
          </div>
        )}
      </div>
      {helperText && (
        <p className={`mt-1 text-xs ${error ? 'text-tg-danger' : success ? 'text-tg-success' : 'text-tg-text-placeholder'}`}>
          {helperText}
        </p>
      )}
    </div>
  );
};

export default Input;
