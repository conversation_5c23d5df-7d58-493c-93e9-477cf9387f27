// src/components/AICodeAssistPanel.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { DiffEditor, loader as monacoLoader, Monaco } from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { Icon } from '@/components/common/Icon';
import type { AICodeAssistPanelProps as OriginalAICodeAssistPanelProps, KnowledgeTome, ProjectKnowledgeTome, AIInteractionHistoryItem, TaskStatus, AppSettings, GenerateCodeContext, ExplainCodeContext, GenerateDocContext, ReviewCodeContext, AnalyzeErrorContext, FileVersion, AILearningLog } from '@/types';
import { TASK_STATUS_ORDER } from '@/types';
import { SaveToKnowledgeModal } from '@/components/settings/SaveToKnowledgeModal';
import { PrismAsyncLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript';
import jsx from 'react-syntax-highlighter/dist/esm/languages/prism/jsx';
import typescript from 'react-syntax-highlighter/dist/esm/languages/prism/typescript';
import tsx from 'react-syntax-highlighter/dist/esm/languages/prism/tsx';
import python from 'react-syntax-highlighter/dist/esm/languages/prism/python';
import css from 'react-syntax-highlighter/dist/esm/languages/prism/css';
import json from 'react-syntax-highlighter/dist/esm/languages/prism/json';
import markdown from 'react-syntax-highlighter/dist/esm/languages/prism/markdown';
// Removed: import { useSettings } from '@/contexts/SettingsContext';

SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('jsx', jsx);
SyntaxHighlighter.registerLanguage('typescript', typescript);
SyntaxHighlighter.registerLanguage('tsx', tsx);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('css', css);
SyntaxHighlighter.registerLanguage('json', json);
SyntaxHighlighter.registerLanguage('markdown', markdown);

// Remove settings from AICodeAssistPanelProps as it will be passed from SourceCodeViewer or obtained via context if necessary
type AICodeAssistPanelProps = Omit<OriginalAICodeAssistPanelProps, 'settings'> & { settings: AppSettings };


const MAX_HISTORY_ITEMS = 10;

const isLikelyCode = (text: string): boolean => {
  if (!text) return false;
  const codeChars = ['{', '}', ';', '(', ')', '=', '>', '<', '=>', 'function', 'const', 'let', 'var', 'class', 'import', 'export', 'def', 'public', 'private', 'static', 'void', 'int', 'string'];
  const lines = text.split('\n');
  let codeLineCount = 0;
  for (const line of lines) {
    if (line.trim().startsWith('//') || line.trim().startsWith('#') || line.trim().startsWith('/*') || line.trim().endsWith('*/') || codeChars.some(char => line.includes(char))) {
      codeLineCount++;
    }
  }
  return codeLineCount > lines.length / 3 || text.includes('```');
};

const getLanguageFromLikelyCode = (text: string, filePath?: string | null): string | undefined => {
    if (filePath) {
        const ext = filePath.split('.').pop()?.toLowerCase();
        if (ext) {
             switch (ext) {
                case 'js': return 'javascript';
                case 'jsx': return 'javascript';
                case 'ts': return 'typescript';
                case 'tsx': return 'typescript';
                case 'py': return 'python';
                case 'css': return 'css';
                case 'json': return 'json';
                case 'md': return 'markdown';
                case 'html': return 'html';
                case 'xml': return 'xml';
                case 'yaml': case 'yml': return 'yaml';
                case 'java': return 'java';
                case 'cs': return 'csharp';
                case 'cpp': return 'cpp';
                case 'c': return 'c';
                case 'go': return 'go';
                case 'php': return 'php';
                case 'rb': return 'ruby';
                case 'rs': return 'rust';
                case 'sh': return 'shell';
                case 'sql': return 'sql';
                default: return ext;
            }
        }
    }
    if (!text) return undefined;
    const fenceMatch = text.match(/^```(\w+)/);
    if (fenceMatch && fenceMatch[1]) return fenceMatch[1].toLowerCase();
    // Fallback if no fence: guess based on content (simplified)
    if (text.includes("function") || text.includes("const ") || text.includes("let ")) return "javascript";
    if (text.includes("def ") && text.includes(":")) return "python";
    if (text.includes("<div") || text.includes("<html")) return "html";
    return undefined;
};

// Component implementation using `settings` from props (passed by SourceCodeViewer)
export const AICodeAssistPanel: React.FC<AICodeAssistPanelProps> = ({
  isVisible, title, content, isLoading, error, onClose,
  projectId, projectName, projectCategories = [], currentOperationDescription,
  onTriggerAIAssist, history, onHistoryChange, currentTaskForContext, onUpdateTaskStatus,
  currentFilePath, currentFileContent, onApplyNewCode, settings
}) => {
// ... rest of the component logic (which should be the same as provided in the prompt) ...
// This component will use the `settings` prop passed down from SourceCodeViewer,
// which in turn gets it from the `useOutletContext` that includes `settings` from `useSettings`.

  const [localInput, setLocalInput] = useState('');
  const [showKnowledgeModal, setShowKnowledgeModal] = useState(false);
  const [selectedKnowledgeItem, setSelectedKnowledgeItem] = useState<{title: string, content: string} | null>(null);

  const handleLocalSubmit = () => {
    if (localInput.trim() && onTriggerAIAssist) {
      // Heuristic: if local input looks like code, assume it's a context for 'generate'.
      // Otherwise, assume it's an instruction for 'generate'.
      const isInputCode = isLikelyCode(localInput);
      if (isInputCode) {
        onTriggerAIAssist('generate', "请基于以下代码片段进行操作或生成相关代码：", localInput);
      } else {
        onTriggerAIAssist('generate', localInput, currentFileContent || undefined);
      }
      setLocalInput('');
    }
  };

  const renderContent = () => {
    if (isLoading) return <div className="p-4 text-center"><Icon name="Loader" className="w-6 h-6 animate-spin text-tg-accent-secondary"/> AI 思考中...</div>;
    if (error) return <div className="p-4 text-red-400 bg-red-900/20 rounded-md"><Icon name="AlertTriangle" className="w-5 h-5 inline mr-1"/> 错误: {error}</div>;
    if (!content) return <div className="p-4 text-center text-tg-text-placeholder">AI 的回应将显示在此处。</div>;

    const language = getLanguageFromLikelyCode(content, currentFilePath);

    if (language) {
      return (
        <SyntaxHighlighter
          language={language}
          style={atomDark}
          customStyle={{ margin: 0, padding: '1em', overflowX: 'auto', backgroundColor: 'var(--color-bg-tertiary)' }}
          className="text-xs rounded-md custom-scrollbar"
          showLineNumbers
          lineNumberStyle={{ color: 'var(--color-text-placeholder)', fontSize: '0.75rem', minWidth: '2.5em' }}
        >
          {content.replace(/^```(?:\w*\n)?([\s\S]*?)\n?```$/, '$1').trim()}
        </SyntaxHighlighter>
      );
    }
    return <pre className="whitespace-pre-wrap p-3 text-xs text-tg-text-primary bg-tg-bg-tertiary rounded-md custom-scrollbar">{content}</pre>;
  };

  const handleSaveToKnowledge = async (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'> | Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt' | 'projectId'>, target: 'global' | 'project') => {
      if (target === 'global' && typeof window.api?.database?.addGlobalKnowledgeTome === 'function') {
        await window.api.database.addGlobalKnowledgeTome(tomeData as Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>);
      } else if (target === 'project' && projectId && typeof window.api?.database?.addProjectKnowledgeTome === 'function') {
        await window.api.database.addProjectKnowledgeTome(projectId, tomeData as Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>);
      }
      alert("内容已保存至知识库！");
      setShowKnowledgeModal(false);
  };
  
  const handleLogAISuccess = useCallback(async (interactionType: AIInteractionHistoryItem['type'], responseSummary: string, codeContext?: string) => {
    if (typeof window.api?.database?.addLearningLog !== 'function' || !projectId) return;
    const logEntry: Omit<AILearningLog, 'log_id' | 'timestamp'> = {
        ai_persona: 'xiaolan',
        task_type: `${interactionType}_success`,
        triggering_input_summary: `${title} - ${currentOperationDescription || interactionType}`,
        context_snapshot: JSON.stringify({filePath: currentFilePath, currentTaskId: currentTaskForContext?.task_id, codeContextLength: codeContext?.length || 0}),
        ai_processing_summary: `AI Code Assist panel operation: ${interactionType}`,
        ai_generated_output_summary: responseSummary.substring(0, 200),
        user_feedback_explicit: null, // Could be set later if user rates the output
        success_metric_value: 1.0, // Assume success if this is called
        file_path: currentFilePath,
    };
    await window.api.database.addLearningLog(logEntry);
  }, [projectId, title, currentOperationDescription, currentFilePath, currentTaskForContext?.task_id]);


  if (!isVisible) return null;

  return (
    <div className="absolute top-0 right-0 h-full w-1/3 lg:w-2/5 xl:w-1/3 bg-tg-bg-secondary border-l border-tg-border-primary shadow-2xl flex flex-col z-30 min-w-[320px]">
      <div className="p-3 border-b border-tg-border-primary flex justify-between items-center bg-tg-bg-tertiary flex-shrink-0">
        <h4 className="text-md font-semibold text-tg-accent-secondary flex items-center">
          <Icon name="Sparkles" className="w-5 h-5 mr-2 text-purple-400"/> {title}
        </h4>
        <button onClick={onClose} className="p-1 text-tg-text-placeholder hover:text-tg-text-primary rounded-full"><Icon name="X" className="w-5 h-5"/></button>
      </div>

      {currentOperationDescription && <p className="text-xs text-tg-text-secondary px-3 py-1 bg-tg-bg-primary">{currentOperationDescription}</p>}
      
      <div className="flex-grow overflow-y-auto p-3 custom-scrollbar">
        {renderContent()}
      </div>

      {content && !isLoading && !error && (
        <div className="p-2 border-t border-tg-border-primary flex-shrink-0 flex items-center justify-end space-x-2 bg-tg-bg-tertiary">
            <button 
                onClick={() => {setSelectedKnowledgeItem({title: `${title} - ${currentFilePath?.split('/').pop() || '片段'}`, content}); setShowKnowledgeModal(true);}}
                className="p-1.5 text-xs bg-teal-600 text-white rounded hover:bg-teal-700 flex items-center"
                title="保存到知识库"
            >
                <Icon name="Archive" className="w-4 h-4 mr-1"/> 保存
            </button>
            {onApplyNewCode && content.length < 50000 && ( // Add length check to avoid performance issues with very large diffs/applications
                 <button 
                    onClick={() => onApplyNewCode(content.replace(/^```(?:\w*\n)?([\s\S]*?)\n?```$/, '$1').trim())}
                    className="p-1.5 text-xs bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                    title="应用代码到编辑器"
                >
                    <Icon name="CheckCircle" className="w-4 h-4 mr-1"/> 应用
                </button>
            )}
        </div>
      )}
      
      <div className="p-3 border-t border-tg-border-primary flex-shrink-0 bg-tg-bg-tertiary">
        <textarea
          value={localInput}
          onChange={e => setLocalInput(e.target.value)}
          placeholder="输入指令或粘贴代码以继续..."
          rows={2}
          className="w-full p-2 bg-tg-bg-primary text-tg-text-primary border border-tg-border-primary rounded-md text-xs focus:border-tg-accent-secondary resize-y"
          disabled={isLoading}
        />
        <button 
            onClick={handleLocalSubmit} 
            disabled={isLoading || !localInput.trim()}
            className="mt-2 w-full px-3 py-1.5 text-xs bg-tg-accent-secondary text-white rounded hover:brightness-110 disabled:opacity-50 flex items-center justify-center"
        >
            {isLoading ? <Icon name="Loader2" className="w-4 h-4 mr-1 animate-spin"/> : <Icon name="Send" className="w-4 h-4 mr-1"/>}
            发送给小岚
        </button>
      </div>
       {showKnowledgeModal && selectedKnowledgeItem && (
        <SaveToKnowledgeModal
          isOpen={showKnowledgeModal}
          onClose={() => setShowKnowledgeModal(false)}
          initialTitle={selectedKnowledgeItem.title}
          initialContent={selectedKnowledgeItem.content}
          projectId={projectId}
          projectName={projectName}
          projectCategories={projectCategories}
          onSaveGlobal={async (data) => handleSaveToKnowledge(data, 'global')}
          onSaveProject={async (data) => handleSaveToKnowledge(data, 'project')}
        />
      )}
    </div>
  );
};

export default AICodeAssistPanel;