// electron/services/memoryPersistenceService.js
console.log('MEMORY_PERSISTENCE_SERVICE_JS: File execution started.');

// dbService will be passed as an argument or imported if circular dependencies are managed.

function parseActionCommandsInternal(text) {
    const commands = [];
    // Regex for [[COMMAND_TYPE:{"json_payload"}]]
    const genericCommandRegex = /\[\[([A-Z_]+):({.*?})\]\]/sg;
    let match;

    while ((match = genericCommandRegex.exec(text)) !== null) {
        try {
            const commandType = match[1];
            const payload = JSON.parse(match[2]);
            commands.push({ type: commandType, payload });
        } catch (e) {
            console.error(`MEMORY_PERSISTENCE_SERVICE: Failed to parse command JSON for type ${match[1]}:`, e, match[2]);
        }
    }
    
    const cleanedText = text.replace(genericCommandRegex, '').trim();
        
    return { cleanedText, commands };
}


export async function processResponseForActions(rawResponseText, projectId, dbService) {
    if (typeof rawResponseText !== 'string') {
        console.warn("MEMORY_PERSISTENCE_SERVICE: rawResponseText is not a string. Cannot process actions.");
        return { cleanedText: String(rawResponseText), actionResults: [] };
    }

    const { cleanedText, commands } = parseActionCommandsInternal(rawResponseText);
    const actionResults = [];

    if (commands.length > 0) {
        console.log(`MEMORY_PERSISTENCE_SERVICE: Found ${commands.length} actions to process.`);
        for (const command of commands) {
            try {
                switch (command.type) {
                    case 'STORE_MEMORY':
                        console.log("MEMORY_PERSISTENCE_SERVICE: Executing STORE_MEMORY command:", command.payload);
                        const memoryPayload = { // Ensure all required fields for addCoreMemory are present or defaulted
                            project_context_id: command.payload.project_context_id || projectId || null,
                            ...command.payload
                        };
                        const addedMemory = await dbService.addCoreMemory(memoryPayload);
                        actionResults.push({ type: 'STORE_MEMORY', success: !!addedMemory, detail: addedMemory || "Failed to add memory." });
                        break;
                    case 'DEVELOP_BODY_ZONE':
                        console.log("MEMORY_PERSISTENCE_SERVICE: Executing DEVELOP_BODY_ZONE command:", command.payload);
                        if (command.payload.zone && typeof command.payload.points === 'number') {
                            const devResult = await dbService.updateBodyDevelopment(command.payload.zone, command.payload.points);
                            actionResults.push({ type: 'DEVELOP_BODY_ZONE', success: devResult.success, detail: devResult });
                        } else {
                             actionResults.push({ type: 'DEVELOP_BODY_ZONE', success: false, detail: "Invalid payload for DEVELOP_BODY_ZONE." });
                        }
                        break;
                    case 'TRIGGER_ACHIEVEMENT':
                         console.log("MEMORY_PERSISTENCE_SERVICE: Executing TRIGGER_ACHIEVEMENT command:", command.payload);
                         if (command.payload.id) {
                            const achResult = await dbService.unlockAchievement('default_user', command.payload.id); 
                            actionResults.push({ type: 'TRIGGER_ACHIEVEMENT', success: achResult.success, detail: achResult });
                         } else {
                            actionResults.push({ type: 'TRIGGER_ACHIEVEMENT', success: false, detail: "Invalid payload for TRIGGER_ACHIEVEMENT, missing id." });
                         }
                        break;
                    default:
                        console.warn(`MEMORY_PERSISTENCE_SERVICE: Unknown command type '${command.type}'. Skipping.`);
                        actionResults.push({ type: command.type, success: false, detail: "Unknown command type." });
                }
            } catch(error) {
                 console.error(`MEMORY_PERSISTENCE_SERVICE: Error executing command ${command.type}:`, error);
                 actionResults.push({ type: command.type, success: false, detail: error.message });
            }
        }
    }
    return { cleanedText, actionResults };
}

console.log('MEMORY_PERSISTENCE_SERVICE_JS: File execution finished. Exports configured.');
