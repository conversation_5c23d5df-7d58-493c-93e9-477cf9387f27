
// electron/services/audioService.js
console.log('AUDIO_SERVICE_JS: File execution started.');

let currentAudioState = {
  currentSound: null,
  volume: 0.5,
  isLooping: false,
  isPlaying: false,
};

let mainWindowwebContents = null;
let settingsDbServiceRef = null; 

export function initializeAudioService(initialAppSettings, dbSettingsService) {
  settingsDbServiceRef = dbSettingsService;
  if (initialAppSettings && initialAppSettings.training_room_audio_state) {
    // Merge with defaults to ensure all keys are present
    currentAudioState = { 
        currentSound: null, volume: 0.5, isLooping: false, isPlaying: false, // defaults
        ...(typeof initialAppSettings.training_room_audio_state === 'string' 
            ? JSON.parse(initialAppSettings.training_room_audio_state) 
            : initialAppSettings.training_room_audio_state)
    };
    console.log('AUDIO_SERVICE: Initialized with audio state:', currentAudioState);
  } else {
    console.log('AUDIO_SERVICE: Initialized with default audio state:', currentAudioState);
  }
}

function persistAudioState() {
    if (settingsDbServiceRef && typeof settingsDbServiceRef.getSettings === 'function' && typeof settingsDbServiceRef.saveSettings === 'function') {
        settingsDbServiceRef.getSettings().then(currentSettings => {
            const updatedSettings = {
                ...currentSettings,
                training_room_audio_state: { ...currentAudioState } 
            };
            return settingsDbServiceRef.saveSettings(updatedSettings);
        }).then(result => {
            if (result.success) {
                console.log('AUDIO_SERVICE: Persisted audio state to DB:', currentAudioState);
            } else {
                console.error('AUDIO_SERVICE_ERROR: Failed to persist audio state to DB:', result.error);
            }
        }).catch(err => {
            console.error('AUDIO_SERVICE_ERROR: Exception while persisting audio state:', err);
        });
    } else {
        console.warn('AUDIO_SERVICE_WARN: settingsDbServiceRef not available for persisting audio state.');
    }
}


export function setMainWindowWebContents(webContents) {
  mainWindowwebContents = webContents;
  console.log("AUDIO_SERVICE: mainWindowwebContents has been set.");
  // Immediately notify renderer of current state when webContents is set
  if (mainWindowwebContents && !mainWindowwebContents.isDestroyed()) {
    mainWindowwebContents.send('audio:playbackStateChanged', { ...currentAudioState });
  }
}

function notifyPlaybackStateChanged() {
  if (mainWindowwebContents && !mainWindowwebContents.isDestroyed()) {
    mainWindowwebContents.send('audio:playbackStateChanged', { ...currentAudioState });
    console.log('AUDIO_SERVICE: Notified renderer of playback state change:', currentAudioState);
  } else {
    console.warn('AUDIO_SERVICE_WARN: mainWindowwebContents not available or destroyed, cannot notify renderer.');
  }
  persistAudioState();
}

export async function playSound(soundName, loop) {
  console.log(`AUDIO_SERVICE: Request to play sound: ${soundName}, loop: ${loop}`);
  currentAudioState.currentSound = soundName;
  currentAudioState.isLooping = loop;
  currentAudioState.isPlaying = true;
  notifyPlaybackStateChanged();
  return Promise.resolve();
}

export async function stopSound(soundName) {
  console.log(`AUDIO_SERVICE: Request to stop sound: ${soundName || 'current'}`);
  if ((soundName && currentAudioState.currentSound === soundName && currentAudioState.isPlaying) || 
      (!soundName && currentAudioState.isPlaying)) {
    currentAudioState.isPlaying = false;
    // currentAudioState.currentSound = null; // Decide if stopping also clears the sound, usually yes for explicit stop.
    // If soundName is provided, it implies stopping THAT sound. If it was current, it's no longer playing.
    // If soundName is not provided, it means stop whatever is playing.
    if (!soundName || (soundName && currentAudioState.currentSound === soundName)) {
        // For now, let's keep currentSound so UI can reflect what *was* playing
        // User can select another sound or hit play on the same one to resume.
        // Setting currentSound to null means the UI "forgets" the last selection.
    }
    notifyPlaybackStateChanged();
  } else if (soundName && currentAudioState.currentSound !== soundName) {
    console.log(`AUDIO_SERVICE: Stop request for ${soundName} ignored, current sound is ${currentAudioState.currentSound}.`);
  } else if (!currentAudioState.isPlaying) {
    console.log(`AUDIO_SERVICE: Stop request ignored, no sound is currently playing.`);
  }
  return Promise.resolve();
}

export async function setVolume(volume) {
  console.log(`AUDIO_SERVICE: Request to set master volume to ${volume}`);
  currentAudioState.volume = Math.max(0, Math.min(1, volume)); 
  notifyPlaybackStateChanged();
  return Promise.resolve();
}

export async function getPlaybackState() {
  console.log('AUDIO_SERVICE: Request to get playback state. Returning:', currentAudioState);
  return Promise.resolve({ ...currentAudioState });
}

console.log('AUDIO_SERVICE_JS: File execution finished. Exports configured.');