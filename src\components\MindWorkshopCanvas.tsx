import React, { useState, useEffect, useCallback, useRef } from 'react';
import Draggable from 'react-draggable';
import type { MindNode, MindConnection } from '@/types';
import { Icon } from '@/components/common/Icon'; 

interface MindWorkshopCanvasProps {
  initialNodes: MindNode[];
  initialConnections: MindConnection[];
  onSave: (nodes: MindNode[], connections: MindConnection[]) => void;
}

const NODE_WIDTH = 160; 
const NODE_HEIGHT = 70; 

interface DraggableNodeItemProps {
  node: MindNode;
  onNodeDragStop: (data: { x: number; y: number }) => void;
  isSelected: boolean;
  isConnectingFrom: boolean;
  onNodeDivClick: (event: React.MouseEvent) => void;
  onTextChange: (newText: string) => void;
  onConnectButtonClick: (event: React.MouseEvent) => void;
  onTextAreaFocus: () => void;
}

const DraggableNodeItem: React.FC<DraggableNodeItemProps> = ({
  node,
  onNodeDragStop,
  isSelected,
  isConnectingFrom,
  onNodeDivClick,
  onTextChange,
  onConnectButtonClick,
  onTextAreaFocus,
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);

  return (
    <Draggable
      nodeRef={nodeRef}
      position={{ x: node.x, y: node.y }}
      onStop={(e, data) => onNodeDragStop(data)}
      bounds="parent"
      cancel=".nodrag"
    >
      <div
        ref={nodeRef}
        className={`absolute p-3 rounded-md shadow-lg cursor-grab flex flex-col justify-center items-center transition-all duration-150 ease-in-out bg-tg-bg-tertiary border border-tg-border-primary`}
        style={{
          width: node.width || NODE_WIDTH,
          height: node.height || NODE_HEIGHT,
          boxSizing: 'border-box',
          boxShadow: isSelected ? `0 0 0 2px var(--color-accent-primary), var(--shadow-md)` : 'var(--shadow-md)',
          outline: isConnectingFrom ? '2px dashed var(--color-success)' : 'none',
        }}
        onClick={onNodeDivClick}
        role="treeitem"
        aria-label={`思绪节点: ${node.text}`}
        aria-selected={isSelected}
      >
        <textarea
          value={node.text}
          onChange={(e) => onTextChange(e.target.value)}
          className="nodrag w-full h-full bg-transparent text-sm p-1 outline-none resize-none text-center flex-grow text-tg-text-primary"
          placeholder="节点内容..."
          style={{ caretColor: 'var(--color-accent-primary)' }}
          onClick={(e) => e.stopPropagation()} // Prevent canvas click when clicking textarea
          onFocus={onTextAreaFocus}
          aria-label="编辑节点内容"
        />
        <button
          onClick={onConnectButtonClick}
          className="nodrag absolute -top-2.5 -right-2.5 w-6 h-6 rounded-full flex items-center justify-center shadow-md transition-transform hover:scale-110 bg-tg-success text-white"
          title="从此节点开始连接"
          aria-label="从此节点开始连接"
        >
          <Icon name="Share2" className="w-3 h-3" /> 
        </button>
      </div>
    </Draggable>
  );
};

export const MindWorkshopCanvas: React.FC<MindWorkshopCanvasProps> = ({ initialNodes, initialConnections, onSave }) => {
  const [nodes, setNodes] = useState<MindNode[]>(() =>
    initialNodes ? initialNodes.map(node => ({
      ...node,
      x: typeof node.x === 'number' && !isNaN(node.x) ? node.x : 0,
      y: typeof node.y === 'number' && !isNaN(node.y) ? node.y : 0,
      width: node.width || NODE_WIDTH,
      height: node.height || NODE_HEIGHT
    })) : []
  );
  const [connections, setConnections] = useState<MindConnection[]>(() =>
    initialConnections ? initialConnections.map(conn => ({ ...conn })) : []
  );
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | null>(null);
  const [connectingFromNodeId, setConnectingFromNodeId] = useState<string | null>(null);
  const [tempMousePosition, setTempMousePosition] = useState<{ x: number, y: number } | null>(null);

  const canvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setNodes(initialNodes ? initialNodes.map(node => ({
      ...node,
      x: typeof node.x === 'number' && !isNaN(node.x) ? node.x : 0,
      y: typeof node.y === 'number' && !isNaN(node.y) ? node.y : 0,
      width: node.width || NODE_WIDTH,
      height: node.height || NODE_HEIGHT
    })) : []);
    setConnections(initialConnections ? initialConnections.map(conn => ({ ...conn })) : []);
  }, [initialNodes, initialConnections]);
  

  useEffect(() => {
    const handler = setTimeout(() => {
      const validNodesToSave = nodes.filter(n => 
        typeof n.x === 'number' && !isNaN(n.x) && 
        typeof n.y === 'number' && !isNaN(n.y)
      );
      
      const isCurrentlyEmpty = validNodesToSave.length === 0 && connections.length === 0;
      const wasInitiallyEmpty = initialNodes.length === 0 && initialConnections.length === 0;

      if (!isCurrentlyEmpty || (isCurrentlyEmpty && !wasInitiallyEmpty)) {
        if (typeof window.api?.database?.updateProjectMindMap === 'function') {
            onSave(validNodesToSave, connections);
        } else {
            console.warn("[MindWorkshopCanvas] The API 'window.api.database.updateProjectMindMap' is not available. Save operation skipped.");
        }
      }
    }, 500); 
    return () => clearTimeout(handler);
  }, [nodes, connections, onSave, initialNodes, initialConnections]);


  const addNode = () => {
    const newNode: MindNode = {
      id: crypto.randomUUID(),
      text: '新思路节点',
      x: 50 + (nodes.length % 6) * (NODE_WIDTH + 40),
      y: 50 + Math.floor(nodes.length / 6) * (NODE_HEIGHT + 40),
      width: NODE_WIDTH,
      height: NODE_HEIGHT,
    };
    setNodes(prevNodes => [...prevNodes, newNode]);
  };

  const updateNodeText = (nodeId: string, text: string) => {
    setNodes(prevNodes => prevNodes.map(n => (n.id === nodeId ? { ...n, text } : n)));
  };

  const handleNodeDrag = (nodeId: string, data: { x: number, y: number }) => {
    setNodes(prevNodes => prevNodes.map(n => (n.id === nodeId ? { ...n, x: data.x, y: data.y } : n)));
  };

  const deleteSelected = () => {
    if (selectedNodeId) {
      setNodes(prevNodes => prevNodes.filter(n => n.id !== selectedNodeId));
      setConnections(prevConns => prevConns.filter(c => c.fromNodeId !== selectedNodeId && c.toNodeId !== selectedNodeId));
      setSelectedNodeId(null);
    }
    if (selectedConnectionId) {
      setConnections(prevConns => prevConns.filter(c => c.id !== selectedConnectionId));
      setSelectedConnectionId(null);
    }
  };

  const startConnection = (nodeId: string) => {
    setConnectingFromNodeId(nodeId);
    setSelectedNodeId(null);
    setSelectedConnectionId(null);
  };

  const completeConnection = (toNodeId: string) => {
    if (connectingFromNodeId && connectingFromNodeId !== toNodeId) {
      const existingConnection = connections.find(
        c => (c.fromNodeId === connectingFromNodeId && c.toNodeId === toNodeId) || (c.fromNodeId === toNodeId && c.toNodeId === connectingFromNodeId)
      );
      if (!existingConnection) {
        const newConnection: MindConnection = {
          id: crypto.randomUUID(),
          fromNodeId: connectingFromNodeId,
          toNodeId,
        };
        setConnections(prevConns => [...prevConns, newConnection]);
      }
    }
    setConnectingFromNodeId(null);
    setTempMousePosition(null);
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setSelectedNodeId(null);
      setSelectedConnectionId(null);
      if (connectingFromNodeId) {
        setConnectingFromNodeId(null);
        setTempMousePosition(null);
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (connectingFromNodeId && canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      setTempMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const getEdgePoint = (node: MindNode, _targetX: number, _targetY: number): { x: number, y: number } => {
    const nodeWidth = node.width || NODE_WIDTH;
    const nodeHeight = node.height || NODE_HEIGHT;
    const nodeX = typeof node.x === 'number' && !isNaN(node.x) ? node.x : 0;
    const nodeY = typeof node.y === 'number' && !isNaN(node.y) ? node.y : 0;
    const centerX = nodeX + nodeWidth / 2;
    const centerY = nodeY + nodeHeight / 2;
    return { x: centerX, y: centerY };
  };

  const calculateArrowPath = (fromNode: MindNode, toNode: MindNode): string => {
    const p1 = getEdgePoint(fromNode, toNode.x + (toNode.width || NODE_WIDTH) / 2, toNode.y + (toNode.height || NODE_HEIGHT) / 2);
    const p2 = getEdgePoint(toNode, fromNode.x + (fromNode.width || NODE_WIDTH) / 2, fromNode.y + (fromNode.height || NODE_HEIGHT) / 2);
    return `M${p1.x},${p1.y} L${p2.x},${p2.y}`;
  };

  const calculateTempArrowPath = (fromNode: MindNode, toPos: { x: number, y: number }): string => {
    const p1 = getEdgePoint(fromNode, toPos.x, toPos.y);
    return `M${p1.x},${p1.y} L${toPos.x},${toPos.y}`;
  }

  const getButtonClasses = (variant: 'primary' | 'danger' | 'warning') => {
    let base = "px-3 py-1.5 rounded-md text-sm flex items-center transition-colors";
    if (variant === 'primary') return `${base} bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover`;
    if (variant === 'danger') return `${base} bg-tg-danger text-white hover:bg-tg-danger-hover`;
    if (variant === 'warning') return `${base} bg-tg-warning text-tg-bg-primary hover:brightness-110`;
    return base;
  };

  return (
    <div className="flex-grow flex flex-col rounded-lg shadow-inner h-full relative overflow-hidden bg-tg-bg-secondary">
      <div className="p-2.5 flex items-center space-x-2 sticky top-0 z-10 bg-tg-bg-tertiary border-b border-tg-border-primary">
        <button
          onClick={addNode}
          className={getButtonClasses('primary')}
        >
          <Icon name="PlusCircle" className="w-5 h-5 mr-1.5" /> 添加节点 
        </button>
        {(selectedNodeId || selectedConnectionId) && (
          <button
            onClick={deleteSelected}
            className={getButtonClasses('danger')}
          >
            <Icon name="Trash2" className="w-5 h-5 mr-1.5" /> 删除选中 
          </button>
        )}
        {connectingFromNodeId && (
            <button
                onClick={() => { setConnectingFromNodeId(null); setTempMousePosition(null); }}
                className={getButtonClasses('warning') + " ml-auto"}
            >
                 <Icon name="X" className="w-5 h-5 mr-1.5"/> 取消连接 
            </button>
        )}
      </div>
      <div
        ref={canvasRef}
        className="flex-grow relative overflow-hidden cursor-crosshair bg-tg-bg-primary"
        onClick={handleCanvasClick}
        onMouseMove={handleMouseMove}
      >
        <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <defs>
            <marker
              id="arrowhead"
              markerWidth="8"
              markerHeight="6"
              refX="7" 
              refY="3"
              orient="auto"
              markerUnits="strokeWidth"
            >
              <path d="M0,0 L0,6 L8,3 z" fill="var(--color-border-interactive)" />
            </marker>
          </defs>
          {connections.map(conn => {
            const fromNode = nodes.find(n => n.id === conn.fromNodeId);
            const toNode = nodes.find(n => n.id === conn.toNodeId);
            if (!fromNode || !toNode) return null;
            return (
              <path
                key={conn.id}
                d={calculateArrowPath(fromNode, toNode)}
                stroke={selectedConnectionId === conn.id ? "var(--color-accent-primary)" : "var(--color-border-interactive)"}
                strokeWidth="2"
                fill="none"
                markerEnd="url(#arrowhead)"
                className="pointer-events-auto cursor-pointer"
                onClick={(e: React.MouseEvent<SVGPathElement>) => { e.stopPropagation(); setSelectedConnectionId(conn.id); setSelectedNodeId(null); }}
              />
            );
          })}
          {connectingFromNodeId && tempMousePosition && (() => {
            const fromNode = nodes.find(n => n.id === connectingFromNodeId);
            if (!fromNode) return null;
            return (
                <path
                d={calculateTempArrowPath(fromNode, tempMousePosition)}
                stroke="var(--color-success)"
                strokeWidth="2"
                strokeDasharray="4 2"
                fill="none"
                markerEnd="url(#arrowhead)" 
              />
            );
          })()}
        </svg>
        {nodes.map(node => (
          <DraggableNodeItem
            key={node.id}
            node={node}
            onNodeDragStop={(data) => handleNodeDrag(node.id, data)}
            isSelected={selectedNodeId === node.id}
            isConnectingFrom={connectingFromNodeId === node.id}
            onNodeDivClick={(e) => {
              e.stopPropagation();
              if (connectingFromNodeId && connectingFromNodeId !== node.id) {
                completeConnection(node.id);
              } else {
                setSelectedNodeId(node.id);
                setSelectedConnectionId(null);
                if (connectingFromNodeId === node.id) { 
                    setConnectingFromNodeId(null);
                    setTempMousePosition(null);
                }
              }
            }}
            onTextChange={(newText) => updateNodeText(node.id, newText)}
            onConnectButtonClick={(e) => { e.stopPropagation(); startConnection(node.id); }}
            onTextAreaFocus={() => {setSelectedNodeId(node.id); setSelectedConnectionId(null);}}
          />
        ))}
      </div>
    </div>
  );
};
