// src/utils/assetImporter.ts
// 资产批量导入工具

import { ALL_KIRISHIMA_REINA_ASSETS } from '@/data/kirishima_reina_assets';
import { ALL_LINLUO_NATURAL_ASSETS } from '@/data/linluo_natural_assets';

export class AssetImporter {
  /**
   * 导入雾岛玲奈角色卡及相关资产
   */
  static async importKirishimaReinaAssets(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const results = {
        roleCard: null as any,
        props: [] as any[],
        costumes: [] as any[],
        poses: [] as any[]
      };

      // 1. 导入角色卡到数据库
      console.log('开始导入雾岛玲奈角色卡...');

      // 先检查是否已存在相同名称的角色卡
      const existingRoleCards = await window.api.cms.getRolePlayingCards();
      const existingRoleCard = existingRoleCards.find(card =>
        card.name === ALL_KIRISHIMA_REINA_ASSETS.roleCard.name
      );

      if (existingRoleCard) {
        console.log('角色卡已存在，跳过导入:', existingRoleCard.name);
        results.roleCard = { id: existingRoleCard.id, status: 'already_exists' };
      } else {
        try {
          const roleCardData = {
            ...ALL_KIRISHIMA_REINA_ASSETS.roleCard,
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          results.roleCard = await window.api.cms.addRolePlayingCard(roleCardData);
          console.log('角色卡导入成功:', results.roleCard);
        } catch (error) {
          console.error('角色卡导入失败:', error);
          throw error;
        }
      }

      // 2. 导入道具到 CMS 数据库
      console.log('开始导入道具资产...');
      const propResults = [];
      for (const prop of ALL_KIRISHIMA_REINA_ASSETS.props) {
        try {
          const propData = {
            ...prop,
            type: 'prop', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的道具
          const existingProps = await window.api.cms.getCMSItems('props');
          const existingProp = existingProps.find(p => p.id === prop.id);

          if (existingProp) {
            console.log('道具已存在，跳过:', prop.name);
            propResults.push({ id: prop.id, name: prop.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('props', propData);
          propResults.push({ id: prop.id, name: prop.name, status: 'success' });
          console.log('道具导入成功:', prop.name);
        } catch (error) {
          console.error('道具导入失败:', prop.name, error);
          propResults.push({ id: prop.id, name: prop.name, status: 'error', error: error.message });
        }
      }

      // 3. 导入服装到 CMS 数据库
      console.log('开始导入服装资产...');
      const costumeResults = [];
      for (const costume of ALL_KIRISHIMA_REINA_ASSETS.costumes) {
        try {
          const costumeData = {
            ...costume,
            type: 'costume', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的服装
          const existingCostumes = await window.api.cms.getCMSItems('costumes');
          const existingCostume = existingCostumes.find(c => c.id === costume.id);

          if (existingCostume) {
            console.log('服装已存在，跳过:', costume.name);
            costumeResults.push({ id: costume.id, name: costume.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('costumes', costumeData);
          costumeResults.push({ id: costume.id, name: costume.name, status: 'success' });
          console.log('服装导入成功:', costume.name);
        } catch (error) {
          console.error('服装导入失败:', costume.name, error);
          costumeResults.push({ id: costume.id, name: costume.name, status: 'error', error: error.message });
        }
      }

      // 4. 导入姿势到 CMS 数据库
      console.log('开始导入姿势资产...');
      const poseResults = [];
      for (const pose of ALL_KIRISHIMA_REINA_ASSETS.poses) {
        try {
          const poseData = {
            ...pose,
            type: 'pose', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的姿势
          const existingPoses = await window.api.cms.getCMSItems('poses');
          const existingPose = existingPoses.find(p => p.id === pose.id);

          if (existingPose) {
            console.log('姿势已存在，跳过:', pose.name);
            poseResults.push({ id: pose.id, name: pose.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('poses', poseData);
          poseResults.push({ id: pose.id, name: pose.name, status: 'success' });
          console.log('姿势导入成功:', pose.name);
        } catch (error) {
          console.error('姿势导入失败:', pose.name, error);
          poseResults.push({ id: pose.id, name: pose.name, status: 'error', error: error.message });
        }
      }

      results.props = propResults;
      results.costumes = costumeResults;
      results.poses = poseResults;

      // 统计导入结果
      const successCounts = {
        props: results.props.filter(p => p.status === 'success' || p.status === 'already_exists').length,
        costumes: results.costumes.filter(c => c.status === 'success' || c.status === 'already_exists').length,
        poses: results.poses.filter(p => p.status === 'success' || p.status === 'already_exists').length
      };

      const totalCounts = {
        props: ALL_KIRISHIMA_REINA_ASSETS.props.length,
        costumes: ALL_KIRISHIMA_REINA_ASSETS.costumes.length,
        poses: ALL_KIRISHIMA_REINA_ASSETS.poses.length
      };

      const message = `雾岛玲奈资产导入完成！
角色卡: ${results.roleCard ? '成功' : '失败'}
道具: ${successCounts.props}/${totalCounts.props} 成功
服装: ${successCounts.costumes}/${totalCounts.costumes} 成功
姿势: ${successCounts.poses}/${totalCounts.poses} 成功

所有资产已保存到对应的资产库中，可在绝对领域训练室中直接使用。`;

      return {
        success: true,
        message,
        details: results
      };

    } catch (error) {
      console.error('资产导入过程中发生错误:', error);
      return {
        success: false,
        message: `导入失败: ${error.message}`,
        details: error
      };
    }
  }

  /**
   * 检查雾岛玲奈资产是否已存在
   */
  static async checkKirishimaReinaAssetsExist(): Promise<{
    roleCard: boolean;
    assetPack: boolean;
  }> {
    try {
      // 检查角色卡
      const existingRoleCards = await window.api.cms.getRolePlayingCards();
      const roleCardExists = existingRoleCards.some(card => card.id === 'SC001');

      // 检查资产包（通过检查是否有相关资产）
      const allAssets = await window.api.assets.getLoadedAssets();
      const hasKirishimaAssets = [
        ...ALL_KIRISHIMA_REINA_ASSETS.props,
        ...ALL_KIRISHIMA_REINA_ASSETS.costumes,
        ...ALL_KIRISHIMA_REINA_ASSETS.poses
      ].some(asset => {
        const assetTypeKey = `${asset.type}s`;
        return allAssets[assetTypeKey]?.some(existing => existing.id === asset.id);
      });

      return {
        roleCard: roleCardExists,
        assetPack: hasKirishimaAssets
      };

    } catch (error) {
      console.error('检查资产存在性时发生错误:', error);
      return {
        roleCard: false,
        assetPack: false
      };
    }
  }

  /**
   * 删除雾岛玲奈相关资产（用于重新导入）
   */
  static async removeKirishimaReinaAssets(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // 删除角色卡
      try {
        await window.api.cms.deleteRolePlayingCard('SC001');
        console.log('角色卡删除成功');
      } catch (error) {
        console.log('角色卡删除失败或不存在:', error.message);
      }

      // 删除资产包文件
      try {
        await window.api.assets.deleteAssetPack('kirishima_reina_pack.yaml');
        console.log('资产包删除成功');

        // 刷新资产缓存
        await window.api.assets.refreshAssetPacks();
        console.log('资产缓存刷新完成');
      } catch (error) {
        console.log('资产包删除失败或不存在:', error.message);
      }

      return {
        success: true,
        message: '雾岛玲奈相关资产删除完成'
      };

    } catch (error) {
      console.error('删除资产时发生错误:', error);
      return {
        success: false,
        message: `删除失败: ${error.message}`
      };
    }
  }

  /**
   * 导入林珞自然骚魂版角色卡及相关资产
   */
  static async importLinLuoNaturalAssets(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const results = {
        roleCard: null as any,
        props: [] as any[],
        costumes: [] as any[],
        poses: [] as any[]
      };

      // 1. 导入角色卡到数据库
      console.log('开始导入林珞自然骚魂版角色卡...');

      // 先检查是否已存在相同名称的角色卡
      const existingRoleCards = await window.api.cms.getRolePlayingCards();
      const existingRoleCard = existingRoleCards.find(card =>
        card.name === ALL_LINLUO_NATURAL_ASSETS.roleCard.name
      );

      if (existingRoleCard) {
        console.log('角色卡已存在，跳过导入:', existingRoleCard.name);
        results.roleCard = { id: existingRoleCard.id, status: 'already_exists' };
      } else {
        try {
          const roleCardData = {
            ...ALL_LINLUO_NATURAL_ASSETS.roleCard,
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          results.roleCard = await window.api.cms.addRolePlayingCard(roleCardData);
          console.log('角色卡导入成功:', results.roleCard);
        } catch (error) {
          console.error('角色卡导入失败:', error);
          throw error;
        }
      }

      // 2. 导入道具到 CMS 数据库
      console.log('开始导入道具资产...');
      const propResults = [];
      for (const prop of ALL_LINLUO_NATURAL_ASSETS.props) {
        try {
          const propData = {
            ...prop,
            type: 'prop', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的道具
          const existingProps = await window.api.cms.getCMSItems('props');
          const existingProp = existingProps.find(p => p.id === prop.id);

          if (existingProp) {
            console.log('道具已存在，跳过:', prop.name);
            propResults.push({ id: prop.id, name: prop.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('props', propData);
          propResults.push({ id: prop.id, name: prop.name, status: 'success' });
          console.log('道具导入成功:', prop.name);
        } catch (error) {
          console.error('道具导入失败:', prop.name, error);
          propResults.push({ id: prop.id, name: prop.name, status: 'error', error: error.message });
        }
      }

      // 3. 导入服装到 CMS 数据库
      console.log('开始导入服装资产...');
      const costumeResults = [];
      for (const costume of ALL_LINLUO_NATURAL_ASSETS.costumes) {
        try {
          const costumeData = {
            ...costume,
            type: 'costume', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的服装
          const existingCostumes = await window.api.cms.getCMSItems('costumes');
          const existingCostume = existingCostumes.find(c => c.id === costume.id);

          if (existingCostume) {
            console.log('服装已存在，跳过:', costume.name);
            costumeResults.push({ id: costume.id, name: costume.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('costumes', costumeData);
          costumeResults.push({ id: costume.id, name: costume.name, status: 'success' });
          console.log('服装导入成功:', costume.name);
        } catch (error) {
          console.error('服装导入失败:', costume.name, error);
          costumeResults.push({ id: costume.id, name: costume.name, status: 'error', error: error.message });
        }
      }

      // 4. 导入姿势到 CMS 数据库
      console.log('开始导入姿势资产...');
      const poseResults = [];
      for (const pose of ALL_LINLUO_NATURAL_ASSETS.poses) {
        try {
          const poseData = {
            ...pose,
            type: 'pose', // 确保设置正确的 type 字段
            createdAt: new Date().toISOString(),
            lastModifiedAt: new Date().toISOString()
          };

          // 先检查是否已存在相同 ID 的姿势
          const existingPoses = await window.api.cms.getCMSItems('poses');
          const existingPose = existingPoses.find(p => p.id === pose.id);

          if (existingPose) {
            console.log('姿势已存在，跳过:', pose.name);
            poseResults.push({ id: pose.id, name: pose.name, status: 'already_exists' });
            continue;
          }

          const result = await window.api.cms.addCMSItem('poses', poseData);
          poseResults.push({ id: pose.id, name: pose.name, status: 'success' });
          console.log('姿势导入成功:', pose.name);
        } catch (error) {
          console.error('姿势导入失败:', pose.name, error);
          poseResults.push({ id: pose.id, name: pose.name, status: 'error', error: error.message });
        }
      }

      results.props = propResults;
      results.costumes = costumeResults;
      results.poses = poseResults;

      // 统计导入结果
      const successCounts = {
        props: results.props.filter(p => p.status === 'success' || p.status === 'already_exists').length,
        costumes: results.costumes.filter(c => c.status === 'success' || c.status === 'already_exists').length,
        poses: results.poses.filter(p => p.status === 'success' || p.status === 'already_exists').length
      };

      const totalCounts = {
        props: ALL_LINLUO_NATURAL_ASSETS.props.length,
        costumes: ALL_LINLUO_NATURAL_ASSETS.costumes.length,
        poses: ALL_LINLUO_NATURAL_ASSETS.poses.length
      };

      const message = `林珞自然骚魂版资产导入完成！
角色卡: ${results.roleCard ? '成功' : '失败'}
道具: ${successCounts.props}/${totalCounts.props} 成功
服装: ${successCounts.costumes}/${totalCounts.costumes} 成功
姿势: ${successCounts.poses}/${totalCounts.poses} 成功

所有资产已保存到对应的资产库中，可在绝对领域训练室中直接使用。`;

      return {
        success: true,
        message,
        details: results
      };

    } catch (error) {
      console.error('资产导入过程中发生错误:', error);
      return {
        success: false,
        message: `导入失败: ${error.message}`,
        details: error
      };
    }
  }
}
