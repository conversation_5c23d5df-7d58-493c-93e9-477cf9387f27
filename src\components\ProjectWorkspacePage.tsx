
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useParams, useNavigate, Outlet, useLocation, useOutletContext } from 'react-router-dom';
import type { Project, NoteItem, AppSettings, MindNode, MindConnection, ImportanceLevel, ChatMessage, ProjectKnowledgeTome, GlobalQuickCommandItem, SummarizeAndReplaceResult, AITaskStatus, DevelopmentTask, AIResponseWithStatus, CoreMemory, Task, TaskStatus, ActiveMainTabType, TaskCreationData, DevelopmentTaskCreationPayload, ProjectWorkspacePageOutletContext as AppProvidedContext, Character, Post, Assignment } from '@/types';
import { WisdomPouchType } from '@/types';
import { Icon } from '@/components/common/Icon';
import { parseMessageTextForTheme } from '@/chatConstants';
import type { Content } from '@google/genai';
import { CreateTaskFromChatMessageModal } from '@/components/chat/CreateTaskFromChatMessageModal';
import { ProjectSidebar } from '@/components/ProjectSidebar';
import { ActionPlanModal } from '@/components/ActionPlanModal';
import { WisdomPouchModal } from '@/components/WisdomPouchModal';
import { PlaceholderPanel } from '@/components/PlaceholderPanel';


const CONVERSATION_SUMMARY_THRESHOLD = 50;
const MESSAGES_TO_SUMMARIZE_COUNT = 30;

const getBaseName = (filePath: string): string => {
  if (!filePath) return '';
  const parts = filePath.replace(/\\\\/g, '/').split('/');
  return parts.pop() || '';
};


const stripHtmlBasic = (html: string): string => {
  if (typeof DOMParser === 'undefined') {
    return html.replace(/<[^>]*>?/gm, '');
  }
  try {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || "";
  } catch (e) {
    console.error("Error in stripHtmlBasic:", e);
    return html;
  }
};


export const ProjectWorkspacePage: React.FC = (): React.ReactElement | null => {
  const appProvidedContext = useOutletContext<AppProvidedContext>();
  
  if (!appProvidedContext) {
    return <div className="flex items-center justify-center h-full text-tg-text-primary bg-tg-bg-primary">项目上下文加载失败...</div>;
  }
  
  const {
    projects, // All projects from App.tsx state
    settings, 
    globalQuickCommands, 
    isAIServiceReady, 
    onAddCoreMemory, 
    onUpdateTaskStatusInApp,
    onAideProjectImported, 
    onSaveNewChatMessage: appOnSaveNewChatMessage, 
    onUpdateExistingChatMessage: appOnUpdateExistingChatMessage,
    onMessagesReplacedBySummary: appOnMessagesReplacedBySummary, 
    chatAreaRef, 
    addNoteToProject: appAddNoteToProject, 
    updateNoteInProject: appUpdateNoteInProject, 
    deleteNoteFromProject: appDeleteNoteFromProject, 
    updateProjectMindMap: appUpdateProjectMindMap, 
    addProjectKnowledgeTome: appAddProjectKnowledgeTome, 
    updateProjectKnowledgeTome: appUpdateProjectKnowledgeTome, 
    deleteProjectKnowledgeTome: appDeleteProjectKnowledgeTome, 
    addProjectKnowledgeCategory: appAddProjectKnowledgeCategory, 
    removeProjectKnowledgeCategory: appRemoveProjectKnowledgeCategory, 
    updateProjectGeneral: appUpdateProjectGeneral, 
    taskToOpenInEditor, 
    onEditorOpenedTask, 
    onStartCraftingFromTask,
    aiTaskStatus,
    allCharacters,
    allPosts,
    allAssignments,
  } = appProvidedContext;

  const { projectId } = useParams<{ projectId: string; }>();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Project specific state, derived from projects list
  const [project, setProject] = useState<Project | null>(null);
  const [isLoadingInitial, setIsLoadingInitial] = useState(true); // Initial loading state for this page

  const summaryInProgressRef = useRef(false);
  const [showActionPlanModal, setShowActionPlanModal] = useState(false);
  const [actionPlanTasks, setActionPlanTasks] = useState<Partial<Omit<TaskCreationData, 'project_id'>>[]>([]);
  const [isApprovingActionPlan, setIsApprovingActionPlan] = useState(false);
  const [actionPlanError, setActionPlanError] = useState<string | null>(null);

  const [isCreateTaskModalOpen, setIsCreateTaskModalOpen] = useState(false);
  const [taskModalInitialData, setTaskModalInitialData] = useState<{ title: string; description?: string, originalMessageId?: string }>({ title: '' });
  const [isWisdomPouchModalOpen, setIsWisdomPouchModalOpen] = useState(false);

  const apiKeyIsAvailable = !!settings.apiKey && settings.apiKey.trim() !== "";

  // Effect to find and set the current project based on projectId from URL
  useEffect(() => {
    if (projectId) {
      const currentProjectFromList = projects.find(p => p.id === projectId);
      if (currentProjectFromList) {
        setProject(currentProjectFromList);
        setIsLoadingInitial(false);
      } else {
        console.warn(`ProjectWorkspacePage: Project with ID ${projectId} not found in provided projects list.`);
        navigate('/'); // Or to a "not found" page
        setIsLoadingInitial(false);
      }
    } else {
      // This should not happen if routes are set up correctly, but handle just in case
      navigate('/');
      setIsLoadingInitial(false);
    }
  }, [projectId, projects, navigate]);

  useEffect(() => {
    if (taskToOpenInEditor && projectId === taskToOpenInEditor.project_id) {
        navigate(`/project/${projectId}/cockpit/${taskToOpenInEditor.task_id}`);
        onEditorOpenedTask();
    }
  }, [taskToOpenInEditor, projectId, onEditorOpenedTask, navigate]);

  // Automatic conversation summarization effect
  useEffect(() => {
    const attemptSummarization = async () => {
        if (!project || !project.id || summaryInProgressRef.current || !isAIServiceReady || !apiKeyIsAvailable || aiTaskStatus !== 'idle') return;
        const nonSummaryMessages = (project.discussionMessages || []).filter(m => m.sender !== 'system_summary');
        if (nonSummaryMessages.length <= CONVERSATION_SUMMARY_THRESHOLD) return;
        
        summaryInProgressRef.current = true; 
        if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('xiaolan_thinking');
        
        const oldestMessagesToSummarize = nonSummaryMessages.slice(0, MESSAGES_TO_SUMMARIZE_COUNT);
        if (oldestMessagesToSummarize.length === 0 || (oldestMessagesToSummarize.length < MESSAGES_TO_SUMMARIZE_COUNT / 2 && nonSummaryMessages.length < CONVERSATION_SUMMARY_THRESHOLD * 1.5)) { 
            summaryInProgressRef.current = false; 
            if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); 
            return; 
        }
        
        if (typeof window.api?.ai?.summarizeConversation !== 'function' || typeof window.api?.database?.summarizeAndReplaceMessages !== 'function') { 
            summaryInProgressRef.current = false; 
            if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); 
            return; 
        }
        
        try {
            const summaryText = await window.api.ai.summarizeConversation(oldestMessagesToSummarize);
            if (summaryText.startsWith("系统错误:") || summaryText.startsWith("系统提示:")) {
                await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '摘要服务错误', text: `自动摘要失败: ${summaryText}`, timestamp: new Date().toISOString(), }); 
            } else {
                const summaryMessage: ChatMessage = { id: crypto.randomUUID(), sender: 'system_summary', senderName: '对话概要', text: summaryText, timestamp: new Date().toISOString(), };
                const replacedIds = oldestMessagesToSummarize.map(m => m.id);
                const result: SummarizeAndReplaceResult = await window.api.database.summarizeAndReplaceMessages(project.id, replacedIds, summaryMessage);
                if (result.success && result.newSummaryMessage && result.replacedMessageIds) {
                    appOnMessagesReplacedBySummary(project.id, result.newSummaryMessage, result.replacedMessageIds);
                } else {
                     await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '摘要存储错误', text: `自动摘要存储失败: ${result.error || '未知数据库错误'}`, timestamp: new Date().toISOString(), }); 
                }
            }
        } catch (e: any) { 
            await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '摘要意外错误', text: `自动摘要时发生意外: ${e.message}`, timestamp: new Date().toISOString(), }); 
        }
        finally { 
            summaryInProgressRef.current = false; 
            if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); 
        }
    };
    const timerId = setTimeout(attemptSummarization, 2000);
    return () => clearTimeout(timerId);
  }, [project?.discussionMessages, project?.id, isAIServiceReady, apiKeyIsAvailable, appOnSaveNewChatMessage, appOnMessagesReplacedBySummary, aiTaskStatus, settings.onAiThinkingStateChange, settings.apiKey]);


  const handleSendNoteToChatArea = useCallback((noteText: string) => {
    const isDiscussionActive = location.pathname.endsWith('/discussion') || location.pathname === `/project/${projectId}`;
    if (chatAreaRef.current) {
        if (isDiscussionActive) chatAreaRef.current.insertTextAtFocus(noteText);
        else { navigate(`/project/${projectId}/discussion`); setTimeout(() => { if (chatAreaRef.current) chatAreaRef.current.insertTextAtFocus(noteText); }, 150); }
        if (typeof chatAreaRef.current.focusTextarea === 'function') chatAreaRef.current.focusTextarea();
    }
  }, [location.pathname, navigate, projectId, chatAreaRef]);

  const handleDecomposeRequirement = useCallback(async (requirementText: string, currentProjectId: string, originalMessageId: string) => {
    if (!project || project.id !== currentProjectId) { await appOnSaveNewChatMessage(currentProjectId, { id: crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: "项目上下文错误，无法分解任务。", timestamp: new Date().toISOString(), }); return; }
    if (!window.api?.ai?.decomposeRequirementToTasks) { await appOnSaveNewChatMessage(currentProjectId, { id: crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: "AI任务分解服务接口缺失。", timestamp: new Date().toISOString(), }); return; }
    if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('xiaolan_thinking'); setActionPlanError(null);
    try {
      const result = await window.api.ai.decomposeRequirementToTasks(requirementText, currentProjectId);
      if (result.success && result.tasks) { setActionPlanTasks(result.tasks); setShowActionPlanModal(true); }
      else { setActionPlanError(result.error || "AI未能成功分解需求。"); await appOnSaveNewChatMessage(currentProjectId, { id: crypto.randomUUID(), sender: 'system', senderName: 'AI任务分解失败', text: result.error || "AI未能成功分解需求。", timestamp: new Date().toISOString(), }); }
    } catch (e: any) { setActionPlanError(`需求分解时发生意外错误: ${e.message}`); await appOnSaveNewChatMessage(currentProjectId, { id: crypto.randomUUID(), sender: 'system', senderName: 'AI任务分解意外错误', text: `需求分解时发生意外错误: ${e.message}`, timestamp: new Date().toISOString(), }); }
    finally { if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); }
  }, [project, settings.onAiThinkingStateChange, appOnSaveNewChatMessage]);

  const handleApproveActionPlan = useCallback(async () => {
    if (!project || actionPlanTasks.length === 0 || !onAideProjectImported) return; setIsApprovingActionPlan(true); setActionPlanError(null);
    const tasksToCreate: TaskCreationData[] = actionPlanTasks.map(draft => ({ project_id: project.id, title: draft.title || "未命名任务", description: draft.description || undefined, priority: draft.priority !== undefined ? draft.priority : 2, status: draft.status || 'todo', }));
    await onAideProjectImported(project.id, tasksToCreate); setIsApprovingActionPlan(false); setShowActionPlanModal(false); setActionPlanTasks([]);
    await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '系统提示', text: `行动计划已批准，相关任务已添加至神谕罗盘。`, timestamp: new Date().toISOString(), });
  }, [project, actionPlanTasks, appOnSaveNewChatMessage, onAideProjectImported]);

  const handleConvertToTask = useCallback((message: ChatMessage) => {
    const cleanText = stripHtmlBasic(message.text);
    const title = cleanText.substring(0, 50) + (cleanText.length > 50 ? '...' : '');
    setTaskModalInitialData({ title, description: cleanText, originalMessageId: message.id });
    setIsCreateTaskModalOpen(true);
  }, []);

  const handleTaskCreatedFromChat = async (taskTitle: string) => {
    if (project && typeof window.api?.database?.createDevelopmentTaskFromChat === 'function') {
      const payload: DevelopmentTaskCreationPayload = { projectId: project.id, title: taskTitle, description: taskModalInitialData.description };
      try {
        const createdTask = await window.api.database.createDevelopmentTaskFromChat(payload);
        if (createdTask) {
            await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '系统提示', text: `开发任务 "${createdTask.title}" 已从消息创建并发布至神谕罗盘。`, timestamp: new Date().toISOString() }); 
             setProject(prev => prev ? ({ ...prev, developmentTasks: [...(prev.developmentTasks || []), createdTask] }) : null);
        } else await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `创建开发任务 "${taskTitle}" 失败。`, timestamp: new Date().toISOString() }); 
      } catch (e:any) { await appOnSaveNewChatMessage(project.id, { id: crypto.randomUUID(), sender: 'system', senderName: '系统错误', text: `创建开发任务时发生意外: ${String(e.message)}`, timestamp: new Date().toISOString() }); }
    }
    setIsCreateTaskModalOpen(false);
  };

  const handleOpenWisdomPouchModal = useCallback(() => { setIsWisdomPouchModalOpen(true); }, []);
  
  const childOutletContextValue: AppProvidedContext = useMemo(() => ({
    project, 
    projects, 
    settings, 
    globalQuickCommands, 
    isAIServiceReady, 
    onAddCoreMemory, 
    onUpdateTaskStatusInApp, 
    onAideProjectImported, 
    handleDecomposeRequirement, 
    handleConvertToTask, 
    onSaveNewChatMessage: appOnSaveNewChatMessage, 
    onUpdateExistingChatMessage: appOnUpdateExistingChatMessage, 
    onMessagesReplacedBySummary: appOnMessagesReplacedBySummary, 
    chatAreaRef, 
    addNoteToProject: (pouchType: WisdomPouchType, text: string, importance?: ImportanceLevel) => appAddNoteToProject(project!.id, pouchType, text, importance),
    updateNoteInProject: (pouchType: WisdomPouchType, note: NoteItem) => appUpdateNoteInProject(project!.id, pouchType, note),
    deleteNoteFromProject: (pouchType: WisdomPouchType, noteId: string) => appDeleteNoteFromProject(project!.id, pouchType, noteId),
    updateProjectMindMap: (nodes: MindNode[], connections: MindConnection[]) => appUpdateProjectMindMap(project!.id, nodes, connections),
    addProjectKnowledgeTome: (tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => appAddProjectKnowledgeTome(project!.id, tomeData),
    updateProjectKnowledgeTome: (updatedTome: ProjectKnowledgeTome) => appUpdateProjectKnowledgeTome(project!.id, updatedTome),
    deleteProjectKnowledgeTome: (tomeId: string) => appDeleteProjectKnowledgeTome(project!.id, tomeId),
    addProjectKnowledgeCategory: (category: string) => appAddProjectKnowledgeCategory(project!.id, category),
    removeProjectKnowledgeCategory: (category: string) => appRemoveProjectKnowledgeCategory(project!.id, category),
    updateProjectGeneral: appUpdateProjectGeneral, 
    taskToOpenInEditor, 
    onEditorOpenedTask, 
    onStartCraftingFromTask,
    aiTaskStatus, 
    onOpenWisdomPouch: handleOpenWisdomPouchModal, 
    allCharacters, 
    allPosts, 
    allAssignments,
    onCallAI: async () => Promise.resolve(), // Placeholder, actual call handled by child (SandboxChat)
  }), [
    project, projects, settings, globalQuickCommands, isAIServiceReady, onAddCoreMemory, onUpdateTaskStatusInApp, onAideProjectImported,
    handleDecomposeRequirement, handleConvertToTask, appOnSaveNewChatMessage, appOnUpdateExistingChatMessage, appOnMessagesReplacedBySummary,
    chatAreaRef, appAddNoteToProject, appUpdateNoteInProject, appDeleteNoteFromProject, appUpdateProjectMindMap,
    appAddProjectKnowledgeTome, appUpdateProjectKnowledgeTome, appDeleteProjectKnowledgeTome, appAddProjectKnowledgeCategory,
    appRemoveProjectKnowledgeCategory, appUpdateProjectGeneral, taskToOpenInEditor, onEditorOpenedTask, onStartCraftingFromTask,
    aiTaskStatus, handleOpenWisdomPouchModal, allCharacters, allPosts, allAssignments
  ]);

  if (isLoadingInitial && !project) { return ( <div className="flex items-center justify-center h-full text-tg-text-primary bg-tg-bg-primary"> 工坊数据加载中...</div> ); }
  if (!project && !isLoadingInitial) { 
      const currentProjectIdFromUrl = location.pathname.split('/')[2];
      if (currentProjectIdFromUrl) {
        return <PlaceholderPanel title="项目加载失败" message={`未能找到ID为 ${currentProjectIdFromUrl} 的项目。它可能已被删除或ID不正确。请返回主殿尝试重新进入或检查项目是否存在。`} iconName="ServerCrash"/>
      }
      return <PlaceholderPanel title="项目未选定" message="请从主殿选择一个项目进入其工作区。" iconName="FolderSearch"/>
  }

  return (
    <div id="project-workspace-page" className="flex-grow flex h-full overflow-hidden bg-tg-bg-primary">
      {project && <ProjectSidebar projectId={project.id} />}
      <main className="flex-grow overflow-hidden">
        {project ? ( 
            <Outlet context={childOutletContextValue} />
        ) : (
            <div className="flex items-center justify-center h-full text-tg-text-primary bg-tg-bg-primary">项目数据加载中或未找到...</div>
        )}
      </main>
      <button id="project-workspace-page-wisdom-pouch-trigger" onClick={handleOpenWisdomPouchModal} style={{ display: 'none' }} aria-hidden="true" />

      {(!apiKeyIsAvailable || !isAIServiceReady) && ( <div role="alert" className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-yellow-500 text-yellow-900 px-3 py-1.5 rounded-md shadow-lg text-xs flex items-center z-20" style={{ maxWidth: 'calc(100% - 2rem)'}} > <Icon name="AlertTriangle" className="w-4 h-4 mr-2 text-yellow-800 flex-shrink-0"/> <span className="truncate">{!apiKeyIsAvailable ? "当前未配置Gemini API Key。" : (!isAIServiceReady ? "AI服务初始化中或连接失败。" : "")}</span> </div> )}
      {aiTaskStatus === 'summarizing' && ( <div role="status" className="fixed bottom-12 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-3 py-1.5 rounded-md shadow-lg text-xs flex items-center z-20 animate-pulse"> <Icon name="Loader2" className="w-4 h-4 mr-2 animate-spin"/> <span>对话记忆压缩中...</span> </div> )}
      <ActionPlanModal isOpen={showActionPlanModal} onClose={() => { setShowActionPlanModal(false); setActionPlanTasks([]); setActionPlanError(null); }} tasks={actionPlanTasks} onApprovePlan={handleApproveActionPlan} isApproving={isApprovingActionPlan} />
      {actionPlanError && ( <div className="fixed bottom-12 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-md shadow-lg text-sm z-20"> 任务分解错误: {actionPlanError} <button onClick={() => setActionPlanError(null)} className="ml-2 text-red-100 hover:text-white">&times;</button> </div> )}
      {isCreateTaskModalOpen && project?.id && ( <CreateTaskFromChatMessageModal isOpen={isCreateTaskModalOpen} onClose={() => setIsCreateTaskModalOpen(false)} initialTitle={taskModalInitialData.title} initialDescription={taskModalInitialData.description} projectId={project.id} onTaskCreated={handleTaskCreatedFromChat} /> )}
      {project && ( <WisdomPouchModal isOpen={isWisdomPouchModalOpen} onClose={() => setIsWisdomPouchModalOpen(false)} project={project} addNoteToProject={(pouchType, text, importance) => appAddNoteToProject(project!.id, pouchType, text, importance)} updateNoteInProject={(pouchType, note) => appUpdateNoteInProject(project!.id, pouchType, note)} deleteNoteFromProject={(pouchType, noteId) => appDeleteNoteFromProject(project!.id, pouchType, noteId)} onSendNoteToChat={handleSendNoteToChatArea} /> )}
    </div>
  );
};
export default ProjectWorkspacePage; // Ensure ProjectWorkspacePage is exported if it's directly used in App.tsx <Route element>
