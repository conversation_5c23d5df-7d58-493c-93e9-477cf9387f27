// electron/services/aiOrchestrationService.ts
console.log('AI_ORCHESTRATION_SERVICE_JS: File execution started (Refactored from aiService.js).');

import * as aiKernelService from './aiKernelService'; 
import * as promptBuilderService from './promptBuilderService'; 
import * as memoryAugmentationService from './memoryAugmentationService'; 
import * as memoryPersistenceService from './memoryPersistenceService'; 
import * as dbService from './databaseService'; 
import { crypto as cryptoLib } from './databaseCore'; 
import * as universalAssetService from './universalAssetService'; 
import * as fileSystemService from './fileSystemService'; 
// Use direct relative path for globalConfig
import { GEMINI_TEXT_MODEL, GEMINI_EMBEDDING_MODEL, DEFAULT_SETTINGS as APP_DEFAULT_SETTINGS } from '../../src/config/globalConfig'; 
import { DEFAULT_POSTS, DEFAULT_CHARACTERS } from '../constants/default-organization'; // CORRECTED IMPORT
import type { AiCallContext, LinLuoDetailedStatus, AgentCoreSettingId, Character, Post, Assignment, FileNode as AppFileNode, ChatMessage, RouteUserIntentResponse } from '../../src/types';
import type { ScriptModeInfo } from '../../src/types/aiTypes'; 


const ROUNDTABLE_HISTORY_CONTEXT_SIZE = 10; 

interface FileSystemError { error: string; }
function isFileSystemError(result: any): result is FileSystemError {
    return typeof result === 'object' && result !== null && 'error' in result;
}


export function initializeAIAgent(apiKey: string) {
    return aiKernelService.initializeSharedAIAgent(apiKey);
}

export async function getAvailableModels() {
    return [
        { id: GEMINI_TEXT_MODEL, name: GEMINI_TEXT_MODEL },
    ];
}

export async function executeRawLLMQuery(contents: any, config: any, modelNameFromArgs?: string, apiKeyFromArgs?: string) {
    console.log("AI_ORCHESTRATION_SERVICE: executeRawLLMQuery called.");
    return aiKernelService.generateContentInternal(contents, config, modelNameFromArgs, apiKeyFromArgs);
}

async function executeFileSystemCommand(parsedCommand: { command: string; parameters: any; }) {
    const { command, parameters } = parsedCommand;
    console.log(`AI_ORCHESTRATION_SERVICE (executeFileSystemCommand): Command: ${command}, Params:`, parameters);
    let resultText = "";
    let resultJson:any = parsedCommand; 

    switch (command) {
      case 'listFiles':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '列出文件' 指令缺少有效的 'path' 参数。"; break;}
        const listResult = await fileSystemService.listFiles(parameters.path, parameters.recursive, parameters.depth);
        if (isFileSystemError(listResult)) { resultText = `错误: 列出文件失败 - ${listResult.error}`; break; }
        
        const filesArray = listResult as AppFileNode[];
        if (filesArray.length === 0) { resultText = `目录 "${parameters.path}" 为空或不包含文件。`; break; }
        const formatNode = (node: AppFileNode, indent = ""): string => { let str = `${indent}- ${node.name} ${node.type === 'directory' ? '(目录)' : '(文件)'}\n`; if (node.children && node.children.length > 0) node.children.forEach(child => { str += formatNode(child, indent + "  "); }); return str; };
        resultText = `目录 "${parameters.path}" 下的文件列表:\n${filesArray.map(node => formatNode(node)).join('')}`;
        resultJson = { command, parameters, result: listResult };
        break;
      case 'writeFile':
        if (!parameters || typeof parameters.path !== 'string' || typeof parameters.content !== 'string') { resultText = "错误: '写入文件' 指令缺少有效的 'path' 或 'content' 参数。"; break; }
        const writeResult = await fileSystemService.writeFile(parameters.path, parameters.content);
        resultText = writeResult.success ? `文件 "${parameters.path}" 已成功写入！` : `错误: 写入文件 "${parameters.path}" 失败 - ${writeResult.error}`;
        resultJson = { command, parameters, result: writeResult };
        break;
      case 'readFile':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '读取文件' 指令缺少有效的 'path' 参数。"; break; }
        const readResult = await fileSystemService.readFile(parameters.path);
        if (isFileSystemError(readResult)) { resultText = `错误: 读取文件失败 - ${readResult.error}`; break; }
        
        const fileContentString = readResult as string;
        let fileContentDisplay = fileContentString;
        resultText = `文件 "${parameters.path}" 内容:\n\`\`\`\n${fileContentDisplay.substring(0,2000)}${fileContentDisplay.length > 2000 ? "\n... (截断)" : ""}\n\`\`\``;
        resultJson = { command, parameters, result: { content: fileContentDisplay } }; 
        break;
      case 'isDirectoryEmpty':
        if (!parameters || typeof parameters.path !== 'string') { resultText = "错误: '检查目录是否为空' 指令缺少有效的 'path' 参数。"; break; }
        const emptyResult = await fileSystemService.isDirectoryEmpty(parameters.path); // Corrected call
        if (typeof emptyResult === 'object' && 'error' in emptyResult) { resultText = `错误: 检查目录失败 - ${(emptyResult as FileSystemError).error}`; break; }
        resultText = `目录 "${parameters.path}" ${emptyResult ? '是空的' : '不是空的'}。`;
        resultJson = { command, parameters, result: { isEmpty: emptyResult } };
        break;
      case 'copyDirectoryContents':
         if (!parameters || typeof parameters.sourceDir !== 'string' || typeof parameters.targetDir !== 'string') { resultText = "错误: '复制目录内容' 指令缺少有效的 'sourceDir' 或 'targetDir' 参数。"; break; }
        const copyResult = await fileSystemService.copyDirectoryContents(parameters.sourceDir, parameters.targetDir); // Corrected call
        resultText = copyResult.success ? `已成功将 "${parameters.sourceDir}" 的内容复制到 "${parameters.targetDir}"。` : `错误: 复制目录内容失败 - ${copyResult.error}`;
        resultJson = { command, parameters, result: copyResult };
        break;
      case 'unknown':
      case 'error':
        resultText = `抱歉，小岚无法识别或执行您的文件操作指令: "${parameters?.originalInput || '未知输入'}" ${parameters?.error ? `(AI错误: ${parameters.error})` : ''}`;
        resultJson = { command, parameters, error: parameters?.error || "无法识别的指令" };
        break;
      default:
        resultText = `错误: AI返回了未知的指令类型 "${command}"。`;
        resultJson = { command, parameters, error: `未知的指令类型 "${command}"` };
    }
    return { text: resultText, status: null, scriptChoices: null, newSceneCardId: null, json: resultJson };
}


export async function callAI(
    userRawPrompt: string | undefined,
    targetIdentifier: AgentCoreSettingId | Character['id'] | Post['id'] | string,
    context: Partial<AiCallContext> = {}
) {
    console.log(`AI_ORCHESTRATION_SERVICE: callAI with targetIdentifier: ${targetIdentifier}, contextType: ${context.contextType}, prompt: "${userRawPrompt ? userRawPrompt.substring(0, 50) : 'N/A'}..."`);

    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) {
        const errorMsg = "AI service not initialized or API key missing for callAI.";
        const isJsonPersona = ['OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'IntentClassifierPersona'].includes(targetIdentifier as string);
        return { text: errorMsg, status: null, scriptChoices: null, newSceneCardId: null, json: isJsonPersona ? { command: 'error', parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
    }
    
    const currentApiKey = aiKernelService.getCurrentApiKey();

    let classifiedIntent = 'unknown';
    if (targetIdentifier !== 'IntentClassifierPersona' && userRawPrompt && userRawPrompt.trim() !== "") {
        try {
            const intentSystemInstruction = await promptBuilderService.buildSystemInstruction('IntentClassifierPersona', null, null, null, null, null, null, null);
            const intentRequestContents = [{ role: "user", parts: [{ text: userRawPrompt }] }];
            const intentConfig = { systemInstruction: intentSystemInstruction, responseMimeType: "application/json" };
            
            console.log("AI_ORCHESTRATION_SERVICE: Requesting intent classification for:", userRawPrompt.substring(0, 50) + "...");
            let rawIntentJson = await aiKernelService.generateContentInternal(intentRequestContents, intentConfig, GEMINI_TEXT_MODEL, currentApiKey);
            
            if (typeof rawIntentJson === 'string') {
                let jsonStrToParse = rawIntentJson.trim();
                const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
                const match = jsonStrToParse.match(fenceRegex);
                if (match && match[2]) jsonStrToParse = match[2].trim();
                
                const parsedIntentResponse = JSON.parse(jsonStrToParse);
                if (parsedIntentResponse && parsedIntentResponse.intent) {
                    classifiedIntent = parsedIntentResponse.intent;
                    console.log(`AI_ORCHESTRATION_SERVICE: Classified intent as: ${classifiedIntent}`);
                } else {
                    console.warn("AI_ORCHESTRATION_SERVICE: IntentClassifierPersona returned invalid JSON or no intent field:", parsedIntentResponse);
                }
            } else {
                 console.warn("AI_ORCHESTRATION_SERVICE: IntentClassifierPersona did not return a string response.");
            }
        } catch (intentError: any) {
            console.error("AI_ORCHESTRATION_SERVICE: Error during intent classification:", intentError.message);
        }
    }
    
    const updatedContext: Partial<AiCallContext> & { classifiedIntent: string } = { ...context, classifiedIntent };


    if (updatedContext.scriptModeInfo && updatedContext.scriptModeInfo.scriptId) {
        const { scriptId, currentSceneId, userChoiceText } = updatedContext.scriptModeInfo;
        const scriptAsset = universalAssetService.getAssetById(scriptId);
        if (!scriptAsset || scriptAsset.type !== 'script') return { text: "错误：未能找到指定的剧本。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
        let currentScene = scriptAsset.scenes.find(s => s.id === currentSceneId);
        if (!currentScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到场景 ID: ${currentSceneId}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
        
        let nextScene = null;
        if (userChoiceText && currentScene.user_choices) {
            const choiceMade = currentScene.user_choices.find(c => c.text === userChoiceText);
            if (choiceMade) {
                if (choiceMade.next_scene_id) {
                    nextScene = scriptAsset.scenes.find(s => s.id === choiceMade.next_scene_id);
                     if (!nextScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到由选项 '${userChoiceText}' 指向的下一场景 ID: ${choiceMade.next_scene_id}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
                } else if (choiceMade.action) {
                    console.log(`AI_ORCHESTRATION_SERVICE: Script action triggered:`, choiceMade.action);
                    if (choiceMade.action.end_script) return { text: choiceMade.action.end_message || "剧本结束。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
                }
            } else return { text: `错误：在场景 ${currentSceneId} 中未找到选项 '${userChoiceText}'。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
        }
        const sceneToDisplay = nextScene || currentScene;
        let newSceneCardId = sceneToDisplay.scenario_card_id !== undefined ? sceneToDisplay.scenario_card_id : (scriptAsset.scenario_card_id !== undefined ? scriptAsset.scenario_card_id : (updatedContext.selectedSceneCardId !== undefined ? updatedContext.selectedSceneCardId : null));
        return { text: sceneToDisplay.plot_text, status: updatedContext.currentLinLuoDetailedStatus, scriptChoices: sceneToDisplay.user_choices || null, newSceneCardId: newSceneCardId, json: null };
    }

    let finalPostId: string | null = null;
    let finalCharacterId: Character['id'] | null = null;
    let isFunctionalCall = false;
    const functionalPersonas: string[] = ['SummarizerPersona', 'ClassifierPersona', 'OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'RoundtableFacilitatorPersona', 'IntentClassifierPersona'];

    if (functionalPersonas.includes(targetIdentifier as string)) {
        isFunctionalCall = true;
    } else {
        if (updatedContext.explicitPersona && DEFAULT_CHARACTERS.find(c => c.id === updatedContext.explicitPersona)) {
            finalCharacterId = updatedContext.explicitPersona;
        } else if (DEFAULT_CHARACTERS.find(c => c.id === targetIdentifier)) {
            finalCharacterId = targetIdentifier as Character['id'];
        }

        if (updatedContext.requiredPostId && DEFAULT_POSTS.find(p => p.id === updatedContext.requiredPostId)) {
            finalPostId = updatedContext.requiredPostId;
        } else if (DEFAULT_POSTS.find(p => p.id === targetIdentifier)) { 
            finalPostId = targetIdentifier as Post['id'];
        } else if (finalCharacterId) { 
            const char = DEFAULT_CHARACTERS.find(c => c.id === finalCharacterId);
            if(char?.default_post_id) finalPostId = char.default_post_id;
        } else { 
            if (updatedContext.contextType === 'code_assistance') finalPostId = 'technical_officer';
        }
        if (!finalPostId) finalPostId = 'first_officer'; 

        if (!finalCharacterId) {
            const assignedCharId = await dbService.getAssignmentByPostId(finalPostId);
            if (assignedCharId && DEFAULT_CHARACTERS.find(c => c.id === assignedCharId)) {
                finalCharacterId = assignedCharId;
            } else { 
                const characterForPost = DEFAULT_CHARACTERS.find(c => c.default_post_id === finalPostId);
                if (characterForPost) {
                    finalCharacterId = characterForPost.id;
                } else {
                    finalCharacterId = 'linluo'; 
                }
            }
        }
        if (finalCharacterId && !finalPostId) {
            const char = DEFAULT_CHARACTERS.find(c => c.id === finalCharacterId);
            finalPostId = char?.default_post_id || 'first_officer';
        }
    }
    
    console.log(`AI_ORCHESTRATION_SERVICE: Determined - isFunctional: ${isFunctionalCall}, PostID: ${finalPostId}, CharacterID: ${finalCharacterId}, OriginalTarget: ${targetIdentifier}, ClassifiedIntent: ${classifiedIntent}`);

    try {
        let modelConfigOverride = {};
        let systemInstruction;

        if (isFunctionalCall) {
            systemInstruction = await promptBuilderService.buildSystemInstruction(targetIdentifier, updatedContext.selectedRoleCardId, updatedContext.currentLinLuoDetailedStatus, null, null, updatedContext.userRawPromptForCockpit, updatedContext.cockpitFullContext, null); 
            if (['OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'RoundtableFacilitatorPersona', 'IntentClassifierPersona'].includes(targetIdentifier as string)) {
                 modelConfigOverride = { responseMimeType: "application/json" };
            }
        } else {
            systemInstruction = await promptBuilderService.buildSystemInstruction(finalCharacterId!, updatedContext.selectedRoleCardId, updatedContext.currentLinLuoDetailedStatus, finalPostId!, finalCharacterId!, updatedContext.userRawPromptForCockpit, updatedContext.cockpitFullContext, classifiedIntent);
        }
        
        let memoryContextString = "";
         if (!isFunctionalCall && currentApiKey && updatedContext.contextType !== 'task_resource_suggestion') {
            const queryTextForRag = userRawPrompt || (updatedContext.history && updatedContext.history.length > 0 ? updatedContext.history[updatedContext.history.length-1].parts[0].text : "");
            if (queryTextForRag) {
                memoryContextString = await memoryAugmentationService.retrieveAndFormatMemories(queryTextForRag, finalCharacterId!, updatedContext.projectId, updatedContext.contextType, updatedContext.keywordsForMemory, updatedContext.desiredMemoryTypes, currentApiKey, GEMINI_EMBEDDING_MODEL, dbService, aiKernelService);
            }
        }
        
        let finalUserPromptForLLM = userRawPrompt;
        if (targetIdentifier === 'TaskCockpitIntentRouterPersona') {
            systemInstruction = await promptBuilderService.buildSystemInstruction(targetIdentifier, null, null, null, null, userRawPrompt, updatedContext, null);
            finalUserPromptForLLM = "Please classify the user's intent based on the provided context and system instruction.";
        } else if (['OrchestratorPersona', 'AideProjectAnalyzerPersona'].includes(targetIdentifier as string)) {
             finalUserPromptForLLM = userRawPrompt; 
        }

        const requestContents = promptBuilderService.prepareUserHistoryForAI(updatedContext.history || [], memoryContextString, finalUserPromptForLLM);
        
        const rawAiText = await aiKernelService.generateContentInternal(requestContents, { systemInstruction, ...modelConfigOverride }, GEMINI_TEXT_MODEL, currentApiKey);

        if (typeof rawAiText !== 'string') {
            console.error(`AI_ORCHESTRATION_SERVICE: LLM call did not return a string. Response:`, rawAiText);
            const errorMsg = `AI核心返回了非文本响应。 (Target: ${targetIdentifier}, Post: ${finalPostId}, Char: ${finalCharacterId})`;
            const isJsonPersona = ['OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'RoundtableFacilitatorPersona', 'IntentClassifierPersona'].includes(targetIdentifier as string);
            return { text: errorMsg, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: isJsonPersona ? { command: 'error', parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
        }

        const { cleanedText, actionResults } = await memoryPersistenceService.processResponseForActions(rawAiText, updatedContext.projectId, dbService);
        
        if (['OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'RoundtableFacilitatorPersona', 'IntentClassifierPersona'].includes(targetIdentifier as string)) {
            let jsonStr = cleanedText.trim(); 
            const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s; const match = jsonStr.match(fenceRegex); if (match && match[2]) jsonStr = match[2].trim();
            try { 
                const parsedJson = JSON.parse(jsonStr); 
                if(targetIdentifier === 'OrchestratorPersona') return await executeFileSystemCommand(parsedJson); 
                if (targetIdentifier === 'TaskCockpitIntentRouterPersona') {
                     return { type: parsedJson.intent || 'unknown_intent', data: parsedJson.parameters || {}, originalCommand: parsedJson.originalCommand || userRawPrompt, aiPersona: parsedJson.aiPersona || 'System', json: parsedJson } as any; 
                }
                return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: parsedJson };
            } 
            catch (e: any) { 
                const errorMsg = `AI (${targetIdentifier}) 返回的JSON格式无效: ${e.message}. Raw: ${cleanedText.substring(0,100)}`; 
                console.error(`AI_ORCHESTRATION_SERVICE (JSON Persona Error):`, errorMsg);
                if (targetIdentifier === 'TaskCockpitIntentRouterPersona') {
                    return { type: 'error', data: { message: errorMsg }, originalCommand: userRawPrompt, aiPersona: 'System', json: {error: errorMsg}} as any; 
                }
                 if (targetIdentifier === 'IntentClassifierPersona') { 
                    console.warn(`AI_ORCHESTRATION_SERVICE: IntentClassifierPersona failed to parse JSON, classifiedIntent remains '${classifiedIntent}' or default 'unknown'. Raw response: ${cleanedText}`);
                    return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { intent: classifiedIntent } }; 
                }
                return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { error: errorMsg, originalInput: userRawPrompt }}; 
            }
        }
        
        let statusOutput = null;
        let textForDisplay = cleanedText;
        const statusRegex = /\[\[STATUS:({.*?})\]\]/s;
        const statusMatch = cleanedText.match(statusRegex);
        if (statusMatch && statusMatch[1]) {
            try { statusOutput = JSON.parse(statusMatch[1]); textForDisplay = textForDisplay.replace(statusRegex, '').trim(); } 
            catch (e: any) { console.error("AI_ORCHESTRATION_SERVICE: Failed to parse status JSON:", e.message, statusMatch[1]); }
        }
        
        return { text: textForDisplay, status: statusOutput, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null, characterId: finalCharacterId };

    } catch (error: any) {
        console.error(`AI_ORCHESTRATION_SERVICE_ERROR (callAI with Target: ${targetIdentifier}, Post: ${finalPostId}, Char: ${finalCharacterId}):`, error.message);
        return { text: `调用AI时发生错误: ${error.message}`, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null };
    }
}

export async function routeUserIntent(userInputText: string, context: Partial<AiCallContext>): Promise<RouteUserIntentResponse> {
  console.log(`AI_ORCHESTRATION_SERVICE: routeUserIntent with userInput: "${userInputText.substring(0, 50)}..." and context:`, context);
  return callAI(userInputText, 'TaskCockpitIntentRouterPersona', context) as Promise<RouteUserIntentResponse>; 
}


export async function startRoundtableMeeting(initialPrompt: string, participantCharacterIds: string[], turnLimit: number, projectId: string, initialHistory: ChatMessage[] = [], webContents: any) {
    console.log(`AI_ORCHESTRATION_SERVICE: Starting Roundtable Meeting for project ${projectId}. Topic: "${initialPrompt.substring(0,50)}..." Participants: ${participantCharacterIds.join(', ')}, Turns: ${turnLimit}`);
    
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) {
        const errorMsg = "AI service not initialized or API key missing for Roundtable Meeting.";
        return { success: false, error: errorMsg, history: initialHistory };
    }
    const currentApiKey = aiKernelService.getCurrentApiKey();
    const meetingId = `roundtable-${cryptoLib.randomUUID()}`;
    let currentMeetingHistory: ChatMessage[] = [...initialHistory]; 
    let lastSpeakerId = 'user'; 
    let lastResponseText = initialPrompt;

    try {
        for (let turn = 0; turn < turnLimit; turn++) {
            for (const characterId of participantCharacterIds) {
                const characterInfo = DEFAULT_CHARACTERS.find(c => c.id === characterId);
                if (!characterInfo) {
                    console.warn(`Roundtable: Character ID ${characterId} not found. Skipping turn.`);
                    continue;
                }
                
                const postInfo = DEFAULT_POSTS.find(p => p.id === characterInfo.default_post_id) || DEFAULT_POSTS[0];
                const postIdForAI = postInfo.id;

                console.log(`Roundtable Turn ${turn + 1}, Speaker: ${characterInfo.name} (ID: ${characterId}, Post: ${postIdForAI})`);

                const systemInstruction = await promptBuilderService.buildRoundtableSystemInstruction(
                    characterId, postIdForAI, initialPrompt, 
                    participantCharacterIds.filter(id => id !== characterId).map(id => DEFAULT_CHARACTERS.find(c => c.id === id)?.name || id), 
                    turn + 1, lastSpeakerId, lastResponseText 
                );

                const historyForThisTurn = currentMeetingHistory.slice(-ROUNDTABLE_HISTORY_CONTEXT_SIZE);
                const requestContents = promptBuilderService.prepareUserHistoryForAI(historyForThisTurn, "", "请继续讨论。");

                const rawAiText = await aiKernelService.generateContentInternal(
                    requestContents, { systemInstruction }, GEMINI_TEXT_MODEL, currentApiKey
                );

                if (typeof rawAiText !== 'string') {
                    const errorMsg = `AI (${characterInfo.name}) returned non-text response.`;
                    console.error(errorMsg, rawAiText);
                    if (webContents && !webContents.isDestroyed()) {
                        webContents.send('roundtable:newMessage', {
                            id: cryptoLib.randomUUID(), sender: 'system', senderName: '会议错误',
                            text: errorMsg, timestamp: new Date().toISOString(), projectId, meetingId
                        });
                    }
                    continue; 
                }
                
                const aiChatMessage: ChatMessage = {
                    id: cryptoLib.randomUUID(), sender: 'roundtable_ai', senderName: characterInfo.name,
                    text: rawAiText.trim(), timestamp: new Date().toISOString(), projectId, 
                    characterId: characterId, turnNumber: turn + 1, meetingId: meetingId,
                };
                currentMeetingHistory.push(aiChatMessage);
                
                if (projectId && projectId !== "global_sandbox") {
                    await dbService.saveChatMessage(projectId, aiChatMessage); 
                }

                lastSpeakerId = characterId;
                lastResponseText = rawAiText.trim();

                if (webContents && !webContents.isDestroyed()) {
                    webContents.send('roundtable:newMessage', aiChatMessage);
                } else {
                     console.warn(`AI_ORCHESTRATION_SERVICE (Roundtable): webContents not available or destroyed. Cannot send message for meeting ${meetingId}`);
                }
                await new Promise(resolve => setTimeout(resolve, 200)); 
            }
        }
        console.log("AI_ORCHESTRATION_SERVICE: Roundtable Meeting finished.");
        return { success: true, history: currentMeetingHistory, meetingId };
    } catch (error: any) {
        console.error(`AI_ORCHESTRATION_SERVICE_ERROR (startRoundtableMeeting):`, error.message);
        return { success: false, error: `圆桌会议执行失败: ${error.message}`, history: currentMeetingHistory, meetingId };
    }
}


export async function summarizeConversation(historyChunk: ChatMessage[]) {
    console.log("AI_ORCHESTRATION_SERVICE: summarizeConversation called.");
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return "系统错误: AI服务未初始化或API Key缺失，无法进行摘要。";
    const summarizerPersonaSetting = await dbService.getAgentCoreSetting('summarizer_persona');
    const systemInstruction = summarizerPersonaSetting?.content || 'You are a helpful AI assistant that summarizes conversations concisely.';
    const conversationText = historyChunk.map(msg => `${msg.senderName || msg.sender}: ${msg.text.replace(/<[^>]+>/g, '')}`).join('\n');
    try {
        return await aiKernelService.generateContentInternal(
            [{role: "user", parts: [{text: `Please summarize the following conversation snippet:\n\n${conversationText}`}]}],
            { systemInstruction }, GEMINI_TEXT_MODEL, aiKernelService.getCurrentApiKey()
        );
    } catch (error: any) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (summarizeConversation):`, error.message); return `系统错误: 摘要生成失败 - ${error.message}`; }
}

export async function suggestResourcesForTask(taskTitle: string, taskDescription: string | undefined, projectId: string) {
    console.log(`AI_ORCHESTRATION_SERVICE: suggestResourcesForTask called for project ${projectId}, title: ${taskTitle}`);
    return [];
}

export async function decomposeRequirementToTasks(requirementText: string, projectId: string) {
    console.log(`AI_ORCHESTRATION_SERVICE: Decomposing requirement for project ${projectId}: "${requirementText.substring(0, 100)}..."`);
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
    const systemInstruction = (await dbService.getAgentCoreSetting('TaskDecomposerPersona' as AgentCoreSettingId))?.content || `You are an AI assistant for task decomposition. Output valid JSON array of tasks: {"title": string, "description": string, "priority": 0|1|2|3, "status": "todo"}. Max 5-7 tasks. Empty array [] if vague. No extra text.`;
    const prompt = `Requirement Text:\n"""\n${requirementText}\n"""\n\nJSON Array of Tasks:`;
    try {
        const rawResponse = await aiKernelService.generateContentInternal([{ role: "user", parts: [{ text: prompt }] }], { systemInstruction, responseMimeType: "application/json" }, GEMINI_TEXT_MODEL, aiKernelService.getCurrentApiKey());
        if (typeof rawResponse !== 'string') throw new Error("AI返回了非文本响应。");
        let jsonStr = rawResponse.trim(); const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s; const match = jsonStr.match(fenceRegex); if (match && match[2]) jsonStr = match[2].trim();
        const parsedTasks = JSON.parse(jsonStr);
        if (!Array.isArray(parsedTasks)) throw new Error("AI response was not a JSON array.");
        const validatedTasks = parsedTasks.map(task => ({ title: task.title || "未命名任务", description: task.description || "", priority: typeof task.priority === 'number' && task.priority >=0 && task.priority <=3 ? task.priority : 2, status: 'todo' }));
        return { success: true, tasks: validatedTasks };
    } catch (error: any) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (decomposeRequirementToTasks):`, error.message); return { success: false, error: `任务拆解失败: ${error.message}`, tasks: [] }; }
}

export async function analyzeAndDecomposeAideProject(projectId: string, projectPath: string) {
    console.log(`AI_ORCHESTRATION_SERVICE: Analyzing AIDE project ${projectId} at ${projectPath}.`);
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
    let fileListString = "无法获取文件列表。"; try { const files = await fileSystemService.listFiles(projectPath, true, 2); if (Array.isArray(files)) { const formatNode = (node: AppFileNode, indent = ""):string => { let str = `${indent}- ${node.name} ${node.type === 'directory' ? '(目录)' : '(文件)'}\n`; if (node.children && node.children.length > 0) str += node.children.map(child => formatNode(child, indent + "  ")).join(''); return str; }; fileListString = files.map(node => formatNode(node)).join(''); if (fileListString.length > 2000) fileListString = fileListString.substring(0, 2000) + "\n... (截断)"; } else if (isFileSystemError(files)) fileListString = `获取文件列表时出错: ${files.error}`; } catch (fsError: any) { fileListString = `扫描项目文件时发生错误: ${fsError.message}`; }
    const userPromptForAide = `项目文件结构 (部分):\n${fileListString}\n\n请基于此结构，反向推导一组已完成的开发任务列表。`;
    try {
        const aiResponse = await callAI(userPromptForAide, 'AideProjectAnalyzerPersona', { projectId, contextType: 'aide_project_analysis', aideProjectPath: projectPath, aideFileStructure: fileListString });
        if (aiResponse.json && Array.isArray(aiResponse.json)) { const tasks = aiResponse.json.map(task => ({ title: task.title || "AI生成的任务", description: task.description || "由AI根据项目结构逆向工程生成。", priority: typeof task.priority === 'number' ? task.priority : 2, status: 'done' })); return { success: true, tasks }; } 
        else { const errorMsg = aiResponse.text || "AI未能解析项目结构或返回了无效的JSON。"; return { success: false, error: errorMsg, tasks: [] }; }
    } catch (error: any) { console.error(`AI_ORCHESTRATION_SERVICE_ERROR (analyzeAndDecomposeAideProject):`, error.message); return { success: false, error: `AIDE项目任务逆向工程失败: ${error.message}`, tasks: [] }; }
}

export async function analyzeErrorLogFromService(logContent: string) {
    console.log("AI_ORCHESTRATION_SERVICE: analyzeErrorLogFromService called.");
    if (!aiKernelService.isAgentInitialized() && !aiKernelService.getCurrentApiKey() && !process.env.API_KEY) return "错误：API Key未配置，无法分析日志。";
    const personaSetting = await dbService.getAgentCoreSetting('CommandLogAnalyzerPersona' as AgentCoreSettingId); 
    let systemInstruction = personaSetting?.content || `You are an expert AI system analyst. Analyze the following log content, identify potential causes, and suggest solutions.`;
    
    let userPromptForLogAnalysis = "Please analyze the log provided in the system instruction (if applicable) or the following log:";

    if (systemInstruction.includes("{logContent}")) { 
        systemInstruction = systemInstruction.replace("{logContent}", logContent);
    } else {
        userPromptForLogAnalysis = `Please analyze the following log content:\n\n\`\`\`\n${logContent}\n\`\`\``;
    }

    try {
        return await aiKernelService.generateContentInternal(
            [{ role: 'user', parts: [{ text: userPromptForLogAnalysis }] }], 
            { systemInstruction }, 
            GEMINI_TEXT_MODEL, 
            aiKernelService.getCurrentApiKey()
        );
    } catch (error: any) { console.error("AI_ORCHESTRATION_SERVICE_ERROR (analyzeErrorLogFromService):", error.message); return `AI分析日志时发生错误: ${error.message}`; }
}


console.log('AI_ORCHESTRATION_SERVICE_TS: File execution finished. Exports configured.');

declare module '../../src/types' {
  interface AiCallContext {
    userRawPromptForCockpit?: string;
    cockpitFullContext?: any; 
  }
}
