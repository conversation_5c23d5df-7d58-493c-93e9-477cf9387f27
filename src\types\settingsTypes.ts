// src/types/settingsTypes.ts
export type SoundName =
  | 'ambient_classroom' | 'ambient_office_night' | 'ambient_bedroom_soft' | 'ambient_interrogation_tense'
  | 'ui_click' | 'ui_unlock' | 'ui_error'
  | string;

export interface AudioPlaybackState {
  currentSound: SoundName | null;
  volume: number;
  isLooping: boolean;
  isPlaying: boolean;
}

export type AppTheme = 'theme-default' | 'theme-light';
export type AiThinkingState = 'idle' | 'linluo_thinking' | 'xiaolan_thinking' | 'roundtable_thinking';

export interface AppSettings {
  apiKey: string;
  chatModel: string;
  embeddingModel: string;
  backupPath: string;
  defaultCover: string | null;
  terminalShell?: string;
  terminalCwd?: string;
  absolute_territory_password?: string | null;
  user_avatar_path?: string | null;
  linluo_avatar_path?: string | null;
  xiaolan_avatar_path?: string | null;
  training_room_audio_state?: Partial<AudioPlaybackState>;
  currentTheme?: AppTheme;
  onAiThinkingStateChange?: (newState: AiThinkingState) => void;
}
