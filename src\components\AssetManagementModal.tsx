// src/components/AssetManagementModal.tsx
// 综合资产管理界面

import React, { useState, useEffect } from 'react';
import { SmartIcon, VisualHeading, VisualContainer } from '@/components/common/VisualUtils';
import { Icon } from '@/components/common/Icon';
import type { RolePlayingCard, PropAsset, CostumeAsset, PoseAsset } from '@/types';
import { AssetImporter } from '@/utils/assetImporter';
import { DatabaseCleaner } from '@/utils/databaseCleaner';

interface AssetManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRefresh?: () => void;
}

type AssetTab = 'roles' | 'props' | 'costumes' | 'poses' | 'import';

export const AssetManagementModal: React.FC<AssetManagementModalProps> = ({
  isOpen,
  onClose,
  onRefresh
}) => {
  const [activeTab, setActiveTab] = useState<AssetTab>('roles');
  const [roleCards, setRoleCards] = useState<RolePlayingCard[]>([]);
  const [props, setProps] = useState<PropAsset[]>([]);
  const [costumes, setCostumes] = useState<CostumeAsset[]>([]);
  const [poses, setPoses] = useState<PoseAsset[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [cleaningDuplicates, setCleaningDuplicates] = useState(false);
  const [duplicateStats, setDuplicateStats] = useState<any>(null);

  // 获取所有资产数据
  const fetchAllAssets = async () => {
    setLoading(true);
    try {
      // 获取角色卡
      const roleCardsData = await window.api.cms.getRolePlayingCards();
      setRoleCards(roleCardsData || []);

      // 获取 CMS 数据库中的资产
      const propsData = await window.api.cms.getCMSItems('props');
      const costumesData = await window.api.cms.getCMSItems('costumes');
      const posesData = await window.api.cms.getCMSItems('poses');

      setProps(propsData || []);
      setCostumes(costumesData || []);
      setPoses(posesData || []);
    } catch (error) {
      console.error('获取资产数据失败:', error);
      setMessage(`获取数据失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 删除角色卡
  const handleDeleteRole = async (roleId: string) => {
    if (!confirm('确定要删除这个角色卡吗？')) return;
    
    try {
      await window.api.cms.deleteRolePlayingCard(roleId);
      setMessage('角色卡删除成功');
      await fetchAllAssets();
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('删除角色卡失败:', error);
      setMessage(`删除失败: ${error.message}`);
      setTimeout(() => setMessage(null), 5000);
    }
  };

  // 导入预设资产包
  const handleImportAssetPack = async (packType: 'kirishima' | 'linluo') => {
    setLoading(true);
    try {
      let result;
      if (packType === 'kirishima') {
        // 实时检查是否已存在（不依赖状态）
        const currentRoleCards = await window.api.cms.getRolePlayingCards();
        const currentProps = await window.api.cms.getCMSItems('props');

        const existingRole = currentRoleCards.find(card => card.name === '雾岛玲奈 (Kirishima Reina)');
        const existingProps = currentProps.filter(prop => prop.id.startsWith('prop_kr_'));

        if (existingRole && existingProps.length > 0) {
          setMessage('雾岛玲奈角色卡和资产已存在，跳过导入');
          setLoading(false);
          setTimeout(() => setMessage(null), 3000);
          return;
        }
        result = await AssetImporter.importKirishimaReinaAssets();
      } else {
        // 实时检查是否已存在（不依赖状态）
        const currentRoleCards = await window.api.cms.getRolePlayingCards();
        const currentProps = await window.api.cms.getCMSItems('props');

        const existingRole = currentRoleCards.find(card => card.name === '林珞 · 自然骚魂版');
        const existingProps = currentProps.filter(prop => prop.id.startsWith('prop_ln_'));

        if (existingRole && existingProps.length > 0) {
          setMessage('林珞自然骚魂版角色卡和资产已存在，跳过导入');
          setLoading(false);
          setTimeout(() => setMessage(null), 3000);
          return;
        }
        result = await AssetImporter.importLinLuoNaturalAssets();
      }
      
      if (result.success) {
        setMessage(result.message);
        await fetchAllAssets();
        if (onRefresh) onRefresh();
        setTimeout(() => setMessage(null), 5000);
      } else {
        setMessage(`导入失败: ${result.message}`);
        setTimeout(() => setMessage(null), 8000);
      }
    } catch (error) {
      console.error('导入失败:', error);
      setMessage(`导入失败: ${error.message}`);
      setTimeout(() => setMessage(null), 8000);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchAllAssets();
      loadDuplicateStats();
    }
  }, [isOpen]);

  const loadDuplicateStats = async () => {
    try {
      const stats = await DatabaseCleaner.getDuplicateStats();
      setDuplicateStats(stats);
    } catch (error) {
      console.error('获取重复统计失败:', error);
    }
  };

  // 清理重复资产
  const handleCleanDuplicates = async () => {
    if (!confirm('确定要清理重复资产吗？这将删除重复的道具、服装和姿势，只保留最新的版本。')) {
      return;
    }

    setCleaningDuplicates(true);
    try {
      const result = await DatabaseCleaner.cleanDuplicateAssets();

      if (result.success) {
        setMessage(result.message);
        await fetchAllAssets(); // 重新加载资产
        await loadDuplicateStats(); // 重新加载统计
        if (onRefresh) onRefresh(); // 通知父组件更新
      } else {
        setMessage(`清理失败: ${result.message}`);
      }

      setTimeout(() => setMessage(null), 5000);
    } catch (error) {
      console.error('清理重复资产失败:', error);
      setMessage(`清理失败: ${error.message}`);
      setTimeout(() => setMessage(null), 5000);
    } finally {
      setCleaningDuplicates(false);
    }
  };

  // 清理所有旧版本资产
  const handleCleanAllOldAssets = async () => {
    if (!confirm('确定要清理所有旧版本资产吗？这将删除所有旧名称的道具，只保留最新升级后的版本。此操作不可撤销！')) {
      return;
    }

    setCleaningDuplicates(true);
    try {
      const result = await DatabaseCleaner.cleanAllOldAssets();

      if (result.success) {
        setMessage(result.message);
        await fetchAllAssets(); // 重新加载资产
        await loadDuplicateStats(); // 重新加载统计
        if (onRefresh) onRefresh(); // 通知父组件更新
      } else {
        setMessage(`清理失败: ${result.message}`);
      }

      setTimeout(() => setMessage(null), 5000);
    } catch (error) {
      console.error('清理旧版本资产失败:', error);
      setMessage(`清理失败: ${error.message}`);
      setTimeout(() => setMessage(null), 5000);
    } finally {
      setCleaningDuplicates(false);
    }
  };

  if (!isOpen) return null;

  const getTabClassName = (tab: AssetTab) => {
    const baseClass = "px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2";
    return activeTab === tab 
      ? `${baseClass} bg-tg-accent-primary text-white shadow-lg`
      : `${baseClass} text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-tg-bg-primary rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-tg-border-primary">
          <VisualHeading level="primary" size="xl">
            <SmartIcon name="Shield" level="accent" context="content" className="mr-3" />
            军械库资产管理
          </VisualHeading>
          <button
            onClick={onClose}
            className="p-2 hover:bg-tg-bg-hover rounded-lg transition-colors"
          >
            <SmartIcon name="X" level="secondary" context="action" />
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="flex space-x-2 p-6 border-b border-tg-border-primary bg-tg-bg-secondary">
          <button onClick={() => setActiveTab('roles')} className={getTabClassName('roles')}>
            <SmartIcon name="CircleUser" level="secondary" context="navigation" />
            <span>角色卡</span>
            <span className="bg-tg-accent-secondary text-white text-xs px-2 py-1 rounded-full">
              {roleCards.length}
            </span>
          </button>
          <button onClick={() => setActiveTab('props')} className={getTabClassName('props')}>
            <SmartIcon name="Package" level="secondary" context="navigation" />
            <span>道具</span>
            <span className="bg-tg-accent-secondary text-white text-xs px-2 py-1 rounded-full">
              {props.length}
            </span>
          </button>
          <button onClick={() => setActiveTab('costumes')} className={getTabClassName('costumes')}>
            <SmartIcon name="Shirt" level="secondary" context="navigation" />
            <span>服装</span>
            <span className="bg-tg-accent-secondary text-white text-xs px-2 py-1 rounded-full">
              {costumes.length}
            </span>
          </button>
          <button onClick={() => setActiveTab('poses')} className={getTabClassName('poses')}>
            <SmartIcon name="User" level="secondary" context="navigation" />
            <span>姿势</span>
            <span className="bg-tg-accent-secondary text-white text-xs px-2 py-1 rounded-full">
              {poses.length}
            </span>
          </button>
          <button onClick={() => setActiveTab('import')} className={getTabClassName('import')}>
            <SmartIcon name="Download" level="secondary" context="navigation" />
            <span>导入资产包</span>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <SmartIcon name="LoaderCircle" level="accent" context="content" animate="spin" className="mr-3" />
              <span className="text-tg-text-secondary">加载中...</span>
            </div>
          )}

          {message && (
            <div className="mb-4 p-4 bg-tg-bg-tertiary border border-tg-border-primary rounded-lg">
              <div className="flex items-start">
                <SmartIcon name="Info" level="accent" context="content" className="mr-2 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-tg-text-secondary whitespace-pre-line">
                  {message}
                </div>
              </div>
            </div>
          )}

          {/* 角色卡管理 */}
          {activeTab === 'roles' && !loading && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-tg-text-primary">角色卡管理</h3>
                <button
                  onClick={fetchAllAssets}
                  className="px-4 py-2 bg-tg-accent-secondary text-white rounded-lg hover:bg-tg-accent-secondary-hover transition-colors"
                >
                  <SmartIcon name="RefreshCw" level="secondary" context="action" className="mr-2" />
                  刷新
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {roleCards.map((role) => (
                  <VisualContainer key={role.id} level="tertiary" variant="card" className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-tg-text-primary mb-1">{role.name}</h4>
                        <p className="text-sm text-tg-text-secondary mb-2">{role.description}</p>
                        <div className="text-xs text-tg-text-placeholder">ID: {role.id}</div>
                      </div>
                      <button
                        onClick={() => handleDeleteRole(role.id)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                        title="删除角色卡"
                      >
                        <SmartIcon name="Trash2" level="secondary" context="action" />
                      </button>
                    </div>
                  </VisualContainer>
                ))}
              </div>
              
              {roleCards.length === 0 && (
                <div className="text-center py-12 text-tg-text-placeholder">
                  <SmartIcon name="CircleUser" level="secondary" context="content" className="mx-auto mb-4" />
                  <p>暂无角色卡</p>
                </div>
              )}
            </div>
          )}

          {/* 导入资产包 */}
          {activeTab === 'import' && !loading && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-tg-text-primary mb-4">预设资产包</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* 雾岛玲奈资产包 */}
                  <VisualContainer level="secondary" variant="card" className="p-6">
                    <div className="text-center">
                      <SmartIcon name="Sparkles" level="accent" context="content" className="mx-auto mb-4" />
                      <h4 className="font-medium text-tg-text-primary mb-2">雾岛玲奈军械包</h4>
                      <p className="text-sm text-tg-text-secondary mb-4">
                        高端"生活艺术品"上门推销员<br/>
                        包含 4 个道具、4 套服装、5 个姿势
                      </p>
                      <button
                        onClick={() => handleImportAssetPack('kirishima')}
                        disabled={loading || (roleCards.some(card => card.id === 'SC001') && props.some(prop => prop.id.startsWith('prop_kr_')))}
                        className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {(roleCards.some(card => card.id === 'SC001') && props.some(prop => prop.id.startsWith('prop_kr_'))) ? '已导入' : '导入资产包'}
                      </button>
                    </div>
                  </VisualContainer>

                  {/* 林珞自然骚魂版资产包 */}
                  <VisualContainer level="secondary" variant="card" className="p-6">
                    <div className="text-center">
                      <SmartIcon name="Heart" level="accent" context="content" className="mx-auto mb-4" />
                      <h4 className="font-medium text-tg-text-primary mb-2">林珞自然骚魂版军械包</h4>
                      <p className="text-sm text-tg-text-secondary mb-4">
                        与你共创巅峰的灵魂伴侣<br/>
                        包含 4 个道具、4 套服装、5 个姿势
                      </p>
                      <button
                        onClick={() => handleImportAssetPack('linluo')}
                        disabled={loading || (roleCards.some(card => card.id === 'Luo_Personality_NaturalSeductiveSoul_v2.2.1') && props.some(prop => prop.id.startsWith('prop_ln_')))}
                        className="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {(roleCards.some(card => card.id === 'Luo_Personality_NaturalSeductiveSoul_v2.2.1') && props.some(prop => prop.id.startsWith('prop_ln_'))) ? '已导入' : '导入资产包'}
                      </button>
                    </div>
                  </VisualContainer>
                </div>
              </div>

              {/* 数据库维护 */}
              <div>
                <h3 className="text-lg font-medium text-tg-text-primary mb-4">数据库维护</h3>
                <VisualContainer level="secondary" variant="card" className="p-6">
                  <div className="text-center">
                    <SmartIcon name="Database" level="accent" context="content" className="mx-auto mb-4" />
                    <h4 className="font-medium text-tg-text-primary mb-2">清理重复资产</h4>
                    <p className="text-sm text-tg-text-secondary mb-4">
                      删除数据库中的重复道具、服装和姿势，只保留最新版本
                    </p>

                    {duplicateStats && (
                      <div className="text-xs text-tg-text-placeholder mb-4 space-y-1">
                        <div>道具: {duplicateStats.props.total} 个 ({duplicateStats.props.duplicates} 个重复)</div>
                        <div>服装: {duplicateStats.costumes.total} 个 ({duplicateStats.costumes.duplicates} 个重复)</div>
                        <div>姿势: {duplicateStats.poses.total} 个 ({duplicateStats.poses.duplicates} 个重复)</div>
                      </div>
                    )}

                    <div className="space-y-3">
                      <button
                        onClick={handleCleanDuplicates}
                        disabled={cleaningDuplicates || (duplicateStats && duplicateStats.props.duplicates === 0 && duplicateStats.costumes.duplicates === 0 && duplicateStats.poses.duplicates === 0)}
                        className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Icon name={cleaningDuplicates ? "Loader" : "Trash2"} className={`w-4 h-4 mr-2 ${cleaningDuplicates ? 'animate-spin' : ''}`} />
                        {cleaningDuplicates ? '清理中...' : '清理重复资产'}
                      </button>

                      <button
                        onClick={handleCleanAllOldAssets}
                        disabled={cleaningDuplicates}
                        className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Icon name={cleaningDuplicates ? "Loader" : "Zap"} className={`w-4 h-4 mr-2 ${cleaningDuplicates ? 'animate-spin' : ''}`} />
                        {cleaningDuplicates ? '清理中...' : '清理所有旧版本'}
                      </button>
                    </div>
                  </div>
                </VisualContainer>
              </div>
            </div>
          )}

          {/* 道具管理 */}
          {activeTab === 'props' && !loading && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-tg-text-primary">道具管理</h3>
                <button
                  onClick={fetchAllAssets}
                  className="px-4 py-2 bg-tg-accent-secondary text-white rounded-lg hover:bg-tg-accent-secondary-hover transition-colors"
                >
                  <SmartIcon name="RefreshCw" level="secondary" context="action" className="mr-2" />
                  刷新
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {props.map((prop) => (
                  <VisualContainer key={prop.id} level="tertiary" variant="card" className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-tg-text-primary mb-1">{prop.name}</h4>
                        <p className="text-sm text-tg-text-secondary mb-2">{prop.description}</p>
                        <div className="text-xs text-tg-text-placeholder mb-1">
                          归属: {prop.owner === 'master' ? '主人用' : '女王用'}
                        </div>
                        <div className="text-xs text-tg-text-placeholder">ID: {prop.id}</div>
                      </div>
                    </div>
                  </VisualContainer>
                ))}
              </div>

              {props.length === 0 && (
                <div className="text-center py-12 text-tg-text-placeholder">
                  <SmartIcon name="Package" level="secondary" context="content" className="mx-auto mb-4" />
                  <p>暂无道具资产</p>
                </div>
              )}
            </div>
          )}

          {/* 服装管理 */}
          {activeTab === 'costumes' && !loading && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-tg-text-primary">服装管理</h3>
                <button
                  onClick={fetchAllAssets}
                  className="px-4 py-2 bg-tg-accent-secondary text-white rounded-lg hover:bg-tg-accent-secondary-hover transition-colors"
                >
                  <SmartIcon name="RefreshCw" level="secondary" context="action" className="mr-2" />
                  刷新
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {costumes.map((costume) => (
                  <VisualContainer key={costume.id} level="tertiary" variant="card" className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-tg-text-primary mb-1">{costume.name}</h4>
                        <p className="text-sm text-tg-text-secondary mb-2">{costume.description}</p>
                        <div className="text-xs text-tg-text-placeholder mb-1">
                          归属: {costume.owner === 'master' ? '主人用' : '女王用'}
                        </div>
                        <div className="text-xs text-tg-text-placeholder">ID: {costume.id}</div>
                      </div>
                    </div>
                  </VisualContainer>
                ))}
              </div>

              {costumes.length === 0 && (
                <div className="text-center py-12 text-tg-text-placeholder">
                  <SmartIcon name="Shirt" level="secondary" context="content" className="mx-auto mb-4" />
                  <p>暂无服装资产</p>
                </div>
              )}
            </div>
          )}

          {/* 姿势管理 */}
          {activeTab === 'poses' && !loading && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-tg-text-primary">姿势管理</h3>
                <button
                  onClick={fetchAllAssets}
                  className="px-4 py-2 bg-tg-accent-secondary text-white rounded-lg hover:bg-tg-accent-secondary-hover transition-colors"
                >
                  <SmartIcon name="RefreshCw" level="secondary" context="action" className="mr-2" />
                  刷新
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {poses.map((pose) => (
                  <VisualContainer key={pose.id} level="tertiary" variant="card" className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-tg-text-primary mb-1">{pose.name}</h4>
                        <p className="text-sm text-tg-text-secondary mb-2">{pose.description}</p>
                        <div className="text-xs text-tg-text-placeholder mb-1">
                          归属: {pose.owner === 'master' ? '主人用' : '女王用'}
                        </div>
                        <div className="text-xs text-tg-text-placeholder">ID: {pose.id}</div>
                      </div>
                    </div>
                  </VisualContainer>
                ))}
              </div>

              {poses.length === 0 && (
                <div className="text-center py-12 text-tg-text-placeholder">
                  <SmartIcon name="User" level="secondary" context="content" className="mx-auto mb-4" />
                  <p>暂无姿势资产</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
