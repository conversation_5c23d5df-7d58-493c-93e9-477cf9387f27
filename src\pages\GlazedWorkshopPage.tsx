// src/pages/GlazedWorkshopPage.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon';
import { PlaceholderPanel } from '@/components/PlaceholderPanel';

export const GlazedWorkshopPage: React.FC = () => {
  return (
    <div className="p-6 h-full flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 via-indigo-900 to-pink-900 text-white">
        <PlaceholderPanel
            title="琉璃坊 - 视觉工作室 (建设中)"
            message="此区域为语镜总监的专属视觉工作室，用于进行UI设计、图像生成与编辑、以及所有与视觉美学相关的炼制工作。目前正在由小岚紧张施工中，敬请期待！"
            icon={<Icon name="Paintbrush" className="w-16 h-16 text-pink-400 mb-4" />}
        />
         <p className="text-sm text-indigo-300 mt-4">
            未来，您将能在这里与“语镜”AI一同雕琢天工阁的每一寸视觉细节。
        </p>
    </div>
  );
};