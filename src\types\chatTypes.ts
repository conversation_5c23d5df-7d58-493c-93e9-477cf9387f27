// src/types/chatTypes.ts
import type { AppSettings, WisdomPouchType } from './'; // Import from main index

export type ChatMessageSenderType = 'user' | 'ai' | 'system' | 'system_summary' | 'roundtable_ai';
export type MessageThemeType = 'idea' | 'instruction' | 'report' | 'review';

export interface XPHypothesisElementDetail {
  id: string;
  name: string;
  prompt?: string;
}

export interface XPHypothesisElements {
  costume: XPHypothesisElementDetail | null;
  prop: XPHypothesisElementDetail | null;
  pose: XPHypothesisElementDetail | null;
}

export interface XPHypothesisChoice {
  id: "A" | "B" | "C";
  text: string;
}

export interface XPHypothesisProposalData {
  hypothesisId: string;
  elements: XPHypothesisElements;
  choices: XPHypothesisChoice[];
}

export interface XPHypothesis {
  id: string;
  descriptionForAI: string;
  elements: XPHypothesisElements;
  suggestedInteractionPrompt: string;
}

export interface ChatMessage {
  id: string;
  sender: ChatMessageSenderType;
  senderName: string;
  avatarPath?: string | null;
  text: string;
  timestamp: string;
  isEditing?: boolean;
  isStarred?: boolean;
  isPinned?: boolean;
  theme?: MessageThemeType;
  projectId?: string;
  itemIconPath?: string;
  itemCgPath?: string;
  replyToMessageId?: string;
  triggeringUserMessageId?: string;
  avatarPathOverride?: string | null;
  isHypothesisProposal?: boolean;
  hypothesisData?: XPHypothesisProposalData;
  isError?: boolean;
  isLoading?: boolean;
  characterId?: string; // For roundtable AI messages
  turnNumber?: number; // For roundtable AI messages
  meetingId?: string; // Added for roundtable messages
}

export interface SummarizeAndReplaceResult {
  success: boolean;
  newSummaryMessage?: ChatMessage;
  replacedMessageIds?: string[];
  error?: string;
}

export interface ExtractedChatMessageItemProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  onUpdateMessage: (update: { messageId: string; newHtml: string }) => void;
  onToggleStar: (messageId: string) => void;
  onTogglePin: (messageId: string) => void;
  onCopy: (textToCopy: string) => void;
  isRichEditingActive: boolean;
  onRequestRichEdit: (messageId: string | null) => void;
  onReplayCg?: (cgPath: string) => void;
  onAddToCoreMemory?: (message: ChatMessage) => void;
  onResendMessage?: (message: ChatMessage) => void;
  onRefreshAiResponse?: (aiMessage: ChatMessage) => void;
  onForwardMessage?: (message: ChatMessage, targetAi: 'LinLuo' | 'XiaoLan' | 'YuJing') => void;
  onSendToPouch?: (message: ChatMessage, pouchType?: WisdomPouchType) => void;
  isWorkspaceContext?: boolean;
  currentUserName?: string;
  onHypothesisFeedback?: (hypothesisId: string, choice: string, modification?: string, originalElements?: any) => Promise<void>;
  onDecomposeRequirement?: (requirementText: string, projectId: string, originalMessageId: string) => Promise<void>;
  onConvertToTask?: (message: ChatMessage) => void;
}