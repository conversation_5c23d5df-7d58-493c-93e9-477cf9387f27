// src/components/SourceCodeViewer.tsx
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useOutletContext } from 'react-router-dom';
import type { Project, FileNode, AppSettings, GenerateCodeContext, ExplainCodeContext, GenerateDocContext, ReviewCodeContext, AnalyzeErrorContext, Task, TaskStatus, AIInteractionHistoryItem, TaskCreationData, ProjectWorkspacePageOutletContext } from '@/types';
import { FileTreeNode } from '@/components/FileTreeNode';
import { Icon } from '@/components/common/Icon';
import Editor, { Monaco, loader as monacoLoader, OnMount } from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { AICodeAssistPanel } from '@/components/AICodeAssistPanel';


monacoLoader.init().then(monacoInstance => {
  console.log("Monaco Editor initialized via loader.");
}).catch(error => console.error('Failed to initialize Monaco Editor:', error));


interface SourceCodeViewerProps {
  updateProjectSettings: (updatedProject: Project) => void;
  settings: AppSettings; // Settings now come as a prop
  initialTaskForCodeAssist?: Task | null;
  onUpdateTaskStatus: (taskId: string, newStatus: TaskStatus, projectId: string) => Promise<Task | null>;
  onAideProjectImported?: (projectId: string, newTasksData: TaskCreationData[]) => Promise<void>;
}

const getLanguageForFile = (filePath: string): string => {
  const extension = filePath.split('.').pop()?.toLowerCase();
  if (!extension) return 'plaintext';
  switch (extension) {
    case 'js': return 'javascript';
    case 'jsx': return 'javascript';
    case 'ts': return 'typescript';
    case 'tsx': return 'typescript';
    case 'css': return 'css';
    case 'json': return 'json';
    case 'py': return 'python';
    case 'md': return 'markdown';
    case 'java': return 'java';
    case 'cs': return 'csharp';
    case 'go': return 'go';
    case 'rs': return 'rust';
    case 'sql': return 'sql';
    case 'html': return 'html';
    case 'xml': return 'xml';
    case 'yaml': return 'yaml';
    case 'yml': return 'yaml';
    case 'sh': return 'shell';
    case 'rb': return 'ruby';
    case 'php': return 'php';
    case 'swift': return 'swift';
    case 'kt': return 'kotlin';
    default: return 'plaintext';
  }
};

const combinePaths = (base: string, relative: string): string => {
    if (!base) return relative;
    if (!relative) return base;

    const normBase = base.replace(/[/\\]+$/, '');
    const normRelative = relative.replace(/^[/\\]+/, '');
    return `${normBase}/${normRelative}`;
};

export const SourceCodeViewer: React.FC<SourceCodeViewerProps> = (props) => {
  const outletContext = useOutletContext<ProjectWorkspacePageOutletContext>();
  // Settings and Project are now from outletContext
  const settings = outletContext.settings;
  const project = outletContext.project; 

  const { updateProjectSettings, initialTaskForCodeAssist, onUpdateTaskStatus, onAideProjectImported } = props;
  const [fileTree, setFileTree] = useState<FileNode[] | null>(null);
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [editorContent, setEditorContent] = useState<string | null>(null);
  const [isLoadingFileTree, setIsLoadingFileTree] = useState(false);
  const [fileTreeError, setFileTreeError] = useState<string | null>(null);
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);

  const [isPanelVisible, setIsPanelVisible] = useState(false);
  const [panelTitle, setPanelTitle] = useState("AI 代码助手");
  const [panelContent, setPanelContent] = useState<string | null>(null);
  const [isPanelLoading, setIsPanelLoading] = useState(false);
  const [panelError, setPanelError] = useState<string | null>(null);
  const [currentOperationDescription, setCurrentOperationDescription] = useState<string | null>(null);
  const [aiHistory, setAiHistory] = useState<AIInteractionHistoryItem[]>([]);
  const [currentTaskForContextInternal, setCurrentTaskForContextInternal] = useState<Task | null>(null);
  const [isIndexingFile, setIsIndexingFile] = useState(false);
  const [indexingStatus, setIndexingStatus] = useState<string | null>(null);

  const apiKeyAvailable = !!settings.apiKey && settings.apiKey.trim() !== "";

  const loadFileTree = useCallback(async (path: string) => {
    if (!path) {
        setFileTree(null);
        setFileTreeError("项目源码路径未设置。");
        return;
    }
    setIsLoadingFileTree(true);
    setFileTreeError(null);
    try {
      const result = await window.api.fs.readDirectory(path);
      if ('error' in result) {
        setFileTreeError(`加载文件树失败: ${result.error}`);
        setFileTree(null);
      } else {
        setFileTree(result);
      }
    } catch (e: any) {
      setFileTreeError(`加载文件树时发生意外错误: ${e.message}`);
      setFileTree(null);
    } finally {
      setIsLoadingFileTree(false);
    }
  }, []);

  useEffect(() => {
    if (project?.sourceCodePath) {
      loadFileTree(project.sourceCodePath);
    } else {
      setFileTree(null);
      setFileTreeError(null);
    }
    setSelectedFile(null);
    setEditorContent(null);
  }, [project?.sourceCodePath, project?.id, loadFileTree]);

  const handleFileSelect = useCallback(async (filePath: string) => {
    try {
      const contentResult = await window.api.fs.readFileContent(filePath);
      if (typeof contentResult === 'string') {
        setSelectedFile({ name: filePath.split(/[\\/]/).pop() || filePath, path: filePath, type: 'file' });
        setEditorContent(contentResult);
        if (panelContent && !isPanelLoading && (!panelError || panelError.startsWith("AI操作失败"))) {
            setPanelContent(null);
            setPanelTitle("AI 代码助手");
        }
        console.log(`[SourceCodeViewer] File selected and content loaded: ${filePath}`);
      } else {
        setFileTreeError(`读取文件失败: ${contentResult.error}`);
        setEditorContent(null);
        setSelectedFile(null);
      }
    } catch (e: any) {
      setFileTreeError(`读取文件时发生意外错误: ${e.message}`);
      setEditorContent(null);
      setSelectedFile(null);
    }
  }, [panelContent, isPanelLoading, panelError]);

  useEffect(() => {
    if (initialTaskForCodeAssist) {
        console.log(`[SourceCodeViewer] Initial task received for code assist: ${initialTaskForCodeAssist.task_id}, title: ${initialTaskForCodeAssist.title}`);
        setCurrentTaskForContextInternal(initialTaskForCodeAssist);

        if (project?.sourceCodePath) {
            const firstFilePathResource = initialTaskForCodeAssist.resource_links?.find(link => link.resource_type === 'file_path');
            if (firstFilePathResource?.resource_identifier) {
                const fullPath = combinePaths(project.sourceCodePath, firstFilePathResource.resource_identifier);
                console.log(`[SourceCodeViewer] Attempting to open file from task: ${fullPath}`);
                handleFileSelect(fullPath);
            } else {
                console.log(`[SourceCodeViewer] No file_path resource found in task or project source path missing. Task resources:`, initialTaskForCodeAssist.resource_links);
            }
        } else {
            console.log(`[SourceCodeViewer] Initial task received but project sourceCodePath is not set. Task: ${initialTaskForCodeAssist.task_id}`);
        }

        if (initialTaskForCodeAssist.status !== 'done' && initialTaskForCodeAssist.status !== 'pending_review') {
           setIsPanelVisible(true);
        }
    } else {
        setCurrentTaskForContextInternal(null);
    }
  }, [initialTaskForCodeAssist, project?.sourceCodePath, handleFileSelect]);


  const handleEditorDidMount: OnMount = (editorInstance, monacoInstance) => {
    editorRef.current = editorInstance;
    monacoRef.current = monacoInstance;
    setIsEditorReady(true);

    editorInstance.addAction({
      id: 'ai-generate-code',
      label: 'AI: 生成/修改代码...',
      keybindings: [monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyG],
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.5,
      run: async (ed) => {
        const selection = ed.getSelection();
        const selectedText = selection ? ed.getModel()?.getValueInRange(selection) : "";
        const userInstruction = prompt("请输入代码生成或修改指令 (可用于简单修改或基于选中内容生成):", selectedText ? `基于当前选中内容: ${selectedText.substring(0,30)}...` : "");
        if (userInstruction) {
          handleTriggerAIAssist('generate', userInstruction, selectedText || "");
        }
      }
    });
     editorInstance.addAction({
      id: 'ai-explain-code',
      label: 'AI: 解释选中代码',
      keybindings: [monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyE],
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.6,
      run: (ed) => {
        const selection = ed.getSelection();
        const selectedText = selection ? ed.getModel()?.getValueInRange(selection) : "";
        if (selectedText) handleTriggerAIAssist('explain', undefined, selectedText);
        else alert("请先选择一段代码以供解释。");
      }
    });
    editorInstance.addAction({
        id: 'ai-generate-doc',
        label: 'AI: 为选中代码生成文档',
        contextMenuGroupId: 'navigation',
        contextMenuOrder: 1.7,
        run: (ed) => {
            const selection = ed.getSelection();
            const selectedText = selection ? ed.getModel()?.getValueInRange(selection) : "";
            if (selectedText) handleTriggerAIAssist('document', undefined, selectedText);
            else alert("请先选择代码以生成文档。");
        }
    });
    editorInstance.addAction({
        id: 'ai-review-code',
        label: 'AI: 审查选中代码',
        contextMenuGroupId: 'navigation',
        contextMenuOrder: 1.8,
        run: (ed) => {
            const selection = ed.getSelection();
            const selectedText = selection ? ed.getModel()?.getValueInRange(selection) : "";
            if (selectedText) handleTriggerAIAssist('review', undefined, selectedText);
            else alert("请先选择代码以供审查。");
        }
    });
  };

  const handleSaveEditorContent = async () => {
    if (selectedFile && editorRef.current) {
      const currentContent = editorRef.current.getValue();
      try {
        const result = await window.api.fs.saveFileContent(selectedFile.path, currentContent);
        if (result.success) {
          setEditorContent(currentContent);
          alert("文件已保存！");
        } else {
          setFileTreeError(`保存文件失败: ${result.error}`);
        }
      } catch (e: any) {
         setFileTreeError(`保存文件时发生意外错误: ${e.message}`);
      }
    }
  };

  const handleSetProjectSourcePath = async (isAideTakeover: boolean = false) => {
    let dirPathToAnalyze: string | null = null;

    if (isAideTakeover && project?.sourceCodePath) {
      dirPathToAnalyze = project.sourceCodePath;
      console.log(`[SourceCodeViewer] AIDE takeover using existing path: ${dirPathToAnalyze}`);
    } else {
      const selectedDirPath = await window.api.fs.openDirectoryDialog();
      if (!selectedDirPath) return;

      if (project) {
        // updateProjectSettings prop is used to update the project in App.tsx's state
        updateProjectSettings({ ...project, sourceCodePath: selectedDirPath });
      }
      if (isAideTakeover) {
        dirPathToAnalyze = selectedDirPath;
        console.log(`[SourceCodeViewer] AIDE takeover using newly selected path: ${dirPathToAnalyze}`);
      }
    }

    if (isAideTakeover && dirPathToAnalyze && project && onAideProjectImported) {
      if (!apiKeyAvailable) {
        setFileTreeError("错误：API Key 未配置。无法执行AIDE项目分析。");
        return;
      }
      if (typeof window.api?.ai?.analyzeAndDecomposeAideProject !== 'function') {
        setFileTreeError("错误：AIDE项目分析服务 (analyzeAndDecomposeAideProject) 不可用。");
        console.error("Error: window.api.ai.analyzeAndDecomposeAideProject is not a function.");
        return;
      }

      setFileTreeError("正在分析AIDE项目结构并逆向生成任务...");
      try {
        const result = await window.api.ai.analyzeAndDecomposeAideProject(project.id, dirPathToAnalyze);
        if (result.success && result.tasks) {
          await onAideProjectImported(project.id, result.tasks);
          setFileTreeError(`AIDE项目任务已成功导入 (${result.tasks.length}个)。`);
          setTimeout(() => setFileTreeError(null), 5000);
        } else {
          setFileTreeError(`AIDE项目分析失败: ${result.error || "未能生成任务。"}`);
        }
      } catch (e: any) {
        console.error("SourceCodeViewer: Error during AIDE project analysis IPC call:", e);
        setFileTreeError(`AIDE项目分析时发生意外错误: ${e.message}`);
      }
    } else if (isAideTakeover && !project) {
        setFileTreeError("错误：项目信息未加载，无法执行AIDE项目分析。");
    } else if (isAideTakeover && !dirPathToAnalyze) {
        setFileTreeError("错误：未能确定分析路径，AIDE项目分析取消。");
    }
  };


  const addInteractionToHistoryInternal = (interaction: Omit<AIInteractionHistoryItem, 'id' | 'timestamp'>) => {
    const newInteraction: AIInteractionHistoryItem = {
      ...interaction,
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    };
    setAiHistory(prev => [newInteraction, ...prev].slice(0, 5));
  };

  const handleTriggerAIAssist = useCallback(async (
    type: AIInteractionHistoryItem['type'],
    userInstruction?: string,
    codeToProcess?: string
    ) => {
    if (!apiKeyAvailable) {
      setPanelError("API Key 未配置。请前往天工阁设置。");
      setIsPanelVisible(true);
      return;
    }
    if (isPanelLoading) return;

    setIsPanelVisible(true);
    setIsPanelLoading(true);
    setPanelError(null);
    setPanelContent(null);

    const currentEditorContentForAI = editorRef.current?.getValue() || editorContent || "";
    const currentSelectedTextForAI = codeToProcess || (editorRef.current?.getSelection() ? editorRef.current?.getModel()?.getValueInRange(editorRef.current.getSelection()!) : "") || "";
    const language = selectedFile ? getLanguageForFile(selectedFile.path) : 'plaintext';

    let result = "";
    let operationTitle = "";

    try {
      switch (type) {
        case 'generate':
          operationTitle = "AI 代码生成/修改";
          setCurrentOperationDescription(userInstruction ? `根据指令: "${userInstruction.substring(0,30)}..." 生成代码...` : "生成代码中...");
          const generateContext: GenerateCodeContext = {
            currentSelection: currentSelectedTextForAI,
            surroundingCode: currentEditorContentForAI,
            language,
            userInstruction: userInstruction || "请根据上下文和选中内容（如有）生成合适的代码。",
            filePath: selectedFile?.path,
            projectId: project?.id,
            queryForMemory: (userInstruction || "") + currentSelectedTextForAI + currentEditorContentForAI,
            keywordsForMemory: (userInstruction?.match(/\b\w+\b/g) || []).slice(0,5),
          };
          result = await window.api.ai.assist.generateCode(generateContext);
          addInteractionToHistoryInternal({ type, instruction: userInstruction, codeContext: currentSelectedTextForAI || currentEditorContentForAI, response: result, error: null, associatedTaskId: currentTaskForContextInternal?.task_id });
          break;
        case 'explain':
          operationTitle = "AI 代码解释";
          setCurrentOperationDescription("解释代码中...");
          if (!currentSelectedTextForAI) { result = "错误：未选择任何代码。"; break; }
          const explainContext: ExplainCodeContext = { codeToExplain: currentSelectedTextForAI, language, filePath: selectedFile?.path, projectId: project?.id, queryForMemory: currentSelectedTextForAI };
          result = await window.api.ai.assist.explainCode(explainContext);
          addInteractionToHistoryInternal({ type, codeContext: currentSelectedTextForAI, response: result, error: null, associatedTaskId: currentTaskForContextInternal?.task_id });
          break;
        case 'document':
            operationTitle = "AI 代码文档生成";
            setCurrentOperationDescription("生成文档中...");
            if (!currentSelectedTextForAI) { result = "错误：未选择任何代码。"; break; }
            const docContext: GenerateDocContext = { codeToDoc: currentSelectedTextForAI, language, filePath: selectedFile?.path, projectId: project?.id, queryForMemory: currentSelectedTextForAI };
            result = await window.api.ai.assist.generateDocForCode(docContext);
            addInteractionToHistoryInternal({ type, codeContext: currentSelectedTextForAI, response: result, error: null, associatedTaskId: currentTaskForContextInternal?.task_id });
            break;
        case 'review':
            operationTitle = "AI 代码审查";
            setCurrentOperationDescription("审查代码中...");
            if (!currentSelectedTextForAI) { result = "错误：未选择任何代码。"; break; }
            const reviewContext: ReviewCodeContext = { codeToReview: currentSelectedTextForAI, language, filePath: selectedFile?.path, projectId: project?.id, queryForMemory: currentSelectedTextForAI };
            result = await window.api.ai.assist.reviewCode(reviewContext);
            addInteractionToHistoryInternal({ type, codeContext: currentSelectedTextForAI, response: result, error: null, associatedTaskId: currentTaskForContextInternal?.task_id });
            break;
        case 'analyze_log':
            operationTitle = "AI 错误日志分析";
            setCurrentOperationDescription("分析日志中...");
            const logContext: AnalyzeErrorContext = { errorLog: codeToProcess || currentEditorContentForAI, language, filePath: selectedFile?.path, projectId: project?.id, surroundingCode: currentSelectedTextForAI, queryForMemory: (codeToProcess || currentEditorContentForAI) + currentSelectedTextForAI};
            result = await window.api.ai.assist.analyzeErrorLog(logContext);
            addInteractionToHistoryInternal({ type, codeContext: codeToProcess || currentEditorContentForAI, response: result, error: null, associatedTaskId: currentTaskForContextInternal?.task_id });
            break;
        default:
          throw new Error("未知的AI辅助操作类型");
      }
      setPanelTitle(operationTitle);
      setPanelContent(result);
    } catch (e: any) {
      setPanelError(`AI操作失败: ${e.message}`);
      addInteractionToHistoryInternal({ type, instruction: userInstruction, codeContext: currentSelectedTextForAI || currentEditorContentForAI, response: "", error: e.message, associatedTaskId: currentTaskForContextInternal?.task_id });
    } finally {
      setIsPanelLoading(false);
      setCurrentOperationDescription(null);
    }
  }, [apiKeyAvailable, isPanelLoading, editorContent, selectedFile, project, settings.apiKey, settings.embeddingModel, currentTaskForContextInternal?.task_id, addInteractionToHistoryInternal]);

  const handleSyncToMemoryCore = async () => {
    if (!selectedFile || !editorContent || !project || !apiKeyAvailable || !window.api?.rag?.indexFileContent) {
      setIndexingStatus("错误：无法同步，缺少文件、内容、项目信息或API Key。");
      setTimeout(() => setIndexingStatus(null), 3000);
      return;
    }
    setIsIndexingFile(true);
    setIndexingStatus("正在同步文件内容到记忆核心...");
    try {
      const result = await window.api.rag.indexFileContent(
        project.id,
        selectedFile.path,
        editorContent,
        { apiKey: settings.apiKey, embeddingModelName: settings.embeddingModel }
      );
      if (result.success) {
        setIndexingStatus(`文件 "${selectedFile.name}" 已成功同步到记忆核心！`);
      } else {
        setIndexingStatus(`同步失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) {
      setIndexingStatus(`同步时发生意外错误: ${e.message}`);
    } finally {
      setIsIndexingFile(false);
      setTimeout(() => setIndexingStatus(null), 4000);
    }
  };

  if (!project) {
    return <div className="p-4 text-tg-text-placeholder text-center h-full flex items-center justify-center">请先从主殿选择一个项目以打开源码洞天。</div>;
  }

  return (
    <div className="flex flex-col h-full bg-tg-bg-primary">
      {/* Top Bar for Path Management */}
      <div className="flex-shrink-0 p-2.5 border-b border-tg-border-primary bg-tg-bg-secondary flex items-center space-x-2 text-xs">
        {!project.sourceCodePath ? (
          <>
            <span className="text-tg-text-placeholder">项目源码根目录未设置。</span>
            <button onClick={() => handleSetProjectSourcePath(false)} className="px-2.5 py-1 bg-tg-accent-secondary text-white rounded hover:brightness-110">设置目录</button>
            <button onClick={() => handleSetProjectSourcePath(true)} className="px-2.5 py-1 bg-purple-600 text-white rounded hover:bg-purple-700">接管AIDE源码</button>
          </>
        ) : (
          <>
            <span className="text-tg-text-secondary truncate flex-shrink min-w-0" title={`当前根目录: ${project.sourceCodePath}`}>
              <Icon name="FolderOpen" className="w-4 h-4 inline-block mr-1 text-tg-accent-primary"/>
              <span className="font-semibold">根目录:</span> {project.sourceCodePath.split(/[\\/]/).pop() || project.sourceCodePath}
            </span>
            <div className="flex-grow"></div> {/* Spacer */}
            <button onClick={() => handleSetProjectSourcePath(false)} className="text-purple-400 hover:underline flex-shrink-0">更改目录</button>
            <button onClick={() => loadFileTree(project.sourceCodePath!)} className="text-teal-400 hover:underline flex-shrink-0">刷新目录</button>
          </>
        )}
      </div>
      {indexingStatus && (
        <div className={`p-1.5 text-center text-xs ${indexingStatus.includes("失败") || indexingStatus.includes("错误") ? 'bg-red-700/80 text-white' : 'bg-green-700/80 text-white'}`}>
          {indexingStatus}
        </div>
      )}


      <div className="flex flex-grow overflow-hidden"> {/* Main content area */}
        {/* File Tree Panel */}
        <div className="w-1/4 lg:w-1/5 border-r border-tg-border-primary p-2.5 flex flex-col min-w-[200px] bg-tg-bg-secondary">
          <h4 className="text-md font-semibold mb-2 text-tg-text-primary flex items-center">
              <Icon name="Folder" className="w-5 h-5 mr-2 text-tg-accent-primary"/>源码目录
          </h4>
          {isLoadingFileTree && <p className="text-xs text-tg-text-placeholder">加载文件树...</p>}
          {fileTreeError && <p className="text-xs text-red-400 p-1 bg-red-900/20 rounded">{fileTreeError}</p>}
          {fileTree && !isLoadingFileTree && !fileTreeError && (
            <div className="flex-grow overflow-y-auto custom-scrollbar pr-1">
              {fileTree.map(node => (
                <FileTreeNode
                  key={node.path}
                  node={node}
                  onFileSelect={handleFileSelect}
                  level={0}
                  selectedFilePath={selectedFile?.path || null}
                />
              ))}
            </div>
          )}
        </div>

        {/* Editor Panel */}
        <div className="flex-grow flex flex-col overflow-hidden relative">
          {selectedFile ? (
            <>
              <div className="p-2.5 border-b border-tg-border-primary bg-tg-bg-tertiary flex justify-between items-center">
                <h5 className="text-sm font-medium text-tg-text-primary truncate flex-shrink min-w-0" title={selectedFile.path}>
                  <Icon name="File" className="w-4 h-4 mr-1.5 inline-block text-tg-accent-secondary"/>
                  {selectedFile.name}
                </h5>
                <div className="space-x-2 flex-shrink-0">
                  <button onClick={handleSyncToMemoryCore} disabled={isIndexingFile || !apiKeyAvailable} className="px-2.5 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs flex items-center disabled:opacity-50" title="同步当前文件内容到RAG记忆核心">
                      <Icon name={isIndexingFile ? "RotateCcw" : "DatabaseZap"} className={`w-3.5 h-3.5 mr-1 ${isIndexingFile ? 'animate-spin' : ''}`}/> 同步记忆
                  </button>
                  <button onClick={handleSaveEditorContent} className="px-2.5 py-1 bg-tg-accent-secondary text-white rounded hover:brightness-110 text-xs flex items-center" title="保存当前文件 (Ctrl+S)">
                      <Icon name="Save" className="w-3.5 h-3.5 mr-1"/> 保存
                  </button>
                  <button onClick={() => setIsPanelVisible(!isPanelVisible)} className="px-2.5 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 text-xs flex items-center" title="AI 代码助手">
                      <Icon name="Bot" className="w-3.5 h-3.5 mr-1"/> AI助手
                  </button>
                </div>
              </div>
              <div className="flex-grow relative">
                {editorContent !== null ? (
                  <Editor
                    height="100%"
                    path={selectedFile.path}
                    defaultLanguage={getLanguageForFile(selectedFile.path)}
                    value={editorContent}
                    onMount={handleEditorDidMount}
                    theme={settings.currentTheme === 'theme-light' ? "vs" : "vs-dark"}
                    options={{
                      readOnly: false, minimap: { enabled: true }, scrollbar: { verticalScrollbarSize: 8, horizontalScrollbarSize: 8 },
                      fontSize: 13, wordWrap: 'on', automaticLayout: true,
                    }}
                  />
                ) : (
                   <div className="flex items-center justify-center h-full text-tg-text-placeholder">
                       <Icon name="Loader" className="w-6 h-6 animate-spin mr-2"/> 加载文件中...
                   </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-tg-text-placeholder">
              <Icon name="CodeSquare" className="w-12 h-12 mb-3 text-tg-border-primary"/>
              <p>从左侧选择一个文件以查看和编辑。</p>
            </div>
          )}
        </div>
         {isPanelVisible && (
          <AICodeAssistPanel
            isVisible={isPanelVisible}
            title={panelTitle}
            content={panelContent}
            isLoading={isPanelLoading}
            error={panelError}
            onClose={() => setIsPanelVisible(false)}
            projectId={project?.id}
            projectName={project?.name}
            projectCategories={project?.projectKnowledgeCategories || []}
            currentOperationDescription={currentOperationDescription}
            onTriggerAIAssist={handleTriggerAIAssist}
            history={aiHistory}
            onHistoryChange={setAiHistory}
            currentTaskForContext={currentTaskForContextInternal}
            onUpdateTaskStatus={onUpdateTaskStatus}
            currentFilePath={selectedFile?.path}
            currentFileContent={editorContent}
            onApplyNewCode={(newCode) => {
               if (selectedFile) {
                  setEditorContent(newCode);
                  if(editorRef.current) editorRef.current.setValue(newCode);
               }
            }}
            settings={settings}
          />
        )}
      </div>
    </div>
  );
};