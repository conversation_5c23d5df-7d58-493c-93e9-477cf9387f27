// src/components/cms/CMSManagementModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import type { CMSType, CMSItemBase, PropItem, CostumeItem, PoseItem, RolePlayingCard, AssetType } from '@/types'; // AssetType is now relevant
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon
// CMSEditItemModal import removed as it's no longer directly used here for props/costumes/poses
// RoleCardEditModal import is also removed as this modal no longer manages Role Card editing.

interface CMSManagementModalProps {
    isOpen: boolean;
    onClose: () => void;
    activeTabInitial?: CMSType; // Kept for potential future use with DB-backed CMS items
    fetchCMSItems: (type?: CMSType | 'all' | AssetType) => Promise<void>; // Allow AssetType if needed for other things
    initialPropsItems: PropItem[]; // Keep receiving for display/potential future re-integration
    initialCostumesItems: CostumeItem[];
    initialPosesItems: PoseItem[];
    initialRoleCards: RolePlayingCard[]; // Still passed to view Role Cards
}

export const CMSManagementModal: React.FC<CMSManagementModalProps> = ({ 
    isOpen, onClose, activeTabInitial = 'role_cards', fetchCMSItems, // Default to role_cards or a placeholder
    initialPropsItems, initialCostumesItems, initialPosesItems, initialRoleCards
}) => {
    const [activeTab, setActiveTab] = useState<CMSType>(activeTabInitial); 
    
    // States for DB-managed items (currently only Role Cards are viewed here)
    const [roleCards, setRoleCards] = useState(initialRoleCards);
    useEffect(() => { setRoleCards(initialRoleCards); }, [initialRoleCards]);

    const getTabClass = (tabName: CMSType) =>
        `py-2 px-4 text-sm font-medium rounded-t-md transition-colors focus:outline-none ${
        activeTab === tabName ? 'bg-purple-600 text-white' : 'bg-tg-bg-tertiary text-gray-400 hover:bg-purple-800 hover:text-white'
        }`;

    const renderRoleCardList = (items: RolePlayingCard[]) => (
        <div className="space-y-2">
            {items.length === 0 && <p className="text-center text-gray-500 py-4">暂无角色卡定义。</p>}
            {items.map(item => (
                <div key={item.id} className="flex justify-between items-center p-2 bg-tg-bg-primary rounded border border-pink-700/50">
                    <div className="flex items-center">
                        {item.icon_path && <img src={`at-asset://${item.icon_path}?t=${new Date().getTime()}`} alt={item.name} className="w-8 h-8 mr-2 rounded object-contain"/>}
                        <span className="text-sm text-gray-200">{item.name}</span>
                    </div>
                    {/* Edit/Delete buttons for RoleCards are managed in RoleCardPanel/RoleCardEditModal */}
                </div>
            ))}
            <p className="text-center text-xs text-gray-500 py-3 italic">
                角色卡的添加、编辑和删除请在“绝对领域”侧边栏的“姐姐的角色卡”面板中进行。
            </p>
        </div>
    );
    
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[110] p-4 backdrop-blur-md" onClick={onClose}>
            <div className="bg-tg-bg-secondary p-5 rounded-lg shadow-2xl w-full max-w-2xl border border-purple-500 max-h-[85vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold text-purple-300">内容管理中心 (CMS)</h3>
                    <button onClick={onClose} className="p-1 text-gray-400 hover:text-white"><Icon name="X" className="w-5 h-5"/></button>
                </div>
                <div className="flex border-b border-purple-700 mb-3">
                    {/* Tabs for Props, Costumes, Poses are removed as they are now TGC assets */}
                    <button className={getTabClass('role_cards')} onClick={() => setActiveTab('role_cards')}>角色卡预览</button>
                </div>
                <div className="flex-grow overflow-y-auto pr-1 mb-3">
                    {activeTab === 'role_cards' && renderRoleCardList(roleCards)}
                    {(activeTab === 'props' || activeTab === 'costumes' || activeTab === 'poses') && (
                        <div className="text-center text-gray-400 py-6">
                            <p className="text-lg mb-2">"{activeTab === 'props' ? '道具' : activeTab === 'costumes' ? '服装' : '姿势'}" 资产已迁移至【通用资产核心 (TGC-Core)】体系。</p>
                            <p className="text-sm">请在 <code>userData/asset_packs/</code> 目录下通过 <code>.pack.yaml</code> 文件进行管理。</p>
                            <p className="text-xs mt-2">（此界面不再提供对此类资产的编辑功能。）</p>
                        </div>
                    )}
                </div>
                {/* Add New Item button is removed as primary asset types are now YAML based */}
                 <p className="text-center text-xs text-gray-500 py-2 italic border-t border-purple-800/30">
                    通用资产 (道具、服装、姿势等) 请通过编辑 <code>.pack.yaml</code> 文件进行管理。
                </p>
            </div>
            {/* CMSEditItemModal is no longer opened from here for props/costumes/poses */}
        </div>
    );
};