// src/types/roundtableTypes.ts
import type { Character } from './organizationTypes';
import type { ChatMessage } from './chatTypes';
import type { TaskCreationData } from './taskTypes';
import type { AppSettings } from './settingsTypes';
import type { GlobalQuickCommandItem } from './knowledgeTypes';

export interface RoundtableParticipant extends Character {
  isActiveInMeeting: boolean;
  avatarPath?: string | null; // Add avatarPath if not already in Character type
}

export interface ToDoItem {
  id: string;
  text: string;
  isPublished?: boolean;
  isEditing?: boolean;
  assigneeCharacterId?: string | null; // Optional: Assign a character to the to-do
  priority?: number; // Optional: Priority for the to-do
}

// ChatDiscussionAreaRoundtableContext is now defined and exported from uiTypes.ts

export interface ParticipantsPanelProps {
  allCharacters: Character[]; // All available characters (Post, Character etc. from organizationService)
  activeParticipantIds: string[];
  onParticipantToggle: (characterId: string) => void;
  discussionRounds: number;
  onDiscussionRoundsChange: (rounds: number) => void;
  onStartMeeting: () => void; // Called when "Start Meeting" is clicked in ChatDiscussionArea, not directly by panel
  isMeetingActive: boolean;
  isLoading: boolean; // For AI processing or loading characters
}

export interface RoundtableDraftPanelProps {
  sharedDraftContent: string;
  onSharedDraftChange: (content: string) => void;
  toDoItems: ToDoItem[];
  onAddToDoItem: () => void;
  onUpdateToDoItemText: (itemId: string, newText: string) => void;
  onDeleteToDoItem: (itemId: string) => void;
  onPublishToDoItem: (itemId: string) => void;
  onPublishAllToDoItems: () => void;
  isMeetingActive: boolean;
}

export interface ToDoListProps {
  items: ToDoItem[];
  onUpdateItemText: (itemId: string, newText: string) => void;
  onDeleteItem: (itemId: string) => void;
  onPublishItem: (itemId: string) => void;
  onPublishAll: () => void;
  onAddItem: () => void;
  isMeetingActive: boolean;
}

export interface ToDoListItemProps {
  item: ToDoItem;
  onUpdateText: (newText: string) => void;
  onDelete: () => void;
  onPublish: () => void;
  isMeetingActive: boolean;
}