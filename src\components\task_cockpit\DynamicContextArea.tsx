// src/components/task_cockpit/DynamicContextArea.tsx
import React, { useState, useEffect, useRef } from 'react';
import Editor, { Monaco, loader as monacoLoader, OnMount } from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import type { StreamLogEntry, AppSettings } from '@/types';
import { LogOutputDisplay } from './LogOutputDisplay'; // Re-use for focused log view
import { Icon } from '@/components/common/Icon';

monacoLoader.init().then(monacoInstance => {
  console.log("Monaco Editor (DynamicContextArea) initialized via loader.");
}).catch(error => console.error('Failed to initialize Monaco Editor (DynamicContextArea):', error));


type ViewType = 'code_editor' | 'log_viewer' | 'knowledge_viewer' | null;

interface DynamicContextAreaProps {
  viewType: ViewType;
  fileData: { path: string; content: string } | null;
  logData: StreamLogEntry[]; // Focused log stream for this view
  knowledgeData: string | null; // Content of a knowledge tome or RAG result
  onEditorContentChange: (newContent: string) => void;
  onEditorSelectionChange: (selectedText: string) => void;
  onSwitchView: (viewType: ViewType) => void; // To allow panel to request view switch
  settings: AppSettings;
}

const getLanguageForFile = (filePath: string): string => {
  const extension = filePath.split('.').pop()?.toLowerCase();
  if (!extension) return 'plaintext';
  switch (extension) {
    case 'js': return 'javascript';
    case 'jsx': return 'javascript'; 
    case 'ts': return 'typescript';
    case 'tsx': return 'typescript'; 
    case 'css': return 'css';
    case 'json': return 'json';
    case 'py': return 'python';
    case 'md': return 'markdown';
    default: return 'plaintext';
  }
};

export const DynamicContextArea: React.FC<DynamicContextAreaProps> = ({
  viewType,
  fileData,
  logData,
  knowledgeData,
  onEditorContentChange,
  onEditorSelectionChange,
  onSwitchView,
  settings,
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);

  const handleEditorDidMount: OnMount = (editorInstance, monacoInstance) => {
    editorRef.current = editorInstance;
    monacoRef.current = monacoInstance;
    editorInstance.onDidChangeCursorSelection(e => {
        const selectedText = editorInstance.getModel()?.getValueInRange(e.selection) || '';
        onEditorSelectionChange(selectedText);
    });
  };

  const renderContent = () => {
    switch (viewType) {
      case 'code_editor':
        if (!fileData) return <p className="p-4 text-center text-tg-text-placeholder">请从任务资源或通过AI指令打开一个文件。</p>;
        return (
          <Editor
            height="100%"
            path={fileData.path}
            defaultLanguage={getLanguageForFile(fileData.path)}
            value={fileData.content}
            onChange={(value) => onEditorContentChange(value || '')}
            onMount={handleEditorDidMount}
            theme={settings.currentTheme === 'theme-light' ? "vs" : "vs-dark"}
            options={{
              readOnly: false, // Or make it configurable
              minimap: { enabled: true },
              scrollbar: { verticalScrollbarSize: 8, horizontalScrollbarSize: 8 },
              fontSize: 13,
              wordWrap: 'on',
              automaticLayout: true,
            }}
          />
        );
      case 'log_viewer':
        if (!logData || logData.length === 0) return <p className="p-4 text-center text-tg-text-placeholder">暂无日志输出。请执行一个命令以查看其日志。</p>;
        return (
          <div className="p-2 bg-tg-bg-primary h-full overflow-y-auto custom-scrollbar">
            {logData.map(logEntry => <LogOutputDisplay key={logEntry.id} logEntry={logEntry} isFocusedView={true}/>)}
          </div>
        );
      case 'knowledge_viewer':
        if (!knowledgeData) return <p className="p-4 text-center text-tg-text-placeholder">暂无知识内容。请通过AI搜索或指定查阅。</p>;
        return <div className="p-3 prose prose-sm prose-invert max-w-none whitespace-pre-wrap overflow-y-auto custom-scrollbar" dangerouslySetInnerHTML={{ __html: knowledgeData.replace(/\n/g, '<br />') }} />;
      default:
        return (
          <div className="p-4 text-center text-tg-text-placeholder flex flex-col items-center justify-center h-full">
            <Icon name="Lightbulb" className="w-10 h-10 mb-3 text-tg-accent-secondary opacity-70"/>
            <p>动态上下文区域</p>
            <p className="text-xs mt-1">此处将显示代码、日志或知识库内容。</p>
          </div>
        );
    }
  };
  
  const getTabClass = (tabName: ViewType) =>
    `px-3 py-1.5 text-xs rounded-t-md transition-colors flex items-center space-x-1 focus:outline-none
     ${viewType === tabName ? 'bg-tg-bg-primary text-tg-accent-primary border-tg-border-primary border-b-0' : 'bg-tg-bg-tertiary text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary'}`;


  return (
    <div className="h-full flex flex-col bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary overflow-hidden">
      <div className="flex-shrink-0 p-2 border-b border-tg-border-primary flex items-center justify-between">
        <div className="flex items-center space-x-1">
            <button onClick={() => onSwitchView('code_editor')} className={getTabClass('code_editor')} title="代码编辑器">
                <Icon name="CodeBracket" className="w-4 h-4"/> <span>代码</span>
            </button>
            <button onClick={() => onSwitchView('log_viewer')} className={getTabClass('log_viewer')} title="日志查看器">
                <Icon name="ListBullet" className="w-4 h-4"/> <span>日志</span>
            </button>
            <button onClick={() => onSwitchView('knowledge_viewer')} className={getTabClass('knowledge_viewer')} title="知识查阅">
                <Icon name="BookOpen" className="w-4 h-4"/> <span>知识</span>
            </button>
        </div>
        {viewType && (
            <button onClick={() => onSwitchView(null)} className="p-1 text-tg-text-placeholder hover:text-tg-text-primary rounded-full" title="关闭当前视图">
                <Icon name="X" className="w-4 h-4"/>
            </button>
        )}
      </div>
      <div className="flex-grow overflow-hidden relative">
        {renderContent()}
      </div>
    </div>
  );
};