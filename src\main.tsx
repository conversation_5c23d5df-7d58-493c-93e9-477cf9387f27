// src/main.tsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import App from '@/App'; // Adjusted to use @/App consistent with tsconfig
import { HashRouter } from 'react-router-dom';
import '@/assets/styles/main.css'; // Ensured path is to global styles

function renderApp() {
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    console.error("Fatal Error: Could not find root element to mount to. App cannot start.");
    // Optionally, provide a user-facing error message in the body
    document.body.innerHTML = '<div style="color: white; text-align: center; padding-top: 50px;">天工阁启动失败：未能找到核心组件挂载点。请检查浏览器控制台获取更多信息或联系技术支持。</div>';
    return; 
  }

  const root = createRoot(rootElement);
  root.render(
    // StrictMode was previously removed based on user request. If issues persist, it might be worth re-evaluating.
    // <React.StrictMode>
      <HashRouter>
        <App />
      </HashRouter>
    // </React.StrictMode>
  );
}

// Ensures the app renders after the DOM is fully loaded.
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', renderApp);
} else {
  // DOMContentLoaded has already fired
  renderApp();
}