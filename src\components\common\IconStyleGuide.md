# 天工阁图标使用指南

## 标准图标尺寸

为了保持视觉一致性，请使用以下标准尺寸：

- `xs` (12px): 非常小的图标，用于内联文本或紧凑布局
- `sm` (16px): 小图标，用于按钮内或列表项
- `md` (20px): 默认尺寸，用于大多数界面元素
- `lg` (24px): 大图标，用于主要操作按钮
- `xl` (32px): 超大图标，用于重要功能区域
- `2xl` (48px): 特大图标，用于加载状态或占位符

## 使用示例

```tsx
// 使用标准尺寸
<Icon name="House" size="md" />
<Icon name="Settings" size="lg" />

// 使用自定义尺寸
<Icon name="Star" size={28} />

// 带回退机制
<Icon name="CustomIcon" fallback="alert" showTooltipOnError />
```

## 常见图标映射

以下是一些常用图标的推荐映射：

- 加载状态: `Loader` (带动画)
- 设置: `Settings`
- 船舶/舰桥: `Anchor`
- 服装: `Sparkles`
- 滑块控制: `Sliders`
- 心情/情感: `Heart`
- 水滴: `Droplet`

## 颜色使用

建议使用 Tailwind CSS 的颜色类：

- 主要图标: `text-tg-text-primary`
- 次要图标: `text-tg-text-secondary`
- 强调图标: `text-tg-accent-primary`
- 警告图标: `text-tg-warning`
- 危险图标: `text-tg-danger`

## 动画效果

对于加载状态，使用 `animate-spin` 类：

```tsx
<Icon name="Loader" className="animate-spin" />
```

## 错误处理

Icon 组件内置了错误处理机制：

1. 自动映射常见的错误图标名称
2. 提供多种回退选项
3. 开发模式下显示警告信息
4. 可选的错误提示工具提示
