// src/pages/TaskBoardPage.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { useParams, Link, useNavigate, useOutletContext } from 'react-router-dom'; 
import type { Task, TaskStatus, Project, AppSettings, ActiveMainTabType, TaskPriority, ProjectWorkspacePageOutletContext, TaskCreationData } from '@/types';
import { TASK_STATUS_ORDER, PRIORITY_LABELS } from '@/types'; 
import { TaskCard } from '@/components/TaskCard';
import { TaskDetailModal } from '@/components/TaskDetailModal';
import { Icon } from '@/components/common/Icon'; 
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { CaptainsDecisionCenter } from '@/components/bridge_command_center/CaptainsDecisionCenter';


export const TaskBoardPage: React.FC = () => {
  const context = useOutletContext<ProjectWorkspacePageOutletContext>();
  const { project: currentProjectFromContext, projects, settings, onStartCraftingFromTask, onUpdateTaskStatusInApp } = context;
  
  const { projectId: urlProjectId } = useParams<{ projectId?: string }>(); 
  const navigate = useNavigate(); 
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTaskForModal, setSelectedTaskForModal] = useState<Task | null>(null);
  
  const currentProjectId = currentProjectFromContext?.id;
  const currentProject = currentProjectFromContext;

  const [filters, setFilters] = useState<{priority?: TaskPriority, assignee?: string}>({}); // TaskPriority is now number
  const [sortOptions, setSortOptions] = useState<{field: 'created_at' | 'due_date', order: 'ASC' | 'DESC'}>({field: 'created_at', order: 'DESC'});
  const [showFilterSortPanel, setShowFilterSortPanel] = useState(false); // Default to false to hide it initially


  const fetchTasks = useCallback(async (pId: string) => {
    if (!pId) {
      setTasks([]);
      setIsLoading(false);
      setError("请先选择一个项目以查看其任务。");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const apiFilters: any = {};
      if (filters.priority !== undefined) apiFilters.priority = filters.priority;
      if (filters.assignee?.trim()) apiFilters.assignee_id = filters.assignee.trim();
      
      const fetchedTasks = await window.api.tasks.getTasksByProjectId(pId, apiFilters, sortOptions);
      setTasks(fetchedTasks || []);
    } catch (err: any) {
      setError(`加载任务失败: ${err.message}`);
      setTasks([]);
    } finally {
      setIsLoading(false);
    }
  }, [filters, sortOptions]);

  useEffect(() => {
    if (currentProjectId) {
      fetchTasks(currentProjectId);
    } else { 
        setIsLoading(false);
        if (projects.length > 0) {
             setError("请通过侧边栏选择一个项目。");
        } else {
            setError("系统中暂无项目，请先创建项目。");
        }
        setTasks([]);
    }
  }, [currentProjectId, fetchTasks, projects]);
  

  const handleTaskLocallyUpdated = (updatedTask: Task) => {
    setTasks(prevTasks => prevTasks.map(t => t.task_id === updatedTask.task_id ? updatedTask : t));
    if (currentProjectId) fetchTasks(currentProjectId); 
  };
  
  const handleOpenModal = (task: Task | null = null) => {
    setSelectedTaskForModal(task);
    setIsModalOpen(true);
  };

  const handleCloseModal = (refresh?: boolean) => {
    setIsModalOpen(false);
    setSelectedTaskForModal(null);
    if (refresh && currentProjectId) {
      fetchTasks(currentProjectId);
    }
  };

  const onDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId } = result;
    if (!destination || !currentProjectId) return;
    if (source.droppableId === destination.droppableId && source.index === destination.index) return;

    const taskToMove = tasks.find(t => t.task_id === draggableId);
    if (!taskToMove) return;

    const newStatus = destination.droppableId as TaskStatus;
    if (taskToMove.status === newStatus) return;

    const originalTask = { ...taskToMove };
    
    setTasks(prevTasks =>
        prevTasks.map(task =>
            task.task_id === draggableId
                ? { ...task, status: newStatus }
                : task
        )
    );
    
    try {
      const updatedTaskFromApi = await onUpdateTaskStatusInApp(draggableId, newStatus, currentProjectId);
      if (!updatedTaskFromApi) {
        throw new Error("更新任务状态失败，API未返回有效数据。");
      }
      fetchTasks(currentProjectId); 
    } catch (err: any) {
      setError(`任务状态更新失败: ${err.message}. 正在回滚更改...`);
      setTasks(prevTasks => prevTasks.map(t => t.task_id === originalTask.task_id ? originalTask : t));
      setTimeout(() => setError(null), 5000);
    }
  };

  const kanbanColumns: TaskStatus[] = TASK_STATUS_ORDER;
  const statusDisplayNames: Record<TaskStatus, string> = {
    todo: "待办事项",
    doing: "进行中",
    pending_review: "待审核",
    done: "已完成",
    blocked: "已阻塞"
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex flex-col h-full p-4 md:p-6 bg-tg-bg-primary text-tg-text-primary">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
          <h2 className="text-2xl font-bold flex items-center mb-2 sm:mb-0">
              <Icon name="ClipboardList" className="w-7 h-7 mr-2 text-tg-accent-primary"/>神谕罗盘
              {currentProject && <span className="text-lg text-tg-text-secondary ml-2">- {currentProject.name}</span>}
          </h2>
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            <button 
              onClick={() => setShowFilterSortPanel(!showFilterSortPanel)}
              className="p-2 text-sm font-medium rounded-md flex items-center bg-tg-bg-secondary text-tg-text-secondary border border-tg-border-primary hover:bg-tg-bg-hover transition-colors"
              title="筛选与排序"
            >
              <Icon name="AdjustmentsHorizontal" className="w-5 h-5"/>
            </button>
            <button 
              onClick={() => handleOpenModal(null)}
              disabled={!currentProjectId}
              className="px-3 py-2 text-sm font-medium rounded-md flex items-center bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover transition-colors shadow-sm disabled:opacity-50"
            >
              <Icon name="Plus" className="w-5 h-5 mr-1"/> 新建任务
            </button>
          </div>
        </div>

        {currentProject && (
            <div className="mb-4">
                <CaptainsDecisionCenter
                    tasks={tasks}
                    project={currentProject} 
                    onUpdateTaskStatus={async (taskId, newStatus) => {
                        if (currentProjectId) {
                            await onUpdateTaskStatusInApp(taskId, newStatus, currentProjectId);
                            fetchTasks(currentProjectId); 
                        }
                    }}
                />
            </div>
        )}

        {showFilterSortPanel && (
          <div className="p-3 mb-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary shadow-sm text-xs grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 items-end">
            <div>
              <label htmlFor="priority-filter" className="block text-tg-text-secondary mb-1">优先级:</label>
              <select id="priority-filter" value={filters.priority === undefined ? 'all' : String(filters.priority)} onChange={e => setFilters(f => ({...f, priority: e.target.value === 'all' ? undefined : Number(e.target.value) as TaskPriority}))}
                className="w-full p-1.5 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary text-xs">
                <option value="all">所有</option>
                {Object.keys(PRIORITY_LABELS).map(pKeyString => (
                  <option key={pKeyString} value={pKeyString}>
                    {PRIORITY_LABELS[Number(pKeyString) as TaskPriority]}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="assignee-filter" className="block text-tg-text-secondary mb-1">责任人:</label>
              <input id="assignee-filter" type="text" value={filters.assignee || ''} onChange={e => setFilters(f => ({...f, assignee: e.target.value}))} placeholder="输入责任人ID"
                className="w-full p-1.5 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary text-xs"/>
            </div>
            <div>
              <label htmlFor="sort-field-filter" className="block text-tg-text-secondary mb-1">排序字段:</label>
              <select id="sort-field-filter" value={sortOptions.field} onChange={e => setSortOptions(s => ({...s, field: e.target.value as 'created_at' | 'due_date'}))}
                className="w-full p-1.5 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary text-xs">
                <option value="created_at">创建时间</option>
                <option value="due_date">截止日期</option>
              </select>
            </div>
            <div>
              <label htmlFor="sort-order-filter" className="block text-tg-text-secondary mb-1">排序顺序:</label>
              <button id="sort-order-filter" onClick={() => setSortOptions(s => ({...s, order: s.order === 'ASC' ? 'DESC' : 'ASC'}))}
                className="w-full p-1.5 bg-tg-bg-tertiary border border-tg-border-primary rounded hover:bg-tg-bg-hover flex items-center justify-center text-xs">
                {sortOptions.order === 'ASC' ? <Icon name="ChevronUp" className="w-3 h-3 mr-1"/> : <Icon name="ChevronDown" className="w-3 h-3 mr-1"/>}
                {sortOptions.order === 'ASC' ? '升序' : '降序'}
              </button>
            </div>
          </div>
        )}

        {isLoading && <div className="flex-grow flex items-center justify-center"><Icon name="ArrowPath" className="w-8 h-8 animate-spin text-tg-accent-secondary"/> 加载任务中...</div>}
        {error && !isLoading && <div className="my-4 p-3 bg-red-800/30 text-red-300 rounded-md text-sm text-center"><Icon name="ExclamationTriangle" className="w-5 h-5 inline mr-1.5"/>{error}</div>}
        
        {!isLoading && !error && !currentProjectId && projects.length > 0 && (
            <div className="flex-grow flex items-center justify-center text-tg-text-placeholder">请通过侧边栏选择一个项目以查看其任务。</div>
        )}
        {!isLoading && !error && currentProjectId && tasks.length === 0 && !error && (
            <div className="flex-grow flex items-center justify-center text-tg-text-placeholder">此项目暂无任务，或无匹配筛选结果。点击“创建新任务”开始吧！</div>
        )}

        {!isLoading && !error && currentProjectId && (
          <div className="flex-grow grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 overflow-x-auto pb-4">
            {kanbanColumns.map(status => (
              <Droppable key={status} droppableId={status}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...(provided.droppableProps || {})}
                    className={`p-3 rounded-lg bg-tg-bg-secondary shadow-md flex flex-col min-h-[200px] border-2
                                ${snapshot.isDraggingOver ? 'border-tg-accent-primary bg-tg-bg-hover' : 'border-transparent'}`}
                    aria-label={`任务列: ${statusDisplayNames[status]}`}
                  >
                    <h3 className="text-md font-semibold mb-3 text-tg-text-primary sticky top-0 bg-tg-bg-secondary py-1">{statusDisplayNames[status]} ({tasks.filter(t => t.status === status).length})</h3>
                    <div className="space-y-2.5 overflow-y-auto flex-grow custom-scrollbar pr-1 min-h-[100px]"> {/* Task card container */}
                      {tasks.filter(t => t.status === status).length === 0 && (
                          <p className="text-xs text-tg-text-placeholder text-center py-4 italic">此列暂无任务</p>
                      )}
                      {tasks.filter(t => t.status === status)
                        .sort((a,b) => a.priority - b.priority || new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
                        .map((task, index) => (
                        <Draggable key={task.task_id} draggableId={task.task_id} index={index}>
                          {(providedDraggable, snapshotDraggable) => (
                            <div
                              ref={providedDraggable.innerRef}
                              {...providedDraggable.draggableProps}
                              {...providedDraggable.dragHandleProps}
                              style={{
                                ...(providedDraggable.draggableProps.style ?? {}),
                                opacity: snapshotDraggable.isDragging ? 0.8 : 1,
                                boxShadow: snapshotDraggable.isDragging ? '0 5px 15px rgba(var(--color-shadow-rgb),0.2)' : 'none', 
                              }}
                            >
                              <TaskCard 
                                task={task} 
                                onClick={() => handleOpenModal(task)}
                                onEnterCockpit={(e) => { 
                                    e.stopPropagation();
                                    if (currentProjectId) {
                                      navigate(`/project/${currentProjectId}/cockpit/${task.task_id}`);
                                    }
                                }}
                                onDragStart={() => {}} 
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            ))}
          </div>
        )}

        {isModalOpen && currentProjectId && (
          <TaskDetailModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            task={selectedTaskForModal}
            projectId={currentProjectId}
            allProjectTasks={tasks} 
            onTaskUpdated={handleTaskLocallyUpdated}
            onStartCrafting={onStartCraftingFromTask}
          />
        )}
      </div>
    </DragDropContext>
  );
};
