import React, { useState } from 'react';
import type { FileNode } from '@/types';
import { Icon } from '@/components/common/Icon';

interface FileTreeNodeProps {
  node: FileNode;
  onFileSelect: (filePath: string) => void;
  level: number;
  selectedFilePath: string | null;
}

export const FileTreeNode: React.FC<FileTreeNodeProps> = ({ node, onFileSelect, level, selectedFilePath }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    if (node.type === 'directory') {
      setIsOpen(!isOpen);
    }
  };

  const handleFileClick = () => {
    if (node.type === 'file') {
      onFileSelect(node.path);
    } else if (node.type === 'directory') {
      handleToggle(); 
    }
  };
  
  const isSelected = node.type === 'file' && node.path === selectedFilePath;

  return (
    <div>
      <div
        className={`flex items-center p-1.5 rounded-md cursor-pointer hover:bg-tg-bg-tertiary transition-colors
                    ${isSelected ? 'bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover' : 'text-tg-text-secondary'}`}
        style={{ paddingLeft: `${level * 16 + (node.type === 'directory' ? 0 : 16)}px` }} 
        onClick={handleFileClick}
        role="treeitem"
        aria-expanded={node.type === 'directory' ? isOpen : undefined}
        aria-selected={isSelected}
        title={node.path}
      >
        {node.type === 'directory' && (
          <button 
            onClick={(e) => { e.stopPropagation(); handleToggle();}} 
            className={`mr-1 p-0.5 rounded hover:bg-tg-bg-hover ${isSelected ? 'text-white' : 'text-tg-text-placeholder'}`}
            aria-label={isOpen ? "折叠" : "展开"}
          >
            <Icon name={isOpen ? "ChevronDown" : "ChevronRight"} className="w-3.5 h-3.5" />
          </button>
        )}
        <Icon 
            name={node.type === 'directory' ? 'Folder' : 'File'}
            className={`w-4 h-4 mr-1.5 flex-shrink-0 ${isSelected ? 'text-white' : (node.type === 'directory' ? 'text-tg-accent-secondary' : 'text-tg-text-primary')}`} 
        />
        <span className={`text-sm truncate ${isSelected ? 'font-semibold' : ''}`}>{node.name}</span>
         {node.error && (
            <Icon name="AlertTriangle" className="w-3.5 h-3.5 ml-auto text-tg-warning flex-shrink-0" title={`错误: ${node.error}`}/>
        )}
      </div>
      {node.type === 'directory' && isOpen && node.children && (
        <div className="pl-0"> 
          {node.children.length === 0 && !node.error && (
            <div 
                className="p-1.5 text-xs text-tg-text-placeholder italic"
                style={{ paddingLeft: `${(level + 1) * 16 + 16}px` }}
            >
                (空目录)
            </div>
          )}
          {node.children.map(child => (
            <FileTreeNode 
                key={child.path} 
                node={child} 
                onFileSelect={onFileSelect} 
                level={level + 1} 
                selectedFilePath={selectedFilePath}
            />
          ))}
        </div>
      )}
    </div>
  );
};