# 天工阁视觉层次设计指南

## 图标尺寸层次

### 主要层次
- **特大图标 (2xl - 48px)**: 主要功能区域、加载状态、重要提示
- **超大图标 (xl - 32px)**: 页面标题、主要操作按钮
- **大图标 (lg - 24px)**: 导航菜单、重要功能按钮
- **中等图标 (md - 20px)**: 默认尺寸，一般界面元素
- **小图标 (sm - 16px)**: 次要功能、内联图标
- **微小图标 (xs - 12px)**: 状态指示器、装饰性图标

### 使用场景

#### 导航和主要功能
```tsx
// 主导航
<Icon name="House" size="lg" className="text-tg-accent-primary" />

// 页面标题图标
<Icon name="Settings" size="xl" className="text-gradient" />

// 主要操作按钮
<Icon name="Plus" size="lg" className="text-white" />
```

#### 内容和次要功能
```tsx
// 列表项图标
<Icon name="File" size="md" className="text-tg-text-secondary" />

// 内联操作
<Icon name="Edit" size="sm" className="text-tg-text-placeholder" />

// 状态指示
<Icon name="CheckCircle" size="xs" className="text-tg-success" />
```

## 颜色层次

### 主要颜色
- **主色调**: `text-tg-accent-primary` - 重要功能和强调
- **渐变色**: `text-gradient` - 标题和特殊元素
- **成功色**: `text-tg-success` - 成功状态和确认操作
- **警告色**: `text-tg-warning` - 警告和注意事项
- **危险色**: `text-tg-danger` - 错误和删除操作

### 次要颜色
- **主要文本**: `text-tg-text-primary` - 一般内容
- **次要文本**: `text-tg-text-secondary` - 辅助信息
- **占位符**: `text-tg-text-placeholder` - 提示和占位

### 特殊效果
- **发光效果**: `glow-on-hover` - 重要交互元素
- **浮动动画**: `animate-float` - 装饰性元素
- **脉冲效果**: `animate-pulse` - 状态指示器

## 间距和布局

### 组件间距
- **紧密**: `space-x-1 space-y-1` (4px) - 相关元素
- **标准**: `space-x-2 space-y-2` (8px) - 一般间距
- **宽松**: `space-x-4 space-y-4` (16px) - 组件间
- **分离**: `space-x-6 space-y-6` (24px) - 区域间

### 内边距
- **紧凑**: `p-2` (8px) - 小按钮、标签
- **标准**: `p-4` (16px) - 一般容器
- **宽松**: `p-6` (24px) - 卡片、面板
- **超宽**: `p-8` (32px) - 主要容器

## 阴影和深度

### 阴影层次
- **浅阴影**: `shadow-sm` - 轻微分离
- **标准阴影**: `shadow-md` - 一般卡片
- **深阴影**: `shadow-lg` - 重要元素
- **超深阴影**: `shadow-xl` - 模态框、悬浮元素
- **特殊阴影**: `shadow-2xl` - 最高层级

### 发光效果
- **蓝色发光**: `shadow-glow` - 主要交互
- **紫色发光**: `shadow-glow-purple` - 特殊功能
- **绿色发光**: `shadow-glow-green` - 成功状态

## 圆角设计

### 圆角层次
- **微圆角**: `rounded-sm` (2px) - 小元素
- **标准圆角**: `rounded-md` (6px) - 按钮
- **大圆角**: `rounded-lg` (8px) - 卡片
- **超大圆角**: `rounded-xl` (12px) - 面板
- **特大圆角**: `rounded-2xl` (16px) - 主要容器
- **圆形**: `rounded-full` - 头像、图标按钮

## 动画和过渡

### 标准过渡
```css
transition-all duration-300 ease-in-out
```

### 悬停效果
```css
hover:scale-105 hover:shadow-lg hover:-translate-y-1
```

### 加载动画
```css
animate-spin animate-pulse animate-bounce-subtle
```

## 实施建议

1. **保持一致性**: 同类元素使用相同的视觉处理
2. **建立层次**: 重要性不同的元素使用不同的视觉权重
3. **适度使用**: 避免过度装饰，保持界面清洁
4. **响应式设计**: 在不同屏幕尺寸下保持良好的视觉效果
5. **可访问性**: 确保足够的对比度和可读性
