// src/types.ts
// This file re-exports types from their new, more organized locations.

// Explicitly re-export types from each sub-module
export type { ImportanceLevel, FileNode, ModelOption, PaginationOptions, FileVersion, BodyZoneKey } from './types/commonTypes';

// Use export * for projectTypes to correctly export both value and type for WisdomPouchType
export * from './types/projectTypes';

export type { ChatMessageSenderType, MessageThemeType, XPHypothesisElementDetail, XPHypothesisElements, XPHypothesisChoice, XPHypothesisProposalData, XPHypothesis, ChatMessage, SummarizeAndReplaceResult, ExtractedChatMessageItemProps } from './types/chatTypes';
export type { CMSType, CMSItemBase, PropItem, CostumeItem, PoseItem, RolePlayingCard, LinLuoBodyDevelopment, LinLuoDetailedStatus, AchievementTypeTGC, AchievementRewardType, AchievementCriteriaType, AchievementCriteria, AchievementReward, Achievement, UserAchievement, UnlockRequirement } from './types/cmsTypes';
export type { KnowledgeTomeCategory, KnowledgeTome, GlobalQuickCommandItem, RetrievedChunk } from './types/knowledgeTypes';
export type { TaskStatus, TaskPriority, ResourceType, TaskResourceLink, Task, TaskCreationData, DevelopmentTask, DevelopmentTaskCreationPayload } from './types/taskTypes';
export type { SoundName, AudioPlaybackState, AppTheme, AiThinkingState, AppSettings } from './types/settingsTypes';
export type { AITaskStatus, AiCallContextType, XPHypothesisFeedbackData, LinLuoSystemUpdatePayload, AIResponseWithStatus, AiCallContext, AiCallContextInfoForMemory, AgentCoreSettingId, AgentCoreSetting, CoreMemoryPersonaTarget, CoreMemoryImportance, CoreMemoryStatus, CoreMemory, RelevantMemoriesSet, AILearningLogTaskType, AILearningLog, GenerateCodeContext, ExplainCodeContext, GenerateDocContext, ReviewCodeContext, AnalyzeErrorContext, AIInteractionHistoryItem, RouteUserIntentResponse, AICommandAnalysisResult } from './types/aiTypes';
export type { CommandExecutionEvent, IElectronAPI } from './types/electronAPITypes';
export type { FloatingStyleToolbarProps, ActiveMainTabType, AICodeAssistPanelProps, SaveToKnowledgeModalProps, ChatDiscussionAreaRef, AbsoluteTerritoryPageProps, ActionPlanModalProps, TaskDetailModalProps, CreateTaskFromChatMessageModalProps, ProjectSidebarProps, ProjectWorkspacePageOutletContext } from './types/uiTypes'; 
export type { AssetType, AssetBase, PropAsset, CostumeAsset, PoseAsset, TGCRoleCardAsset, SceneCardAsset, SceneNodeTGC, ScriptAsset, ScriptChoice, AnyLoadedAsset, ThemeAsset } from './types/assetTypes'; // ThemeAsset ADDED HERE
export type { Post, Character, Assignment } from './types/organizationTypes';

// Corrected exports for roundtable and UI context types
export type { ChatDiscussionAreaRoundtableContext } from './types/uiTypes';
export type { RoundtableParticipant, ToDoItem, ParticipantsPanelProps, RoundtableDraftPanelProps, ToDoListProps, ToDoListItemProps } from './types/roundtableTypes';


// Specific constant re-exports
export { KNOWLEDGE_TOME_CATEGORIES_MAP } from '@/features/knowledge_tomes/knowledgeConstants';
export { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';
export { ALL_BODY_ZONES } from './types/commonTypes';
export { TASK_STATUS_ORDER, PRIORITY_LABELS, RESOURCE_TYPE_DISPLAY_NAMES } from './types/taskTypes';

// Export IconProps from the new central Icon component
export type { IconProps } from '@/components/common/Icon';

// Corrected import path for aiTypes
export type { InvokeSandboxRequestArgs, InvokeTerritoryRequestArgs, ScriptModeInfo } from './types/aiTypes'; 


export {}; // Ensure this file is treated as a module
