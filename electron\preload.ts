// electron/preload.ts
import { context<PERSON><PERSON>, ipcRenderer, OpenDialogOptions, IpcRendererEvent } from 'electron';
import type { 
    AiCallContextInfoForMemory, AILearningLog, RolePlayingCard, CMSItemBase, CMSType, BodyZoneKey, LinLuoBodyDevelopment, Achievement, UserAchievement, 
    Task, TaskResourceLink, TaskStatus, TaskPriority, ResourceType, AIResponseWithStatus,
    GenerateCodeContext, ExplainCodeContext, GenerateDocContext, ReviewCodeContext, AnalyzeErrorContext, 
    AITaskStatus, AiCallContextType, AgentCoreSettingId, AICodeAssistPanelProps, AIInteractionHistoryItem,
    FileNode, 
    CommandExecutionEvent, 
    Project, AppSettings, NoteItem, MindNode, MindConnection, KnowledgeTome, GlobalQuickCommandItem,
    ProjectKnowledgeTome, SummarizeAndReplaceResult, WisdomPouchType, DevelopmentTask, CoreMemory, ModelOption, AudioPlaybackState, SoundName,
    NewProjectDataForApi, PaginationOptions, RouteUserIntentResponse, 
    TaskCreationData, AnyLoadedAsset, FileVersion, AICommandAnalysisResult,
    ChatMessage, CoreMemoryPersonaTarget, DevelopmentTaskCreationPayload,
    Post, Character, Assignment, RoundtableParticipant, ToDoItem, AiCallContext, 
    InvokeSandboxRequestArgs, InvokeTerritoryRequestArgs
} from '../types'; 

const exposedAPI = {
  testPreload: () => console.log('Preload script is alive!'),
  database: {
    getSettings: () => ipcRenderer.invoke('db:getSettings'),
    saveSettings: (settings: AppSettings) => ipcRenderer.invoke('db:saveSettings', settings),
    getAllProjects: () => ipcRenderer.invoke('db:getAllProjects'),
    addProject: (projectData: NewProjectDataForApi) => ipcRenderer.invoke('db:addProject', projectData),
    getProjectById: (projectId: string) => ipcRenderer.invoke('db:getProjectById', projectId),
    updateProject: (projectData: Project) => ipcRenderer.invoke('db:updateProject', projectData),
    deleteProject: (projectId: string) => ipcRenderer.invoke('db:deleteProject', projectId),
    duplicateProject: (projectId: string) => ipcRenderer.invoke('db:duplicateProject', projectId),
    addNoteToProject: (projectId: string, pouchType: WisdomPouchType, noteData: NoteItem) => ipcRenderer.invoke('db:addNoteToProject', projectId, pouchType, noteData),
    updateNoteInProject: (pouchType: WisdomPouchType, noteData: NoteItem) => ipcRenderer.invoke('db:updateNoteInProject', pouchType, noteData),
    deleteNoteFromProject: (pouchType: WisdomPouchType, noteId: string) => ipcRenderer.invoke('db:deleteNoteFromProject', pouchType, noteId),
    updateProjectMindMap: (projectId: string, nodes: MindNode[], connections: MindConnection[]) => ipcRenderer.invoke('db:updateProjectMindMap', projectId, nodes, connections),
    getAllGlobalKnowledgeTomes: () => ipcRenderer.invoke('db:getAllGlobalKnowledgeTomes'),
    addGlobalKnowledgeTome: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => ipcRenderer.invoke('db:addGlobalKnowledgeTome', tomeData),
    updateGlobalKnowledgeTome: (tomeData: KnowledgeTome) => ipcRenderer.invoke('db:updateGlobalKnowledgeTome', tomeData),
    deleteGlobalKnowledgeTome: (tomeId: string) => ipcRenderer.invoke('db:deleteGlobalKnowledgeTome', tomeId),
    addProjectKnowledgeTome: (projectId: string, tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => ipcRenderer.invoke('db:addProjectKnowledgeTome', projectId, tomeData),
    updateProjectKnowledgeTome: (projectId: string, tomeData: ProjectKnowledgeTome) => ipcRenderer.invoke('db:updateProjectKnowledgeTome', projectId, tomeData),
    deleteProjectKnowledgeTome: (projectId: string, tomeId: string) => ipcRenderer.invoke('db:deleteProjectKnowledgeTome', projectId, tomeId),
    addProjectKnowledgeCategory: (projectId: string, categoryName: string) => ipcRenderer.invoke('db:addProjectKnowledgeCategory', projectId, categoryName),
    removeProjectKnowledgeCategory: (projectId: string, categoryName: string) => ipcRenderer.invoke('db:removeProjectKnowledgeCategory', projectId, categoryName),
    getAllDevelopmentTasks: () => ipcRenderer.invoke('db:getAllDevelopmentTasks'),
    addDevelopmentTask: (projectId: string, title: string) => ipcRenderer.invoke('db:addDevelopmentTask', projectId, title),
    createDevelopmentTaskFromChat: (payload: DevelopmentTaskCreationPayload) => ipcRenderer.invoke('db:createDevelopmentTaskFromChat', payload),
    deleteDevelopmentTask: (taskId: string) => ipcRenderer.invoke('db:deleteDevelopmentTask', taskId),
    updateDevelopmentTaskContextFiles: (taskId: string, contextFiles: string[]) => ipcRenderer.invoke('db:updateDevelopmentTaskContextFiles', taskId, contextFiles),
    updateDevelopmentTaskGeneratedCode: (taskId: string, generatedCode: string) => ipcRenderer.invoke('db:updateDevelopmentTaskGeneratedCode', taskId, generatedCode),
    getAgentCoreSetting: (settingId: AgentCoreSettingId) => ipcRenderer.invoke('db:getAgentCoreSetting', settingId),
    getAllAgentCoreSettings: () => ipcRenderer.invoke('db:getAllAgentCoreSettings'),
    saveAgentCoreSetting: (settingId: AgentCoreSettingId, content: string) => ipcRenderer.invoke('db:saveAgentCoreSetting', settingId, content),
    findRelevantMemories: (queryEmbedding: number[], contextInfo: AiCallContextInfoForMemory, limit: number) => ipcRenderer.invoke('db:findRelevantMemories', queryEmbedding, contextInfo, limit),
    getCoreMemories: (personaTarget: CoreMemoryPersonaTarget, projectContextId: string | null, limit: number, importanceThreshold: string | null, keywords: string[]) => ipcRenderer.invoke('db:getCoreMemories', personaTarget, projectContextId, limit, importanceThreshold, keywords),
    getAllCoreMemories: (filters: any, sort: any, pagination: PaginationOptions) => ipcRenderer.invoke('db:getAllCoreMemories', filters, sort, pagination),
    addCoreMemory: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => ipcRenderer.invoke('db:addCoreMemory', memoryData),
    addCoreMemoryFromChat: (messageText: string, personaTarget: CoreMemoryPersonaTarget, projectId: string | null) => ipcRenderer.invoke('db:addCoreMemoryFromChat', messageText, personaTarget, projectId),
    updateCoreMemory: (memoryData: CoreMemory) => ipcRenderer.invoke('db:updateCoreMemory', memoryData),
    deleteCoreMemory: (memoryId: string) => ipcRenderer.invoke('db:deleteCoreMemory', memoryId),
    getCoreMemoryById: (memoryId: string) => ipcRenderer.invoke('db:getCoreMemoryById', memoryId),
    getAbsoluteTerritoryMessages: (limit: number, beforeTimestamp?: string) => ipcRenderer.invoke('db:getAbsoluteTerritoryMessages', limit, beforeTimestamp),
    addAbsoluteTerritoryMessage: (message: ChatMessage) => ipcRenderer.invoke('db:addAbsoluteTerritoryMessage', message),
    clearAbsoluteTerritoryHistory: () => ipcRenderer.invoke('db:clearAbsoluteTerritoryHistory'),
    addLearningLog: (logData: Omit<AILearningLog, 'log_id' | 'timestamp'>) => ipcRenderer.invoke('db:addLearningLog', logData),
    getLearningLogs: (filters: any, limit: number, offset: number) => ipcRenderer.invoke('db:getLearningLogs', filters, limit, offset),
    getBodyDevelopment: (zone_id: BodyZoneKey) => ipcRenderer.invoke('db:getBodyDevelopment', zone_id),
    getAllBodyDevelopment: () => ipcRenderer.invoke('db:getAllBodyDevelopment'),
    updateBodyDevelopment: (zone_id: BodyZoneKey, pointsToAdd: number) => ipcRenderer.invoke('db:updateBodyDevelopment', zone_id, pointsToAdd),
    getAllAchievements: () => ipcRenderer.invoke('db:getAllAchievements'),
    getUserAchievements: (userId: string) => ipcRenderer.invoke('db:getUserAchievements', userId),
    unlockAchievement: (userId: string, achievementId: string) => ipcRenderer.invoke('db:unlockAchievement', userId, achievementId),
    getAchievementById: (achievementId: string) => ipcRenderer.invoke('db:getAchievementById', achievementId),
    saveChatMessage: (projectId: string, message: ChatMessage) => ipcRenderer.invoke('db:saveChatMessage', projectId, message),
    updateChatMessage: (message: ChatMessage) => ipcRenderer.invoke('db:updateChatMessage', message),
    getInitialChatMessages: (projectId: string, limit: number) => ipcRenderer.invoke('db:getInitialChatMessages', projectId, limit),
    getOlderChatMessages: (projectId: string, beforeTimestamp: string, limit: number) => ipcRenderer.invoke('db:getOlderChatMessages', projectId, beforeTimestamp, limit),
    summarizeAndReplaceMessages: (projectId: string, messageIds: string[], summaryMessage: ChatMessage) => ipcRenderer.invoke('db:summarizeAndReplaceMessages', projectId, messageIds, summaryMessage),
    getAllGlobalQuickCommands: () => ipcRenderer.invoke('db:getAllGlobalQuickCommands'),
    addGlobalQuickCommand: (commandData: Omit<GlobalQuickCommandItem, 'id' | 'createdAt' | 'lastModifiedAt'>) => ipcRenderer.invoke('db:addGlobalQuickCommand', commandData),
    updateGlobalQuickCommand: (commandData: GlobalQuickCommandItem) => ipcRenderer.invoke('db:updateGlobalQuickCommand', commandData),
    deleteGlobalQuickCommand: (commandId: string) => ipcRenderer.invoke('db:deleteGlobalQuickCommand', commandId),
  },
  settings: { 
    getAbsoluteTerritoryPassword: () => ipcRenderer.invoke('settings:getAbsoluteTerritoryPassword'),
    setAbsoluteTerritoryPassword: (password: string | null) => ipcRenderer.invoke('settings:setAbsoluteTerritoryPassword', password),
    verifyAbsoluteTerritoryPassword: (password: string) => ipcRenderer.invoke('settings:verifyAbsoluteTerritoryPassword', password),
  },
  tasks: { 
    getTaskById: (taskId: string) => ipcRenderer.invoke('tasks:getTaskById', taskId),
    getTasksByProjectId: (projectId: string, filters: any, sortOptions: any) => ipcRenderer.invoke('tasks:getTasksByProjectId', projectId, filters, sortOptions),
    getTasksByStatus: (projectId: string, statusArray: TaskStatus[]) => ipcRenderer.invoke('tasks:getTasksByStatus', projectId, statusArray),
    addTask: (taskData: TaskCreationData) => ipcRenderer.invoke('tasks:addTask', taskData),
    updateTask: (taskId: string, updates: Partial<Task>) => ipcRenderer.invoke('tasks:updateTask', taskId, updates),
    deleteTask: (taskId: string) => ipcRenderer.invoke('tasks:deleteTask', taskId),
    addResourceLinkToTask: (taskId: string, resourceData: Omit<TaskResourceLink, 'link_id' | 'task_id'>) => ipcRenderer.invoke('tasks:addResourceLink', taskId, resourceData),
    getResourceLinksForTask: (taskId: string) => ipcRenderer.invoke('tasks:getResourceLinks', taskId),
    removeResourceLinkFromTask: (linkId: string) => ipcRenderer.invoke('tasks:removeResourceLink', linkId),
    suggestResourcesForTask: (taskId: string, taskTitle: string, taskDescription: string | undefined, projectId: string) => ipcRenderer.invoke('tasks:suggestResources', taskId, taskTitle, taskDescription, projectId),
  },
  ai: {
    invokeSandboxRequest: (args: InvokeSandboxRequestArgs) => ipcRenderer.invoke('ai:invoke-sandbox-request', args),
    invokeTerritoryRequest: (args: InvokeTerritoryRequestArgs) => ipcRenderer.invoke('ai:invoke-territory-request', args),
    getAvailableModels: () => ipcRenderer.invoke('ai:getAvailableModels'),
    callAI: (prompt: string, persona: string, context: AiCallContext) => ipcRenderer.invoke('ai:callAI', prompt, persona, context),
    discussWithAI: (contents: any, config: any, modelName: string, apiKey?: string) => ipcRenderer.invoke('ai:discussWithAI', contents, config, modelName, apiKey),
    summarizeConversation: (historyChunk: ChatMessage[]) => ipcRenderer.invoke('ai:summarizeConversation', historyChunk),
    decomposeRequirementToTasks: (requirementText: string, projectId: string) => ipcRenderer.invoke('ai:decomposeRequirementToTasks', requirementText, projectId),
    analyzeAndDecomposeAideProject: (projectId: string, projectPath: string) => ipcRenderer.invoke('ai:analyzeAndDecomposeAideProject', projectId, projectPath),
    routeUserIntent: (userInputText: string, context: AiCallContext) => ipcRenderer.invoke('ai:routeUserIntent', userInputText, context),
    startRoundtableMeeting: (initialPrompt: string, participantCharacterIds: string[], turnLimit: number, projectId: string, initialHistory: ChatMessage[]) => ipcRenderer.invoke('ai:startRoundtableMeeting', initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory),
    assist: { 
        generateCode: (context: GenerateCodeContext) => ipcRenderer.invoke('aiCodeAssist:generateCode', context),
        explainCode: (context: ExplainCodeContext) => ipcRenderer.invoke('aiCodeAssist:explainCode', context),
        generateDocForCode: (context: GenerateDocContext) => ipcRenderer.invoke('aiCodeAssist:generateDocForCode', context),
        reviewCode: (context: ReviewCodeContext) => ipcRenderer.invoke('aiCodeAssist:reviewCode', context),
        analyzeErrorLog: (context: AnalyzeErrorContext) => ipcRenderer.invoke('aiCodeAssist:analyzeErrorLog', context),
        modifyCode: (context: { filePath: string, userInstruction: string, originalCode: string }) => ipcRenderer.invoke('ai:assist:modifyCode', context),
    }
  },
  bridgeAi: { 
    processIntent: (userInputText: string) => ipcRenderer.invoke('bridgeAi:processIntent', userInputText),
  },
  fs: {
    openDirectoryDialog: () => ipcRenderer.invoke('fs:openDirectoryDialog'),
    readDirectory: (dirPath: string) => ipcRenderer.invoke('fs:readDirectory', dirPath),
    readFileContent: (filePath: string) => ipcRenderer.invoke('fs:readFileContent', filePath),
    saveFileContent: (filePath: string, content: string) => ipcRenderer.invoke('fs:saveFileContent', filePath, content),
    openFileDialog: (options?: OpenDialogOptions) => ipcRenderer.invoke('fs:openFileDialog', options),
    openMultipleFilesDialog: (extensions: string[]) => ipcRenderer.invoke('fs:openMultipleFilesDialog', extensions),
    copyFileToUserData: (sourcePath: string, targetSubdir: string, targetFilename?: string) => ipcRenderer.invoke('fs:copyFileToUserData', sourcePath, targetSubdir, targetFilename),
    exportChatHistory: (messages: ChatMessage[], format: 'md' | 'html', defaultFileName: string) => ipcRenderer.invoke('fs:exportChatHistory', messages, format, defaultFileName),
    listFiles: (directoryPath: string, recursive?: boolean, depth?: number) => ipcRenderer.invoke('fs:listFiles', directoryPath, recursive, depth),
    readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
    writeFile: (filePath: string, content: string) => ipcRenderer.invoke('fs:writeFile', filePath, content),
    archiveFileVersion: (filePath: string, originalCode: string, userInstruction: string) => ipcRenderer.invoke('fs:archiveFileVersion', filePath, originalCode, userInstruction),
    isDirectoryEmpty: (directoryPath: string) => ipcRenderer.invoke('fs:isDirectoryEmpty', directoryPath),
    copyDirectoryContents: (sourceDir: string, targetDir: string) => ipcRenderer.invoke('fs:copyDirectoryContents', sourceDir, targetDir),
  },
  file: {
    readPackageJsonScripts: (projectPath: string) => ipcRenderer.invoke('file:read-package-json-scripts', projectPath),
  },
  rag: {
    runProjectIndexing: (projectId: string, projectPath: string, options: { apiKey: string; embeddingModelName: string }) => ipcRenderer.invoke('rag:runProjectIndexing', projectId, projectPath, options),
    retrieveRelevantChunks: (queryText: string, projectId: string, apiKey: string, embeddingModelName: string, topK?: number) => ipcRenderer.invoke('rag:retrieveRelevantChunks', queryText, projectId, apiKey, embeddingModelName, topK),
    indexFileContent: (projectId: string, filePath: string, fileContent: string, options: { apiKey: string; embeddingModelName: string }) => ipcRenderer.invoke('rag:indexFileContent', projectId, filePath, fileContent, options),
  },
  audio: {
    playSound: (soundName: SoundName, loop: boolean) => ipcRenderer.invoke('audio:playSound', soundName, loop),
    stopSound: (soundName?: SoundName) => ipcRenderer.invoke('audio:stopSound', soundName),
    setVolume: (volume: number) => ipcRenderer.invoke('audio:setVolume', volume),
    getPlaybackState: () => ipcRenderer.invoke('audio:getPlaybackState'),
    onPlaybackStateChanged: (callback: (newState: AudioPlaybackState) => void) => {
        const handler = (event: IpcRendererEvent, newState: AudioPlaybackState) => callback(newState);
        ipcRenderer.on('audio:playbackStateChanged', handler);
        return () => ipcRenderer.removeListener('audio:playbackStateChanged', handler);
    },
  },
  electronUtils: {
    getPlatform: () => ipcRenderer.invoke('electronUtils:getPlatform'),
  },
  utils: {
    combinePaths: (basePath: string, relativePath: string) => ipcRenderer.invoke('utils:combinePaths', basePath, relativePath),
  },
  assets: {
    getLoadedAssets: () => ipcRenderer.invoke('assets:getLoadedAssets'),
    refreshAssetPacks: () => ipcRenderer.invoke('assets:refreshAssetPacks'),
    createAssetPack: (fileName: string, assetPackData: any) => ipcRenderer.invoke('assets:createAssetPack', fileName, assetPackData),
    deleteAssetPack: (fileName: string) => ipcRenderer.invoke('assets:deleteAssetPack', fileName),
  },
  command: {
    execute: (params: { commandString: string; args: string[]; cwd: string }) => ipcRenderer.invoke('command:execute', params),
    kill: (internalPid: string) => ipcRenderer.invoke('command:kill', internalPid),
    onEvent: (callback: (event: CommandExecutionEvent) => void) => {
        const handler = (event: IpcRendererEvent, commandEvent: CommandExecutionEvent) => callback(commandEvent);
        ipcRenderer.on('command:event', handler);
        return () => ipcRenderer.removeListener('command:event', handler);
    },
    analyzeLog: (logContent: string) => ipcRenderer.invoke('command:analyze-log', logContent),
  },
  cms: {
    getCMSItems: (type: CMSType) => ipcRenderer.invoke('cms:getCMSItems', type),
    addCMSItem: (type: CMSType, itemData: any, iconBase64?: string | null, cgBase64?: string | null, statusEffectsJson?: string, developmentEffectsJson?: string, unlockRequirementsJson?: string) => ipcRenderer.invoke('cms:addCMSItem', type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson),
    updateCMSItem: (type: CMSType, itemId: string, itemData: any, iconBase64?: string | null, cgBase64?: string | null, statusEffectsJson?: string, developmentEffectsJson?: string, unlockRequirementsJson?: string) => ipcRenderer.invoke('cms:updateCMSItem', type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson),
    deleteCMSItem: (type: CMSType, itemId: string) => ipcRenderer.invoke('cms:deleteCMSItem', type, itemId),
    triggerHuntingTime: () => ipcRenderer.invoke('cms:triggerHuntingTime'),
    getRolePlayingCards: () => ipcRenderer.invoke('cms:getRolePlayingCards'),
    getRolePlayingCardById: (cardId: string) => ipcRenderer.invoke('cms:getRolePlayingCardById', cardId),
    addRolePlayingCard: (cardData: Partial<RolePlayingCard> & { icon_base64?: string | null }) => ipcRenderer.invoke('cms:addRolePlayingCard', cardData),
    updateRolePlayingCard: (cardData: Partial<RolePlayingCard> & { icon_base64?: string | null | undefined }) => ipcRenderer.invoke('cms:updateRolePlayingCard', cardData),
    deleteRolePlayingCard: (cardId: string) => ipcRenderer.invoke('cms:deleteRolePlayingCard', cardId),
  },
  organization: { 
    getPosts: () => ipcRenderer.invoke('org:getPosts'),
    getCharacters: () => ipcRenderer.invoke('org:getCharacters'),
    getAssignments: () => ipcRenderer.invoke('org:getAssignments'),
    setAssignment: (postId: string, characterId: string) => ipcRenderer.invoke('org:setAssignment', postId, characterId),
    getAssignmentByPostId: (postId: string) => ipcRenderer.invoke('org:getAssignmentByPostId', postId), 
  },
  ipc: {
    onAIServiceReady: (callback: () => void) => {
        const handler = () => callback();
        ipcRenderer.on('ai-service-ready', handler);
        return () => ipcRenderer.removeListener('ai-service-ready', handler);
    },
    removeAIServiceReadyListener: (callback: () => void) => { 
        ipcRenderer.removeListener('ai-service-ready', callback); 
    },
    onRoundtableMessage: (callback: (message: ChatMessage) => void) => {
      const handler = (event: IpcRendererEvent, message: ChatMessage) => callback(message);
      ipcRenderer.on('roundtable:newMessage', handler);
      return () => ipcRenderer.removeListener('roundtable:newMessage', handler);
    },
  }
};

contextBridge.exposeInMainWorld('api', exposedAPI);

console.log('Preload script context bridge exposed `api`.');
