// src/components/task_cockpit/TaskBriefingPanel.tsx
import React from 'react';
import type { Task, TaskResourceLink } from '@/types';
import { Icon } from '@/components/common/Icon'; 
import { PRIORITY_LABELS, RESOURCE_TYPE_DISPLAY_NAMES } from '@/types';

interface TaskBriefingPanelProps {
  task: Task;
  onResourceLinkClick: (resource: TaskResourceLink) => void;
}

const getPriorityColorClass = (priority: Task['priority']): string => {
  switch (priority) {
    case 0: return 'bg-red-600 text-white';
    case 1: return 'bg-yellow-500 text-yellow-900';
    case 2: return 'bg-sky-500 text-white';
    case 3: return 'bg-gray-500 text-white';
    default: return 'bg-gray-400 text-gray-800';
  }
};

export const TaskBriefingPanel: React.FC<TaskBriefingPanelProps> = ({ task, onResourceLinkClick }) => {
  return (
    <div className="h-full flex flex-col bg-tg-bg-secondary p-3 rounded-lg shadow-md border border-tg-border-primary overflow-y-auto custom-scrollbar">
      <h2 className="text-lg font-semibold text-tg-text-primary mb-1 flex items-center">
        <Icon name="ListTodo" className="w-5 h-5 mr-2 text-tg-accent-primary" />
        任务简报
      </h2>
      <p className="text-xs text-tg-text-secondary mb-3">当前聚焦任务详情</p>

      <div className="mb-3 pb-3 border-b border-tg-border-primary">
        <h3 className="text-md font-semibold text-tg-accent-secondary mb-1" title={task.title}>{task.title}</h3>
        {task.description && (
          <p className="text-xs text-tg-text-secondary whitespace-pre-wrap leading-relaxed max-h-28 overflow-y-auto custom-scrollbar pr-1">
            {task.description}
          </p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-x-3 gap-y-1.5 text-xs mb-3 pb-3 border-b border-tg-border-primary">
        <div>
          <strong className="text-tg-text-secondary block">状态:</strong>
          <span className={`px-1.5 py-0.5 rounded-full text-[10px] font-medium ${
              task.status === 'done' ? 'bg-green-500 text-white' : 
              task.status === 'doing' ? 'bg-blue-500 text-white' :
              task.status === 'pending_review' ? 'bg-yellow-500 text-yellow-900' :
              task.status === 'blocked' ? 'bg-red-700 text-white' :
              'bg-gray-500 text-white'
            }`}
          >
            {task.status === 'todo' ? '待办' : task.status === 'doing' ? '进行中' : task.status === 'pending_review' ? '待审核' : task.status === 'done' ? '已完成' : '已阻塞'}
          </span>
        </div>
        <div>
          <strong className="text-tg-text-secondary block">优先级:</strong>
           <span className={`px-1.5 py-0.5 rounded-full text-[10px] font-medium ${getPriorityColorClass(task.priority)}`}>
            {PRIORITY_LABELS[task.priority] || '未知'}
          </span>
        </div>
        {task.assignee_id && (
          <div className="col-span-2">
            <strong className="text-tg-text-secondary block">责任人:</strong>
            <span className="text-tg-text-primary flex items-center"><Icon name="CircleUser" className="w-3.5 h-3.5 mr-1 text-tg-text-placeholder"/>{task.assignee_id}</span>
          </div>
        )}
        {task.due_date && (
          <div className="col-span-2">
            <strong className="text-tg-text-secondary block">截止日期:</strong>
            <span className="text-tg-text-primary flex items-center"><Icon name="Clock" className="w-3.5 h-3.5 mr-1 text-tg-text-placeholder"/>{new Date(task.due_date).toLocaleDateString()}</span>
          </div>
        )}
      </div>

      <div>
        <h4 className="text-sm font-semibold text-tg-text-primary mb-1.5 flex items-center">
          <Icon name="Link" className="w-4 h-4 mr-1.5 text-tg-accent-secondary"/>
          关联资源
        </h4>
        {(task.resource_links && task.resource_links.length > 0) ? (
          <ul className="space-y-1 text-xs max-h-40 overflow-y-auto custom-scrollbar pr-1">
            {task.resource_links.map(link => (
              <li key={link.link_id}>
                <button 
                  onClick={() => onResourceLinkClick(link)}
                  className="w-full text-left p-1.5 bg-tg-bg-tertiary hover:bg-tg-bg-hover rounded border border-tg-border-primary/70 group"
                  title={`打开资源: ${link.resource_name || link.resource_identifier}\n类型: ${RESOURCE_TYPE_DISPLAY_NAMES[link.resource_type]}\n描述: ${link.description || '无'}`}
                >
                  <div className="font-medium text-tg-text-primary group-hover:text-tg-accent-primary truncate">
                    {link.resource_name || link.resource_identifier}
                  </div>
                  <div className="text-tg-text-secondary text-[10px] truncate">
                    {RESOURCE_TYPE_DISPLAY_NAMES[link.resource_type]}
                    {link.description && ` - ${link.description}`}
                  </div>
                </button>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-xs text-tg-text-placeholder italic">此任务暂无关联资源。</p>
        )}
      </div>
    </div>
  );
};