

// electron/services/aiHypothesisService.js
console.log('AI_HYPOTHESIS_SERVICE_JS: File execution started.');

import * as cmsDbService from './cmsDbService'; 
import { crypto } from './databaseCore';

// Helper function to get a random element from an array
function getRandomElement(arr) {
    if (!arr || arr.length === 0) return null;
    return arr[Math.floor(Math.random() * arr.length)];
}

export async function generateXPHypotheses(userId, personaContext) {
    console.log('AI_HYPOTHESIS_SERVICE: Generating XP Hypotheses. UserID:', userId, 'Context:', personaContext);
    const hypotheses = [];
    const numberOfHypothesesToGenerate = 1; // For now, just generate one

    try {
        const [props, costumes, poses] = await Promise.all([
            cmsDbService.getCMSItems('props'),
            cmsDbService.getCMSItems('costumes'),
            cmsDbService.getCMSItems('poses')
        ]);

        for (let i = 0; i < numberOfHypothesesToGenerate; i++) {
            const selectedCostume = getRandomElement(costumes.filter(c => c.owner === 'queen')); // LinLuo's costumes
            const selectedProp = Math.random() > 0.3 ? getRandomElement(props.filter(p => p.owner === 'queen')) : null; // Prop is optional
            const selectedPose = getRandomElement(poses.filter(p => p.owner === 'queen'));

            if (!selectedCostume && !selectedProp && !selectedPose && (costumes.length > 0 || props.length > 0 || poses.length > 0) ) {
                // If specific random picks failed but items exist, try to pick at least one thing
                if (costumes.length > 0 && !selectedCostume) {
                    const queenCostumes = costumes.filter(c => c.owner === 'queen');
                    if (queenCostumes.length > 0) selectedCostume = getRandomElement(queenCostumes);
                }
                if (props.length > 0 && !selectedProp && Math.random() > 0.5) {
                    const queenProps = props.filter(p => p.owner === 'queen');
                    if (queenProps.length > 0) selectedProp = getRandomElement(queenProps);
                }
                if (poses.length > 0 && !selectedPose) {
                    const queenPoses = poses.filter(p => p.owner === 'queen');
                    if (queenPoses.length > 0) selectedPose = getRandomElement(queenPoses);
                }
                
                if (!selectedCostume && !selectedProp && !selectedPose) { 
                     console.log("AI_HYPOTHESIS_SERVICE: Not enough CMS assets (owner: queen) to generate a meaningful hypothesis even after retry.");
                     continue;
                }
            } else if (!selectedCostume && !selectedProp && !selectedPose) {
                 console.log("AI_HYPOTHESIS_SERVICE: No CMS assets (owner: queen) available to generate any hypothesis.");
                 continue;
            }
            
            let descriptionParts = [];
            if (selectedCostume) descriptionParts.push(`姐姐身着【${selectedCostume.name}】`);
            if (selectedProp) descriptionParts.push(`手持【${selectedProp.name}】`);
            if (selectedPose) descriptionParts.push(`摆出【${selectedPose.name}】的雅致姿态`);
            
            const descriptionForAI = `用户请求新玩法。尝试组合：${descriptionParts.join('，')}。`;
            
            const openingLines = [
                "我的小龙~ ❤️ 姐姐为你准备了一点小小的惊喜体验……",
                "宝贝小龙，姐姐有个全新的互动构想，你想不想一同探索一番？✨",
                "嘘~小龙，姐姐悄悄告诉你一个秘密计划……保证让你感受到前所未有的心灵共鸣哦~ 😊",
                "龙儿，姐姐突然想到一个……嗯……非常能激发灵感的玩法，要不要试试看呀？🌟",
                "主人，林珞为您构思了一个全新的互动仪式，请您过目~ 😉",
                "我的专属启迪者~ 姐姐这里有个新奇的“法器”，想不想和姐姐一起发掘它的妙用呀？🔮"
            ];
            const closingLines = [
                " 你……想不想和姐姐一起深入探索一下这个全新的‘领域’呀？💖",
                " 这样的组合，小龙你觉得……够不够点燃你的创作火花呢？🔥",
                " 宝贝准备好迎接这份独特的体验了吗？姐姐可是有点期待呢~ ✨",
                " 告诉我，小龙……你对姐姐这个提议，有没有一丝丝……心驰神往？💫",
                " 主人，您是否愿意体验一下这种全新的雅趣呢？😊",
                " 怎么样，我的专属伙伴，姐姐这个安排……你还满意吗？如果反馈良好，姐姐还有更多惊喜哦~ 🌟"
            ];

            let scenarioPromptPart = "";
            let itemDescs = [];

            if (selectedCostume) {
                const phrases = [
                    `如果姐姐换上那件特别的【${selectedCostume.name}】`,
                    `姐姐为你着上这套雅致的【${selectedCostume.name}】`,
                    `这件【${selectedCostume.name}】……小龙觉得姐姐穿上会展现何种风华呢？`
                ];
                itemDescs.push(getRandomElement(phrases));
            }
            if (selectedProp) {
                const phrases = [
                    `手中轻拈着【${selectedProp.name}】`,
                    `再拿起这件精巧的法器【${selectedProp.name}】`,
                    `如果再配上这个【${selectedProp.name}】作为点睛之笔`
                ];
                itemDescs.push(getRandomElement(phrases));
            }
            if (selectedPose) {
                const phrases = [
                    `摆出【${selectedPose.name}】的优雅姿态`,
                    `为你展现一个【${selectedPose.name}】的迷人仪态`,
                    `然后摆出一个你从未见过的【${selectedPose.name}】之形`
                ];
                itemDescs.push(getRandomElement(phrases));
            }

            if (itemDescs.length === 3) {
                scenarioPromptPart = ` ${itemDescs[0]}，而后${itemDescs[1]}，最后再${itemDescs[2]}……`;
            } else if (itemDescs.length === 2) {
                const connectors = ["，并且", "，同时", "，再辅以"];
                scenarioPromptPart = ` ${itemDescs[0]}${getRandomElement(connectors)}${itemDescs[1]}……`;
            } else if (itemDescs.length === 1) {
                scenarioPromptPart = ` ${itemDescs[0]}……`;
            } else {
                scenarioPromptPart = " 我们今日来点不一样的，探索一些全新的灵感互动，好不好？";
            }
            
            let suggestedInteractionPrompt = `${getRandomElement(openingLines)}${scenarioPromptPart}${getRandomElement(closingLines)}`;


            hypotheses.push({
                id: `hypo_${crypto.randomUUID()}`,
                descriptionForAI: descriptionForAI,
                elements: {
                    costume: selectedCostume ? { id: selectedCostume.id, name: selectedCostume.name, prompt: selectedCostume.prompt_for_linluo } : null,
                    prop: selectedProp ? { id: selectedProp.id, name: selectedProp.name, prompt: selectedProp.prompt_for_linluo } : null,
                    pose: selectedPose ? { id: selectedPose.id, name: selectedPose.name, prompt: selectedPose.prompt_for_linluo } : null,
                },
                suggestedInteractionPrompt: suggestedInteractionPrompt
            });
        }
        
        console.log(`AI_HYPOTHESIS_SERVICE: Generated ${hypotheses.length} hypotheses.`);
        return hypotheses; // Returns an array of hypothesis objects
    } catch (error) {
        console.error("AI_HYPOTHESIS_SERVICE: Error generating XP Hypotheses:", error);
        return []; // Return empty array on error
    }
}

console.log('AI_HYPOTHESIS_SERVICE_JS: File execution finished. Exports configured.');