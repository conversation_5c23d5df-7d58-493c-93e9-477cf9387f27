
// src/components/cms/CMSEditItemModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import type { CMSType, CMSItemBase } from '@/types';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

interface CMSEditItemModalProps {
    isOpen: boolean;
    onClose: () => void;
    itemType: CMSType;
    existingItem: CMSItemBase | null;
    fetchCMSItems: (type?: CMSType | 'all') => Promise<void>; // To refresh list after save
}

export const CMSEditItemModal: React.FC<CMSEditItemModalProps> = ({ isOpen, onClose, itemType, existingItem, fetchCMSItems }) => {
    const [name, setName] = useState('');
    const [owner, setOwner] = useState<'master' | 'queen'>('master');
    const [iconBase64, setIconBase64] = useState<string | null | undefined>(undefined); // undefined means "don't change"
    const [iconPreview, setIconPreview] = useState<string | null>(null);
    const [cgBase64, setCgBase64] = useState<string | null | undefined>(undefined); // undefined means "don't change"
    const [cgPreview, setCgPreview] = useState<string | null>(null);
    const [promptLinLuo, setPromptLinLuo] = useState('');
    const [promptMaster, setPromptMaster] = useState('');
    const [statusEffectsJson, setStatusEffectsJson] = useState('');
    const [developmentEffectsJson, setDevelopmentEffectsJson] = useState(''); 
    const [unlockRequirementsJson, setUnlockRequirementsJson] = useState(''); 
    const iconFileInputRef = useRef<HTMLInputElement>(null);
    const cgFileInputRef = useRef<HTMLInputElement>(null);
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isOpen) {
            setName(existingItem?.name || '');
            setOwner(existingItem?.owner || 'master');
            setPromptLinLuo(existingItem?.prompt_for_linluo || '');
            setPromptMaster(existingItem?.prompt_for_master || '');
            setStatusEffectsJson(existingItem?.status_effects_json || '{}');
            setDevelopmentEffectsJson(existingItem?.development_effects_json || '[]');
            setUnlockRequirementsJson(existingItem?.unlock_requirements_json || '[]');
            setIconPreview(existingItem?.icon_path ? `at-asset://${existingItem.icon_path}?t=${Date.now()}` : null);
            setCgPreview(existingItem?.cg_image_path ? `at-asset://${existingItem.cg_image_path}?t=${Date.now()}` : null);
            setIconBase64(undefined); 
            setCgBase64(undefined);
            setIsSaving(false);
            setError(null);
        }
    }, [existingItem, isOpen]);

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>, type: 'icon' | 'cg') => {
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                if (type === 'icon') {
                    setIconBase64(reader.result as string);
                    setIconPreview(reader.result as string);
                } else {
                    setCgBase64(reader.result as string);
                    setCgPreview(reader.result as string);
                }
            };
            reader.readAsDataURL(file);
        }
        if (event.target) event.target.value = ''; // Reset file input
    };

    const handleClearImage = (type: 'icon' | 'cg') => {
        if (type === 'icon') {
            setIconBase64(null); // Explicitly set to null for clearing
            setIconPreview(null);
            if (iconFileInputRef.current) iconFileInputRef.current.value = "";
        } else {
            setCgBase64(null); // Explicitly set to null for clearing
            setCgPreview(null);
            if (cgFileInputRef.current) cgFileInputRef.current.value = "";
        }
    };

    const handleSave = async () => {
        if (!name.trim()) { setError("名称不能为空！"); return; }
        setIsSaving(true); setError(null);

        const itemDataPayload = {
            name: name.trim(),
            owner,
            prompt_for_linluo: promptLinLuo.trim(),
            prompt_for_master: promptMaster.trim(),
        };

        try {
            if (existingItem) {
                await window.api.cms.updateCMSItem(
                    itemType, existingItem.id, itemDataPayload,
                    iconBase64, cgBase64, 
                    statusEffectsJson.trim() || '{}', 
                    developmentEffectsJson.trim() || '[]', 
                    unlockRequirementsJson.trim() || '[]'
                );
            } else {
                await window.api.cms.addCMSItem(
                    itemType, itemDataPayload,
                    iconBase64, cgBase64, 
                    statusEffectsJson.trim() || '{}', 
                    developmentEffectsJson.trim() || '[]', 
                    unlockRequirementsJson.trim() || '[]'
                );
            }
            await fetchCMSItems(itemType);
            onClose();
        } catch (err: any) {
            console.error("Failed to save CMS item:", err);
            setError(`保存失败: ${err.message || '未知错误'}`);
        } finally {
            setIsSaving(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[120] p-4 backdrop-blur-sm" onClick={onClose}>
            <div className="bg-tg-bg-secondary p-5 rounded-lg shadow-2xl w-full max-w-lg border border-purple-500 max-h-[90vh] flex flex-col" onClick={e => e.stopPropagation()}>
                <h4 className="text-lg font-semibold mb-4 text-purple-300">{existingItem ? '编辑' : '新增'} {itemType === 'props' ? '道具' : itemType === 'costumes' ? '服装' : '姿势'}</h4>
                {error && <p className="text-xs text-red-400 mb-2 bg-red-900/50 p-2 rounded">{error}</p>}
                <div className="space-y-3 overflow-y-auto pr-1 flex-grow text-sm">
                    {/* Form fields here, example: */}
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">名称*</label>
                        <input type="text" value={name} onChange={e => setName(e.target.value)} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400" />
                    </div>
                     <div>
                        <label className="text-xs text-gray-400 block mb-1">归属</label>
                        <select value={owner} onChange={e => setOwner(e.target.value as 'master' | 'queen')} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400">
                            <option value="master">主人用</option>
                            <option value="queen">女王用</option>
                        </select>
                    </div>
                    {/* Icon Upload */}
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">图标 (可选)</label>
                        <input type="file" accept="image/*" onChange={(e) => handleImageChange(e, 'icon')} ref={iconFileInputRef} className="hidden" />
                        <button onClick={() => iconFileInputRef.current?.click()} className="w-full p-2 bg-tg-bg-tertiary text-gray-300 border border-dashed border-purple-700 rounded hover:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 text-xs">
                            {iconPreview ? "更改图标" : "选择图标"}
                        </button>
                        {iconPreview && (
                            <div className="mt-2 relative w-16 h-16 mx-auto">
                                <img src={iconPreview} alt="Icon Preview" className="w-full h-full object-contain rounded border border-purple-500"/>
                                <button onClick={() => handleClearImage('icon')} className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full p-0.5 text-xs hover:bg-red-700"><Icon name="X" className="w-3 h-3"/></button>
                            </div>
                        )}
                    </div>
                    {/* CG Image Upload */}
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">CG图 (可选)</label>
                        <input type="file" accept="image/*" onChange={(e) => handleImageChange(e, 'cg')} ref={cgFileInputRef} className="hidden" />
                        <button onClick={() => cgFileInputRef.current?.click()} className="w-full p-2 bg-tg-bg-tertiary text-gray-300 border border-dashed border-purple-700 rounded hover:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 text-xs">
                            {cgPreview ? "更改CG图" : "选择CG图"}
                        </button>
                        {cgPreview && (
                             <div className="mt-2 relative w-32 h-32 mx-auto">
                                <img src={cgPreview} alt="CG Preview" className="w-full h-full object-contain rounded border border-purple-500"/>
                                <button onClick={() => handleClearImage('cg')} className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full p-0.5 text-xs hover:bg-red-700"><Icon name="X" className="w-3 h-3"/></button>
                            </div>
                        )}
                    </div>
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">对林珞的提示词</label>
                        <textarea value={promptLinLuo} onChange={e => setPromptLinLuo(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400 text-xs" />
                    </div>
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">对主人的提示词</label>
                        <textarea value={promptMaster} onChange={e => setPromptMaster(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400 text-xs" />
                    </div>
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">状态效果 (JSON)</label>
                        <textarea value={statusEffectsJson} onChange={e => setStatusEffectsJson(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400 text-xs font-mono" placeholder='例如: {"arousal": 10, "mood": "愉悦"}'/>
                    </div>
                     <div>
                        <label className="text-xs text-gray-400 block mb-1">身体开发效果 (JSON Array)</label>
                        <textarea value={developmentEffectsJson} onChange={e => setDevelopmentEffectsJson(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400 text-xs font-mono" placeholder='例如: [{"target_zone": "skin", "points": 5}]'/>
                    </div>
                    <div>
                        <label className="text-xs text-gray-400 block mb-1">解锁条件 (JSON Array)</label>
                        <textarea value={unlockRequirementsJson} onChange={e => setUnlockRequirementsJson(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-purple-700 rounded focus:border-purple-400 focus:ring-purple-400 text-xs font-mono" placeholder='例如: [{"zone": "mind", "points_required": 50}]'/>
                    </div>
                </div>
                <div className="flex justify-end space-x-2 mt-4 pt-3 border-t border-purple-800">
                    <button onClick={onClose} className="py-2 px-3 text-xs bg-gray-600 hover:bg-gray-700 text-gray-100 rounded">取消</button>
                    <button onClick={handleSave} disabled={isSaving} className="py-2 px-3 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded disabled:opacity-50">
                        {isSaving ? '保存中...' : (existingItem ? '保存更改' : '新增项目')}
                    </button>
                </div>
            </div>
        </div>
    );
};
