// src/components/task_cockpit/TaskUpdateDisplay.tsx
import React from 'react';
import type { StreamTaskUpdate } from '@/pages/TaskCockpitPage';
import { Icon } from '@/components/common/Icon';

interface TaskUpdateDisplayProps {
  updateItem: StreamTaskUpdate;
}

export const TaskUpdateDisplay: React.FC<TaskUpdateDisplayProps> = ({ updateItem }) => {
  const timestamp = new Date(updateItem.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  
  let iconName: React.ComponentProps<typeof Icon>['name'] = "Info";
  let bgColor = 'bg-sky-800/30';
  let borderColor = 'border-sky-700/50';

  if (updateItem.message.includes("完成") || updateItem.message.includes("approved") || updateItem.newStatus === 'done') {
    iconName = "CheckCircle2";
    bgColor = 'bg-green-800/30';
    borderColor = 'border-green-700/50';
  } else if (updateItem.message.includes("错误") || updateItem.message.includes("失败") || updateItem.message.includes("error")) {
    iconName = "AlertTriangle";
    bgColor = 'bg-red-800/30';
    borderColor = 'border-red-700/50';
  }


  return (
    <div className={`my-1 p-2.5 rounded-md text-xs shadow-sm border ${bgColor} ${borderColor} flex items-center max-w-[85%] self-center w-auto`}>
      <Icon name={iconName} className={`w-4 h-4 mr-2 flex-shrink-0 ${iconName === 'CheckCircle2' ? 'text-green-400' : (iconName === 'AlertTriangle' ? 'text-red-400' : 'text-sky-400')}`}/>
      <span className="text-tg-text-secondary flex-grow">{updateItem.message}</span>
      <span className="text-tg-text-placeholder ml-2 flex-shrink-0">[{timestamp}]</span>
    </div>
  );
};