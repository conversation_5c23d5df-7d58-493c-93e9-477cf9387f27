// src/components/SearchResultsDisplay.tsx
import React from 'react';
import type { RetrievedChunk } from '@/types';
import { Icon } from '@/components/common/Icon'; 

interface SearchResultsDisplayProps {
  isVisible: boolean;
  query: string;
  results: RetrievedChunk[] | null;
  isLoading: boolean;
  error: string | null;
  onClose: () => void;
  onCopy: (text: string) => Promise<void> | void; // Allow void for simple clipboard, or promise if async feedback is needed
}

const highlightQuery = (text: string, query: string): string => {
  if (!query || !query.trim() || !text) return text;
  try {
    const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedQuery})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-300 text-yellow-800 px-0.5 rounded">$1</mark>');
  } catch (e) {
    console.error("Error creating regex for highlighting:", e);
    return text;
  }
};

const getBaseName = (filePath: string): string => {
  if (!filePath) return '';
  const parts = filePath.replace(/\\/g, '/').split('/');
  return parts.pop() || '';
};


export const SearchResultsDisplay: React.FC<SearchResultsDisplayProps> = ({
  isVisible,
  query,
  results,
  isLoading,
  error,
  onClose,
  onCopy,
}) => {
  if (!isVisible) return null;

  const handleCopyToClipboardAndQuote = async (text: string) => {
    try {
      await onCopy(text);
      // Optionally, you can add a small visual feedback like "Copied!"
      // For now, no alert to avoid disrupting user flow.
      // The `onCopy` function (passed as `insertTextAtFocus` from ChatDiscussionArea)
      // will handle inserting the text into the input.
    } catch (err) {
      console.error('Failed to copy/quote text from search result: ', err);
      alert('引用失败，请检查控制台获取更多信息。');
    }
  };


  return (
    <div 
      className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] p-4 backdrop-blur-sm" 
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="search-results-title"
    >
      <div 
        className="bg-tg-bg-secondary p-5 md:p-6 rounded-lg shadow-2xl w-full max-w-3xl border border-tg-border-primary max-h-[85vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 pb-3 border-b border-tg-border-primary">
          <h3 id="search-results-title" className="text-xl font-semibold text-tg-accent-primary">
            灵犀检索结果
            {query && <span className="text-base text-tg-text-secondary ml-2">(匹配: "{query}")</span>}
          </h3>
          <button
            onClick={onClose}
            className="p-1.5 rounded-full text-tg-text-placeholder hover:bg-tg-bg-hover hover:text-tg-text-primary transition-colors"
            title="关闭检索结果"
            aria-label="关闭检索结果"
          >
            <Icon name="X" className="w-6 h-6" /> 
          </button>
        </div>

        <div className="overflow-y-auto flex-grow pr-2 space-y-3">
          {isLoading && (
            <div className="text-center text-tg-text-placeholder py-10 flex flex-col items-center">
                <Icon name="Loader" className="w-8 h-8 animate-spin mb-3 text-tg-accent-secondary"/>
                检索记忆碎片中，请稍候...
            </div>
          )}
          {error && (
            <div className="text-center text-tg-danger py-8 bg-red-900/20 p-4 rounded-md flex flex-col items-center">
                <Icon name="ExclamationTriangle" className="w-8 h-8 mb-3"/> 
                检索出错: {error}
            </div>
          )}
          
          {!isLoading && !error && (!results || results.length === 0) && (
            <p className="text-center text-tg-text-placeholder py-10">
              未能找到与您查询相关的记忆碎片。
              <br/>
              请尝试更换关键词或确保目标项目的知识索引已构建。
            </p>
          )}

          {!isLoading && !error && results && results.length > 0 && (
            results.map((chunk) => (
              <div key={chunk.id} className="bg-tg-bg-tertiary p-3.5 rounded-md border border-tg-border-primary shadow-sm hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start text-xs text-tg-text-secondary mb-1.5">
                  <span className="truncate mr-2 font-medium" title={`来源文件: ${chunk.source_file_path}`}>
                    <strong className="text-tg-text-primary">来源:</strong> {chunk.metadata.original_file_name || getBaseName(chunk.source_file_path)} (片段 {chunk.metadata.chunk_index + 1})
                  </span>
                  <span className="whitespace-nowrap text-tg-accent-secondary font-semibold">
                    相似度: {chunk.similarityScore.toFixed(4)}
                  </span>
                </div>
                <div 
                  className="prose prose-sm prose-tg-text-primary max-w-none mb-2 text-tg-text-primary text-sm leading-relaxed whitespace-pre-wrap break-words max-h-40 overflow-y-auto p-1 bg-tg-bg-primary rounded"
                  dangerouslySetInnerHTML={{ __html: highlightQuery(chunk.chunk_text, query) }}
                  style={{ scrollbarWidth: 'thin' }}
                />
                 <button
                    onClick={() => handleCopyToClipboardAndQuote(chunk.chunk_text)}
                    className="p-1.5 text-xs bg-tg-accent-primary hover:bg-tg-accent-primary-hover text-white rounded-md transition-colors flex items-center"
                    title="复制内容并引用至输入框"
                  >
                    <Icon name="Quote" className="w-3.5 h-3.5 mr-1" /> 
                    复制并引用
                  </button>
              </div>
            ))
          )}
        </div>
        <button
            onClick={onClose}
            className="mt-5 py-2.5 px-4 w-full rounded-md transition-colors text-sm font-medium bg-tg-bg-hover text-tg-text-secondary border border-tg-border-primary hover:bg-tg-bg-tertiary hover:text-tg-text-primary focus:outline-none focus:ring-2 focus:ring-tg-accent-primary"
          >
            关闭
          </button>
      </div>
    </div>
  );
};
