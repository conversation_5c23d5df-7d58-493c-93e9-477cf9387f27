// src/App.tsx
import React, { useState, useEffect, useCallback, useRef, useLayoutEffect } from 'react';
import { Routes, Route, useNavigate, useLocation, Link, Navigate, Outlet } from 'react-router-dom';
import { MainHallPage } from '@/components/MainHallPage';
import { ProjectWorkspacePage } from '@/components/ProjectWorkspacePage';
import { SettingsPage } from '@/components/SettingsPage';
import { KnowledgeBasePage } from '@/components/KnowledgeBasePage';
import { AbsoluteTerritoryPage } from '@/components/AbsoluteTerritoryPage';
// Import pages that will be nested under ProjectWorkspacePage

import { TaskBoardPage } from '@/pages/TaskBoardPage';
import { BridgePage } from '@/pages/BridgePage';
import { ChatDiscussionArea } from '@/components/chat/ChatDiscussionArea'; 
import { ProjectKnowledgeBaseView } from '@/components/ProjectKnowledgeBaseView';
import { SourceCodeViewer } from '@/components/SourceCodeViewer';
import { MindWorkshopCanvas } from '@/components/MindWorkshopCanvas';
import { ProjectTimelinePage } from '@/pages/project/ProjectTimelinePage';
import { TaskCockpitPage } from '@/pages/TaskCockpitPage';
import { GlazedWorkshopPage } from '@/pages/GlazedWorkshopPage';


import type {
  Project, AppSettings as AppSettingsType, NoteItem, ImportanceLevel, MindNode, MindConnection,
  ChatMessage, KnowledgeTome, GlobalQuickCommandItem, ProjectKnowledgeTome,
  FileNode, RetrievedChunk, ModelOption,
  DevelopmentTask, SummarizeAndReplaceResult, AudioPlaybackState,
  AiThinkingState, CoreMemory, CoreMemoryPersonaTarget, NewProjectDataForApi, Task, TaskStatus, ActiveMainTabType,
  TaskCreationData
} from '@/types';
import type { KnowledgeTomeCategory } from '@/features/knowledge_tomes/knowledgeConstants';
import { WisdomPouchType } from '@/types';
import { DEFAULT_SETTINGS, APP_TITLE, AVAILABLE_CHAT_MODELS } from '@/config/globalConfig';
import { Icon } from '@/components/common/Icon';


// Use AppSettingsType to avoid conflict with local variable if any
type AppSettings = AppSettingsType;


const App: React.FC = (): React.ReactElement | null => {
  console.log('✅ [App.tsx] 1. Component rendering START...');
  const [projects, setProjects] = useState<Project[]>([]);
  const [aiThinkingState, setAiThinkingState] = useState<AiThinkingState>('idle');

  const [settings, setSettings] = useState<AppSettings>(() => {
    const initialAudioState: AudioPlaybackState = {
        currentSound: null,
        volume: 0.5,
        isLooping: false,
        isPlaying: false,
        ...(DEFAULT_SETTINGS.training_room_audio_state || {})
    };
    return {
      ...DEFAULT_SETTINGS,
      training_room_audio_state: initialAudioState,
      currentTheme: DEFAULT_SETTINGS.currentTheme || 'theme-default',
      onAiThinkingStateChange: (newState: AiThinkingState) => setAiThinkingState(newState)
    };
  });

  const [globalKnowledgeTomes, setGlobalKnowledgeTomes] = useState<KnowledgeTome[]>([]);
  const [globalQuickCommands, setGlobalQuickCommands] = useState<GlobalQuickCommandItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiChatModels, setApiChatModels] = useState<ModelOption[]>(() =>
    AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id }))
  );
  const [isAIServiceReadyForApp, setIsAIServiceReadyForApp] = useState(false);
  const [taskToOpenInEditor, setTaskToOpenInEditor] = useState<Task | null>(null);
  


  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.log('App.tsx: Initializing application state and loading data...');
    if (typeof (window as any).api?.testPreload === 'function') {
        console.log('App.tsx: Preload script `window.api.testPreload` is available.');
    } else {
        console.warn('App.tsx: Preload script `window.api.testPreload` is NOT available.');
    }

    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!window.api || !window.api.database) {
          throw new Error("window.api or window.api.database is not available. Preload script might have failed to expose core APIs.");
        }

        let loadedSettingsFromDB = await window.api.database.getSettings();
        
        const baseDefaultAudioState: AudioPlaybackState = {
            currentSound: null,
            volume: 0.5,
            isLooping: false,
            isPlaying: false,
            ...(DEFAULT_SETTINGS.training_room_audio_state || {})
        };
        
        let finalSettings: AppSettings;

        if (!loadedSettingsFromDB) {
          console.warn("App.tsx: No settings loaded from DB, using full DEFAULT_SETTINGS including audio.");
          finalSettings = {
            ...DEFAULT_SETTINGS,
            training_room_audio_state: baseDefaultAudioState,
            currentTheme: DEFAULT_SETTINGS.currentTheme || 'theme-default',
          };
        } else {
          console.log("App.tsx: Settings loaded from DB.");
          finalSettings = {
            ...DEFAULT_SETTINGS, // Start with defaults
            ...loadedSettingsFromDB, // Override with DB settings
            training_room_audio_state: { // Carefully merge audio state
                ...baseDefaultAudioState, // Base defaults for audio
                ...(loadedSettingsFromDB.training_room_audio_state || {}) // DB audio state overrides defaults
            },
            user_avatar_path: loadedSettingsFromDB.user_avatar_path ?? null,
            linluo_avatar_path: loadedSettingsFromDB.linluo_avatar_path ?? null,
            xiaolan_avatar_path: loadedSettingsFromDB.xiaolan_avatar_path ?? null,
            currentTheme: loadedSettingsFromDB.currentTheme || DEFAULT_SETTINGS.currentTheme || 'theme-default',
            defaultCover: loadedSettingsFromDB.defaultCover === "" ? null : loadedSettingsFromDB.defaultCover,
          };
        }
        
        if (finalSettings.apiKey && finalSettings.apiKey.trim() !== "") {
          if (typeof window.api?.ai?.getAvailableModels === 'function') {
            try {
              const fetchedModels = await window.api.ai.getAvailableModels();
              if (fetchedModels && fetchedModels.length > 0) {
                setApiChatModels(fetchedModels);
                if (!fetchedModels.some(m => m.id === finalSettings.chatModel)) {
                  finalSettings.chatModel = fetchedModels[0].id;
                }
              } else {
                setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
              }
            } catch (modelError) {
              console.error("Failed to fetch API models:", modelError);
              setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
            }
          } else {
            console.warn("App.tsx: window.api.ai.getAvailableModels is not available. Using fallback models.");
            setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
          }
        } else {
           setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
           setIsAIServiceReadyForApp(false);
        }
        
        setSettings({...finalSettings, onAiThinkingStateChange: (newState: AiThinkingState) => setAiThinkingState(newState) });

        const [loadedProjects, loadedGlobalTomes, loadedGlobalCmds] = await Promise.all([
          window.api.database.getAllProjects().catch(e => { console.error("Error getAllProjects:", e); return []; }),
          window.api.database.getAllGlobalKnowledgeTomes().catch(e => { console.error("Error getAllGlobalKnowledgeTomes:", e); return []; }),
          window.api.database.getAllGlobalQuickCommands().catch(e => { console.error("Error getAllGlobalQuickCommands:", e); return []; }),
        ]);
        
        setProjects((loadedProjects || []).filter(p => !p.isDeleted).map(p => ({...p, tasks: p.tasks || []})));
        setGlobalKnowledgeTomes(loadedGlobalTomes || []);
        setGlobalQuickCommands(loadedGlobalCmds || []);

      } catch (err: any) {
        console.error("Failed to load data from backend:", err);
        setError(`天工阁数据加载失败: ${err.message}. 请检查后台服务或联系技术支持。`);
         const fallbackAudioState: AudioPlaybackState = {
            currentSound: null, volume: 0.5, isLooping: false, isPlaying: false,
            ...(DEFAULT_SETTINGS.training_room_audio_state || {})
        };
        setSettings({...DEFAULT_SETTINGS, training_room_audio_state: fallbackAudioState, currentTheme: 'theme-default', onAiThinkingStateChange: (newState: AiThinkingState) => setAiThinkingState(newState) });
        setProjects([]);
        setGlobalKnowledgeTomes([]);
        setGlobalQuickCommands([]);
        setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
        setIsAIServiceReadyForApp(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();

    const handleAIServiceReady = () => {
        console.log("App.tsx: Received 'ai-service-ready' event from main process. Setting AI service to READY.");
        setIsAIServiceReadyForApp(true);
    };

    if (window.api?.ipc?.onAIServiceReady) {
        window.api.ipc.onAIServiceReady(handleAIServiceReady);
        console.log("App.tsx: Subscribed to 'ai-service-ready' IPC event.");
    } else {
        console.warn("App.tsx: window.api.ipc.onAIServiceReady is not available. AI service ready state might not be updated from main process events.");
    }

    return () => {
        if (window.api?.ipc?.removeAIServiceReadyListener) {
            window.api.ipc.removeAIServiceReadyListener(handleAIServiceReady);
            console.log("App.tsx: Unsubscribed from 'ai-service-ready' IPC event.");
        }
    };

  }, []);

  useEffect(() => {
    if (settings.currentTheme) {
      document.documentElement.setAttribute('data-theme', settings.currentTheme);
    } else {
      document.documentElement.removeAttribute('data-theme');
    }
  }, [settings.currentTheme]);

  const handleSaveSettings = useCallback(async (newSettingsOrCallback: AppSettings | ((prevState: AppSettings) => AppSettings)) => {
    let newSettingsToSave: AppSettings;
    if (typeof newSettingsOrCallback === 'function') {
        newSettingsToSave = newSettingsOrCallback(settings);
    } else {
        newSettingsToSave = newSettingsOrCallback;
    }

    const baseDefaultAudioState: AudioPlaybackState = {
        currentSound: null, volume: 0.5, isLooping: false, isPlaying: false,
        ...(DEFAULT_SETTINGS.training_room_audio_state || {})
    };

    newSettingsToSave.training_room_audio_state = {
        ...baseDefaultAudioState,
        ...(newSettingsToSave.training_room_audio_state || {})
    };

    newSettingsToSave.user_avatar_path = newSettingsToSave.user_avatar_path === "" ? null : (newSettingsToSave.user_avatar_path !== undefined ? newSettingsToSave.user_avatar_path : settings.user_avatar_path);
    newSettingsToSave.linluo_avatar_path = newSettingsToSave.linluo_avatar_path === "" ? null : (newSettingsToSave.linluo_avatar_path !== undefined ? newSettingsToSave.linluo_avatar_path : settings.linluo_avatar_path);
    newSettingsToSave.xiaolan_avatar_path = newSettingsToSave.xiaolan_avatar_path === "" ? null : (newSettingsToSave.xiaolan_avatar_path !== undefined ? newSettingsToSave.xiaolan_avatar_path : settings.xiaolan_avatar_path);
    newSettingsToSave.defaultCover = newSettingsToSave.defaultCover === "" ? null : (newSettingsToSave.defaultCover !== undefined ? newSettingsToSave.defaultCover : settings.defaultCover);
    newSettingsToSave.currentTheme = newSettingsToSave.currentTheme || settings.currentTheme || 'theme-default';


    if (typeof newSettingsToSave.onAiThinkingStateChange !== 'function') {
        newSettingsToSave.onAiThinkingStateChange = (newState: AiThinkingState) => setAiThinkingState(newState);
    }

    const oldApiKey = settings.apiKey;
    const { onAiThinkingStateChange, ...serializableSettingsForIPC } = newSettingsToSave;
    const result = await window.api.database.saveSettings(serializableSettingsForIPC);

    if (result.success) {
        setSettings(newSettingsToSave);
        const newApiKeyProvided = newSettingsToSave.apiKey && newSettingsToSave.apiKey.trim() !== "";
        if (newApiKeyProvided && newSettingsToSave.apiKey !== oldApiKey) {
            setIsAIServiceReadyForApp(false);
            if (typeof window.api?.ai?.getAvailableModels === 'function') {
                try {
                    const fetchedModels = await window.api.ai.getAvailableModels();
                    if (fetchedModels && fetchedModels.length > 0) {
                        setApiChatModels(fetchedModels);
                        if (!fetchedModels.some(m => m.id === newSettingsToSave.chatModel)) {
                            const updatedChatModelSettings = { ...newSettingsToSave, chatModel: fetchedModels[0].id };
                            const { onAiThinkingStateChange: _cb2, ...serializableChatModelSettings } = updatedChatModelSettings;
                            await window.api.database.saveSettings(serializableChatModelSettings);
                            setSettings(prev => ({...prev, ...updatedChatModelSettings }));
                        }
                    } else {
                         setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
                    }
                } catch (modelError) {
                    console.error("Failed to fetch API models after API key change:", modelError);
                     setApiChatModels(AVAILABLE_CHAT_MODELS.map(id => ({ id, name: id.split('/').pop() || id })));
                }
            }
        } else if (!newApiKeyProvided) {
             setIsAIServiceReadyForApp(false);
        }
    } else {
      console.error("Failed to save settings to backend:", result.error);
      setError(`设置保存失败: ${result.error || '未知错误'}`);
    }
  }, [settings, setAiThinkingState]);

  const handleAddProject = useCallback(async (projectName: string): Promise<Project | null> => {
    const projectDataForApi: NewProjectDataForApi = {
      id: crypto.randomUUID(),
      name: projectName,
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      isDeleted: false,
      sourceCodePath: null,
      coverImageUrl: undefined, 
    };
    if (typeof window.api?.database?.addProject !== 'function') {
        const errMsg = 'CRITICAL_ERROR: window.api.database.addProject is NOT a function!';
        console.error(errMsg, 'window.api object:', window.api);
        setError(errMsg);
        return null;
    }
    try {
        const result = await window.api.database.addProject(projectDataForApi);
        if (result && result.success && result.project) {
            const newProjectFull: Project = {
                ...result.project,
                discussionMessages: result.project.discussionMessages || [],
                inspirationNotes: result.project.inspirationNotes || [],
                bugMemoNotes: result.project.bugMemoNotes || [],
                quickCommandsNotes: result.project.quickCommandsNotes || [],
                mindNodes: result.project.mindNodes || [],
                mindConnections: result.project.mindConnections || [],
                projectKnowledgeTomes: result.project.projectKnowledgeTomes || [],
                projectKnowledgeCategories: result.project.projectKnowledgeCategories || [],
                developmentTasks: result.project.developmentTasks || [],
                tasks: [], 
            };
            setProjects(prev => [newProjectFull, ...prev].filter(p => p && !p.isDeleted));
            return newProjectFull;
        } else {
            const errorMessage = result?.error || '新建项目失败，后端未返回成功或项目数据不完整。';
            setError(`新建项目失败: ${errorMessage}`);
            return null;
        }
    } catch (error: any) {
        setError(`调用新建项目接口时发生异常: ${error.message || JSON.stringify(error)}`);
        return null;
    }
  }, [setError]);

  const handleUpdateProject = useCallback(async (updatedProject: Project) => {
    const result = await window.api.database.updateProject({...updatedProject, lastModifiedAt: new Date().toISOString()});
    if (result.success && result.project) {
      setProjects(prevProjects =>
        prevProjects.map(p => p.id === result.project!.id ? {...result.project!, tasks: result.project!.tasks || []} : p).filter(p => !p.isDeleted)
      );
    } else {
      setError(`项目更新失败: ${result.error || '未知错误'}`);
    }
  }, []);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    const result = await window.api.database.deleteProject(projectId);
    if (result.success) {
      setProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));
    } else {
      setError(`项目删除失败: ${result.error || '未知错误'}`);
    }
  }, []);

  const handleDuplicateProject = useCallback(async (projectId: string): Promise<Project | null> => {
    try {
        const duplicatedProject = await window.api.database.duplicateProject(projectId);
        if (duplicatedProject) {
            const fullDuplicatedProject: Project = {
                ...duplicatedProject,
                discussionMessages: duplicatedProject.discussionMessages || [],
                inspirationNotes: duplicatedProject.inspirationNotes || [],
                bugMemoNotes: duplicatedProject.bugMemoNotes || [],
                quickCommandsNotes: duplicatedProject.quickCommandsNotes || [],
                mindNodes: duplicatedProject.mindNodes || [],
                mindConnections: duplicatedProject.mindConnections || [],
                projectKnowledgeTomes: duplicatedProject.projectKnowledgeTomes || [],
                projectKnowledgeCategories: duplicatedProject.projectKnowledgeCategories || [],
                developmentTasks: duplicatedProject.developmentTasks || [],
                tasks: duplicatedProject.tasks || [], 
            };
            setProjects(prev => [fullDuplicatedProject, ...prev].filter(p => p && !p.isDeleted));
            return fullDuplicatedProject;
        } else {
            setError("复制项目失败：后端未能成功复制项目。");
            return null;
        }
    } catch (error: any) {
        setError(`复制项目时发生错误: ${error.message || "未知错误"}`);
        return null;
    }
  }, [setError]);

  const handleAddNoteToProjectPouch = useCallback(async (projectId: string, pouchType: WisdomPouchType, text: string, importance?: ImportanceLevel): Promise<NoteItem | null> => {
    const newNote: NoteItem = { 
      id: crypto.randomUUID(),
      text,
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      importance: importance || 'medium',
    };
    const savedNote = await window.api.database.addNoteToProject(projectId, pouchType, newNote);
    if (savedNote) {
        setProjects(prevProjects => prevProjects.map(p => {
            if (p.id === projectId) {
                const pouchKey = pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : (pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes');
                const updatedPouch = [...(p[pouchKey] || []), savedNote];
                return { ...p, [pouchKey]: updatedPouch, lastModifiedAt: new Date().toISOString() };
            }
            return p;
        }).filter(p => !p.isDeleted));
    }
    return savedNote;
  }, []);

  const handleUpdateNoteInProject = ((projectId: string, pouchType: WisdomPouchType, updatedNote: NoteItem) => {
     window.api.database.updateNoteInProject(pouchType, { ...updatedNote, lastModifiedAt: new Date().toISOString() }).then(savedNote => {
        if (savedNote) {
            setProjects(prevProjects => prevProjects.map(p => {
                if (p.id === projectId) {
                    const pouchKey = pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : (pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes');
                    const updatedPouch = (p[pouchKey] || []).map(n => n.id === savedNote.id ? savedNote : n);
                    return { ...p, [pouchKey]: updatedPouch, lastModifiedAt: new Date().toISOString() };
                }
                return p;
            }).filter(p => !p.isDeleted));
        }
    });
  });

  const handleDeleteNoteFromProject = ((projectId: string, pouchType: WisdomPouchType, noteId: string) => {
     window.api.database.deleteNoteFromProject(pouchType, noteId).then(result => {
        if (result.success) {
            setProjects(prevProjects => prevProjects.map(p => {
                 if (p.id === projectId) {
                    const pouchKey = pouchType === WisdomPouchType.INSPIRATION ? 'inspirationNotes' : (pouchType === WisdomPouchType.BUGS ? 'bugMemoNotes' : 'quickCommandsNotes');
                    const updatedPouch = (p[pouchKey] || []).filter(n => n.id !== noteId);
                    return { ...p, [pouchKey]: updatedPouch, lastModifiedAt: new Date().toISOString() };
                }
                return p;
            }).filter(p => !p.isDeleted));
        }
    });
  });

  const handleUpdateProjectMindMap = useCallback((projectId: string, nodes: MindNode[], connections: MindConnection[]) => {
    window.api.database.updateProjectMindMap(projectId, nodes, connections).then(result => {
      if (result.success) {
        setProjects(prevProjects => prevProjects.map(p =>
          p.id === projectId ? { ...p, mindNodes: nodes, mindConnections: connections, lastModifiedAt: new Date().toISOString() } : p
        ).filter(p => !p.isDeleted));
      }
    });
  }, []); 

  const handleAddGlobalTome = async (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>): Promise<KnowledgeTome | undefined> => {
    const newTome: KnowledgeTome = {
        id: crypto.randomUUID(),
        title: tomeData.title,
        content: tomeData.content,
        category: tomeData.category,
        tags: tomeData.tags,
        createdAt: new Date().toISOString(),
        lastModifiedAt: new Date().toISOString(),
    };
    const savedTome = await window.api.database.addGlobalKnowledgeTome(newTome);
    if (savedTome) {
        setGlobalKnowledgeTomes(prev => [savedTome, ...prev]);
    }
    return savedTome || undefined;
  };
  const handleUpdateGlobalTome = (updatedTome: KnowledgeTome) => {
    const tomeToSave = {...updatedTome, lastModifiedAt: new Date().toISOString() };
    window.api.database.updateGlobalKnowledgeTome(tomeToSave).then(savedTome => {
      if (savedTome) {
        setGlobalKnowledgeTomes(prev => prev.map(t => t.id === savedTome.id ? savedTome : t));
      }
    });
  };
  const handleDeleteGlobalTome = (tomeId: string) => {
    window.api.database.deleteGlobalKnowledgeTome(tomeId).then(result => {
      if (result.success) {
        setGlobalKnowledgeTomes(prev => prev.filter(t => t.id !== tomeId));
      }
    });
  };

  const handleAddProjectTome = async (projectId: string, tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>): Promise<ProjectKnowledgeTome | undefined> => {
    const newTome: ProjectKnowledgeTome = {
        id: crypto.randomUUID(),
        title: tomeData.title,
        content: tomeData.content,
        projectCategory: tomeData.projectCategory,
        tags: tomeData.tags,
        createdAt: new Date().toISOString(),
        lastModifiedAt: new Date().toISOString(),
    };
    const savedTome = await window.api.database.addProjectKnowledgeTome(projectId, newTome);
    if (savedTome) {
        setProjects(prevProjects => prevProjects.map(p =>
            p.id === projectId ? { ...p, projectKnowledgeTomes: [savedTome, ...(p.projectKnowledgeTomes || [])], lastModifiedAt: new Date().toISOString() } : p
        ).filter(p => !p.isDeleted));
    }
    return savedTome || undefined;
  };
  const handleUpdateProjectTome = (projectId: string, updatedTome: ProjectKnowledgeTome) => {
    const tomeToSave = { ...updatedTome, lastModifiedAt: new Date().toISOString() };
    window.api.database.updateProjectKnowledgeTome(projectId, tomeToSave).then(savedTome => {
      if (savedTome) {
        setProjects(prevProjects => prevProjects.map(p => {
          if (p.id === projectId) {
            const updatedTomes = (p.projectKnowledgeTomes || []).map(t => t.id === savedTome.id ? savedTome : t);
            return { ...p, projectKnowledgeTomes: updatedTomes, lastModifiedAt: new Date().toISOString() };
          }
          return p;
        }).filter(p => !p.isDeleted));
      }
    });
  };
  const handleDeleteProjectTome = (projectId: string, tomeId: string) => {
    window.api.database.deleteProjectKnowledgeTome(projectId, tomeId).then(result => {
      if (result.success) {
        setProjects(prevProjects => prevProjects.map(p => {
          if (p.id === projectId) {
            const updatedTomes = (p.projectKnowledgeTomes || []).filter(t => t.id !== tomeId);
            return { ...p, projectKnowledgeTomes: updatedTomes, lastModifiedAt: new Date().toISOString() };
          }
          return p;
        }).filter(p => !p.isDeleted));
      }
    });
  };
  const handleAddProjectCategory = async (projectId: string, category: string): Promise<string | undefined> => {
    const addedCategory = await window.api.database.addProjectKnowledgeCategory(projectId, category);
    if (addedCategory) {
        setProjects(prevProjects => prevProjects.map(p =>
            p.id === projectId ? { ...p, projectKnowledgeCategories: [...(p.projectKnowledgeCategories || []).filter(c => c !== addedCategory), addedCategory].sort(), lastModifiedAt: new Date().toISOString() } : p
        ).filter(p => !p.isDeleted));
    }
    return addedCategory;
  };
  const handleRemoveProjectCategory = (projectId: string, category: string) => {
    window.api.database.removeProjectKnowledgeCategory(projectId, category).then(result => {
      if (result.success) {
        setProjects(prevProjects => prevProjects.map(p => {
          if (p.id === projectId) {
            const updatedCategories = (p.projectKnowledgeCategories || []).filter(c => c !== category);
            const updatedTomes = (p.projectKnowledgeTomes || []).map(tome =>
                tome.projectCategory === category ? { ...tome, projectCategory: '未分类' } : tome
            );
            return { ...p, projectKnowledgeCategories: updatedCategories, projectKnowledgeTomes: updatedTomes, lastModifiedAt: new Date().toISOString() };
          }
          return p;
        }).filter(p => !p.isDeleted));
      }
    });
  };

  const handleSaveNewChatMessage = useCallback(async (projectId: string, message: ChatMessage) => {
    if (!projectId) {
      console.error("App.tsx (handleSaveNewChatMessage): projectId is undefined. Cannot save message.");
      return;
    }
    const savedMessage = await window.api.database.saveChatMessage(projectId, message);
    if (savedMessage) {
        setProjects(prevProjects => prevProjects.map(p => {
            if (p.id === projectId) {
                return { ...p, discussionMessages: [...(p.discussionMessages || []), savedMessage], lastModifiedAt: new Date().toISOString() };
            }
            return p;
        }).filter(p => !p.isDeleted));
    } else {
        console.error(`App.tsx (handleSaveNewChatMessage): Failed to save message for project ${projectId}. Backend returned null.`);
    }
  }, []);

  const handleUpdateExistingChatMessage = useCallback(async (projectId: string, updatedMessage: ChatMessage) => {
    if (!projectId) {
      console.error("App.tsx (handleUpdateExistingChatMessage): projectId is undefined. Cannot update message.");
      return;
    }
    const updatedMsgFromDb = await window.api.database.updateChatMessage(updatedMessage); // projectId is part of updatedMessage if needed by DB
    if (updatedMsgFromDb) {
        setProjects(prevProjects => prevProjects.map(p => {
            if (p.id === projectId) {
                return {
                    ...p,
                    discussionMessages: (p.discussionMessages || []).map(m =>
                        m.id === updatedMsgFromDb.id ? updatedMsgFromDb : m
                    ),
                    lastModifiedAt: new Date().toISOString()
                };
            }
            return p;
        }).filter(p => !p.isDeleted));
    }
  }, []);

  const handleMessagesReplacedBySummary = useCallback((projectId: string, newSummaryMessage: ChatMessage, replacedMessageIds: string[]) => {
    setProjects(prevProjects => prevProjects.map(p => {
      if (p.id === projectId) {
        const remainingMessages = (p.discussionMessages || []).filter(m => !replacedMessageIds.includes(m.id));
        return {
          ...p,
          discussionMessages: [...remainingMessages, newSummaryMessage].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
          lastModifiedAt: new Date().toISOString()
        };
      }
      return p;
    }).filter(p => !p.isDeleted));
  }, []);

  const handleAddGlobalQuickCommand = async (commandData: Omit<GlobalQuickCommandItem, 'id' | 'createdAt' | 'lastModifiedAt'>): Promise<GlobalQuickCommandItem | undefined> => {
    const newCommand: GlobalQuickCommandItem = {
        id: crypto.randomUUID(),
        title: commandData.title,
        commandText: commandData.commandText,
        createdAt: new Date().toISOString(),
        lastModifiedAt: new Date().toISOString(),
    };
    const savedCommand = await window.api.database.addGlobalQuickCommand(newCommand);
    if (savedCommand) {
        setGlobalQuickCommands(prev => [savedCommand, ...prev]);
    }
    return savedCommand || undefined;
  };
  const handleUpdateGlobalQuickCommand = (updatedCommand: GlobalQuickCommandItem) => {
     const commandToSave = {...updatedCommand, lastModifiedAt: new Date().toISOString() };
     window.api.database.updateGlobalQuickCommand(commandToSave).then(savedCommand => {
      if (savedCommand) {
        setGlobalQuickCommands(prev => prev.map(c => c.id === savedCommand.id ? savedCommand : c));
      }
    });
  };
  const handleDeleteGlobalQuickCommand = (commandId: string) => {
    window.api.database.deleteGlobalQuickCommand(commandId).then(result => {
      if (result.success) {
        setGlobalQuickCommands(prev => prev.filter(c => c.id !== commandId));
      }
    });
  };

  const handleAddCoreMemory = async (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => {
    const newMemory = await window.api.database.addCoreMemory(memoryData);
    if (newMemory) {
      console.log("App.tsx: Core memory added:", newMemory);
    }
    return newMemory;
  };
  
  const handleUpdateTaskStatusInApp = useCallback(async (taskId: string, newStatus: TaskStatus, projectIdToUpdate: string): Promise<Task | null> => {
    if (typeof window.api?.tasks?.updateTask !== 'function') {
      console.error("App.tsx: window.api.tasks.updateTask is not available.");
      setError("任务状态更新功能不可用。");
      return null;
    }
    try {
      const updatedTask = await window.api.tasks.updateTask(taskId, { status: newStatus });
      if (updatedTask) {
          setProjects(prevProjects => prevProjects.map(p => {
              if (p.id === projectIdToUpdate) {
                  const updatedTasksList = (p.tasks || []).map(t => t.task_id === taskId ? updatedTask : t);
                  return { ...p, tasks: updatedTasksList, lastModifiedAt: new Date().toISOString() };
              }
              return p;
          }).filter(p => !p.isDeleted));
      }
      return updatedTask;
    } catch (err: any) {
        console.error("App.tsx: Error updating task status:", err);
        setError(`更新任务状态失败: ${err.message}`);
        return null;
    }
  }, [setError]);
  
  const handleAideProjectImported = useCallback(async (projectId: string, newTasksData: TaskCreationData[]) => {
    const createdTasks: Task[] = [];
    for (const taskData of newTasksData) {
        const taskPayloadWithProjectId = { ...taskData, project_id: projectId };
        if (!window.api?.tasks?.addTask) {
            console.error("App.tsx: window.api.tasks.addTask is not available.");
            setError("添加任务功能不可用。");
            return; // Exit early if critical API is missing
        }
        const created = await window.api.tasks.addTask(taskPayloadWithProjectId);
        if (created) {
            createdTasks.push(created);
        } else {
            console.warn("App.tsx: Failed to create task during AIDE import:", taskData.title);
        }
    }
    if (createdTasks.length > 0) {
        setProjects(prev => prev.map(p => {
            if (p.id === projectId) {
                return { ...p, tasks: [...(p.tasks || []), ...createdTasks], lastModifiedAt: new Date().toISOString() };
            }
            return p;
        }));
        console.log(`App.tsx: Imported ${createdTasks.length} tasks from AIDE project analysis into project ${projectId}.`);
    }
    // Ensure an explicit return if the function is expected to return a Promise<void>
  }, [setError]);

  const handleTaskEditorOpen = useCallback((task: Task, targetTab?: ActiveMainTabType) => {
    setTaskToOpenInEditor(task);
    navigate(`/project/${task.project_id}/cockpit`); 
  }, [navigate]);
  
  const handleTaskEditorOpened = useCallback(() => {
    setTaskToOpenInEditor(null); 
  }, []);



  const currentPath = location.pathname;
  const [pageTitle, setPageTitle] = useState(APP_TITLE);

  useEffect(() => {
    let title = APP_TITLE;
    if (currentPath === '/') {
      title = `主殿 - ${APP_TITLE}`;
    } else if (currentPath.startsWith('/project/')) {
      const pathParts = currentPath.split('/');
      const currentProjectId = pathParts[2];
      const currentProj = projects.find(p => p.id === currentProjectId);
      if (currentProj) {
        const subRoute = pathParts[3];
        let subRouteName = "";
        switch(subRoute) {
            case 'discussion': subRouteName = '战略沙盘'; break;
            case 'task-board': subRouteName = '神谕罗盘'; break;
            case 'cockpit': subRouteName = '任务驾驶舱'; break;
            case 'source-code': subRouteName = '源码洞天'; break;
            case 'mind-workshop': subRouteName = '思路工坊'; break;
            case 'timeline': subRouteName = '项目时间轴'; break;
            case 'glazed-workshop': subRouteName = '琉璃坊'; break;
            case 'project-knowledge-base': subRouteName = '项目知库'; break;
            default: subRouteName = '工作区';
        }
        title = `${subRouteName} - ${currentProj.name} - ${APP_TITLE}`;
      }
    } else if (currentPath === '/settings') {
      title = `天工阁设置 - ${APP_TITLE}`;
    } else if (currentPath === '/knowledge-base') {
      title = `万象书海 - ${APP_TITLE}`;
    } else if (currentPath === '/absolute-territory') {
      title = `训练室 - ${APP_TITLE}`;
    }
    setPageTitle(title);
  }, [currentPath, projects]);

  useEffect(() => {
    document.title = pageTitle;
  }, [pageTitle]);


  if (isLoading && !error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-tg-bg-primary text-tg-text-primary">
        <Icon name="Loader" className="w-12 h-12 animate-spin text-tg-accent-primary" />
        <p className="mt-4 text-lg">天工阁启动中，请稍候...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-tg-bg-primary text-tg-text-primary p-4">
        <h2 className="text-2xl font-bold text-red-400 mb-4">天工阁启动失败</h2>
        <p className="text-tg-text-secondary mb-2">很抱歉，加载核心数据时遇到问题。</p>
        <p className="text-xs text-tg-text-placeholder bg-tg-bg-secondary p-3 rounded-md">{error}</p>
        <button
            onClick={() => window.location.reload()}
            className="mt-6 px-4 py-2 bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover"
        >
            尝试重新加载
        </button>
      </div>
    );
  }

  const apiKeyIsSet = settings.apiKey && settings.apiKey.trim() !== "";
  let apiStatusIndicatorColor = 'bg-red-500';
  let apiStatusTooltip = "Gemini AI 服务未配置。请前往设置。";
  if (apiKeyIsSet) {
    if (isAIServiceReadyForApp) {
      apiStatusIndicatorColor = 'bg-green-500';
      apiStatusTooltip = "Gemini AI 服务已连接。";
    } else {
      apiStatusIndicatorColor = 'bg-yellow-500 animate-pulse';
      apiStatusTooltip = "Gemini AI 服务连接中或遇到问题。";
    }
  }

  const nonDeletedProjects = projects.filter(p => !p.isDeleted);

  const navLinkClass = (path: string) => 
    `px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-2
     ${currentPath.startsWith(path) && (path !== '/' || currentPath === '/') 
        ? 'bg-tg-accent-primary text-white shadow-sm' 
        : 'text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary'}`;
  
  console.log('✅ [App.tsx] 6. Reaching final JSX return. UI should now be visible.');
  return (
    <div className="flex flex-col h-screen bg-tg-bg-primary text-tg-text-primary">
      {/* Top Navigation Bar */}
      <nav className="bg-tg-bg-secondary shadow-md px-4 py-2 flex items-center justify-between flex-shrink-0">
        <div className="flex items-center space-x-2">
          <span className="text-xl font-bold text-tg-accent-primary">天工阁</span>
        </div>
        <div className="flex items-center space-x-3">
          <Link to="/" title="主殿" className={navLinkClass('/')}>
            <Icon name="House" className="w-5 h-5" />
            <span>主殿</span>
          </Link>
          <Link to="/knowledge-base" title="万象书海" className={navLinkClass('/knowledge-base')}>
            <Icon name="Archive" className="w-5 h-5" />
            <span>万象书海</span>
          </Link>
          <Link to="/settings" title="天工阁设置" className={navLinkClass('/settings')}>
            <Icon name="Settings" className="w-5 h-5" />
            <span>设置</span>
          </Link>
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="flex-grow overflow-auto relative">
        {aiThinkingState !== 'idle' && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-3 py-1 text-xs rounded-md shadow-lg z-50 animate-pulse">
            {aiThinkingState === 'linluo_thinking' ? "林珞姐姐思考中..." : (aiThinkingState === 'xiaolan_thinking' ? "小岚炼器中..." : "AI 运转中...")}
          </div>
        )}
        <Routes>
          <Route path="/" element={<MainHallPage
                                    projects={nonDeletedProjects}
                                    addProject={handleAddProject}
                                    deleteProject={handleDeleteProject}
                                    updateProject={handleUpdateProject}
                                    duplicateProject={handleDuplicateProject}
                                    settings={settings}
                                />} />
          <Route
            path="/project/:projectId/*" 
            element={
              <ProjectWorkspacePage
                projects={nonDeletedProjects}
                updateProjectGeneral={handleUpdateProject}
                addNoteToProject={handleAddNoteToProjectPouch}
                updateNoteInProject={handleUpdateNoteInProject}
                deleteNoteFromProject={handleDeleteNoteFromProject}
                updateProjectMindMap={handleUpdateProjectMindMap}
                settings={settings}
                globalQuickCommands={globalQuickCommands}
                addProjectKnowledgeTome={handleAddProjectTome}
                updateProjectKnowledgeTome={handleUpdateProjectTome}
                deleteProjectKnowledgeTome={handleDeleteProjectTome}
                addProjectKnowledgeCategory={handleAddProjectCategory}
                removeProjectKnowledgeCategory={handleRemoveProjectCategory}
                onSaveNewChatMessage={handleSaveNewChatMessage}
                onUpdateExistingChatMessage={handleUpdateExistingChatMessage}
                isAIServiceReady={isAIServiceReadyForApp}
                onMessagesReplacedBySummary={handleMessagesReplacedBySummary}
                onAddCoreMemory={handleAddCoreMemory}
                onUpdateTaskStatusInApp={handleUpdateTaskStatusInApp}
                taskToOpenInEditor={taskToOpenInEditor}
                onEditorOpenedTask={handleTaskEditorOpened}
                onAideProjectImported={handleAideProjectImported}
                onStartCraftingFromTask={handleTaskEditorOpen}
              />
            }
          >
            <Route index element={<Navigate to="discussion" replace />} /> 
            <Route path="discussion" element={<ChatDiscussionArea
                messages={projects.find(p => p.id === location.pathname.split('/')[2])?.discussionMessages || []}
                onSaveUserMessage={handleSaveNewChatMessage as any} // Cast to simpler signature if needed, or adjust handleSaveNewChatMessage
                onUpdateUserMessage={handleUpdateExistingChatMessage as any}
                onCallAI={async (prompt, hist, rag, context) => {}}
                aiTaskStatus={aiThinkingState}
                currentUserName={settings.user_avatar_path ? "我" : "用户"}
                settings={settings}
                globalQuickCommands={globalQuickCommands}
                isAIServiceReady={isAIServiceReadyForApp}
                projectId={location.pathname.split('/')[2]}
                onDecomposeRequirement={async (req, projId, msgId) => {}}
                onConvertToTask={(message) => {}}
             />} />
            <Route path="project-knowledge-base" element={<ProjectKnowledgeBaseView 
                projectTomes={projects.find(p => p.id === location.pathname.split('/')[2])?.projectKnowledgeTomes || []}
                projectCategories={projects.find(p => p.id === location.pathname.split('/')[2])?.projectKnowledgeCategories || []}
                onAddTome={(tomeData) => handleAddProjectTome(location.pathname.split('/')[2], tomeData)}
                onUpdateTome={(tome) => handleUpdateProjectTome(location.pathname.split('/')[2], tome)}
                onDeleteTome={(tomeId) => handleDeleteProjectTome(location.pathname.split('/')[2], tomeId)}
                onAddCategory={(category) => handleAddProjectCategory(location.pathname.split('/')[2], category)}
                onRemoveCategory={(category) => handleRemoveProjectCategory(location.pathname.split('/')[2], category)}
                projectName={projects.find(p => p.id === location.pathname.split('/')[2])?.name || ""}
            />} />
            <Route path="source-code" element={<SourceCodeViewer 
                updateProjectSettings={handleUpdateProject} 
                settings={settings}
                initialTaskForCodeAssist={taskToOpenInEditor}
                onUpdateTaskStatus={handleUpdateTaskStatusInApp}
                onAideProjectImported={handleAideProjectImported}
            />} />
            <Route path="mind-workshop" element={<MindWorkshopCanvas 
                initialNodes={projects.find(p => p.id === location.pathname.split('/')[2])?.mindNodes || []}
                initialConnections={projects.find(p => p.id === location.pathname.split('/')[2])?.mindConnections || []}
                onSave={(nodes, connections) => handleUpdateProjectMindMap(location.pathname.split('/')[2], nodes, connections)}
            />} />
            <Route path="timeline" element={<ProjectTimelinePage />} />
            <Route path="task-board" element={
              <TaskBoardPage 
              />} 
            />
            <Route path="cockpit/:taskId?" element={
                <TaskCockpitPage 
                />
            } />
            <Route path="glazed-workshop" element={<GlazedWorkshopPage />} />
          </Route>
          
          <Route path="/settings" element={
            <SettingsPage
                settings={settings}
                setSettings={handleSaveSettings}
                projects={nonDeletedProjects}
                globalKnowledgeTomes={globalKnowledgeTomes}
                availableChatModels={apiChatModels}
                onAddCoreMemory={handleAddCoreMemory}
            />}
          />
          <Route path="/knowledge-base" element={
             <KnowledgeBasePage
                projects={nonDeletedProjects}
                globalKnowledgeTomes={globalKnowledgeTomes}
                onAddTome={handleAddGlobalTome}
                onUpdateTome={handleUpdateGlobalTome}
                onDeleteTome={handleDeleteGlobalTome}
                settings={settings}
             />
            }
          />
           <Route
            path="/absolute-territory"
            element={
              <AbsoluteTerritoryPage
                settings={settings}
                globalQuickCommands={globalQuickCommands}
                isAIServiceReady={isAIServiceReadyForApp}
                onAddCoreMemory={handleAddCoreMemory}
              />
            }
          />
           <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </main>

      {/* Bottom Information Bar */}
      <footer className="bg-tg-bg-secondary px-4 py-1.5 border-t border-tg-border-primary text-xs text-tg-text-secondary flex items-center justify-between flex-shrink-0">
        <span>天工阁·创世框架 v3.3.0</span>
        <div className="flex items-center space-x-2">
          <span>AI服务状态:</span>
          <div
            title={apiStatusTooltip}
            aria-label={`Gemini API Status: ${apiStatusTooltip}`}
            className="p-1 relative cursor-pointer"
            onClick={() => navigate('/settings')} 
          >
            <Icon name="Sparkles" className={`w-4 h-4 ${apiKeyIsSet && isAIServiceReadyForApp ? 'text-green-400' : (apiKeyIsSet ? 'text-yellow-400' : 'text-red-400')}`} />
            <span className={`absolute top-0.5 right-0.5 block w-2 h-2 rounded-full border border-tg-bg-secondary ${apiStatusIndicatorColor}`}></span>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default App;
