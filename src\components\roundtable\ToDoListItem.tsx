// src/components/roundtable/ToDoListItem.tsx
import React, { useState, useRef, useEffect } from 'react';
import type { ToDoListItemProps } from '@/types';
import { Icon } from '@/components/common/Icon';

export const ToDoListItem: React.FC<ToDoListItemProps> = ({
  item,
  onUpdateText,
  onDelete,
  onPublish,
  isMeetingActive,
}) => {
  const [isEditing, setIsEditing] = useState(item.isEditing || false);
  const [editText, setEditText] = useState(item.text);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      // Auto-resize textarea
      inputRef.current.style.height = 'auto';
      inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
    }
  }, [isEditing]);
  
  useEffect(() => {
    setEditText(item.text); // Sync with prop changes if item itself is replaced
  }, [item.text]);

  const handleSave = () => {
    if (editText.trim()) {
      onUpdateText(editText.trim());
    } else {
      // If text is empty, revert to original or delete? For now, revert.
      setEditText(item.text);
    }
    setIsEditing(false);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditText(item.text); // Revert
    }
  };
  
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditText(e.target.value);
    // Auto-resize textarea
    if (inputRef.current) {
      inputRef.current.style.height = 'auto';
      inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
    }
  };

  return (
    <li className={`p-2.5 rounded-md shadow-sm flex items-start space-x-2 transition-colors duration-150
                    ${item.isPublished ? 'bg-green-800/30 border border-green-700/50' : 'bg-tg-bg-primary border border-tg-border-primary/70'}`}>
      
      <div className="flex-grow min-w-0">
        {isEditing && isMeetingActive ? (
          <textarea
            ref={inputRef}
            value={editText}
            onChange={handleTextChange}
            onBlur={handleSave} // Save on blur
            onKeyDown={handleKeyDown}
            className="w-full p-1 bg-tg-bg-tertiary text-tg-text-primary border border-tg-accent-primary rounded-md text-xs focus:ring-1 focus:ring-tg-accent-primary resize-none overflow-hidden"
            rows={1}
            aria-label="编辑待办事项内容"
          />
        ) : (
          <p
            className={`text-xs whitespace-pre-wrap break-words ${item.isPublished ? 'text-green-300 line-through' : 'text-tg-text-primary'} ${isMeetingActive && !item.isPublished ? 'cursor-text' : ''}`}
            onClick={() => { if (isMeetingActive && !item.isPublished) setIsEditing(true); }}
            title={isMeetingActive && !item.isPublished ? "点击编辑" : item.text}
          >
            {item.text}
          </p>
        )}
      </div>

      <div className="flex-shrink-0 flex items-center space-x-1">
        {item.isPublished ? (
          <Icon name="CheckCircle" className="w-4 h-4 text-green-500" title="已发布"/>
        ) : (
          isMeetingActive && (
            <>
              {!isEditing && (
                <button onClick={() => setIsEditing(true)} className="p-1 text-tg-text-placeholder hover:text-tg-warning rounded" title="编辑">
                  <Icon name="Pencil" className="w-3.5 h-3.5"/>
                </button>
              )}
              <button onClick={onPublish} className="p-1 text-tg-text-placeholder hover:text-green-500 rounded" title="发布此待办">
                <Icon name="UploadCloud" className="w-3.5 h-3.5"/>
              </button>
              <button onClick={onDelete} className="p-1 text-tg-text-placeholder hover:text-tg-danger rounded" title="删除">
                <Icon name="Trash2" className="w-3.5 h-3.5"/>
              </button>
            </>
          )
        )}
      </div>
    </li>
  );
};