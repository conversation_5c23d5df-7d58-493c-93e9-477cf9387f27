// src/components/roundtable/RoundtableDraftPanel.tsx
import React from 'react';
import type { RoundtableDraftPanelProps } from '@/types';
import { Icon } from '@/components/common/Icon';
import { SharedDraftCanvas } from '@/components/common/SharedDraftCanvas';
import { ToDoList } from './ToDoList'; 

export const RoundtableDraftPanel: React.FC<RoundtableDraftPanelProps> = ({
  sharedDraftContent,
  onSharedDraftChange,
  toDoItems,
  onAddToDoItem,
  onUpdateToDoItemText,
  onDeleteToDoItem,
  onPublishToDoItem,
  onPublishAllToDoItems,
  isMeetingActive,
}) => {
  const handleCopyDraft = () => {
    navigator.clipboard.writeText(sharedDraftContent).catch(err => console.error("Failed to copy draft: ", err));
  };
  const handleSaveDraft = () => {
    // Placeholder: Implement save draft logic (e.g., to a project note)
    console.log("Save draft action triggered. Content:", sharedDraftContent);
    alert("“保存草案”功能待实现。");
  };

  return (
    <div className="p-3 bg-tg-bg-tertiary rounded-lg shadow-md h-full flex flex-col">
      <h3 className="text-md font-semibold text-tg-accent-secondary mb-2 flex items-center flex-shrink-0">
        <Icon name="PencilLine" className="w-5 h-5 mr-2" />
        战争草案
      </h3>
      
      <div className="flex-grow min-h-0 flex flex-col space-y-3">
        {/* Top Section: Shared Whiteboard / Draft Canvas */}
        <div className="flex-grow min-h-[200px] flex flex-col bg-tg-bg-primary p-2 rounded-md border border-tg-border-primary">
          <div className="flex justify-between items-center mb-1.5 flex-shrink-0">
            <p className="text-xs text-tg-text-secondary">共享白板:</p>
            <div className="flex space-x-1.5">
                <button onClick={handleCopyDraft} className="p-1 text-tg-text-placeholder hover:text-tg-accent-primary rounded" title="复制白板内容">
                    <Icon name="Clipboard" className="w-4 h-4"/>
                </button>
                <button onClick={handleSaveDraft} className="p-1 text-tg-text-placeholder hover:text-tg-accent-secondary rounded" title="保存白板内容">
                    <Icon name="Save" className="w-4 h-4"/>
                </button>
            </div>
          </div>
          <SharedDraftCanvas
            content={sharedDraftContent}
            onChange={onSharedDraftChange}
            isEditable={isMeetingActive}
            placeholder="在此共同起草会议纪要、思路或方案..."
            className="flex-grow" 
            showToolbar={isMeetingActive}
          />
        </div>

        {/* Bottom Section: To-Do List */}
        <div className="border-t border-tg-border-primary pt-2 flex-grow min-h-[200px] flex flex-col bg-tg-bg-primary p-2 rounded-md border">
          <div className="flex justify-between items-center mb-1.5 flex-shrink-0">
            <p className="text-xs text-tg-text-secondary">待敲定To-Do列表:</p>
            {/* Buttons for ToDoList are now part of ToDoList component's footer */}
          </div>
          <ToDoList
            items={toDoItems}
            onUpdateItemText={onUpdateToDoItemText}
            onDeleteItem={onDeleteToDoItem}
            onPublishItem={onPublishToDoItem}
            onPublishAll={onPublishAllToDoItems}
            onAddItem={onAddToDoItem}
            isMeetingActive={isMeetingActive}
          />
        </div>
      </div>
    </div>
  );
};