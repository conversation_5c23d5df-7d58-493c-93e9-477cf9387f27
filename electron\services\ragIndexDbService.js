

// electron/services/ragIndexDbService.js
console.log('RAG_INDEX_DB_SERVICE_JS: File execution started.');

import { db, crypto } from './databaseCore'; // crypto needed for new IDs if not passed

export function clearKnowledgeIndexForProject(projectId) {
 if (!db) { console.error("RAG_INDEX_DB_ERROR: clearKnowledgeIndexForProject - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare('DELETE FROM knowledge_index WHERE source_project_id = ?');
 try {
        stmt.run(projectId);
        console.log(`RAG_INDEX_DB: Cleared knowledge index for project ${projectId}.`);
        return { success: true };
 } catch (error) {
        console.error(`RAG_INDEX_DB_ERROR: Error clearing knowledge index for project ${projectId}:`, error);
        return { success: false, error: error.message };
 }
}

export function addKnowledgeIndexEntry(entry) {
 if (!db) { console.error("RAG_INDEX_DB_ERROR: addKnowledgeIndexEntry - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare('INSERT INTO knowledge_index (id, source_project_id, source_file_path, chunk_text, chunk_vector, metadata, indexed_at) VALUES (?, ?, ?, ?, ?, ?, ?)');
 try {
        const entryToSave = {
            id: entry.id || crypto.randomUUID(),
            indexed_at: entry.indexed_at || new Date().toISOString(),
            ...entry
        };
        stmt.run(entryToSave.id, entryToSave.source_project_id, entryToSave.source_file_path, entryToSave.chunk_text, entryToSave.chunk_vector, entryToSave.metadata, entryToSave.indexed_at);
        return { success: true, id: entryToSave.id };
 } catch (error) {
        console.error('RAG_INDEX_DB_ERROR: Error adding knowledge index entry:', error);
        return { success: false, error: error.message };
 }
}

export function getKnowledgeIndexEntriesForProject(projectId) {
 if (!db) { console.error("RAG_INDEX_DB_ERROR: getKnowledgeIndexEntriesForProject - db not available."); return []; }
 try {
        const entries = db.prepare('SELECT * FROM knowledge_index WHERE source_project_id = ?').all(projectId);
        return entries;
 } catch (error) {
        console.error(`RAG_INDEX_DB_ERROR: Error getting knowledge index entries for project ${projectId}:`, error);
        return [];
 }
}

console.log('RAG_INDEX_DB_SERVICE_JS: File execution finished. Exports configured.');