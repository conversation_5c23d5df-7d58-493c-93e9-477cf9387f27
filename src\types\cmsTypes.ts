// src/types/cmsTypes.ts
import type { BodyZoneKey, AssetType } from './'; // Import from main index

export type CMSType = 'props' | 'costumes' | 'poses' | 'role_cards';

export interface CMSItemBase {
  id: string;
  name: string;
  owner: 'master' | 'queen';
  icon_path: string | null;
  cg_image_path: string | null;
  prompt_for_linluo: string;
  prompt_for_master: string;
  status_effects_json?: string | null;
  development_effects_json?: string | null;
  unlock_requirements_json?: string | null;
  createdAt: string;
  lastModifiedAt: string;
}
export type PropItem = CMSItemBase;
export type CostumeItem = CMSItemBase;
export type PoseItem = CMSItemBase;

export interface RolePlayingCard {
  id: string;
  name: string;
  description: string;
  icon_path: string | null;
  initial_status_override_json: string | null;
  persona_snippet_override: string;
  sort_order?: number;
  createdAt: string;
  lastModifiedAt: string;
  base_avatar_clothing_key?: string;
}

// For Absolute Territory specific states
export interface LinLuoBodyDevelopment {
  zone_id: BodyZoneKey;
  development_points: number;
  last_developed_at: string;
}

export interface LinLuoDetailedStatus {
  arousal?: number;
  mood?: string;
  sanityPoints: number;
  sensitivityLevel: number;
  obedienceScore: number;
  shameLevel?: number;
  wetnessLevel?: number;
  orgasmProximity?: number;
  interfaceExpansion?: number;
  specificBodyPartStatus?: Record<string, Record<string, number>>;
  specialFluidGauge?: number;
  obedienceActionCount?: number;
}

export type AchievementTypeTGC = 'development' | 'interaction' | 'hidden';
export type AchievementRewardType = 'cg' | 'item' | 'title' | 'mode' | 'role_card' | 'none';

export type AchievementCriteriaType =
  | 'body_zone_points'
  | 'any_zone_min_points'
  | 'all_zones_min_points'
  | 'obedience_action_count'
  | 'specific_interaction'
  | 'cms_combo_count';

export interface AchievementCriteria {
  type: AchievementCriteriaType;
  zone?: BodyZoneKey;
  points_required?: number;
  count_required?: number;
  action_name?: string;
}

export interface AchievementReward {
  type: AchievementRewardType;
  value: string;
  item_type?: CMSType | AssetType | 'special_item';
  description?: string;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  type: AchievementTypeTGC;
  criteria_json: string;
  reward_json: string;
  icon_path?: string | null;
  unlocked_at?: string | null;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
}

export interface UnlockRequirement {
  zone: BodyZoneKey;
  points_required: number;
}
