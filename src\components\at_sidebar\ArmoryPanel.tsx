// src/components/at_sidebar/ArmoryPanel.tsx
import React from 'react';
import type { PropItem, CostumeItem, PoseItem, CMSType, CMSItemBase, LinLuoBodyDevelopment, UnlockRequirement, AssetType, AnyLoadedAsset } from '@/types';
import { Icon } from '@/components/common/Icon';
import { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';

interface ArmoryPanelProps {
  propsItems: PropItem[]; // These will now be PropAsset[] from TGC
  costumesItems: CostumeItem[]; // These will now be CostumeAsset[] from TGC
  posesItems: PoseItem[]; // These will now be PoseAsset[] from TGC
  currentArmoryOwner: 'master' | 'queen';
  onCMSItemSelected: (item: AnyLoadedAsset, type: AssetType) => void; // Use AnyLoadedAsset and AssetType
  isLoadingCMS: boolean;
  bodyDevelopment: LinLuoBodyDevelopment[];
  newlyUnlockedItems: Set<string>;
  setNewlyUnlockedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  activePouch: AssetType; // Changed from CMSType to AssetType
  onPouchChange: (pouch: AssetType) => void; 
}

const checkCMSItemUnlocked = (item: AnyLoadedAsset, currentDevelopment: LinLuoBodyDevelopment[]): boolean => {
    if (!item.unlock_requirements_json || item.unlock_requirements_json === '[]') return true;
    try {
      const requirements: UnlockRequirement[] = JSON.parse(item.unlock_requirements_json);
      if (!Array.isArray(requirements) || requirements.length === 0) return true;
      return requirements.every(req => {
        const zoneDev = currentDevelopment.find(dev => dev.zone_id === req.zone);
        return zoneDev && zoneDev.development_points >= req.points_required;
      });
    } catch (e) { return false; }
};

const getUnlockTooltip = (item: AnyLoadedAsset, currentDevelopment: LinLuoBodyDevelopment[]): string => {
    if (!item.unlock_requirements_json || item.unlock_requirements_json === '[]') return "可直接使用";
    try {
      const requirements: UnlockRequirement[] = JSON.parse(item.unlock_requirements_json);
      if (!Array.isArray(requirements) || requirements.length === 0) return "可直接使用";
      const unmetReqs = requirements.map(req => {
        const zoneDev = currentDevelopment.find(dev => dev.zone_id === req.zone);
        const currentPoints = zoneDev?.development_points || 0;
        return `【${BODY_ZONE_DISPLAY_NAMES[req.zone] || req.zone}】开发度 ${currentPoints}/${req.points_required}`;
      }).join('；');
      return `解锁需: ${unmetReqs}`;
    } catch (e) { return "解锁条件解析错误"; }
};


export const ArmoryPanel: React.FC<ArmoryPanelProps> = ({
  propsItems, costumesItems, posesItems, currentArmoryOwner, onCMSItemSelected, isLoadingCMS, bodyDevelopment, newlyUnlockedItems, setNewlyUnlockedItems, activePouch, onPouchChange
}) => {

  console.log('🎮 ArmoryPanel 接收到的数据:', {
    activePouch,
    propsCount: propsItems.length,
    costumesCount: costumesItems.length,
    posesCount: posesItems.length,
    currentOwner: currentArmoryOwner
  });

  const renderCMSItems = (items: AnyLoadedAsset[], type: AssetType) => { // Changed type to AssetType
    console.log(`🔍 ArmoryPanel 渲染 ${type}:`, {
      totalItems: items.length,
      currentOwner: currentArmoryOwner,
      targetType: type,
      sampleItem: items[0]
    });

    const filteredItems = items.filter(item => item.owner === currentArmoryOwner && item.type === type);
    const allOwners = items.map(i => i.owner);
    const allTypes = items.map(i => i.type);

    console.log(`🔍 过滤后的 ${type}:`, {
      filteredCount: filteredItems.length,
      allOwners: [...new Set(allOwners)], // 显示唯一值
      allTypes: [...new Set(allTypes)], // 显示唯一值
      sampleOwners: allOwners.slice(0, 5), // 显示前5个
      sampleTypes: allTypes.slice(0, 5), // 显示前5个
      sampleFilteredItems: filteredItems.slice(0, 3).map(item => ({ id: item.id, name: item.name, owner: item.owner, type: item.type }))
    });

    if (isLoadingCMS) return <div className="text-center text-xs text-gray-400 py-3"><Icon name="Loader" className="w-4 h-4 animate-spin inline mr-1"/>加载中...</div>;
    if (filteredItems.length === 0) return <p className="text-xs text-center text-gray-500 py-3">此分类下暂无 ({currentArmoryOwner === 'master' ? '主人用' : '女王用'}) 物品。</p>;
    
    return (
        <div className="grid grid-cols-3 gap-2 p-1">
            {filteredItems.map(item => {
                const isUnlocked = checkCMSItemUnlocked(item, bodyDevelopment);
                const isNew = newlyUnlockedItems.has(item.id);
                const tooltip = isUnlocked ? item.name : getUnlockTooltip(item, bodyDevelopment);

                return (
                    <button
                        key={item.id}
                        onClick={() => {
                            console.log('🔘 道具按钮点击:', { name: item.name, id: item.id, isUnlocked, type });
                            if (isUnlocked) {
                                console.log('✅ 道具已解锁，调用 onCMSItemSelected');
                                onCMSItemSelected(item, type);
                                if (isNew) {
                                    setNewlyUnlockedItems(prev => {
                                        const next = new Set(prev);
                                        next.delete(item.id);
                                        return next;
                                    });
                                }
                            } else {
                                console.log('🔒 道具未解锁，无法使用');
                            }
                        }}
                        className={`flex flex-col items-center p-1.5 rounded-md transition-all border aspect-square relative
                                    ${isUnlocked ? 'bg-tg-bg-primary hover:bg-purple-700/50 border-purple-700/30' : 'bg-gray-700 border-gray-600 opacity-60 cursor-not-allowed'}`}
                        title={tooltip}
                        disabled={!isUnlocked}
                    >
                        {!isUnlocked && <Icon name="Lock" className="absolute top-1 right-1 w-3 h-3 text-yellow-400 z-10"/>}
                        {isUnlocked && isNew && (
                            <Icon name="Sparkles" className="absolute top-0.5 right-0.5 w-3.5 h-3.5 text-green-400 z-10 animate-pulse" title="新解锁!"/>
                        )}
                        {item.icon_path ? (
                            <img 
                                src={item.icon_path} // Use directly as it's now a tgc-asset:// URL
                                alt={item.name} 
                                className={`w-8 h-8 object-contain rounded mb-1 ${!isUnlocked ? 'filter grayscale' : ''}`}
                            />
                        ) : (
                            <Icon name="Image" className={`w-8 h-8 text-gray-500 mb-1 ${!isUnlocked ? 'filter grayscale' : ''}`}/>
                        )}
                        <span className={`text-[10px] text-gray-300 truncate w-full text-center ${!isUnlocked ? 'opacity-70' : ''}`}>{item.name}</span>
                    </button>
                );
            })}
        </div>
    );
  };

  return (
    <div className="flex-grow overflow-y-auto rounded-lg p-1.5 shadow-inner min-h-0 bg-gray-900/70 border border-purple-700/30">
      {activePouch === 'prop' && renderCMSItems(propsItems as AnyLoadedAsset[], 'prop')}
      {activePouch === 'costume' && renderCMSItems(costumesItems as AnyLoadedAsset[], 'costume')}
      {activePouch === 'pose' && renderCMSItems(posesItems as AnyLoadedAsset[], 'pose')}
    </div>
  );
};