// src/types/taskTypes.ts

export type TaskStatus = 'todo' | 'doing' | 'pending_review' | 'done' | 'blocked';
export const TASK_STATUS_ORDER: TaskStatus[] = ['todo', 'doing', 'pending_review', 'done', 'blocked'];

export type TaskPriority = number; // Changed from 0 | 1 | 2 | 3;
export const PRIORITY_LABELS: Record<number, string> = { // Changed TaskPriority to number
  0: '十万火急', 1: '优先处理', 2: '常规', 3: '低优先级'
};

export type ResourceType =
  | 'global_knowledge_tome'
  | 'project_knowledge_tome'
  | 'core_memory_id'
  | 'file_path'
  | 'url'
  | 'chat_message_segment';

export const RESOURCE_TYPE_DISPLAY_NAMES: Record<ResourceType, string> = {
  global_knowledge_tome: "全局卷宗",
  project_knowledge_tome: "项目卷宗",
  core_memory_id: "核心记忆",
  file_path: "文件路径",
  url: "网页链接",
  chat_message_segment: "聊天记录",
};

export interface TaskResourceLink {
  link_id: string;
  task_id: string;
  resource_type: ResourceType;
  resource_identifier: string;
  resource_name?: string;
  description?: string;
}

export interface Task {
  task_id: string;
  project_id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority; // Will now be number
  assignee_id?: string;
  created_at: string;
  updated_at?: string;
  due_date?: string;
  parent_task_id?: string | null;
  resource_links?: TaskResourceLink[];
  estimated_duration_hours?: number | null;
  actual_duration_hours?: number | null;
  complexity_score?: number | null;
  dependencies?: string[] | null;
  blockers?: string | null;
  skill_requirements?: string[] | null;
  ai_confidence_score?: number | null;
  manual_override_reason?: string | null;
}

export interface TaskCreationData {
  project_id: string;
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority; // Will now be number
  assignee_id?: string;
  due_date?: string;
  parent_task_id?: string | null;
  estimated_duration_hours?: number | null;
  complexity_score?: number | null;
  dependencies?: string[] | null;
  skill_requirements?: string[] | null;
}

export interface DevelopmentTask {
  id: string;
  projectId: string;
  title: string;
  status: 'todo' | 'inprogress' | 'done';
  createdAt: string;
  context_files: string[];
  generated_code: string | null;
}

export interface DevelopmentTaskCreationPayload {
  projectId: string;
  title: string;
  description?: string;
}