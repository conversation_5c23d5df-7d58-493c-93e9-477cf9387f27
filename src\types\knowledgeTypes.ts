// src/types/knowledgeTypes.ts
import type { KnowledgeTomeCategory as OriginalKnowledgeTomeCategory } from '@/features/knowledge_tomes/knowledgeConstants';

export type KnowledgeTomeCategory = OriginalKnowledgeTomeCategory;

export interface KnowledgeTome {
  id: string;
  title: string;
  content: string;
  category: KnowledgeTomeCategory;
  tags: string[];
  createdAt: string;
  lastModifiedAt: string;
}

export interface GlobalQuickCommandItem {
  id: string;
  title: string;
  commandText: string;
  createdAt: string;
  lastModifiedAt: string;
}

export interface RetrievedChunk {
  id: string;
  source_project_id: string;
  source_file_path: string;
  chunk_text: string;
  metadata: {
    file_type: string;
    chunk_index: number;
    original_file_name?: string;
  };
  indexed_at: string;
  similarityScore: number;
  totalScore?: number;
}
