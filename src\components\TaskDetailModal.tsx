// src/components/TaskDetailModal.tsx
import React, { useState, useEffect, useCallback } from 'react';
import type { Task, TaskStatus, TaskPriority, ResourceType, TaskResourceLink, TaskDetailModalProps as TaskDetailModalPropsType, Partial, TaskCreationData } from '@/types';
import { TASK_STATUS_ORDER, RESOURCE_TYPE_DISPLAY_NAMES, PRIORITY_LABELS } from '@/types'; 
import { Icon } from '@/components/common/Icon'; 
import { GenericModal } from '@/components/GenericModal';

export const TaskDetailModal: React.FC<TaskDetailModalPropsType> = ({ 
    isOpen, 
    onClose, 
    task: initialTask, 
    projectId, 
    allProjectTasks,
    onTaskUpdated,
    onStartCrafting 
}) => {
  const [task, setTask] = useState<Task | null>(initialTask);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [status, setStatus] = useState<TaskStatus>('todo');
  const [priority, setPriority] = useState<TaskPriority>(2); // TaskPriority is now number
  const [dueDate, setDueDate] = useState<string>('');
  const [assigneeId, setAssigneeId] = useState<string>('');
  const [parentTaskId, setParentTaskId] = useState<string | null>(null);
  const [localResourceLinks, setLocalResourceLinks] = useState<TaskResourceLink[]>([]);
  
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [suggestedResources, setSuggestedResources] = useState<Partial<TaskResourceLink>[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [suggestionError, setSuggestionError] = useState<string | null>(null);

  const fetchTaskDetailsAndSuggestions = useCallback(async (currentTask: Task) => {
    if (!currentTask) return;
    try {
      const links = await window.api.tasks.getResourceLinksForTask(currentTask.task_id);
      setLocalResourceLinks(links || []);
    } catch (err: any) {
      setError(`加载关联资源失败: ${err.message}`);
      setLocalResourceLinks([]);
    }

    if (currentTask.title && currentTask.task_id) {
      setIsLoadingSuggestions(true);
      setSuggestionError(null);
      try {
        const suggestions = await window.api.tasks.suggestResourcesForTask(currentTask.task_id, currentTask.title, currentTask.description, projectId);
        setSuggestedResources(suggestions || []);
      } catch (err: any) {
        console.error("Error fetching suggested resources:", err);
        setSuggestionError(`获取AI智能推荐资源失败: ${err.message}`);
      } finally {
        setIsLoadingSuggestions(false);
      }
    }
  }, [projectId]);

  useEffect(() => {
    setTask(initialTask);
    if (initialTask) {
      setTitle(initialTask.title);
      setDescription(initialTask.description || '');
      setStatus(initialTask.status);
      setPriority(initialTask.priority);
      setDueDate(initialTask.due_date ? initialTask.due_date.split('T')[0] : '');
      setAssigneeId(initialTask.assignee_id || '');
      setParentTaskId(initialTask.parent_task_id || null);
      setLocalResourceLinks(initialTask.resource_links || []); 
      
      if (isOpen) { 
        fetchTaskDetailsAndSuggestions(initialTask);
      }
    } else { 
      setTitle('');
      setDescription('');
      setStatus('todo');
      setPriority(2); 
      setDueDate('');
      setAssigneeId('');
      setParentTaskId(null);
      setLocalResourceLinks([]);
      setSuggestedResources([]);
    }
    setError(null);
    setSuggestionError(null);
    setIsSaving(false);
  }, [initialTask, isOpen, fetchTaskDetailsAndSuggestions]);


  const handleSubmit = async () => {
    if (!title.trim()) {
      setError("任务标题不能为空。");
      return;
    }
    setIsSaving(true);
    setError(null);
    try {
      const taskDataPayload: Partial<Task> & { project_id: string } = { // Ensure project_id is included for creation
        project_id: projectId,
        title: title.trim(),
        description: description.trim(),
        status,
        priority,
        assignee_id: assigneeId.trim() || undefined,
        due_date: dueDate || undefined,
        parent_task_id: parentTaskId,
      };

      let savedTask: Task | null = null;
      if (task) { 
        // For update, project_id is not part of the updates object typically
        const { project_id, ...updatePayload } = taskDataPayload;
        savedTask = await window.api.tasks.updateTask(task.task_id, updatePayload);
        if (savedTask) { 
            savedTask.resource_links = localResourceLinks;
        }
      } else { 
        savedTask = await window.api.tasks.addTask(taskDataPayload as TaskCreationData); // Cast for creation
      }
      
      if (!savedTask) throw new Error(task ? "更新任务失败。" : "创建新任务失败。");
      
      onTaskUpdated(savedTask); 
      onClose(true); 
    } catch (err: any) {
      setError(`保存任务失败: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddSuggestedResource = async (suggestedLink: Partial<TaskResourceLink>) => {
    if (!task || !suggestedLink.resource_type || !suggestedLink.resource_identifier) {
      setError("无法关联资源：任务信息不完整或推荐信息有误。");
      return;
    }
    setError(null);
    try {
      const newLinkData: Omit<TaskResourceLink, 'link_id' | 'task_id'> = {
        resource_type: suggestedLink.resource_type,
        resource_identifier: suggestedLink.resource_identifier,
        resource_name: suggestedLink.resource_name,
        description: suggestedLink.description,
      };
      const createdLink = await window.api.tasks.addResourceLinkToTask(task.task_id, newLinkData);
      if (createdLink) {
        const updatedLinks = [...localResourceLinks, createdLink];
        setLocalResourceLinks(updatedLinks);
        const updatedTaskForParent = { ...task, resource_links: updatedLinks };
        onTaskUpdated(updatedTaskForParent);
        setSuggestedResources(prev => prev.filter(s => !(s.resource_type === createdLink.resource_type && s.resource_identifier === createdLink.resource_identifier)));
      } else {
        throw new Error("添加资源链接失败，API未返回链接对象。");
      }
    } catch (err: any) {
      setError(`关联AI推荐资源失败: ${err.message}`);
    }
  };


  const handleRemoveResourceLink = async (linkIdToRemove: string) => {
    if (!task) return; 
    setError(null);
    try {
      const result = await window.api.tasks.removeResourceLinkFromTask(linkIdToRemove);
      if (result.success) {
        const updatedLinks = localResourceLinks.filter(link => link.link_id !== linkIdToRemove);
        setLocalResourceLinks(updatedLinks);
        const updatedTask = { ...task, resource_links: updatedLinks };
        setTask(updatedTask); 
        onTaskUpdated(updatedTask); 
      } else {
        throw new Error(result.error || "移除资源链接失败。");
      }
    } catch (err:any) {
      setError(`移除资源失败: ${err.message}`);
    }
  };

  const handleStartCraftingClick = () => {
    if (task) {
      onStartCrafting(task);
      onClose(); 
    }
  };
  
  const priorityOptions: { value: TaskPriority; label: string }[] = [ // TaskPriority is now number
    { value: 0, label: '十万火急' }, { value: 1, label: '优先处理' },
    { value: 2, label: '常规' }, { value: 3, label: '低优先级' },
  ];


  return (
    <GenericModal
      isOpen={isOpen}
      onClose={() => onClose(false)}
      title={task ? "编辑任务详情" : "创建新任务"}
      size="xl"
      footerContent={
        <div className="w-full flex justify-between items-center">
          <button 
            onClick={handleStartCraftingClick}
            disabled={!task || isSaving} 
            className="py-2 px-3 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            <Icon name="Play" className="w-4 h-4 mr-1.5"/> 开始炼制
          </button>
          <div>
            <button onClick={() => onClose(false)} className="py-2 px-3 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary disabled:opacity-50" disabled={isSaving}>取消</button>
            <button onClick={handleSubmit} className="ml-2 py-2 px-3 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover disabled:opacity-50" disabled={isSaving}>
              {isSaving ? <Icon name="Loader2" className="w-4 h-4 mr-1.5 animate-spin inline-block"/> : null}
              {isSaving ? "保存中..." : (task ? "保存更改" : "创建任务")}
            </button>
          </div>
        </div>
      }
    >
      {error && <p className="mb-3 p-2 text-sm text-red-400 bg-red-900/30 rounded flex items-center"><Icon name="AlertTriangle" className="w-4 h-4 mr-1.5"/>{error}</p>}
      <div className="space-y-4 text-sm max-h-[70vh] overflow-y-auto pr-2 custom-scrollbar">
        <div>
          <label htmlFor="task-title" className="block text-xs text-tg-text-secondary mb-1">任务标题*</label>
          <input id="task-title" type="text" value={title} onChange={e => setTitle(e.target.value)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
        </div>
        <div>
          <label htmlFor="task-description" className="block text-xs text-tg-text-secondary mb-1">任务描述</label>
          <textarea id="task-description" value={description} onChange={e => setDescription(e.target.value)} rows={4} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded min-h-[80px] resize-y focus:border-tg-accent-primary"/>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="task-status" className="block text-xs text-tg-text-secondary mb-1">状态</label>
            <select id="task-status" value={status} onChange={e => setStatus(e.target.value as TaskStatus)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
              {TASK_STATUS_ORDER.map(s => <option key={s} value={s}>{s === 'todo' ? '待办' : s === 'doing' ? '进行中' : s === 'pending_review' ? '待审核' : s === 'done' ? '已完成' : '已阻塞'}</option>)}
            </select>
          </div>
          <div>
            <label htmlFor="task-priority" className="block text-xs text-tg-text-secondary mb-1">优先级</label>
            <select id="task-priority" value={priority} onChange={e => setPriority(Number(e.target.value))} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
              {priorityOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
            </select>
          </div>
          <div>
            <label htmlFor="task-due-date" className="block text-xs text-tg-text-secondary mb-1">截止日期</label>
            <input id="task-due-date" type="date" value={dueDate} onChange={e => setDueDate(e.target.value)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
          </div>
          <div>
            <label htmlFor="task-assignee" className="block text-xs text-tg-text-secondary mb-1">责任人 (可选)</label>
            <input id="task-assignee" type="text" value={assigneeId} onChange={e => setAssigneeId(e.target.value)} placeholder="输入负责人ID或名称" className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary"/>
          </div>
          <div>
            <label htmlFor="task-parent" className="block text-xs text-tg-text-secondary mb-1">父任务 (可选)</label>
            <select id="task-parent" value={parentTaskId || ''} onChange={e => setParentTaskId(e.target.value || null)} className="w-full p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded focus:border-tg-accent-primary">
              <option value="">无父任务</option>
              {allProjectTasks.filter(pt => pt.task_id !== task?.task_id).map(pt => <option key={pt.task_id} value={pt.task_id}>{pt.title}</option>)}
            </select>
          </div>
        </div>
        
        <div className="pt-3 mt-3 border-t border-tg-border-primary">
          <h5 className="text-sm font-medium text-tg-text-secondary mb-2 flex items-center">
            <Icon name="Link" className="w-4 h-4 mr-1.5"/>已关联资源
          </h5>
          {localResourceLinks.length > 0 ? (
            <ul className="space-y-1.5 max-h-28 overflow-y-auto mb-3 custom-scrollbar pr-1">
              {localResourceLinks.map(link => (
                <li key={link.link_id} className="flex justify-between items-center p-1.5 bg-tg-bg-primary rounded text-xs border border-tg-border-primary">
                  <div className="truncate">
                    <span className="font-medium text-tg-accent-secondary mr-1.5 capitalize">{RESOURCE_TYPE_DISPLAY_NAMES[link.resource_type] || link.resource_type}:</span>
                    <span className="text-tg-text-primary truncate" title={link.resource_identifier}>{link.resource_name || link.resource_identifier}</span>
                    {link.description && <span className="text-tg-text-placeholder ml-1 italic truncate" title={link.description}>({link.description})</span>}
                  </div>
                  <button onClick={() => handleRemoveResourceLink(link.link_id)} className="p-0.5 text-red-500 hover:text-red-300 flex-shrink-0 ml-2" title="移除此资源链接"><Icon name="Trash2" className="w-3.5 h-3.5"/></button>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-xs text-tg-text-placeholder text-center py-1 mb-3">暂无已关联资源。</p>
          )}

          {task && ( 
            <>
              <h5 className="text-sm font-medium text-tg-text-secondary mb-2 flex items-center">
                <Icon name="Sparkles" className="w-4 h-4 mr-1.5 text-yellow-400"/>AI智能推荐资源
              </h5>
              {isLoadingSuggestions && <p className="text-xs text-tg-text-placeholder"><Icon name="Loader2" className="w-3 h-3 mr-1 animate-spin inline"/>小岚努力思考中...</p>}
              {suggestionError && <p className="text-xs text-red-400 bg-red-900/20 p-1.5 rounded">{suggestionError}</p>}
              {!isLoadingSuggestions && !suggestionError && suggestedResources.length > 0 && (
                <ul className="space-y-1.5 max-h-32 overflow-y-auto mb-2 custom-scrollbar pr-1">
                    {suggestedResources.map((sug, idx) => (
                        <li key={`${sug.resource_type}-${sug.resource_identifier}-${idx}`} className="flex justify-between items-center p-1.5 bg-tg-bg-primary rounded text-xs border border-tg-border-primary hover:bg-tg-bg-hover">
                            <div className="truncate flex-grow">
                                <span className="font-medium text-tg-accent-secondary mr-1.5 capitalize">{RESOURCE_TYPE_DISPLAY_NAMES[sug.resource_type!] || sug.resource_type}:</span>
                                <span className="text-tg-text-primary truncate" title={sug.resource_identifier}>{sug.resource_name || sug.resource_identifier}</span>
                                {sug.description && <span className="text-tg-text-placeholder ml-1 italic truncate" title={sug.description}>({sug.description})</span>}
                            </div>
                            <button onClick={() => handleAddSuggestedResource(sug)} className="p-0.5 text-green-500 hover:text-green-300 flex-shrink-0 ml-2" title="关联此推荐资源"><Icon name="Plus" className="w-4 h-4"/></button>
                        </li>
                    ))}
                </ul>
              )}
              {!isLoadingSuggestions && !suggestionError && suggestedResources.length === 0 && (
                 <p className="text-xs text-tg-text-placeholder text-center py-1 mb-2">暂无AI推荐，或当前任务信息不足以生成推荐。</p>
              )}
            </>
          )}
        </div>
      </div>
    </GenericModal>
  );
};
