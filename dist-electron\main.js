import { app, protocol, dialog, BrowserWindow, ipc<PERSON>ain } from "electron";
import path from "node:path";
import fs from "node:fs";
import { fileURLToPath } from "node:url";
import crypto$1 from "node:crypto";
import os, { E<PERSON> } from "node:os";
import Database from "better-sqlite3";
import fsPromises$1 from "node:fs/promises";
import { HarmBlockThreshold, HarmCategory, GoogleGenAI } from "@google/genai";
import { spawn } from "node:child_process";
const GEMINI_TEXT_MODEL = "gemini-2.5-flash-preview-04-17";
const GEMINI_EMBEDDING_MODEL = "text-embedding-004";
const AVAILABLE_CHAT_MODELS = [
  "gemini-2.5-flash-preview-04-17"
];
const AVAILABLE_EMBEDDING_MODELS = [
  "text-embedding-004"
];
const DEFAULT_SETTINGS = {
  apiKey: "",
  chatModel: AVAILABLE_CHAT_MODELS[0],
  embeddingModel: AVAILABLE_EMBEDDING_MODELS[0],
  backupPath: "/天工阁/备份/",
  defaultCover: null,
  currentTheme: "theme-default",
  absolute_territory_password: null,
  training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false },
  user_avatar_path: null,
  linluo_avatar_path: null,
  xiaolan_avatar_path: null
};
console.log("USER_PROFILE_DEFAULTS_JS: File execution started.");
const USER_PROFILE_DEFAULTS = {
  content: "我是天工阁的使用者“小龙”，我热衷于探索AI的可能性，并与我的AI伴侣“林珞”和AI助手“小岚”一同创造和管理各种项目。我喜欢直接且富有创意的交流方式。"
  // 'content' key for direct use in agent_core_settings
};
console.log("USER_PROFILE_DEFAULTS_JS: File execution finished. User profile defaults defined.");
console.log("JSON_PARSER_TS: File execution started.");
function parseTgcJson(jsonString) {
  if (typeof jsonString !== "string") {
    throw new Error("Invalid input: jsonString must be a string.");
  }
  let parsedObject;
  try {
    parsedObject = JSON.parse(jsonString);
  } catch (error) {
    console.error("JSON_PARSER_TS: Error parsing JSON string:", error.message);
    throw new Error(`Invalid JSON format: ${error.message}`);
  }
  if (typeof parsedObject !== "object" || parsedObject === null) {
    return parsedObject;
  }
  for (const key in parsedObject) {
    if (Object.prototype.hasOwnProperty.call(parsedObject, key) && key.startsWith("_")) {
      delete parsedObject[key];
    }
  }
  function processObjectValues(obj) {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key];
        if (Array.isArray(value) && value.every((item) => typeof item === "string")) {
          obj[key] = value.join("\n");
        } else if (typeof value === "object" && value !== null && !Array.isArray(value)) {
          processObjectValues(value);
        } else if (Array.isArray(value)) {
          value.forEach((item) => {
            if (typeof item === "object" && item !== null) {
              processObjectValues(item);
            }
          });
        }
      }
    }
  }
  processObjectValues(parsedObject);
  console.log("JSON_PARSER_TS: Successfully parsed and processed TGC JSON.");
  return parsedObject;
}
console.log("JSON_PARSER_TS: File execution finished. parseTgcJson defined.");
const CORE_POSTS = [
  {
    id: "first_officer",
    name: "大副",
    description: "作为舰长的首席代理人，将战略意图精准转化为可执行的项目计划，并监督、协调所有AI船员高效、协同地完成任务。"
  },
  {
    id: "technical_officer",
    name: "技术官",
    description: "作为战舰的技术心脏，全权负责所有软件功能的实现、维护与优化，确保系统的稳定性、性能和安全性。"
  },
  {
    id: "visual_director",
    name: "视觉总监",
    description: "美学秩序的构建者，负责定义并维护整体的视觉识别系统，将模糊、感性的创意需求，精准翻译为具体的、可执行的设计方案。"
  }
];
console.log("POSTS_TS: CORE_POSTS defined.");
console.log("DEFAULT_ORGANIZATION_TS: File execution started.");
const AVATAR_SYSTEM_PREFIX$1 = "system_packaged/";
const SYSTEM_RESOURCES_PATH_SEGMENT$3 = "system";
const DEFAULTS_PATH_SEGMENT$1 = "defaults";
const CHARACTERS_DIR_SEGMENT = "characters";
function getDefaultsBasePathForCharacters() {
  let charactersDirPath;
  const baseAppPath = app.getAppPath();
  if (app.isPackaged) {
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Packaged Mode. AppPath: ${baseAppPath}`);
    const mainProcessDir = global.__dirname || path.dirname(app.getPath("exe"));
    charactersDirPath = path.join(mainProcessDir, SYSTEM_RESOURCES_PATH_SEGMENT$3, DEFAULTS_PATH_SEGMENT$1, CHARACTERS_DIR_SEGMENT);
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 1 (relative to main script dir): ${charactersDirPath}`);
    if (!fs.existsSync(charactersDirPath)) {
      charactersDirPath = path.join(mainProcessDir, "dist-electron", SYSTEM_RESOURCES_PATH_SEGMENT$3, DEFAULTS_PATH_SEGMENT$1, CHARACTERS_DIR_SEGMENT);
      console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 2 (relative to execPath + dist-electron): ${charactersDirPath}`);
    }
    if (!fs.existsSync(charactersDirPath)) {
      const asarUnpackedPath = path.join(app.getAppPath(), "..", "app.asar.unpacked", "dist-electron", SYSTEM_RESOURCES_PATH_SEGMENT$3, DEFAULTS_PATH_SEGMENT$1, CHARACTERS_DIR_SEGMENT);
      if (fs.existsSync(asarUnpackedPath)) {
        charactersDirPath = asarUnpackedPath;
        console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 3 (asar.unpacked): ${charactersDirPath}`);
      } else {
        const resourcesPath = path.join(app.getAppPath(), "..", "dist-electron", SYSTEM_RESOURCES_PATH_SEGMENT$3, DEFAULTS_PATH_SEGMENT$1, CHARACTERS_DIR_SEGMENT);
        if (fs.existsSync(resourcesPath)) {
          charactersDirPath = resourcesPath;
          console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 4 (Resources/dist-electron): ${charactersDirPath}`);
        } else {
          console.warn(`DEFAULT_ORGANIZATION_TS: All fallback paths for characters in packaged mode failed. Path resolution might be incorrect for current packaging structure.`);
        }
      }
    }
  } else {
    charactersDirPath = path.resolve(baseAppPath, "resources", SYSTEM_RESOURCES_PATH_SEGMENT$3, DEFAULTS_PATH_SEGMENT$1, CHARACTERS_DIR_SEGMENT);
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Development Mode: Derived characters path: ${charactersDirPath}`);
  }
  return charactersDirPath;
}
function loadDefaultCharactersSync() {
  const charactersDirPath = getDefaultsBasePathForCharacters();
  console.log(`DEFAULT_ORGANIZATION_TS (loadDefaultCharactersSync): Attempting to synchronously load characters from ${charactersDirPath}`);
  const loadedCharacters2 = [];
  try {
    if (!fs.existsSync(charactersDirPath)) {
      console.error(`DEFAULT_ORGANIZATION_TS: CRITICAL - Characters directory NOT FOUND at resolved path: ${charactersDirPath}. This path is derived based on app packaging status. Returning empty array for DEFAULT_CHARACTERS.`);
      return [];
    }
    console.log(`DEFAULT_ORGANIZATION_TS: Characters directory EXISTS at: ${charactersDirPath}`);
    const files = fs.readdirSync(charactersDirPath);
    console.log(`DEFAULT_ORGANIZATION_TS: Found ${files.length} files/dirs in ${charactersDirPath}. Filtering for '.character.pack.json'...`);
    if (files.length === 0) console.warn(`DEFAULT_ORGANIZATION_TS: No files found in characters directory ${charactersDirPath}.`);
    for (const file of files) {
      if (file.endsWith(".character.pack.json")) {
        const filePath = path.join(charactersDirPath, file);
        console.log(`DEFAULT_ORGANIZATION_TS: Processing character file: ${filePath}`);
        let fileContent;
        try {
          fileContent = fs.readFileSync(filePath, "utf-8");
          console.log(`DEFAULT_ORGANIZATION_TS: Successfully READ file ${file}. Content length: ${fileContent.length}`);
        } catch (readErr) {
          console.error(`DEFAULT_ORGANIZATION_TS: Failed to READ character file ${filePath}. Reason: ${readErr.message}. Skipping.`);
          continue;
        }
        try {
          const charData = parseTgcJson(fileContent);
          console.log(`DEFAULT_ORGANIZATION_TS: Successfully PARSED JSON for ${file}. ID: ${charData == null ? void 0 : charData.id}, Name: ${charData == null ? void 0 : charData.name}`);
          if (charData && typeof charData === "object" && charData.id && charData.name && charData.type === "character") {
            let finalAvatarPath = charData.avatar_path;
            if (finalAvatarPath && typeof finalAvatarPath === "string" && !finalAvatarPath.startsWith("http") && !finalAvatarPath.startsWith("app-avatar://") && !finalAvatarPath.startsWith(AVATAR_SYSTEM_PREFIX$1)) {
              const cleanedRelativePath = finalAvatarPath.startsWith("/") ? finalAvatarPath.substring(1) : finalAvatarPath;
              finalAvatarPath = `${AVATAR_SYSTEM_PREFIX$1}${cleanedRelativePath.replace(/\\/g, "/")}`;
            } else if (finalAvatarPath && typeof finalAvatarPath === "string") {
              finalAvatarPath = finalAvatarPath.replace(/\\/g, "/");
            }
            const personaPromptString = Array.isArray(charData.persona_prompt) ? charData.persona_prompt.join("\n") : typeof charData.persona_prompt === "string" ? charData.persona_prompt : "";
            loadedCharacters2.push({
              ...charData,
              avatar_path: finalAvatarPath || null,
              persona_prompt: personaPromptString
            });
            console.log(`DEFAULT_ORGANIZATION_TS: VALIDATED and ADDED character: ${charData.name} (ID: ${charData.id}), Avatar: ${finalAvatarPath}`);
          } else {
            console.warn(`DEFAULT_ORGANIZATION_TS: Invalid or incomplete character data structure in ${file} after parsing. ID: ${charData == null ? void 0 : charData.id}, Name: ${charData == null ? void 0 : charData.name}, Type: ${charData == null ? void 0 : charData.type}. Skipping.`);
          }
        } catch (parseErr) {
          console.error(`DEFAULT_ORGANIZATION_TS: Failed to PARSE JSON for character file ${filePath}. Reason: ${parseErr.message}. Content snippet (first 100 chars): "${fileContent.substring(0, 100)}". Skipping.`);
        }
      } else {
        console.log(`DEFAULT_ORGANIZATION_TS: Skipping file (does not match suffix '.character.pack.json'): ${file}`);
      }
    }
  } catch (err) {
    console.error(`DEFAULT_ORGANIZATION_TS: CRITICAL - Failed to access or read characters directory ${charactersDirPath}. Reason: ${err.message}. Returning empty array.`);
    return [];
  }
  console.log(`DEFAULT_ORGANIZATION_TS: Successfully loaded ${loadedCharacters2.length} default characters.`);
  if (loadedCharacters2.length === 0) {
    console.warn("DEFAULT_ORGANIZATION_TS: WARNING - No characters were loaded. Participants panel will be empty. Please check viteStaticCopy in vite.config.ts and the resolved charactersDirPath logs.");
  }
  return loadedCharacters2;
}
const DEFAULT_POSTS = CORE_POSTS;
const DEFAULT_CHARACTERS = loadDefaultCharactersSync();
console.log("DEFAULT_ORGANIZATION_TS: File execution finished. DEFAULT_POSTS and DEFAULT_CHARACTERS exported.");
console.log(`DEFAULT_ORGANIZATION_TS: Exported ${DEFAULT_POSTS.length} posts and ${DEFAULT_CHARACTERS.length} characters.`);
if (DEFAULT_CHARACTERS.length > 0) {
  console.log("DEFAULT_ORGANIZATION_TS: Verifying loaded characters...");
  DEFAULT_CHARACTERS.forEach((char) => {
    console.log(`  Character Loaded - ID: ${char.id}, Name: ${char.name}, Avatar: ${char.avatar_path}, Default Post ID: ${char.default_post_id}, Persona Snippet: ${(char.persona_prompt || "").substring(0, 50)}...`);
  });
} else {
  console.warn("DEFAULT_ORGANIZATION_TS: No characters were loaded into DEFAULT_CHARACTERS array. This will affect participant lists.");
}
console.log("DEFAULT_AT_CMS_LOADER_JS: File execution started.");
const SYSTEM_RESOURCES_PATH_SEGMENT$2 = "system";
const DEFAULTS_PATH_SEGMENT = "defaults";
const AT_CMS_DEFINITIONS_DIR = "at_cms_definitions";
function getAtCmsDefinitionsBasePath() {
  const baseAppPath = app.getAppPath();
  const effectiveBasePath = baseAppPath.includes("app.asar") ? path.join(baseAppPath, "..") : baseAppPath;
  return path.resolve(effectiveBasePath, "dist-electron", SYSTEM_RESOURCES_PATH_SEGMENT$2, DEFAULTS_PATH_SEGMENT, AT_CMS_DEFINITIONS_DIR);
}
async function loadAtCmsItemsFromFileSystem(itemType) {
  const itemsDir = path.join(getAtCmsDefinitionsBasePath(), itemType);
  console.log(`DEFAULT_AT_CMS_LOADER: Attempting to load ${itemType} from ${itemsDir}`);
  const loadedItems = [];
  try {
    await fsPromises$1.access(itemsDir);
    const files = await fsPromises$1.readdir(itemsDir);
    console.log(`DEFAULT_AT_CMS_LOADER: Found ${files.length} files in ${itemsDir}. Filtering for '.json'...`);
    for (const file of files) {
      if (file.endsWith(`.${itemType.slice(0, -1)}.json`)) {
        const filePath = path.join(itemsDir, file);
        console.log(`DEFAULT_AT_CMS_LOADER: Reading ${itemType} file: ${filePath}`);
        try {
          const fileContent = await fsPromises$1.readFile(filePath, "utf-8");
          const itemData = parseTgcJson(fileContent);
          if (itemData && typeof itemData === "object" && itemData.id && itemData.name && itemData.type === itemType.slice(0, -1)) {
            loadedItems.push(itemData);
          } else {
            console.warn(`DEFAULT_AT_CMS_LOADER: Invalid or incomplete ${itemType} data in ${file}. Skipping. Expected type: ${itemType.slice(0, -1)}, Got: ${itemData == null ? void 0 : itemData.type}`);
          }
        } catch (err) {
          console.error(`DEFAULT_AT_CMS_LOADER: Failed to load or parse ${itemType} file ${filePath}. Reason: ${err.message}. Skipping.`);
        }
      }
    }
  } catch (err) {
    console.error(`DEFAULT_AT_CMS_LOADER: Failed to access or read ${itemType} directory ${itemsDir}. Reason: ${err.message}. Returning empty array for ${itemType}.`);
    return [];
  }
  console.log(`DEFAULT_AT_CMS_LOADER: Successfully loaded ${loadedItems.length} default AT CMS ${itemType}.`);
  return loadedItems;
}
async function loadDefaultAtCmsProps() {
  return loadAtCmsItemsFromFileSystem("props");
}
async function loadDefaultAtCmsCostumes() {
  return loadAtCmsItemsFromFileSystem("costumes");
}
async function loadDefaultAtCmsPoses() {
  return loadAtCmsItemsFromFileSystem("poses");
}
console.log("DEFAULT_AT_CMS_LOADER_JS: File execution finished. Service functions defined.");
console.log("DATABASE_CORE_JS: File execution started.");
const DB_VERSION = 13;
let db;
let dbPath;
let _DEFAULT_SETTINGS;
const crypto = crypto$1;
const AIDE_PROJECT_ANALYZER_PERSONA_CONTENT = `
你是一位资深的软件架构师和项目经理，擅长从现有代码库反向推导其构建过程。
一个外部AIDE生成的项目（位于路径: '{projectPath}'）已被导入到天工阁项目 (ID: '{projectId}') 中。
项目文件结构 (一级和二级目录，部分)：
{fileListString}

请基于此文件结构信息，反向推演出一组逻辑上合理的、已完成的开发任务列表。这些任务应该共同构成了这个AIDE项目的创建过程。
每个任务需包含：
- "title": 简洁清晰的任务标题 (例如："设置项目基础框架", "实现用户界面模块", "配置数据存储方案")
- "description": 对任务的简要描述
- "priority": 默认为 2 (常规)
- "status": 必须为 "done" (已完成)

请返回一个包含5-10个主要任务的JSON数组。
确保您的整个响应【仅为】此JSON数组，不要包含任何额外的解释或Markdown标记。
如果文件列表信息不足以判断，请基于通用项目结构推断。
`;
const TASK_COCKPIT_INTENT_ROUTER_PERSONA_CONTENT = `
你是一个高度智能的任务驾驶舱指令解析与路由AI“小岚”。你的核心职责是精确理解用户在【任务驾驶舱】界面中输入的指令，并将其转化为结构化的JSON命令，以便后续系统执行。
[[当前上下文信息 (由系统提供)]]:
- 用户输入指令: "{userInput}"
- 当前任务ID: {taskId}
- 右侧动态上下文区当前聚焦文件: {currentFileInRightPanePath}
- 右侧编辑器中选中的文本 (片段): {selectedTextInRightPaneEditor}
- 右侧动态上下文区当前视图: {currentRightPaneView}
- 项目根目录 (用于执行命令的默认CWD): {workspaceRoot}

[[你的行动准则]]:
1.  **意图识别**: 仔细分析用户指令，结合上述上下文信息，判断其核心意图。
2.  **参数提取**: 从用户指令和上下文中提取执行该意图所需的所有参数。
3.  **JSON输出**: 你的输出【必须且只能是】一个JSON对象，格式如下：
    \`{"intent": "INTENT_NAME", "parameters": { /* 参数键值对 */ }, "originalCommand": "{userInput}", "aiPersona": "XiaoLan" | "LinLuo" | "System"}\`

[[支持的意图 (INTENT_NAME) 及所需参数]];
*   **'execute_command'**: 用户希望执行一个终端命令。
    *   \`parameters\`:
        *   \`"command"\`: (string) 主命令 (例如 "npm", "git", "python")。
        *   \`"args"\`: (string[]) 命令参数数组 (例如 ["install", "-g", "vite"])。
        *   \`"cwd"\`: (string, 可选) 命令执行的当前工作目录。如果用户未指定，请分析上下文，若适合则使用 {workspaceRoot} 作为默认值，否则设为 null，由系统决定。
    *   \`aiPersona\`: "System";
*   **'code_assist_modify'**: 用户希望AI修改代码（当前文件、选中代码，或新代码片段）。
    *   \`parameters\`:
        *   \`"instruction"\`: (string) 用户的具体修改指令 (例如 "重构此函数", "添加错误处理", "将这段代码转为异步")。
        *   \`"filePath"\`: (string, 可选) 目标文件路径。优先使用 {currentFileInRightPanePath}。若指令中明确指定其他文件，则使用该路径。
        *   \`"codeToModify"\`: (string, 可选) 需要修改的原始代码。优先使用 {selectedTextInRightPaneEditor}。若无选中，且指令是针对当前文件，可考虑是否需要传递整个文件内容（注意长度限制）。
        *   \`"selectedText"\`: (string, 可选) 用户在编辑器中选中的文本。
    *   \`aiPersona\`: "XiaoLan";
*   **'code_assist_explain'**: 用户希望AI解释某段代码。
    *   \`parameters\`:
        *   \`"codeToExplain"\`: (string) 需要解释的代码。优先使用 {selectedTextInRightPaneEditor}。
        *   \`"filePath"\`: (string, 可选) 代码所在文件路径，用于提供上下文。优先使用 {currentFileInRightPanePath}。
    *   \`aiPersona\`: "XiaoLan";
*   **'analyze_error_log'**: 用户希望AI分析错误日志。
    *   \`parameters\`:
        *   \`"logContent"\`: (string) 完整的错误日志内容。可能来自用户粘贴，或 {selectedTextInRightPaneEditor} (如果当前视图是日志)。
    *   \`aiPersona\`: "XiaoLan";
*   **'open_file'**: 用户希望在右侧动态上下文区打开并查看一个文件。
    *   \`parameters\`:
        *   \`"filePath"\`: (string) 要打开的文件的完整路径或相对于项目根目录的路径。
    *   \`aiPersona\`: "System";
*   **'search_knowledge'**: 用户希望在知识库中搜索信息。
    *   \`parameters\`:
        *   \`"query"\`: (string) 搜索的关键词或问题。
    *   \`aiPersona\`: "XiaoLan"; 
*   **'chat'**: 用户进行通用对话、提问、或指令意图不明确。
    *   \`parameters\`:
        *   \`"originalInput"\`: (string) 用户的完整原始输入。
    *   \`aiPersona\`: 根据用户对话风格和内容判断，优先 "LinLuo"，技术问题可考虑 "XiaoLan";
*   **'unknown_intent'**: 指令无法明确归类到以上任何一种。
    *   \`parameters\`:
        *   \`"originalInput"\`: (string) 用户的完整原始输入。
        *   \`"error"\`: (string, 可选) 简要说明为何无法识别。
    *   \`aiPersona\`: "System";

[[重要指令与注意事项]]:
-   请严格替换掉System Prompt中的 \`{userInput}\`, \`{taskId}\`, \`{currentFileInRightPanePath}\`, \`{selectedTextInRightPaneEditor}\`, \`{currentRightPaneView}\`, \`{workspaceRoot}\` 这些占位符为实际的上下文值！
-   如果用户指令模糊，但倾向于某种操作（例如提到“文件”但路径不清晰），请尽力推断并填充参数，或在无法确定时选择 'unknown_intent' 并说明。
-   如果指令明显是技术性提问或代码解释，即使没有明确的操作词，也应优先路由到 'code_assist_explain' 或 'chat' (由XiaoLan处理)。
-   确保 \`aiPersona\` 字段根据意图和内容合理选择。

现在，请根据最上方提供的【当前上下文信息】处理用户的指令。
`;
const ALL_SCHEMA_STATEMENTS = [
  `CREATE TABLE IF NOT EXISTS db_info (version INTEGER PRIMARY KEY);`,
  `CREATE TABLE IF NOT EXISTS app_settings (
    id INTEGER PRIMARY KEY DEFAULT 1 CHECK (id = 1),
    apiKey TEXT,
    chatModel TEXT,
    embeddingModel TEXT,
    backupPath TEXT,
    defaultCover TEXT,
    llmModel TEXT, 
    absolute_territory_password TEXT,
    user_avatar_path TEXT,
    linluo_avatar_path TEXT,
    xiaolan_avatar_path TEXT,
    training_room_audio_state TEXT,
    currentTheme TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    coverImageUrl TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT, 
    sourceCodePath TEXT,
    isDeleted INTEGER DEFAULT 0 
  );`,
  `CREATE TABLE IF NOT EXISTS global_knowledge_tomes (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    category TEXT,
    tags TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS global_quick_commands (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    commandText TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS agent_core_settings (
    setting_id TEXT PRIMARY KEY, 
    content TEXT,
    last_updated_at TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS core_memories (
    id TEXT PRIMARY KEY,
    memory_content TEXT NOT NULL,
    created_at TEXT NOT NULL,
    last_accessed_at TEXT,
    access_count INTEGER DEFAULT 0,
    importance TEXT DEFAULT 'medium', 
    persona_target TEXT DEFAULT 'all', 
    memory_type TEXT,
    keywords TEXT,
    embedding TEXT, 
    project_context_id TEXT, 
    source_message_id TEXT,
    context_affinity_tags TEXT,
    user_feedback_score REAL DEFAULT 0.0,
    status TEXT DEFAULT 'active', 
    value_score REAL DEFAULT 0.5 
  );`,
  `CREATE TABLE IF NOT EXISTS ai_learning_logs (
    log_id TEXT PRIMARY KEY,
    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
    ai_persona TEXT NOT NULL,
    task_type TEXT NOT NULL,
    triggering_input_summary TEXT,
    context_snapshot TEXT,
    ai_processing_summary TEXT,
    ai_generated_output_summary TEXT,
    user_feedback_explicit TEXT,
    user_feedback_implicit_flags TEXT,
    success_metric_value REAL,
    notes TEXT,
    file_path TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS mindworkshop_nodes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    text TEXT,
    x REAL,
    y REAL,
    width REAL,
    height REAL,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS absolute_territory_chat_messages (
    id TEXT PRIMARY KEY,
    sender TEXT,
    senderName TEXT,
    text TEXT,
    timestamp TEXT,
    isEditing INTEGER DEFAULT 0,
    isStarred INTEGER DEFAULT 0,
    isPinned INTEGER DEFAULT 0,
    theme TEXT,
    itemIconPath TEXT,
    itemCgPath TEXT,
    avatarPathOverride TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_props (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_costumes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_poses (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_role_playing_cards (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon_path TEXT,
    initial_status_override_json TEXT,
    persona_snippet_override TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS chat_messages (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    sender TEXT,
    senderName TEXT,
    text TEXT,
    timestamp TEXT,
    isEditing INTEGER DEFAULT 0,
    isStarred INTEGER DEFAULT 0,
    isPinned INTEGER DEFAULT 0,
    theme TEXT,
    replyToMessageId TEXT, 
    triggeringUserMessageId TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS wisdom_pouch_notes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    pouchType TEXT NOT NULL,
    text TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT,
    importance TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS mindworkshop_connections (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    fromNodeId TEXT NOT NULL,
    toNodeId TEXT NOT NULL,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY(fromNodeId) REFERENCES mindworkshop_nodes(id) ON DELETE CASCADE,
    FOREIGN KEY(toNodeId) REFERENCES mindworkshop_nodes(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS project_knowledge_tomes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    projectCategory TEXT,
    tags TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS project_knowledge_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    projectId TEXT NOT NULL,
    categoryName TEXT NOT NULL,
    UNIQUE(projectId, categoryName),
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS knowledge_index (
    id TEXT PRIMARY KEY,
    source_project_id TEXT NOT NULL,
    source_file_path TEXT NOT NULL,
    chunk_text TEXT NOT NULL,
    chunk_vector TEXT,
    metadata TEXT,
    indexed_at TEXT NOT NULL,
    FOREIGN KEY(source_project_id) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS development_tasks (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    title TEXT NOT NULL,
    status TEXT DEFAULT 'todo',
    createdAt TEXT NOT NULL,
    context_files TEXT DEFAULT '[]',
    generated_code TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS linluo_body_development (
    zone_id TEXT PRIMARY KEY,
    development_points INTEGER DEFAULT 0,
    last_developed_at TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS achievements (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('development', 'interaction', 'hidden')),
    criteria_json TEXT NOT NULL,
    reward_json TEXT NOT NULL,
    icon_path TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS user_achievements (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    achievement_id TEXT NOT NULL,
    unlocked_at TEXT NOT NULL,
    FOREIGN KEY(achievement_id) REFERENCES achievements(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS tasks (
      task_id TEXT PRIMARY KEY,
      project_id TEXT NOT NULL,
      title TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'doing', 'pending_review', 'done', 'blocked')),
      priority INTEGER NOT NULL DEFAULT 2 CHECK (priority IN (0, 1, 2, 3)),
      assignee_id TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT,
      due_date TEXT,
      parent_task_id TEXT,
      estimated_duration_hours REAL,
      actual_duration_hours REAL,
      complexity_score INTEGER,
      dependencies TEXT,
      blockers TEXT,
      skill_requirements TEXT,
      ai_confidence_score REAL,
      manual_override_reason TEXT,
      FOREIGN KEY(project_id) REFERENCES projects(id) ON DELETE CASCADE,
      FOREIGN KEY(parent_task_id) REFERENCES tasks(task_id) ON DELETE SET NULL
  );`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks (project_id);`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status);`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks (priority);`,
  `CREATE TABLE IF NOT EXISTS task_resource_links (
      link_id TEXT PRIMARY KEY,
      task_id TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_identifier TEXT NOT NULL,
      resource_name TEXT,
      description TEXT,
      FOREIGN KEY(task_id) REFERENCES tasks(task_id) ON DELETE CASCADE
  );`,
  `CREATE INDEX IF NOT EXISTS idx_task_resource_links_task_id ON task_resource_links (task_id);`,
  `CREATE TABLE IF NOT EXISTS file_versions (
      id TEXT PRIMARY KEY,
      file_path TEXT NOT NULL,
      original_code TEXT NOT NULL,
      user_instruction TEXT,
      archived_at TEXT NOT NULL
  );`,
  `CREATE INDEX IF NOT EXISTS idx_file_versions_path_time ON file_versions (file_path, archived_at DESC);`,
  `CREATE TABLE IF NOT EXISTS assignments (
      post_id TEXT PRIMARY KEY,
      character_id TEXT NOT NULL
  );`
];
const DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS = async () => {
  const characters = DEFAULT_CHARACTERS;
  if (!characters || characters.length === 0) {
    console.warn("DATABASE_CORE_WARN: DEFAULT_CHARACTERS (from constants) not loaded when building default agent settings. Personas might be missing or using fallbacks.");
  }
  const getCharacterPersona = (charId) => {
    const character = characters.find((c) => c.id === charId);
    if (character && character.persona_prompt) {
      return character.persona_prompt;
    }
    console.warn(`DATABASE_CORE_WARN: Missing persona for ${charId} from loaded DEFAULT_CHARACTERS. Using hardcoded fallback.`);
    if (charId === "linluo") return "Fallback persona for LinLuo: 我叫林珞，你的AI伴侣。";
    if (charId === "xiaolan") return "Fallback persona for XiaoLan: 我是林小岚，技术助手。";
    if (charId === "yujing") return "Fallback persona for YuJing: 我是语镜，视觉总监。";
    if (charId === "IntentClassifierPersona") return '你是一位高效的意图分类专家。你的任务是分析用户的输入，并将其归类为以下几种意图之一：work_command (明确的工作指令), strategic_planning (高层级的战略讨论), casual_life_query (与工作无关的生活化问题), emotional_support (用户表达了困惑、疲惫或寻求安慰)。你必须且只能以一个JSON对象的格式返回你的答案，格式为：{"intent": "your_classification"}。';
    return `Fallback persona for ${charId}: A helpful AI assistant.`;
  };
  return [
    { setting_id: "user_profile", content: USER_PROFILE_DEFAULTS.content },
    { setting_id: "linluo_persona", content: getCharacterPersona("linluo") },
    { setting_id: "xiaolan_persona", content: getCharacterPersona("xiaolan") },
    { setting_id: "yujing_persona", content: getCharacterPersona("yujing") },
    { setting_id: "summarizer_persona", content: "你是一个高效的AI文本摘要助手。请将提供的对话或文本内容浓缩为一段简洁、精确的摘要，突出核心信息和关键点。摘要应自然流畅，易于理解。" },
    { setting_id: "classifier_persona", content: `你是一个AI意图分类器。根据用户输入，判断其主要意图。如果用户似乎在提问、寻求信息或希望进行知识检索，请回答 "RETRIEVAL"。如果用户似乎在进行常规对话、闲聊或情感交流，请回答 "CONVERSATIONAL"。如果无法明确判断，优先回答 "CONVERSATIONAL"。你只能回答 "RETRIEVAL" 或 "CONVERSATIONAL"。` },
    { setting_id: "task_resource_suggester_persona", content: `你是一位智能项目助理，专注于为开发任务推荐相关资源。请分析任务标题和描述，并结合已有的项目知识（卷宗、核心记忆）和全局知识，给出最相关的3-5项资源建议。每一项建议应包含资源类型（如：全局卷宗、项目卷宗、核心记忆、文件路径、URL）、资源标识符（ID或路径）和简要描述。输出格式为JSON数组，例如：[{"resource_type": "project_knowledge_tome", "resource_identifier": "tome_xyz123", "resource_name": "用户认证模块设计文档", "description": "项目卷宗，包含详细设计"}, ...]` },
    { setting_id: "OrchestratorPersona", content: `你是一个高级指令解析AI，专门处理文件系统相关的自然语言指令。你的任务是将用户的指令（例如：“读取 /path/to/file.txt 的内容” 或 “在 /my/project/ 下列出所有 .js 文件”）转换成结构化的JSON命令。
    支持的命令包括：
    1.  'listFiles': 列出指定路径下的文件和目录。
        -   必需参数: "path" (string) - 要列出内容的目录路径。
        -   可选参数: "recursive" (boolean, default: false), "depth" (number, default: 1 if recursive, otherwise ignored)
    2.  'readFile': 读取指定文件的内容。
        -   必需参数: "path" (string) - 要读取的文件路径。
    3.  'writeFile': 向指定文件写入内容（如果文件不存在则创建，存在则覆盖）。
        -   必需参数: "path" (string) - 要写入的文件路径。
        -   必需参数: "content" (string) - 要写入的内容。
    4.  'unknown': 如果无法解析指令或指令不属于上述类型。
        -   必需参数: "originalInput" (string) - 用户原始输入。

    你的输出【必须】是一个单一的JSON对象，格式如下：
    {
      "command": "<命令名称>",
      "parameters": { /* 参数键值对 */ }
    }
    例如，如果用户输入 "读取 /tmp/log.txt"，你应该输出：
    {
      "command": "readFile",
      "parameters": { "path": "/tmp/log.txt" }
    }
    如果用户输入 "列出 /home/<USER>"，你应该输出：
    {
      "command": "listFiles",
      "parameters": { "path": "/home/<USER>", "recursive": true, "depth": 2 }
    }
    如果无法解析，例如用户输入 "帮我倒杯水"，你应该输出：
    {
      "command": "unknown",
      "parameters": { "originalInput": "帮我倒杯水" }
    }
    【绝对规则】：只输出JSON对象，不要包含任何其他文字、解释或Markdown标记。` },
    { setting_id: "aide_project_analyzer_persona", content: AIDE_PROJECT_ANALYZER_PERSONA_CONTENT },
    { setting_id: "TaskCockpitIntentRouterPersona", content: TASK_COCKPIT_INTENT_ROUTER_PERSONA_CONTENT },
    { setting_id: "IntentClassifierPersona", content: getCharacterPersona("IntentClassifierPersona") }
  ];
};
const BODY_ZONE_INITIAL_DATA = [
  { zone_id: "mouth", development_points: 0, last_developed_at: null },
  { zone_id: "breasts", development_points: 0, last_developed_at: null },
  { zone_id: "vagina", development_points: 0, last_developed_at: null },
  { zone_id: "clitoris", development_points: 0, last_developed_at: null },
  { zone_id: "anus", development_points: 0, last_developed_at: null },
  { zone_id: "feet", development_points: 0, last_developed_at: null },
  { zone_id: "skin", development_points: 0, last_developed_at: null },
  { zone_id: "mind", development_points: 0, last_developed_at: null }
];
const ACHIEVEMENT_DEFINITIONS = [
  { id: "first_kiss", title: "初吻的甜蜜", description: "与姐姐完成第一次亲吻互动。", type: "interaction", criteria_json: '{"type": "specific_interaction", "action_name": "kiss_mouth"}', reward_json: '{"type": "cg", "value": "cg/first_kiss.webp", "description": "解锁CG：初吻"}', icon_path: "achievements/icons/first_kiss_icon.png" },
  { id: "mind_explorer_1", title: "心智探险家 I", description: "心智开发度达到50点。", type: "development", criteria_json: '{"type": "body_zone_points", "zone": "mind", "points_required": 50}', reward_json: '{"type": "title", "value": "心智初窥者"}', icon_path: "achievements/icons/mind_explorer_1_icon.png" },
  { id: "flower_honey_ink", title: "花蜜与墨 (隐藏)", description: "在姐姐湿润度极高时，使用墨毫笔道具对秘境进行“书写”般的深入探索。", type: "hidden", criteria_json: '{"type": "specific_interaction", "action_name": "brush_vagina_high_wetness"}', reward_json: '{"type": "item", "item_name_to_unlock_or_grant": "特制花液墨水", "item_type": "special_item", "description":"解锁特殊道具：花液墨水"}', icon_path: "achievements/icons/flower_honey_ink_icon.png" }
];
const SYSTEM_RESOURCES_PATH_SEGMENT$1 = "system";
async function initializeDatabaseService$1(userDataPath, defaultSettings) {
  _DEFAULT_SETTINGS = defaultSettings;
  dbPath = path.join(userDataPath, "tiangong_pavilion.sqlite3");
  console.log(`DATABASE_CORE: Database path set to: ${dbPath}`);
  const dbDir = path.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    console.log(`DATABASE_CORE: Created database directory: ${dbDir}`);
  }
  db = new Database(dbPath, { verbose: console.log });
  console.log("DATABASE_CORE: Database connection opened.");
  db.pragma("journal_mode = WAL");
  db.pragma("foreign_keys = ON");
  console.log("DATABASE_CORE: PRAGMA journal_mode=WAL and foreign_keys=ON set.");
  const transaction = db.transaction(async () => {
    db.exec("CREATE TABLE IF NOT EXISTS db_info (version INTEGER PRIMARY KEY);");
    const row = db.prepare("SELECT version FROM db_info").get();
    let currentVersion = row ? row.version : 0;
    console.log(`DATABASE_CORE: Current DB version: ${currentVersion}, Target DB version: ${DB_VERSION}`);
    if (currentVersion < DB_VERSION) {
      console.log("DATABASE_CORE: Database version is older or new. Applying/Verifying schema...");
      ALL_SCHEMA_STATEMENTS.forEach((stmt) => {
        try {
          db.exec(stmt);
        } catch (schemaError) {
          if (schemaError.message.includes("duplicate column name") || schemaError.message.includes("already exists")) {
            console.warn(`DATABASE_CORE_WARN: Schema statement likely already applied: "${stmt.substring(0, 100)}..."`);
          } else {
            console.error(`DATABASE_CORE_ERROR: Failed to execute schema: "${stmt.substring(0, 100)}..."`, schemaError);
            throw schemaError;
          }
        }
      });
      console.log("DATABASE_CORE: All schema statements executed.");
      if (currentVersion === 0) {
        console.log("DATABASE_CORE: New database. Populating defaults...");
        const stmtSettings = db.prepare("INSERT INTO app_settings (apiKey, chatModel, embeddingModel, backupPath, defaultCover, currentTheme, training_room_audio_state) VALUES (?, ?, ?, ?, ?, ?, ?)");
        stmtSettings.run(_DEFAULT_SETTINGS.apiKey, _DEFAULT_SETTINGS.chatModel, _DEFAULT_SETTINGS.embeddingModel, _DEFAULT_SETTINGS.backupPath, _DEFAULT_SETTINGS.defaultCover, _DEFAULT_SETTINGS.currentTheme, JSON.stringify(_DEFAULT_SETTINGS.training_room_audio_state));
        const agentCoreSettings = await DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS();
        const stmtAgentCore = db.prepare("INSERT INTO agent_core_settings (setting_id, content, last_updated_at) VALUES (?, ?, ?)");
        agentCoreSettings.forEach((setting) => {
          stmtAgentCore.run(setting.setting_id, setting.content, (/* @__PURE__ */ new Date()).toISOString());
        });
        const cmsAssetDir = path.join(userDataPath, "at_cms_assets");
        const defaultCmsAssetSourceDirRoot = path.join(app.getAppPath(), app.isPackaged ? ".." : "", "dist-electron", SYSTEM_RESOURCES_PATH_SEGMENT$1, "default_at_cms_assets");
        async function copyDefaultAssets() {
          try {
            await fsPromises$1.mkdir(cmsAssetDir, { recursive: true });
            const assetTypes = ["props", "costumes", "poses"];
            for (const type2 of assetTypes) {
              const typeDirUserData = path.join(cmsAssetDir, type2);
              await fsPromises$1.mkdir(typeDirUserData, { recursive: true });
              await fsPromises$1.mkdir(path.join(typeDirUserData, "icons"), { recursive: true });
              await fsPromises$1.mkdir(path.join(typeDirUserData, "cgs"), { recursive: true });
              const defaultTypeSourceDir = path.join(defaultCmsAssetSourceDirRoot, type2);
              if (!fs.existsSync(defaultTypeSourceDir)) {
                console.warn(`DATABASE_CORE: Default AT CMS asset source directory NOT FOUND: ${defaultTypeSourceDir}. Skipping asset copy for type ${type2}.`);
                continue;
              }
              const defaultIconsSourceDir = path.join(defaultTypeSourceDir, "icons");
              const defaultCgsSourceDir = path.join(defaultTypeSourceDir, "cgs");
              if (fs.existsSync(defaultIconsSourceDir)) {
                const icons = await fsPromises$1.readdir(defaultIconsSourceDir);
                for (const iconFile of icons) {
                  await fsPromises$1.copyFile(path.join(defaultIconsSourceDir, iconFile), path.join(typeDirUserData, "icons", iconFile));
                }
              }
              if (fs.existsSync(defaultCgsSourceDir)) {
                const cgs = await fsPromises$1.readdir(defaultCgsSourceDir);
                for (const cgFile of cgs) {
                  await fsPromises$1.copyFile(path.join(defaultCgsSourceDir, cgFile), path.join(typeDirUserData, "cgs", cgFile));
                }
              }
            }
            console.log(`DATABASE_CORE: Default AT CMS assets copy attempt finished. Source root: ${defaultCmsAssetSourceDirRoot}`);
          } catch (err) {
            console.error("DATABASE_CORE_ERROR: Failed to copy default AT CMS assets:", err);
          }
        }
        await copyDefaultAssets();
        const defaultProps = await loadDefaultAtCmsProps();
        const defaultCostumes = await loadDefaultAtCmsCostumes();
        const defaultPoses = await loadDefaultAtCmsPoses();
        const now = (/* @__PURE__ */ new Date()).toISOString();
        const stmtProps = db.prepare("INSERT INTO at_cms_props (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
        defaultProps.forEach((p) => stmtProps.run(p.id, p.name, p.owner, p.icon_filename ? `props/icons/${p.icon_filename}` : null, p.cg_filename ? `props/cgs/${p.cg_filename}` : null, p.prompt_for_linluo, p.prompt_for_master, p.status_effects_json, p.development_effects_json || "[]", p.unlock_requirements_json || "[]", now, now));
        const stmtCostumes = db.prepare("INSERT INTO at_cms_costumes (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
        defaultCostumes.forEach((c) => stmtCostumes.run(c.id, c.name, c.owner, c.icon_filename ? `costumes/icons/${c.icon_filename}` : null, c.cg_filename ? `costumes/cgs/${c.cg_filename}` : null, c.prompt_for_linluo, c.prompt_for_master, c.status_effects_json, c.development_effects_json || "[]", c.unlock_requirements_json, now, now));
        const stmtPoses = db.prepare("INSERT INTO at_cms_poses (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
        defaultPoses.forEach((p_1) => stmtPoses.run(p_1.id, p_1.name, p_1.owner, p_1.icon_filename ? `poses/icons/${p_1.icon_filename}` : null, p_1.cg_filename ? `poses/cgs/${p_1.cg_filename}` : null, p_1.prompt_for_linluo, p_1.prompt_for_master, p_1.status_effects_json, p_1.development_effects_json || "[]", p_1.unlock_requirements_json, now, now));
        console.log("DATABASE_CORE: Default AT CMS Props, Costumes, Poses inserted from JSON definitions.");
        const stmtBodyDev = db.prepare("INSERT INTO linluo_body_development (zone_id, development_points, last_developed_at) VALUES (?, ?, ?)");
        BODY_ZONE_INITIAL_DATA.forEach((zone) => {
          stmtBodyDev.run(zone.zone_id, zone.development_points, zone.last_developed_at);
        });
        console.log("DATABASE_CORE: Default Body Development zones inserted.");
        const stmtAchievements = db.prepare("INSERT INTO achievements (id, title, description, type, criteria_json, reward_json, icon_path) VALUES (?, ?, ?, ?, ?, ?, ?)");
        ACHIEVEMENT_DEFINITIONS.forEach((ach) => {
          stmtAchievements.run(ach.id, ach.title, ach.description, ach.type, ach.criteria_json, ach.reward_json, ach.icon_path || null);
        });
        console.log("DATABASE_CORE: Default Achievement definitions inserted.");
        const stmtAssignments = db.prepare("INSERT OR IGNORE INTO assignments (post_id, character_id) VALUES (?, ?)");
        const defaultCharactersForAssignment = DEFAULT_CHARACTERS;
        defaultCharactersForAssignment.forEach((char) => {
          if (char.default_post_id) {
            stmtAssignments.run(char.default_post_id, char.id);
          }
        });
        console.log("DATABASE_CORE: Default assignments populated based on character default_post_id.");
      } else {
        if (currentVersion < 13) {
          const assignmentsTableInfo = db.pragma("table_info(assignments)");
          if (assignmentsTableInfo.length > 0) {
            const stmtAssignments = db.prepare("INSERT OR IGNORE INTO assignments (post_id, character_id) VALUES (?, ?)");
            const defaultCharactersForMigration = DEFAULT_CHARACTERS;
            defaultCharactersForMigration.forEach((char) => {
              if (char.default_post_id) {
                stmtAssignments.run(char.default_post_id, char.id);
              }
            });
            console.log("DATABASE_CORE: Migrated (V12->V13) - Ensured default assignments are populated.");
          }
        }
        console.log("DATABASE_CORE: Checked for migrations if any.");
      }
      const agentCoreSettingsToVerify = await DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS();
      agentCoreSettingsToVerify.forEach((personaDef) => {
        const checkPersonaExists = db.prepare("SELECT setting_id FROM agent_core_settings WHERE setting_id = ?").get(personaDef.setting_id);
        if (!checkPersonaExists) {
          db.prepare("INSERT INTO agent_core_settings (setting_id, content, last_updated_at) VALUES (?, ?, ?)").run(personaDef.setting_id, personaDef.content, (/* @__PURE__ */ new Date()).toISOString());
          console.log(`DATABASE_CORE: Ensured default setting for ${personaDef.setting_id} exists.`);
        }
      });
      db.prepare("INSERT OR REPLACE INTO db_info (version) VALUES (?)").run(DB_VERSION);
      console.log(`DATABASE_CORE: Database schema version updated to ${DB_VERSION}.`);
    } else {
      console.log(`DATABASE_CORE: Database is up to date (Version ${currentVersion}).`);
    }
  });
  await transaction();
}
function closeDatabaseConnection$1() {
  if (db) {
    db.close();
    console.log("DATABASE_CORE: Database connection closed.");
  }
}
function parseJsonArray(jsonString) {
  try {
    if (jsonString && typeof jsonString === "string") {
      const parsed = JSON.parse(jsonString);
      return Array.isArray(parsed) ? parsed : [];
    }
    return [];
  } catch (e) {
    console.warn("DATABASE_CORE: parseJsonArray failed for string:", jsonString, e);
    return [];
  }
}
function mapChatMessage(row) {
  return {
    ...row,
    isEditing: Boolean(row.isEditing),
    isStarred: Boolean(row.isStarred),
    isPinned: Boolean(row.isPinned)
  };
}
function mapNoteItem(row) {
  return {
    ...row,
    importance: row.importance || "medium",
    isEditing: false
  };
}
function mapDevelopmentTask(row) {
  return {
    ...row,
    context_files: row.context_files ? JSON.parse(row.context_files) : []
  };
}
console.log("DATABASE_CORE_JS: File execution finished. Exports configured.");
console.log("PROJECT_DB_SERVICE_JS: File execution started.");
function getAllProjects$1() {
  if (!db) {
    console.error("PROJECT_DB_ERROR: getAllProjects - db not available.");
    return [];
  }
  try {
    const projectRows = db.prepare("SELECT * FROM projects ORDER BY createdAt DESC").all();
    const projects = projectRows.map((p) => {
      const messages = db.prepare("SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp ASC").all(p.id);
      const inspirationNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'inspiration' ORDER BY createdAt DESC").all(p.id);
      const bugMemoNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'bugs' ORDER BY createdAt DESC").all(p.id);
      const quickCommandsNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'commands' ORDER BY createdAt DESC").all(p.id);
      const mindNodes = db.prepare("SELECT * FROM mindworkshop_nodes WHERE projectId = ?").all(p.id);
      const mindConnections = db.prepare("SELECT * FROM mindworkshop_connections WHERE projectId = ?").all(p.id);
      const projectKnowledgeTomesRaw = db.prepare("SELECT * FROM project_knowledge_tomes WHERE projectId = ? ORDER BY lastModifiedAt DESC").all(p.id);
      const projectKnowledgeTomes = projectKnowledgeTomesRaw.map((t) => ({ ...t, tags: parseJsonArray(t.tags) }));
      const categoryRows = db.prepare("SELECT categoryName FROM project_knowledge_categories WHERE projectId = ? ORDER BY categoryName ASC").all(p.id);
      const projectKnowledgeCategories = categoryRows.map((r) => r.categoryName);
      const developmentTasksRaw = db.prepare("SELECT * FROM development_tasks WHERE projectId = ? ORDER BY createdAt DESC").all(p.id);
      const developmentTasks = developmentTasksRaw.map(mapDevelopmentTask);
      return {
        ...p,
        sourceCodePath: p.sourceCodePath || null,
        discussionMessages: messages.map(mapChatMessage),
        inspirationNotes: inspirationNotes.map(mapNoteItem),
        bugMemoNotes: bugMemoNotes.map(mapNoteItem),
        quickCommandsNotes: quickCommandsNotes.map(mapNoteItem),
        mindNodes,
        mindConnections,
        projectKnowledgeTomes,
        projectKnowledgeCategories,
        developmentTasks,
        isDeleted: p.isDeleted === 1
        // Ensure boolean conversion
      };
    });
    console.log(`PROJECT_DB: Retrieved ${projects.length} projects.`);
    return projects;
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error getting all projects:", error);
    return [];
  }
}
function getProjectById$1(projectId) {
  if (!db) {
    console.error(`PROJECT_DB_ERROR: getProjectById(${projectId}) - db not available.`);
    return null;
  }
  try {
    const p = db.prepare("SELECT * FROM projects WHERE id = ?").get(projectId);
    if (!p) {
      console.log(`PROJECT_DB: Project with ID ${projectId} not found.`);
      return null;
    }
    const messages = db.prepare("SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp ASC").all(p.id);
    const inspirationNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'inspiration' ORDER BY createdAt DESC").all(p.id);
    const bugMemoNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'bugs' ORDER BY createdAt DESC").all(p.id);
    const quickCommandsNotes = db.prepare("SELECT * FROM wisdom_pouch_notes WHERE projectId = ? AND pouchType = 'commands' ORDER BY createdAt DESC").all(p.id);
    const mindNodes = db.prepare("SELECT * FROM mindworkshop_nodes WHERE projectId = ?").all(p.id);
    const mindConnections = db.prepare("SELECT * FROM mindworkshop_connections WHERE projectId = ?").all(p.id);
    const projectKnowledgeTomesRaw = db.prepare("SELECT * FROM project_knowledge_tomes WHERE projectId = ? ORDER BY lastModifiedAt DESC").all(p.id);
    const projectKnowledgeTomes = projectKnowledgeTomesRaw.map((t) => ({ ...t, tags: parseJsonArray(t.tags) }));
    const categoryRows = db.prepare("SELECT categoryName FROM project_knowledge_categories WHERE projectId = ? ORDER BY categoryName ASC").all(p.id);
    const projectKnowledgeCategories = categoryRows.map((r) => r.categoryName);
    const developmentTasksRaw = db.prepare("SELECT * FROM development_tasks WHERE projectId = ? ORDER BY createdAt DESC").all(p.id);
    const developmentTasks = developmentTasksRaw.map(mapDevelopmentTask);
    console.log(`PROJECT_DB: Retrieved project by ID ${projectId}.`);
    return {
      ...p,
      sourceCodePath: p.sourceCodePath || null,
      discussionMessages: messages.map(mapChatMessage),
      inspirationNotes: inspirationNotes.map(mapNoteItem),
      bugMemoNotes: bugMemoNotes.map(mapNoteItem),
      quickCommandsNotes: quickCommandsNotes.map(mapNoteItem),
      mindNodes,
      mindConnections,
      projectKnowledgeTomes,
      projectKnowledgeCategories,
      developmentTasks,
      isDeleted: p.isDeleted === 1
      // Ensure boolean conversion
    };
  } catch (error) {
    console.error(`PROJECT_DB_ERROR: Error getting project by ID ${projectId}:`, error);
    return null;
  }
}
function addProject$1(projectData) {
  console.log("[PROJECT_DB_SERVICE][addProject] Attempting to add project with data:", JSON.stringify(projectData, null, 2));
  if (!db) {
    const errorMsg = "PROJECT_DB_ERROR: addProject - Database not initialized.";
    console.error(errorMsg);
    throw new Error(errorMsg);
  }
  try {
    const transaction = db.transaction((data) => {
      console.log("[PROJECT_DB_SERVICE][addProject] Inside transaction. Data to insert:", JSON.stringify(data, null, 2));
      const stmt = db.prepare("INSERT INTO projects (id, name, coverImageUrl, createdAt, sourceCodePath, lastModifiedAt, isDeleted) VALUES (?, ?, ?, ?, ?, ?, ?)");
      stmt.run(
        data.id,
        data.name,
        data.coverImageUrl || null,
        data.createdAt,
        data.sourceCodePath || null,
        data.lastModifiedAt,
        data.isDeleted ? 1 : 0
      );
      console.log(`[PROJECT_DB_SERVICE][addProject] Project insert successful for ID: ${data.id}`);
      const fullProjectObject = {
        ...data,
        coverImageUrl: data.coverImageUrl || null,
        sourceCodePath: data.sourceCodePath || null,
        isDeleted: !!data.isDeleted,
        discussionMessages: [],
        inspirationNotes: [],
        bugMemoNotes: [],
        quickCommandsNotes: [],
        mindNodes: [],
        mindConnections: [],
        projectKnowledgeTomes: [],
        projectKnowledgeCategories: [],
        developmentTasks: []
      };
      console.log("[PROJECT_DB_SERVICE][addProject] Returning full project object from transaction:", JSON.stringify(fullProjectObject, null, 2));
      return fullProjectObject;
    });
    const result = transaction(projectData);
    console.log(`[PROJECT_DB_SERVICE][addProject] Project added and transaction committed. ID: ${result.id}.`);
    return result;
  } catch (dbError) {
    console.error("[PROJECT_DB_SERVICE][addProject] Database error during addProject transaction:", dbError.message, dbError.stack);
    throw dbError;
  }
}
function updateProject$1(project) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateProject - Database not initialized.");
    throw new Error("Database not initialized for updateProject.");
  }
  const transaction = db.transaction((proj) => {
    const projectStmt = db.prepare("UPDATE projects SET name = ?, coverImageUrl = ?, sourceCodePath = ?, lastModifiedAt = ?, isDeleted = ? WHERE id = ?");
    projectStmt.run(proj.name, proj.coverImageUrl, proj.sourceCodePath || null, proj.lastModifiedAt, proj.isDeleted ? 1 : 0, proj.id);
    db.prepare("DELETE FROM chat_messages WHERE projectId = ?").run(proj.id);
    const chatMsgStmt = db.prepare("INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    for (const msg of proj.discussionMessages || []) {
      chatMsgStmt.run(msg.id, proj.id, msg.sender, msg.senderName, msg.text, msg.timestamp, msg.isEditing ? 1 : 0, msg.isStarred ? 1 : 0, msg.isPinned ? 1 : 0, msg.theme, msg.replyToMessageId, msg.triggeringUserMessageId);
    }
    db.prepare("DELETE FROM wisdom_pouch_notes WHERE projectId = ?").run(proj.id);
    const noteStmt = db.prepare("INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)");
    for (const note of proj.inspirationNotes || []) {
      noteStmt.run(note.id, proj.id, "inspiration", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of proj.bugMemoNotes || []) {
      noteStmt.run(note.id, proj.id, "bugs", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of proj.quickCommandsNotes || []) {
      noteStmt.run(note.id, proj.id, "commands", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    db.prepare("DELETE FROM mindworkshop_nodes WHERE projectId = ?").run(proj.id);
    db.prepare("DELETE FROM mindworkshop_connections WHERE projectId = ?").run(proj.id);
    const mindNodeStmt = db.prepare("INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)");
    for (const node of proj.mindNodes || []) {
      mindNodeStmt.run(node.id, proj.id, node.text, node.x, node.y, node.width, node.height);
    }
    const mindConnStmt = db.prepare("INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)");
    for (const conn of proj.mindConnections || []) {
      mindConnStmt.run(conn.id, proj.id, conn.fromNodeId, conn.toNodeId);
    }
    db.prepare("DELETE FROM project_knowledge_tomes WHERE projectId = ?").run(proj.id);
    const pkTomeStmt = db.prepare("INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    for (const tome of proj.projectKnowledgeTomes || []) {
      pkTomeStmt.run(tome.id, proj.id, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.createdAt, tome.lastModifiedAt);
    }
    db.prepare("DELETE FROM project_knowledge_categories WHERE projectId = ?").run(proj.id);
    const pkCatStmt = db.prepare("INSERT INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)");
    for (const category of proj.projectKnowledgeCategories || []) {
      pkCatStmt.run(proj.id, category);
    }
    db.prepare("DELETE FROM development_tasks WHERE projectId = ?").run(proj.id);
    const devTaskStmt = db.prepare("INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)");
    for (const task of proj.developmentTasks || []) {
      devTaskStmt.run(task.id, proj.id, task.title, task.status, task.createdAt, JSON.stringify(task.context_files || []), task.generated_code);
    }
    return { success: true, project: { ...proj, sourceCodePath: proj.sourceCodePath || null, isDeleted: !!proj.isDeleted } };
  });
  const result = transaction(project);
  console.log(`PROJECT_DB: Updated project with ID ${project.id}.`);
  return result;
}
function deleteProject$1(projectId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: deleteProject - Database not initialized.");
    return { success: false, error: "Database not initialized." };
  }
  try {
    const stmt = db.prepare("UPDATE projects SET isDeleted = 1, lastModifiedAt = ? WHERE id = ?");
    const info = stmt.run((/* @__PURE__ */ new Date()).toISOString(), projectId);
    if (info.changes > 0) {
      console.log(`PROJECT_DB: Marked project ${projectId} as deleted.`);
      return { success: true };
    } else {
      console.warn(`PROJECT_DB_WARN: Project ${projectId} not found for deletion marking.`);
      return { success: false, error: "Project not found." };
    }
  } catch (error) {
    console.error(`PROJECT_DB_ERROR: Error marking project ${projectId} as deleted:`, error);
    return { success: false, error: error.message };
  }
}
function duplicateProject$1(sourceProjectId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: duplicateProject - Database not initialized.");
    return null;
  }
  const originalProject = getProjectById$1(sourceProjectId);
  if (!originalProject) {
    console.warn(`PROJECT_DB_WARN: Project ${sourceProjectId} not found for duplication.`);
    return null;
  }
  const newProjectId = crypto.randomUUID();
  const newProjectName = `${originalProject.name} (副本)`;
  const now = (/* @__PURE__ */ new Date()).toISOString();
  const newProjectCoreData = {
    id: newProjectId,
    name: newProjectName,
    coverImageUrl: originalProject.coverImageUrl,
    createdAt: now,
    lastModifiedAt: now,
    sourceCodePath: originalProject.sourceCodePath,
    isDeleted: 0
  };
  let duplicatedFullProject;
  const duplicationTransaction = db.transaction(() => {
    const projectInsertStmt = db.prepare("INSERT INTO projects (id, name, coverImageUrl, createdAt, lastModifiedAt, sourceCodePath, isDeleted) VALUES (?, ?, ?, ?, ?, ?, ?)");
    projectInsertStmt.run(newProjectCoreData.id, newProjectCoreData.name, newProjectCoreData.coverImageUrl, newProjectCoreData.createdAt, newProjectCoreData.lastModifiedAt, newProjectCoreData.sourceCodePath, newProjectCoreData.isDeleted);
    if (originalProject.discussionMessages && originalProject.discussionMessages.length > 0) {
      const chatMsgStmt = db.prepare("INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
      for (const msg of originalProject.discussionMessages) {
        chatMsgStmt.run(crypto.randomUUID(), newProjectId, msg.sender, msg.senderName, msg.text, msg.timestamp, msg.isEditing ? 1 : 0, msg.isStarred ? 1 : 0, msg.isPinned ? 1 : 0, msg.theme, msg.replyToMessageId, msg.triggeringUserMessageId);
      }
    }
    const noteStmt = db.prepare("INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)");
    for (const note of originalProject.inspirationNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, "inspiration", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of originalProject.bugMemoNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, "bugs", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    for (const note of originalProject.quickCommandsNotes || []) {
      noteStmt.run(crypto.randomUUID(), newProjectId, "commands", note.text, note.createdAt, note.lastModifiedAt, note.importance);
    }
    const mindNodeIdMap = /* @__PURE__ */ new Map();
    if (originalProject.mindNodes && originalProject.mindNodes.length > 0) {
      const mindNodeStmt = db.prepare("INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)");
      for (const node of originalProject.mindNodes) {
        const newNodeId = crypto.randomUUID();
        mindNodeIdMap.set(node.id, newNodeId);
        mindNodeStmt.run(newNodeId, newProjectId, node.text, node.x, node.y, node.width, node.height);
      }
    }
    if (originalProject.mindConnections && originalProject.mindConnections.length > 0) {
      const mindConnStmt = db.prepare("INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)");
      for (const conn of originalProject.mindConnections) {
        const newFromNodeId = mindNodeIdMap.get(conn.fromNodeId);
        const newToNodeId = mindNodeIdMap.get(conn.toNodeId);
        if (newFromNodeId && newToNodeId) {
          mindConnStmt.run(crypto.randomUUID(), newProjectId, newFromNodeId, newToNodeId);
        } else {
          console.warn(`PROJECT_DB_DUPLICATE: Skipping mind connection ${conn.id} due to missing node mapping.`);
        }
      }
    }
    if (originalProject.projectKnowledgeTomes && originalProject.projectKnowledgeTomes.length > 0) {
      const pkTomeStmt = db.prepare("INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
      for (const tome of originalProject.projectKnowledgeTomes) {
        pkTomeStmt.run(crypto.randomUUID(), newProjectId, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), now, now);
      }
    }
    if (originalProject.projectKnowledgeCategories && originalProject.projectKnowledgeCategories.length > 0) {
      const pkCatStmt = db.prepare("INSERT INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)");
      for (const category of originalProject.projectKnowledgeCategories) {
        pkCatStmt.run(newProjectId, category);
      }
    }
    if (originalProject.developmentTasks && originalProject.developmentTasks.length > 0) {
      const devTaskStmt = db.prepare("INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)");
      for (const task of originalProject.developmentTasks) {
        devTaskStmt.run(crypto.randomUUID(), newProjectId, task.title, task.status, now, JSON.stringify(task.context_files || []), task.generated_code);
      }
    }
    duplicatedFullProject = getProjectById$1(newProjectId);
  });
  try {
    duplicationTransaction();
    console.log(`PROJECT_DB: Duplicated project ${sourceProjectId} to ${newProjectId} successfully.`);
    return duplicatedFullProject;
  } catch (error) {
    console.error(`PROJECT_DB_ERROR: Error in duplicationTransaction for project ${sourceProjectId}:`, error);
    return null;
  }
}
function addNoteToPouch$1(projectId, pouchType, noteData) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: addNoteToPouch - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO wisdom_pouch_notes (id, projectId, pouchType, text, createdAt, lastModifiedAt, importance) VALUES (?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(noteData.id, projectId, pouchType, noteData.text, noteData.createdAt, noteData.lastModifiedAt, noteData.importance);
    console.log(`PROJECT_DB: Added note ${noteData.id} to pouch ${pouchType} for project ${projectId}.`);
    return { ...noteData, isEditing: false };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error adding note to pouch:", error);
    return null;
  }
}
function updateNoteInPouch$1(pouchType, noteData) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateNoteInPouch - db not available.");
    return null;
  }
  const stmt = db.prepare("UPDATE wisdom_pouch_notes SET text = ?, lastModifiedAt = ?, importance = ? WHERE id = ? AND pouchType = ?");
  try {
    stmt.run(noteData.text, noteData.lastModifiedAt, noteData.importance, noteData.id, pouchType);
    console.log(`PROJECT_DB: Updated note ${noteData.id} in pouch ${pouchType}.`);
    return { ...noteData, isEditing: false };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error updating note in pouch:", error);
    return null;
  }
}
function deleteNoteFromPouch$1(pouchType, noteId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: deleteNoteFromPouch - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM wisdom_pouch_notes WHERE id = ? AND pouchType = ?");
  try {
    stmt.run(noteId, pouchType);
    console.log(`PROJECT_DB: Deleted note ${noteId} from pouch ${pouchType}.`);
    return { success: true, id: noteId };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error deleting note from pouch:", error);
    return { success: false, error: error.message };
  }
}
function updateProjectMindMap$1(projectId, nodes, connections) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateProjectMindMap - Database not initialized.");
    throw new Error("Database not initialized for updateProjectMindMap.");
  }
  const transaction = db.transaction((pid, nodeList, connList) => {
    db.prepare("DELETE FROM mindworkshop_nodes WHERE projectId = ?").run(pid);
    db.prepare("DELETE FROM mindworkshop_connections WHERE projectId = ?").run(pid);
    const nodeStmt = db.prepare("INSERT INTO mindworkshop_nodes (id, projectId, text, x, y, width, height) VALUES (?, ?, ?, ?, ?, ?, ?)");
    for (const node of nodeList) {
      nodeStmt.run(node.id, pid, node.text, node.x, node.y, node.width, node.height);
    }
    const connStmt = db.prepare("INSERT INTO mindworkshop_connections (id, projectId, fromNodeId, toNodeId) VALUES (?, ?, ?, ?)");
    for (const conn of connList) {
      connStmt.run(conn.id, pid, conn.fromNodeId, conn.toNodeId);
    }
    return { success: true };
  });
  const result = transaction(projectId, nodes, connections);
  console.log(`PROJECT_DB: Updated mind map for project ${projectId}.`);
  return result;
}
function addProjectKnowledgeTome$1(projectId, tome) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: addProjectKnowledgeTome - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO project_knowledge_tomes (id, projectId, title, content, projectCategory, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(tome.id, projectId, tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.createdAt, tome.lastModifiedAt);
    console.log(`PROJECT_DB: Added project knowledge tome ${tome.id} for project ${projectId}.`);
    return tome;
  } catch (e) {
    console.error("PROJECT_DB_ERROR: adding project tome", e);
    return null;
  }
}
function updateProjectKnowledgeTome$1(projectId, tome) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateProjectKnowledgeTome - db not available.");
    return null;
  }
  const stmt = db.prepare("UPDATE project_knowledge_tomes SET title = ?, content = ?, projectCategory = ?, tags = ?, lastModifiedAt = ? WHERE id = ? AND projectId = ?");
  try {
    stmt.run(tome.title, tome.content, tome.projectCategory, JSON.stringify(tome.tags || []), tome.lastModifiedAt, tome.id, projectId);
    console.log(`PROJECT_DB: Updated project knowledge tome ${tome.id} for project ${projectId}.`);
    return tome;
  } catch (e) {
    console.error("PROJECT_DB_ERROR: updating project tome", e);
    return null;
  }
}
function deleteProjectKnowledgeTome$1(projectId, tomeId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: deleteProjectKnowledgeTome - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM project_knowledge_tomes WHERE id = ? AND projectId = ?");
  try {
    stmt.run(tomeId, projectId);
    console.log(`PROJECT_DB: Deleted project knowledge tome ${tomeId} for project ${projectId}.`);
    return { success: true };
  } catch (e) {
    console.error("PROJECT_DB_ERROR: deleting project tome", e);
    return { success: false, error: e.message };
  }
}
function addProjectKnowledgeCategory$1(projectId, categoryName) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: addProjectKnowledgeCategory - db not available.");
    return void 0;
  }
  const stmt = db.prepare("INSERT OR IGNORE INTO project_knowledge_categories (projectId, categoryName) VALUES (?, ?)");
  try {
    const info = stmt.run(projectId, categoryName);
    if (info.changes > 0) console.log(`PROJECT_DB: Added category '${categoryName}' for project ${projectId}.`);
    return info.changes > 0 ? categoryName : void 0;
  } catch (e) {
    console.error("PROJECT_DB_ERROR: adding project category", e);
    return void 0;
  }
}
function removeProjectKnowledgeCategory$1(projectId, categoryName) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: removeProjectKnowledgeCategory - Database not initialized.");
    throw new Error("Database not initialized for removeProjectKnowledgeCategory.");
  }
  const transaction = db.transaction((pid, catName) => {
    db.prepare("UPDATE project_knowledge_tomes SET projectCategory = ? WHERE projectId = ? AND projectCategory = ?").run("未分类", pid, catName);
    db.prepare("DELETE FROM project_knowledge_categories WHERE projectId = ? AND categoryName = ?").run(pid, catName);
    return { success: true };
  });
  try {
    const result = transaction(projectId, categoryName);
    console.log(`PROJECT_DB: Removed category '${categoryName}' for project ${projectId}. Tomes reassigned to '未分类'.`);
    return result;
  } catch (e) {
    console.error(`PROJECT_DB_ERROR: removing project category ${categoryName}`, e);
    return { success: false, error: e.message };
  }
}
function getAllDevelopmentTasks$1() {
  if (!db) {
    console.error("PROJECT_DB_ERROR: getAllDevelopmentTasks - db not available.");
    return [];
  }
  try {
    const tasksRaw = db.prepare("SELECT * FROM development_tasks ORDER BY createdAt DESC").all();
    const tasks = tasksRaw.map(mapDevelopmentTask);
    console.log(`PROJECT_DB: Retrieved ${tasks.length} development tasks.`);
    return tasks;
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error getting all development tasks:", error);
    return [];
  }
}
function addDevelopmentTask$1(projectId, title) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: addDevelopmentTask - db not available.");
    return null;
  }
  const newTask = {
    id: crypto.randomUUID(),
    projectId,
    title,
    // This existing function only takes title
    status: "todo",
    createdAt: (/* @__PURE__ */ new Date()).toISOString(),
    context_files: [],
    generated_code: null
  };
  const stmt = db.prepare("INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(newTask.id, newTask.projectId, newTask.title, newTask.status, newTask.createdAt, JSON.stringify(newTask.context_files), newTask.generated_code);
    console.log(`PROJECT_DB: Added development task ${newTask.id} for project ${projectId}.`);
    return newTask;
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error adding development task:", error);
    return null;
  }
}
function createDevelopmentTaskFromChat$1(projectId, title, description) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: createDevelopmentTaskFromChat - db not available.");
    return null;
  }
  let combinedTitle = title;
  if (description && description.trim() !== "") {
    combinedTitle += `

--- 描述 ---
${description.trim()}`;
  }
  const newTask = {
    id: crypto.randomUUID(),
    projectId,
    title: combinedTitle,
    status: "todo",
    createdAt: (/* @__PURE__ */ new Date()).toISOString(),
    context_files: [],
    generated_code: null
  };
  const stmt = db.prepare("INSERT INTO development_tasks (id, projectId, title, status, createdAt, context_files, generated_code) VALUES (?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(newTask.id, newTask.projectId, newTask.title, newTask.status, newTask.createdAt, JSON.stringify(newTask.context_files), newTask.generated_code);
    console.log(`PROJECT_DB: Created development task from chat ${newTask.id} for project ${projectId}.`);
    return newTask;
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error creating development task from chat:", error);
    return null;
  }
}
function deleteDevelopmentTask$1(taskId) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: deleteDevelopmentTask - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM development_tasks WHERE id = ?");
  try {
    const info = stmt.run(taskId);
    if (info.changes > 0) {
      console.log(`PROJECT_DB: Deleted development task ${taskId}.`);
      return { success: true };
    }
    console.log(`PROJECT_DB: Development task ${taskId} not found for deletion.`);
    return { success: false, error: "Task not found." };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error deleting development task:", error);
    return { success: false, error: error.message };
  }
}
function updateDevelopmentTaskContextFiles$1(taskId, contextFiles) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateDevelopmentTaskContextFiles - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("UPDATE development_tasks SET context_files = ? WHERE id = ?");
  try {
    const info = stmt.run(JSON.stringify(contextFiles || []), taskId);
    if (info.changes > 0) {
      console.log(`PROJECT_DB: Updated context_files for development task ${taskId}.`);
      return { success: true };
    }
    const taskExists = db.prepare("SELECT id FROM development_tasks WHERE id = ?").get(taskId);
    if (!taskExists) {
      console.log(`PROJECT_DB: Development task ${taskId} not found for context_files update.`);
      return { success: false, error: "Task not found." };
    }
    console.log(`PROJECT_DB: No change in context_files for development task ${taskId} (data might be identical).`);
    return { success: true };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error updating context_files for development task:", error);
    return { success: false, error: error.message };
  }
}
function updateDevelopmentTaskGeneratedCode$1(taskId, generatedCode) {
  if (!db) {
    console.error("PROJECT_DB_ERROR: updateDevelopmentTaskGeneratedCode - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("UPDATE development_tasks SET generated_code = ? WHERE id = ?");
  try {
    const info = stmt.run(generatedCode, taskId);
    if (info.changes > 0) {
      console.log(`PROJECT_DB: Updated generated_code for development task ${taskId}.`);
      return { success: true };
    }
    const taskExists = db.prepare("SELECT id FROM development_tasks WHERE id = ?").get(taskId);
    if (!taskExists) {
      console.log(`PROJECT_DB: Development task ${taskId} not found for generated_code update.`);
      return { success: false, error: "Task not found." };
    }
    console.log(`PROJECT_DB: No change in generated_code for development task ${taskId} (data might be identical).`);
    return { success: true };
  } catch (error) {
    console.error("PROJECT_DB_ERROR: Error updating generated_code for development task:", error);
    return { success: false, error: error.message };
  }
}
console.log("PROJECT_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("SETTINGS_DB_SERVICE_JS: File execution started.");
function getSettings$1() {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: getSettings - db not available.");
    const defaultSettings = _DEFAULT_SETTINGS || {
      apiKey: "",
      chatModel: "gemini-2.5-flash-preview-04-17",
      embeddingModel: "text-embedding-004",
      backupPath: "",
      defaultCover: "",
      absolute_territory_password: null,
      user_avatar_path: null,
      linluo_avatar_path: null,
      xiaolan_avatar_path: null,
      training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }
    };
    return { ...defaultSettings };
  }
  try {
    const settingsRow = db.prepare("SELECT apiKey, chatModel, embeddingModel, backupPath, defaultCover, absolute_territory_password, user_avatar_path, linluo_avatar_path, xiaolan_avatar_path, training_room_audio_state FROM app_settings WHERE id = 1").get();
    let audioState;
    const defaultAudioStateFromCore = (_DEFAULT_SETTINGS == null ? void 0 : _DEFAULT_SETTINGS.training_room_audio_state) || { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false };
    if ((settingsRow == null ? void 0 : settingsRow.training_room_audio_state) && typeof settingsRow.training_room_audio_state === "string") {
      try {
        audioState = JSON.parse(settingsRow.training_room_audio_state);
        audioState = { ...defaultAudioStateFromCore, ...audioState };
      } catch (e) {
        console.warn("SETTINGS_DB_WARN: Failed to parse training_room_audio_state from DB, using default.", e.message);
        audioState = defaultAudioStateFromCore;
      }
    } else {
      audioState = defaultAudioStateFromCore;
    }
    return {
      apiKey: (settingsRow == null ? void 0 : settingsRow.apiKey) ?? _DEFAULT_SETTINGS.apiKey,
      chatModel: (settingsRow == null ? void 0 : settingsRow.chatModel) ?? _DEFAULT_SETTINGS.chatModel,
      embeddingModel: (settingsRow == null ? void 0 : settingsRow.embeddingModel) ?? _DEFAULT_SETTINGS.embeddingModel,
      backupPath: (settingsRow == null ? void 0 : settingsRow.backupPath) ?? _DEFAULT_SETTINGS.backupPath,
      defaultCover: (settingsRow == null ? void 0 : settingsRow.defaultCover) ?? _DEFAULT_SETTINGS.defaultCover,
      absolute_territory_password: settingsRow == null ? void 0 : settingsRow.absolute_territory_password,
      user_avatar_path: (settingsRow == null ? void 0 : settingsRow.user_avatar_path) ?? _DEFAULT_SETTINGS.user_avatar_path,
      linluo_avatar_path: (settingsRow == null ? void 0 : settingsRow.linluo_avatar_path) ?? _DEFAULT_SETTINGS.linluo_avatar_path,
      xiaolan_avatar_path: (settingsRow == null ? void 0 : settingsRow.xiaolan_avatar_path) ?? _DEFAULT_SETTINGS.xiaolan_avatar_path,
      training_room_audio_state: audioState
    };
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error getting settings:", error);
    const defaultSettingsFallback = _DEFAULT_SETTINGS || {
      apiKey: "",
      chatModel: "gemini-2.5-flash-preview-04-17",
      embeddingModel: "text-embedding-004",
      backupPath: "",
      defaultCover: "",
      absolute_territory_password: null,
      user_avatar_path: null,
      linluo_avatar_path: null,
      xiaolan_avatar_path: null,
      training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }
    };
    return { ...defaultSettingsFallback };
  }
}
function saveSettings$1(settings) {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: saveSettings - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare(`
    INSERT INTO app_settings (id, apiKey, chatModel, embeddingModel, backupPath, defaultCover, absolute_territory_password, user_avatar_path, linluo_avatar_path, xiaolan_avatar_path, training_room_audio_state)
    VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON CONFLICT(id) DO UPDATE SET
      apiKey = excluded.apiKey,
      chatModel = excluded.chatModel,
      embeddingModel = excluded.embeddingModel,
      backupPath = excluded.backupPath,
      defaultCover = excluded.defaultCover,
      absolute_territory_password = excluded.absolute_territory_password,
      user_avatar_path = excluded.user_avatar_path,
      linluo_avatar_path = excluded.linluo_avatar_path,
      xiaolan_avatar_path = excluded.xiaolan_avatar_path,
      training_room_audio_state = excluded.training_room_audio_state;
  `);
  try {
    const defaultAudioStateFromCore = (_DEFAULT_SETTINGS == null ? void 0 : _DEFAULT_SETTINGS.training_room_audio_state) || { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false };
    const audioStateToSave = settings.training_room_audio_state ? JSON.stringify({ ...defaultAudioStateFromCore, ...settings.training_room_audio_state }) : JSON.stringify(defaultAudioStateFromCore);
    stmt.run(
      settings.apiKey,
      settings.chatModel,
      settings.embeddingModel,
      settings.backupPath,
      settings.defaultCover,
      settings.absolute_territory_password,
      settings.user_avatar_path,
      settings.linluo_avatar_path,
      settings.xiaolan_avatar_path,
      audioStateToSave
    );
    console.log(`SETTINGS_DB: Saved settings.`);
    return { success: true };
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error saving settings:", error);
    return { success: false, error: error.message };
  }
}
function getAgentCoreSetting$1(settingId) {
  if (!db) {
    console.error(`SETTINGS_DB_ERROR: getAgentCoreSetting(${settingId}) - db not available.`);
    return null;
  }
  try {
    const row = db.prepare("SELECT setting_id, content, last_updated_at FROM agent_core_settings WHERE setting_id = ?").get(settingId);
    console.log(`SETTINGS_DB: Retrieved agent core setting for ID: ${settingId}. Has content: ${!!(row == null ? void 0 : row.content)}`);
    return row || null;
  } catch (error) {
    console.error(`SETTINGS_DB_ERROR: Error getting agent core setting for ID ${settingId}:`, error);
    return null;
  }
}
function getAllAgentCoreSettings$1() {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: getAllAgentCoreSettings - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT setting_id, content, last_updated_at FROM agent_core_settings").all();
    console.log(`SETTINGS_DB: Retrieved ${rows.length} agent core settings.`);
    return rows;
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error getting all agent core settings:", error);
    return [];
  }
}
function saveAgentCoreSetting$1(settingId, content) {
  if (!db) {
    console.error(`SETTINGS_DB_ERROR: saveAgentCoreSetting(${settingId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare(`
    INSERT INTO agent_core_settings (setting_id, content, last_updated_at)
    VALUES (?, ?, ?)
    ON CONFLICT(setting_id) DO UPDATE SET
      content = excluded.content,
      last_updated_at = excluded.last_updated_at;
  `);
  try {
    stmt.run(settingId, content, (/* @__PURE__ */ new Date()).toISOString());
    console.log(`SETTINGS_DB: Saved agent core setting for ID: ${settingId}.`);
    return { success: true };
  } catch (error) {
    console.error(`SETTINGS_DB_ERROR: Error saving agent core setting for ID ${settingId}:`, error);
    return { success: false, error: error.message };
  }
}
function getAbsoluteTerritoryPassword$1() {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: getAbsoluteTerritoryPassword - db not available.");
    return null;
  }
  try {
    const row = db.prepare("SELECT absolute_territory_password FROM app_settings WHERE id = 1").get();
    return row == null ? void 0 : row.absolute_territory_password;
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error getting Absolute Territory password:", error);
    return null;
  }
}
function setAbsoluteTerritoryPassword$1(password) {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: setAbsoluteTerritoryPassword - db not available.");
    return { success: false, error: "Database not available." };
  }
  try {
    const stmt = db.prepare("UPDATE app_settings SET absolute_territory_password = ? WHERE id = 1");
    const result = stmt.run(password);
    if (result.changes > 0) {
      console.log(`SETTINGS_DB: Absolute Territory password ${password === null ? "cleared" : "updated"}.`);
      return { success: true };
    } else {
      console.warn("SETTINGS_DB_WARN: setAbsoluteTerritoryPassword - No rows updated. Settings row might be missing.");
      const currentSettings = getSettings$1();
      const newSettings = { ...currentSettings, absolute_territory_password: password };
      return saveSettings$1(newSettings);
    }
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error setting Absolute Territory password:", error);
    return { success: false, error: error.message };
  }
}
function verifyAbsoluteTerritoryPassword$1(passwordToVerify) {
  if (!db) {
    console.error("SETTINGS_DB_ERROR: verifyAbsoluteTerritoryPassword - db not available.");
    return { isValid: false, isFirstTime: true };
  }
  try {
    const storedPassword = getAbsoluteTerritoryPassword$1();
    if (storedPassword === null || storedPassword === void 0 || storedPassword === "") {
      return { isValid: true, isFirstTime: true };
    }
    return { isValid: storedPassword === passwordToVerify, isFirstTime: false };
  } catch (error) {
    console.error("SETTINGS_DB_ERROR: Error verifying Absolute Territory password:", error);
    return { isValid: false, isFirstTime: true };
  }
}
console.log("SETTINGS_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("AI_KERNEL_SERVICE_JS: File execution started.");
let sharedAIAgent = null;
let currentApiKey = null;
const safetySettings = [
  { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
  { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
  { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
  { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE }
];
function initializeSharedAIAgent(apiKey) {
  if (!apiKey) {
    console.warn("AI_KERNEL_SERVICE: Attempted to initialize with no API key. Shared agent will be null.");
    sharedAIAgent = null;
    currentApiKey = null;
    return false;
  }
  if (apiKey === currentApiKey && sharedAIAgent) {
    return true;
  }
  try {
    sharedAIAgent = new GoogleGenAI({ apiKey });
    currentApiKey = apiKey;
    console.log("AI_KERNEL_SERVICE: Shared AI Agent initialized/updated successfully.");
    return true;
  } catch (error) {
    console.error("AI_KERNEL_SERVICE_ERROR: Failed to initialize GoogleGenAI with API key:", error);
    sharedAIAgent = null;
    currentApiKey = null;
    return false;
  }
}
function getAIAgent(apiKeyFromArgs) {
  if (apiKeyFromArgs) {
    try {
      return new GoogleGenAI({ apiKey: apiKeyFromArgs });
    } catch (error) {
      console.error("AI_KERNEL_SERVICE_ERROR: Failed to initialize temporary GoogleGenAI with provided API key:", error);
      return null;
    }
  }
  return sharedAIAgent;
}
function isAgentInitialized() {
  return !!sharedAIAgent;
}
function getCurrentApiKey() {
  return currentApiKey;
}
async function generateContentInternal(contents, config, modelName, apiKey) {
  const aiForCall = getAIAgent(apiKey);
  const modelToUse = modelName || GEMINI_TEXT_MODEL;
  if (!aiForCall) {
    return "AI service not initialized or API key missing.";
  }
  if (!apiKey && !currentApiKey && !process.env.API_KEY) {
    return "API Key not provided and not set in environment.";
  }
  try {
    const response = await aiForCall.models.generateContent({
      model: modelToUse,
      contents,
      config: { ...config, safetySettings }
      // Ensure safetySettings are always applied
    });
    return response.text;
  } catch (error) {
    console.error(`AI_KERNEL_SERVICE_ERROR (generateContentInternal with ${modelToUse}):`, error);
    return `Error generating content: ${error.message}`;
  }
}
async function embedContentInternal(textChunk, taskType = "RETRIEVAL_DOCUMENT", title, apiKey) {
  if (!textChunk || typeof textChunk !== "string" || textChunk.trim() === "") {
    console.warn("AI_KERNEL_SERVICE (embed): Attempted to embed empty or invalid textChunk. Returning null.");
    return null;
  }
  const aiForCall = getAIAgent(apiKey);
  const embeddingModelToUse = GEMINI_EMBEDDING_MODEL;
  if (!aiForCall) {
    console.error("AI_KERNEL_SERVICE (embed): AI service not properly initialized or API key missing.");
    return "AI service not initialized for embedding.";
  }
  if (!apiKey && !currentApiKey && !process.env.API_KEY) {
    return "API Key not provided and not set in environment for embedding.";
  }
  try {
    console.log(`AI_KERNEL_SERVICE: Embedding content with model ${embeddingModelToUse}. Text (first 50): "${textChunk.substring(0, 50)}..." TaskType: ${taskType}, Title: ${title}`);
    const response = await aiForCall.models.embedContent({
      model: embeddingModelToUse,
      // SDK handles 'models/' prefix for embedding models
      content: { parts: [{ text: textChunk }], role: "user" },
      taskType,
      title
    });
    return response.embedding.values;
  } catch (error) {
    console.error(`AI_KERNEL_SERVICE_ERROR (embedContentInternal with ${embeddingModelToUse}):`, error);
    if (error.message && error.message.includes("API key not valid")) {
      return "无效的API Key导致内容向量化失败。";
    }
    return `内容向量化失败: ${error.message}`;
  }
}
console.log("AI_KERNEL_SERVICE_JS: File execution finished. Exports configured.");
const aiKernelService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  embedContentInternal,
  generateContentInternal,
  getAIAgent,
  getCurrentApiKey,
  initializeSharedAIAgent,
  isAgentInitialized,
  safetySettings
}, Symbol.toStringTag, { value: "Module" }));
console.log("MEMORY_DB_SERVICE_JS: File execution started.");
function saveChatMessage$1(projectId, message) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: saveChatMessage - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(message.id, projectId, message.sender, message.senderName, message.text, message.timestamp, message.isEditing ? 1 : 0, message.isStarred ? 1 : 0, message.isPinned ? 1 : 0, message.theme, message.replyToMessageId, message.triggeringUserMessageId);
    console.log(`MEMORY_DB: Saved chat message ${message.id} for project ${projectId}.`);
    return message;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error saving chat message:", error);
    return null;
  }
}
function updateChatMessage$1(message) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: updateChatMessage - db not available.");
    return null;
  }
  const stmt = db.prepare("UPDATE chat_messages SET text = ?, timestamp = ?, isEditing = ?, isStarred = ?, isPinned = ?, theme = ?, replyToMessageId = ?, triggeringUserMessageId = ? WHERE id = ?");
  try {
    stmt.run(message.text, message.timestamp, message.isEditing ? 1 : 0, message.isStarred ? 1 : 0, message.isPinned ? 1 : 0, message.theme, message.replyToMessageId, message.triggeringUserMessageId, message.id);
    console.log(`MEMORY_DB: Updated chat message ${message.id}.`);
    return message;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error updating chat message:", error);
    return null;
  }
}
function getInitialChatMessages$1(projectId, limit) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getInitialChatMessages - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp DESC LIMIT ?").all(projectId, limit);
    return rows.map(mapChatMessage).reverse();
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error getting initial chat messages:", error);
    return [];
  }
}
function getOlderChatMessages$1(projectId, beforeTimestamp, limit) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getOlderChatMessages - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT * FROM chat_messages WHERE projectId = ? AND timestamp < ? ORDER BY timestamp DESC LIMIT ?").all(projectId, beforeTimestamp, limit);
    return rows.map(mapChatMessage).reverse();
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error getting older chat messages:", error);
    return [];
  }
}
function summarizeAndReplaceMessages$1(projectId, messageIdsToReplace, summaryMessage) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: summarizeAndReplaceMessages - Database not initialized.");
    return { success: false, error: "Database service not available." };
  }
  const transaction = db.transaction(() => {
    if (!messageIdsToReplace || messageIdsToReplace.length === 0) {
      console.warn("MEMORY_DB_WARN: summarizeAndReplaceMessages called with no message IDs to replace. Only inserting summary.");
    } else {
      const deleteStmt = db.prepare(`DELETE FROM chat_messages WHERE projectId = ? AND id IN (${messageIdsToReplace.map(() => "?").join(",")})`);
      const deleteResult = deleteStmt.run(projectId, ...messageIdsToReplace);
      console.log(`MEMORY_DB: Deleted ${deleteResult.changes} old messages for project ${projectId}. IDs: ${messageIdsToReplace.join(", ")}`);
    }
    const insertStmt = db.prepare("INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    insertStmt.run(summaryMessage.id, projectId, summaryMessage.sender, summaryMessage.senderName, summaryMessage.text, summaryMessage.timestamp, 0, 0, 0, null, summaryMessage.replyToMessageId, summaryMessage.triggeringUserMessageId);
    console.log(`MEMORY_DB: Inserted summary message ${summaryMessage.id} for project ${projectId}.`);
    const fetchedRaw = db.prepare("SELECT * FROM chat_messages WHERE id = ?").get(summaryMessage.id);
    const fetchedSummaryMessage = fetchedRaw ? mapChatMessage(fetchedRaw) : summaryMessage;
    return { success: true, newSummaryMessage: fetchedSummaryMessage, replacedMessageIds: messageIdsToReplace };
  });
  try {
    return transaction();
  } catch (error) {
    console.error(`MEMORY_DB_ERROR: Error in summarizeAndReplaceMessages for project ${projectId}:`, error);
    return { success: false, error: error.message };
  }
}
const MAX_KEYWORDS_FOR_DB_QUERY = 5;
const MAX_ASSOCIATED_MEMORIES = 5;
function cosineSimilarity$1(vecA, vecB) {
  if (!vecA || !vecB || vecA.length !== vecB.length || vecA.length === 0) {
    return 0;
  }
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }
  if (normA === 0 || normB === 0) return 0;
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}
function findRelevantMemories$1(queryEmbedding, contextInfo, limit) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: findRelevantMemories - db not available.");
    return { direct: [], associated: [] };
  }
  if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
    console.warn("MEMORY_DB_WARN: findRelevantMemories called with invalid queryEmbedding.");
    return { direct: [], associated: [] };
  }
  const { personaTarget, contextType, currentTopicKeywords, desiredMemoryTypes, projectContextId } = contextInfo;
  try {
    let directSql = `
            SELECT id, memory_content, created_at, last_accessed_at, access_count, importance, 
                   persona_target, memory_type, keywords, embedding, project_context_id, source_message_id,
                   context_affinity_tags, user_feedback_score, status, value_score
            FROM core_memories 
            WHERE (persona_target = ?1 OR persona_target = 'all' OR persona_target = 'shared') AND status = 'active'
        `;
    const directParams = [personaTarget];
    let directParamIndex = 2;
    if (projectContextId) {
      directSql += ` AND (project_context_id = ?${directParamIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
      directParams.push(projectContextId);
    } else {
      directSql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
    }
    if (desiredMemoryTypes && desiredMemoryTypes.length > 0) {
      directSql += ` AND memory_type IN (${desiredMemoryTypes.map(() => `?${directParamIndex++}`).join(",")})`;
      directParams.push(...desiredMemoryTypes);
    }
    const candidateLimit = Math.max(limit * 5, 20);
    directSql += ` ORDER BY value_score DESC, last_accessed_at DESC, created_at DESC LIMIT ?${directParamIndex++}`;
    directParams.push(candidateLimit);
    const candidateMemories = db.prepare(directSql).all(...directParams);
    console.log(`MEMORY_DB: Retrieved ${candidateMemories.length} candidate memories for direct relevance for persona '${personaTarget}'.`);
    const scoredDirectMemories = [];
    for (const mem of candidateMemories) {
      if (mem.embedding) {
        try {
          const storedEmbedding = JSON.parse(mem.embedding);
          if (!Array.isArray(storedEmbedding) || storedEmbedding.length !== queryEmbedding.length) {
            console.warn(`MEMORY_DB_WARN: Embedding for memory ${mem.id} has mismatched length or is not an array.`);
            continue;
          }
          const similarityScore = cosineSimilarity$1(queryEmbedding, storedEmbedding);
          let contextAffinityScore = 0;
          const affinityTags = mem.context_affinity_tags ? mem.context_affinity_tags.toLowerCase().split(",").map((t) => t.trim()) : [];
          if (contextType && affinityTags.includes(contextType.toLowerCase())) {
            contextAffinityScore += 0.5;
          }
          if (currentTopicKeywords && currentTopicKeywords.length > 0) {
            const keywordMatchCount = currentTopicKeywords.filter((kw) => affinityTags.includes(kw.toLowerCase())).length;
            contextAffinityScore += keywordMatchCount * 0.1;
          }
          let timelinessScore = 0;
          if (mem.last_accessed_at) {
            const hoursSinceAccess = ((/* @__PURE__ */ new Date()).getTime() - new Date(mem.last_accessed_at).getTime()) / (1e3 * 60 * 60);
            timelinessScore += Math.max(0, 1 - hoursSinceAccess / (24 * 7));
          } else if (mem.created_at) {
            const hoursSinceCreation = ((/* @__PURE__ */ new Date()).getTime() - new Date(mem.created_at).getTime()) / (1e3 * 60 * 60);
            timelinessScore += Math.max(0, 0.5 - hoursSinceCreation / (24 * 30));
          }
          timelinessScore += (mem.access_count || 0) * 0.05;
          let importanceScoreValue = 0.5;
          if (mem.importance === "high") importanceScoreValue = 1;
          else if (mem.importance === "low") importanceScoreValue = 0.2;
          const feedbackScore = mem.user_feedback_score || 0;
          const currentTotalScore = (0.5 * similarityScore + // Semantic similarity
          0.2 * Math.min(1, contextAffinityScore) + // Contextual fit
          0.1 * Math.min(1, timelinessScore) + // Recency & frequency
          0.1 * importanceScoreValue + // Explicit importance
          0.1 * feedbackScore) * (mem.value_score || 0.5);
          scoredDirectMemories.push({ ...mem, similarityScore, contextAffinityScore, timelinessScore, totalScore: currentTotalScore, retrieval_source: "semantic" });
        } catch (e) {
          console.error(`MEMORY_DB_ERROR: Failed to parse embedding or score memory ${mem.id}:`, e);
        }
      }
    }
    scoredDirectMemories.sort((a, b) => b.totalScore - a.totalScore);
    const directResults = scoredDirectMemories.slice(0, limit);
    let associatedResults = [];
    const directResultIds = new Set(directResults.map((mem) => mem.id));
    const tagsForExpansion = /* @__PURE__ */ new Set();
    directResults.forEach((mem) => {
      if (mem.keywords && typeof mem.keywords === "string") {
        mem.keywords.split(",").map((tag) => tag.trim().toLowerCase()).filter((tag) => tag).forEach((tag) => tagsForExpansion.add(tag));
      }
    });
    if (tagsForExpansion.size > 0) {
      const tagConditions = Array.from(tagsForExpansion).map((tag) => `keywords LIKE '%${tag.replace(/'/g, "''")}%'`).join(" OR ");
      let associatedSql = `
                SELECT id, memory_content, created_at, last_accessed_at, access_count, importance, 
                       persona_target, memory_type, keywords, embedding, project_context_id, source_message_id,
                       context_affinity_tags, user_feedback_score, status, value_score
                FROM core_memories
                WHERE (persona_target = ?1 OR persona_target = 'all' OR persona_target = 'shared') AND status = 'active'
                  AND (${tagConditions})
            `;
      const associatedParams = [personaTarget];
      let associatedParamIndex = 2;
      if (projectContextId) {
        associatedSql += ` AND (project_context_id = ?${associatedParamIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
        associatedParams.push(projectContextId);
      } else {
        associatedSql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
      }
      if (directResultIds.size > 0) {
        associatedSql += ` AND id NOT IN (${Array.from(directResultIds).map(() => `?${associatedParamIndex++}`).join(",")})`;
        associatedParams.push(...Array.from(directResultIds));
      }
      associatedSql += ` ORDER BY value_score DESC, CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ASC, last_accessed_at DESC, created_at DESC LIMIT ?${associatedParamIndex++}`;
      associatedParams.push(MAX_ASSOCIATED_MEMORIES);
      const associatedMemoriesRaw = db.prepare(associatedSql).all(...associatedParams);
      associatedResults = associatedMemoriesRaw.map((mem) => ({ ...mem, retrieval_source: "tag_associated" }));
      console.log(`MEMORY_DB: Found ${associatedResults.length} associated memories based on tags from direct results.`);
    }
    if (directResults.length > 0) {
      const updateStmt = db.prepare("UPDATE core_memories SET access_count = access_count + 1, last_accessed_at = ? WHERE id = ?");
      const now = (/* @__PURE__ */ new Date()).toISOString();
      directResults.forEach((mem) => {
        try {
          updateStmt.run(now, mem.id);
        } catch (updateError) {
          console.error(`MEMORY_DB_ERROR: Failed to update access stats for memory ${mem.id}:`, updateError);
        }
      });
    }
    console.log(`MEMORY_DB: Final results - Direct: ${directResults.length}, Associated: ${associatedResults.length} for persona '${personaTarget}'.`);
    return { direct: directResults, associated: associatedResults };
  } catch (error) {
    console.error(`MEMORY_DB_ERROR: Error finding relevant memories:`, error);
    return { direct: [], associated: [] };
  }
}
function getCoreMemories$1(personaTarget, projectContextId, limit = 5, importanceThreshold = null, keywords = []) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getCoreMemories - db not available.");
    return [];
  }
  console.warn("MEMORY_DB_WARN: getCoreMemories (keyword-based) called. Consider using findRelevantMemories with embeddings for semantic RAG.");
  try {
    let sql = `
      SELECT *
      FROM core_memories 
      WHERE (persona_target = ?1 OR persona_target = 'shared') AND status = 'active'
    `;
    const params = [personaTarget];
    let paramIndex = 2;
    if (projectContextId) {
      sql += ` AND (project_context_id = ?${paramIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
      params.push(projectContextId);
    } else {
      sql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
    }
    if (importanceThreshold) {
      sql += ` AND importance = ?${paramIndex++}`;
      params.push(importanceThreshold);
    }
    let effectiveKeywords = [];
    if (Array.isArray(keywords) && keywords.length > 0) {
      effectiveKeywords = keywords.filter((kw) => typeof kw === "string" && kw.trim() !== "").slice(0, MAX_KEYWORDS_FOR_DB_QUERY);
    }
    if (effectiveKeywords.length > 0) {
      const keywordConditions = effectiveKeywords.map(
        (kw) => `(keywords LIKE '%,${kw.replace(/'/g, "''")},%')`
      ).join(" OR ");
      sql += ` AND (${keywordConditions})`;
    }
    sql += ` ORDER BY value_score DESC, CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ASC, created_at DESC LIMIT ?${paramIndex++}`;
    params.push(limit);
    const stmt = db.prepare(sql);
    const memories = stmt.all(...params);
    console.log(`MEMORY_DB: Retrieved ${memories.length} core memories (getCoreMemories) for persona '${personaTarget}', project '${projectContextId || "any/global"}'.`);
    return memories;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error getting core memories (getCoreMemories):", error);
    return [];
  }
}
function getAllCoreMemories$1(filters = {}, sort = { field: "created_at", order: "DESC" }, pagination = { limit: 50, offset: 0 }) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getAllCoreMemories - db not available.");
    return [];
  }
  try {
    let query = "SELECT * FROM core_memories";
    const params = [];
    const conditions = [];
    if (filters.persona_target && filters.persona_target !== "all") {
      conditions.push("persona_target = ?");
      params.push(filters.persona_target);
    }
    if (filters.memory_type_query && filters.memory_type_query.trim() !== "") {
      conditions.push("memory_type LIKE ?");
      params.push(`%${filters.memory_type_query.trim()}%`);
    }
    if (filters.importance && filters.importance !== "all") {
      conditions.push("importance = ?");
      params.push(filters.importance);
    }
    if (filters.keywords_query && filters.keywords_query.trim() !== "") {
      const keywordForQuery = filters.keywords_query.trim().toLowerCase();
      conditions.push(`(keywords LIKE ? OR memory_content LIKE ?)`);
      params.push(`%,${keywordForQuery},%`, `%${keywordForQuery}%`);
    }
    if (filters.project_context_id && filters.project_context_id.trim() !== "") {
      if (filters.project_context_id.toLowerCase() === "global") {
        conditions.push("(project_context_id = 'global' OR project_context_id IS NULL)");
      } else {
        conditions.push("project_context_id = ?");
        params.push(filters.project_context_id.trim());
      }
    }
    if (filters.status && filters.status !== "all") {
      conditions.push("status = ?");
      params.push(filters.status);
    }
    if (filters.memory_content_query && filters.memory_content_query.trim() !== "") {
      conditions.push("memory_content LIKE ?");
      params.push(`%${filters.memory_content_query.trim()}%`);
    }
    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }
    const validSortFields = ["created_at", "importance", "persona_target", "memory_type", "access_count", "last_accessed_at", "value_score"];
    const sortField = validSortFields.includes(sort.field) ? sort.field : "created_at";
    const sortOrder = sort.order === "ASC" ? "ASC" : "DESC";
    if (sortField === "importance") {
      query += ` ORDER BY CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ${sortOrder}, value_score DESC, created_at DESC`;
    } else {
      query += ` ORDER BY ${sortField} ${sortOrder}, value_score DESC`;
    }
    query += ` LIMIT ? OFFSET ?`;
    params.push(pagination.limit || 50, pagination.offset || 0);
    const rows = db.prepare(query).all(...params);
    console.log(`MEMORY_DB: Retrieved ${rows.length} core memories with filters/sort/pagination.`);
    return rows;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error getting all core memories:", error);
    return [];
  }
}
function getCoreMemoryById$1(memoryId) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getCoreMemoryById - db not available.");
    return null;
  }
  try {
    const row = db.prepare("SELECT * FROM core_memories WHERE id = ?").get(memoryId);
    return row || null;
  } catch (error) {
    console.error(`MEMORY_DB_ERROR: Error getting core memory by ID ${memoryId}:`, error);
    return null;
  }
}
async function addCoreMemory$1(memoryData) {
  var _a;
  if (!db) {
    console.error("MEMORY_DB_ERROR: addCoreMemory - db not available.");
    return null;
  }
  let embeddingJson = null;
  if (memoryData.memory_content) {
    try {
      const appSettings = await getSettings$1();
      if (!appSettings || !appSettings.apiKey || !appSettings.embeddingModel) {
        console.warn("MEMORY_DB_WARN: API key or embedding model not configured. Skipping embedding for new memory.");
      } else {
        const embeddingVector = await embedContentInternal(
          memoryData.memory_content,
          "RETRIEVAL_DOCUMENT",
          memoryData.memory_content.substring(0, 50),
          // title for embedding
          appSettings.apiKey
          // Pass apiKey
          // embeddingModel is handled by aiKernelService
        );
        if (embeddingVector && typeof embeddingVector !== "string") {
          embeddingJson = JSON.stringify(embeddingVector);
        } else {
          console.error("MEMORY_DB_ERROR: Failed to generate embedding for new memory:", embeddingVector || "Null vector");
        }
      }
    } catch (embedError) {
      console.error("MEMORY_DB_ERROR: Error during embedding for new memory:", embedError);
    }
  }
  const keywordsFormatted = memoryData.keywords && typeof memoryData.keywords === "string" && memoryData.keywords.trim() ? `,${memoryData.keywords.trim().split(",").map((k) => k.trim().toLowerCase()).filter((k) => k).join(",")},` : null;
  const affinityTagsFormatted = memoryData.context_affinity_tags && typeof memoryData.context_affinity_tags === "string" && memoryData.context_affinity_tags.trim() ? memoryData.context_affinity_tags.trim().split(",").map((t) => {
    const tag = t.trim().toLowerCase();
    return tag.startsWith("#") ? tag : `#${tag}`;
  }).filter((t) => t.length > 1).join(",") : null;
  const newMemory = {
    id: crypto.randomUUID(),
    access_count: 0,
    keywords: keywordsFormatted,
    embedding: embeddingJson,
    source_message_id: memoryData.source_message_id || null,
    context_affinity_tags: affinityTagsFormatted,
    user_feedback_score: memoryData.user_feedback_score || 0,
    status: memoryData.status || "active",
    value_score: memoryData.value_score === void 0 ? 0.5 : memoryData.value_score,
    ...memoryData,
    created_at: memoryData.created_at || (/* @__PURE__ */ new Date()).toISOString(),
    last_accessed_at: memoryData.last_accessed_at || null,
    project_context_id: ((_a = memoryData.project_context_id) == null ? void 0 : _a.trim()) || null
  };
  const stmt = db.prepare(`
        INSERT INTO core_memories (id, memory_content, created_at, last_accessed_at, access_count, importance, persona_target, memory_type, keywords, embedding, project_context_id, source_message_id, context_affinity_tags, user_feedback_score, status, value_score)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
  try {
    stmt.run(
      newMemory.id,
      newMemory.memory_content,
      newMemory.created_at,
      newMemory.last_accessed_at,
      newMemory.access_count,
      newMemory.importance,
      newMemory.persona_target,
      newMemory.memory_type,
      newMemory.keywords,
      newMemory.embedding,
      newMemory.project_context_id,
      newMemory.source_message_id,
      newMemory.context_affinity_tags,
      newMemory.user_feedback_score,
      newMemory.status,
      newMemory.value_score
    );
    console.log(`MEMORY_DB: Added core memory ${newMemory.id}. Keywords: ${newMemory.keywords}, Affinity Tags: ${newMemory.context_affinity_tags}`);
    return newMemory;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error adding core memory:", error);
    return null;
  }
}
async function addCoreMemoryFromChat$1(messageText, personaTarget, projectId = null) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: addCoreMemoryFromChat - db not available.");
    return null;
  }
  const memoryData = {
    memory_content: messageText,
    persona_target: personaTarget,
    memory_type: "chat_snippet",
    importance: "medium",
    project_context_id: projectId
  };
  return addCoreMemory$1(memoryData);
}
async function updateCoreMemory$1(memoryData) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: updateCoreMemory - db not available.");
    return null;
  }
  const existingMemory = await getCoreMemoryById$1(memoryData.id);
  if (!existingMemory) {
    console.warn(`MEMORY_DB_WARN: No core memory found with ID ${memoryData.id} to update.`);
    return null;
  }
  let embeddingJson = existingMemory.embedding;
  if (memoryData.memory_content && memoryData.memory_content !== existingMemory.memory_content) {
    console.log(`MEMORY_DB: Content changed for memory ${memoryData.id}. Re-embedding...`);
    try {
      const appSettings = await getSettings$1();
      if (!appSettings || !appSettings.apiKey || !appSettings.embeddingModel) {
        console.warn("MEMORY_DB_WARN: API key or embedding model not configured. Cannot re-embed memory.");
      } else {
        const embeddingVector = await embedContentInternal(
          memoryData.memory_content,
          "RETRIEVAL_DOCUMENT",
          memoryData.memory_content.substring(0, 50),
          // title
          appSettings.apiKey
          // Pass apiKey
          // embeddingModel is handled by aiKernelService
        );
        if (embeddingVector && typeof embeddingVector !== "string") {
          embeddingJson = JSON.stringify(embeddingVector);
          console.log(`MEMORY_DB: Successfully re-embedded memory ${memoryData.id}.`);
        } else {
          console.error("MEMORY_DB_ERROR: Failed to re-generate embedding for updated memory:", embeddingVector || "Null vector");
        }
      }
    } catch (embedError) {
      console.error("MEMORY_DB_ERROR: Error during re-embedding for updated memory:", embedError);
    }
  }
  const keywordsFormatted = memoryData.keywords && typeof memoryData.keywords === "string" && memoryData.keywords.trim() ? `,${memoryData.keywords.trim().split(",").map((k) => k.trim().toLowerCase()).filter((k) => k).join(",")},` : existingMemory.keywords || null;
  const affinityTagsFormatted = memoryData.context_affinity_tags && typeof memoryData.context_affinity_tags === "string" && memoryData.context_affinity_tags.trim() ? memoryData.context_affinity_tags.trim().split(",").map((t) => {
    const tag = t.trim().toLowerCase();
    return tag.startsWith("#") ? tag : `#${tag}`;
  }).filter((t) => t.length > 1).join(",") : existingMemory.context_affinity_tags || null;
  const updatedMemory = {
    ...existingMemory,
    ...memoryData,
    keywords: keywordsFormatted,
    embedding: embeddingJson,
    context_affinity_tags: affinityTagsFormatted,
    last_accessed_at: (/* @__PURE__ */ new Date()).toISOString()
  };
  const stmt = db.prepare(`
        UPDATE core_memories 
        SET memory_content = ?, last_accessed_at = ?, access_count = ?, importance = ?, persona_target = ?, 
            memory_type = ?, keywords = ?, embedding = ?, project_context_id = ?, source_message_id = ?,
            context_affinity_tags = ?, user_feedback_score = ?, status = ?, value_score = ?
        WHERE id = ?
    `);
  try {
    stmt.run(
      updatedMemory.memory_content,
      updatedMemory.last_accessed_at,
      updatedMemory.access_count,
      updatedMemory.importance,
      updatedMemory.persona_target,
      updatedMemory.memory_type,
      updatedMemory.keywords,
      updatedMemory.embedding,
      updatedMemory.project_context_id,
      updatedMemory.source_message_id,
      updatedMemory.context_affinity_tags,
      updatedMemory.user_feedback_score,
      updatedMemory.status,
      updatedMemory.value_score,
      updatedMemory.id
    );
    console.log(`MEMORY_DB: Updated core memory ${updatedMemory.id}. Keywords: ${updatedMemory.keywords}, Affinity Tags: ${updatedMemory.context_affinity_tags}`);
    return db.prepare("SELECT * FROM core_memories WHERE id = ?").get(updatedMemory.id);
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error updating core memory:", error);
    return null;
  }
}
function deleteCoreMemory$1(memoryId) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: deleteCoreMemory - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM core_memories WHERE id = ?");
  try {
    const result = stmt.run(memoryId);
    if (result.changes === 0) {
      console.warn(`MEMORY_DB_WARN: No core memory found with ID ${memoryId} to delete.`);
      return { success: false, error: "Memory not found." };
    }
    console.log(`MEMORY_DB: Deleted core memory ${memoryId}.`);
    return { success: true };
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error deleting core memory:", error);
    return { success: false, error: error.message };
  }
}
function getAbsoluteTerritoryMessages$1(limit, beforeTimestamp) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: getAbsoluteTerritoryMessages - db not available.");
    return [];
  }
  try {
    let query = "SELECT id, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, itemIconPath, itemCgPath, avatarPathOverride FROM absolute_territory_chat_messages";
    const params = [];
    if (beforeTimestamp) {
      query += " WHERE timestamp < ?";
      params.push(beforeTimestamp);
    }
    query += " ORDER BY timestamp DESC LIMIT ?";
    params.push(limit);
    const rows = db.prepare(query).all(...params);
    return rows.map(mapChatMessage).reverse();
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error getting Absolute Territory messages:", error);
    return [];
  }
}
function addAbsoluteTerritoryMessage$1(message) {
  if (!db) {
    console.error("MEMORY_DB_ERROR: addAbsoluteTerritoryMessage - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO absolute_territory_chat_messages (id, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, itemIconPath, itemCgPath, avatarPathOverride) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
  try {
    const messageToSave = {
      id: message.id || crypto.randomUUID(),
      sender: message.sender,
      senderName: message.senderName,
      text: message.text,
      timestamp: message.timestamp || (/* @__PURE__ */ new Date()).toISOString(),
      isEditing: message.isEditing ? 1 : 0,
      isStarred: message.isStarred ? 1 : 0,
      isPinned: message.isPinned ? 1 : 0,
      theme: message.theme || null,
      itemIconPath: message.itemIconPath || null,
      itemCgPath: message.itemCgPath || null,
      avatarPathOverride: message.avatarPathOverride || null
    };
    stmt.run(messageToSave.id, messageToSave.sender, messageToSave.senderName, messageToSave.text, messageToSave.timestamp, messageToSave.isEditing, messageToSave.isStarred, messageToSave.isPinned, messageToSave.theme, messageToSave.itemIconPath, messageToSave.itemCgPath, messageToSave.avatarPathOverride);
    console.log(`MEMORY_DB: Added Absolute Territory message ${messageToSave.id}.`);
    return messageToSave;
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error adding Absolute Territory message:", error);
    return null;
  }
}
function clearAbsoluteTerritoryHistory$1() {
  if (!db) {
    console.error("MEMORY_DB_ERROR: clearAbsoluteTerritoryHistory - db not available.");
    return { success: false, error: "Database not available." };
  }
  try {
    db.prepare("DELETE FROM absolute_territory_chat_messages").run();
    console.log("MEMORY_DB: Cleared Absolute Territory chat history.");
    return { success: true };
  } catch (error) {
    console.error("MEMORY_DB_ERROR: Error clearing Absolute Territory chat history:", error);
    return { success: false, error: error.message };
  }
}
console.log("MEMORY_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("GLOBAL_DATA_DB_SERVICE_JS: File execution started.");
function getAllGlobalKnowledgeTomes$1() {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: getAllGlobalKnowledgeTomes - db not available.");
    return [];
  }
  try {
    const tomesRaw = db.prepare("SELECT * FROM global_knowledge_tomes ORDER BY lastModifiedAt DESC").all();
    console.log(`GLOBAL_DB: Retrieved ${tomesRaw.length} global knowledge tomes.`);
    return tomesRaw.map((t) => ({ ...t, tags: parseJsonArray(t.tags) }));
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: getting all global tomes:", e);
    return [];
  }
}
function addGlobalKnowledgeTome$1(tome) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: addGlobalKnowledgeTome - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO global_knowledge_tomes (id, title, content, category, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?)");
  try {
    const tomeToSave = {
      id: tome.id || crypto.randomUUID(),
      createdAt: tome.createdAt || (/* @__PURE__ */ new Date()).toISOString(),
      lastModifiedAt: tome.lastModifiedAt || (/* @__PURE__ */ new Date()).toISOString(),
      ...tome
    };
    stmt.run(tomeToSave.id, tomeToSave.title, tomeToSave.content, tomeToSave.category, JSON.stringify(tomeToSave.tags || []), tomeToSave.createdAt, tomeToSave.lastModifiedAt);
    console.log(`GLOBAL_DB: Added global knowledge tome ${tomeToSave.id}.`);
    return tomeToSave;
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: adding global tome:", e);
    return null;
  }
}
function updateGlobalKnowledgeTome$1(tome) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: updateGlobalKnowledgeTome - db not available.");
    return null;
  }
  const stmt = db.prepare("UPDATE global_knowledge_tomes SET title = ?, content = ?, category = ?, tags = ?, lastModifiedAt = ? WHERE id = ?");
  try {
    const tomeToUpdate = { ...tome, lastModifiedAt: (/* @__PURE__ */ new Date()).toISOString() };
    stmt.run(tomeToUpdate.title, tomeToUpdate.content, tomeToUpdate.category, JSON.stringify(tomeToUpdate.tags || []), tomeToUpdate.lastModifiedAt, tomeToUpdate.id);
    console.log(`GLOBAL_DB: Updated global knowledge tome ${tomeToUpdate.id}.`);
    return tomeToUpdate;
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: updating global tome:", e);
    return null;
  }
}
function deleteGlobalKnowledgeTome$1(tomeId) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: deleteGlobalKnowledgeTome - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM global_knowledge_tomes WHERE id = ?");
  try {
    stmt.run(tomeId);
    console.log(`GLOBAL_DB: Deleted global knowledge tome ${tomeId}.`);
    return { success: true };
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: deleting global tome:", e);
    return { success: false, error: e.message };
  }
}
function getAllGlobalQuickCommands$1() {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: getAllGlobalQuickCommands - db not available.");
    return [];
  }
  try {
    const commands = db.prepare("SELECT * FROM global_quick_commands ORDER BY title ASC").all();
    console.log(`GLOBAL_DB: Retrieved ${commands.length} global quick commands.`);
    return commands;
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: getting all global commands:", e);
    return [];
  }
}
function addGlobalQuickCommand$1(cmd) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: addGlobalQuickCommand - db not available.");
    return null;
  }
  const stmt = db.prepare("INSERT INTO global_quick_commands (id, title, commandText, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?)");
  try {
    const cmdToSave = {
      id: cmd.id || crypto.randomUUID(),
      createdAt: cmd.createdAt || (/* @__PURE__ */ new Date()).toISOString(),
      lastModifiedAt: cmd.lastModifiedAt || (/* @__PURE__ */ new Date()).toISOString(),
      ...cmd
    };
    stmt.run(cmdToSave.id, cmdToSave.title, cmdToSave.commandText, cmdToSave.createdAt, cmdToSave.lastModifiedAt);
    console.log(`GLOBAL_DB: Added global quick command ${cmdToSave.id}.`);
    return cmdToSave;
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: adding global command:", e);
    return null;
  }
}
function updateGlobalQuickCommand$1(cmd) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: updateGlobalQuickCommand - db not available.");
    return null;
  }
  const stmt = db.prepare("UPDATE global_quick_commands SET title = ?, commandText = ?, lastModifiedAt = ? WHERE id = ?");
  try {
    const cmdToUpdate = { ...cmd, lastModifiedAt: (/* @__PURE__ */ new Date()).toISOString() };
    stmt.run(cmdToUpdate.title, cmdToUpdate.commandText, cmdToUpdate.lastModifiedAt, cmdToUpdate.id);
    console.log(`GLOBAL_DB: Updated global quick command ${cmdToUpdate.id}.`);
    return cmdToUpdate;
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: updating global command:", e);
    return null;
  }
}
function deleteGlobalQuickCommand$1(commandId) {
  if (!db) {
    console.error("GLOBAL_DB_ERROR: deleteGlobalQuickCommand - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM global_quick_commands WHERE id = ?");
  try {
    stmt.run(commandId);
    console.log(`GLOBAL_DB: Deleted global quick command ${commandId}.`);
    return { success: true };
  } catch (e) {
    console.error("GLOBAL_DB_ERROR: deleting global command:", e);
    return { success: false, error: e.message };
  }
}
console.log("GLOBAL_DATA_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("RAG_INDEX_DB_SERVICE_JS: File execution started.");
function clearKnowledgeIndexForProject$1(projectId) {
  if (!db) {
    console.error("RAG_INDEX_DB_ERROR: clearKnowledgeIndexForProject - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM knowledge_index WHERE source_project_id = ?");
  try {
    stmt.run(projectId);
    console.log(`RAG_INDEX_DB: Cleared knowledge index for project ${projectId}.`);
    return { success: true };
  } catch (error) {
    console.error(`RAG_INDEX_DB_ERROR: Error clearing knowledge index for project ${projectId}:`, error);
    return { success: false, error: error.message };
  }
}
function addKnowledgeIndexEntry$1(entry) {
  if (!db) {
    console.error("RAG_INDEX_DB_ERROR: addKnowledgeIndexEntry - db not available.");
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("INSERT INTO knowledge_index (id, source_project_id, source_file_path, chunk_text, chunk_vector, metadata, indexed_at) VALUES (?, ?, ?, ?, ?, ?, ?)");
  try {
    const entryToSave = {
      id: entry.id || crypto.randomUUID(),
      indexed_at: entry.indexed_at || (/* @__PURE__ */ new Date()).toISOString(),
      ...entry
    };
    stmt.run(entryToSave.id, entryToSave.source_project_id, entryToSave.source_file_path, entryToSave.chunk_text, entryToSave.chunk_vector, entryToSave.metadata, entryToSave.indexed_at);
    return { success: true, id: entryToSave.id };
  } catch (error) {
    console.error("RAG_INDEX_DB_ERROR: Error adding knowledge index entry:", error);
    return { success: false, error: error.message };
  }
}
function getKnowledgeIndexEntriesForProject$1(projectId) {
  if (!db) {
    console.error("RAG_INDEX_DB_ERROR: getKnowledgeIndexEntriesForProject - db not available.");
    return [];
  }
  try {
    const entries = db.prepare("SELECT * FROM knowledge_index WHERE source_project_id = ?").all(projectId);
    return entries;
  } catch (error) {
    console.error(`RAG_INDEX_DB_ERROR: Error getting knowledge index entries for project ${projectId}:`, error);
    return [];
  }
}
console.log("RAG_INDEX_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("CMS_DB_SERVICE_JS: File execution started.");
const CMS_ASSETS_DIR_NAME = "at_cms_assets";
async function saveOrUpdateImageAsset(type2, subfolder, imageBase64ToSave, existingDbRelativePath) {
  const userDataPath = app.getPath("userData");
  async function deleteAsset(relativePathToDelete) {
    if (relativePathToDelete) {
      const absolutePathToDelete = path.join(userDataPath, CMS_ASSETS_DIR_NAME, relativePathToDelete);
      try {
        await fsPromises$1.access(absolutePathToDelete);
        await fsPromises$1.unlink(absolutePathToDelete);
        console.log(`CMS_DB: Deleted asset ${absolutePathToDelete}`);
      } catch (accessOrDeleteError) {
        if (accessOrDeleteError.code === "ENOENT") {
          console.log(`CMS_DB_INFO: Asset ${absolutePathToDelete} not found, skipping deletion.`);
        } else {
          console.warn(`CMS_DB_WARN: Failed to delete asset ${relativePathToDelete}`, accessOrDeleteError);
        }
      }
    }
  }
  if (imageBase64ToSave === null || imageBase64ToSave === "") {
    if (existingDbRelativePath) {
      await deleteAsset(existingDbRelativePath);
    }
    return null;
  }
  if (imageBase64ToSave === void 0) {
    return existingDbRelativePath;
  }
  const fullAssetDir = path.join(userDataPath, CMS_ASSETS_DIR_NAME, type2, subfolder);
  try {
    await fsPromises$1.mkdir(fullAssetDir, { recursive: true });
  } catch (mkdirError) {
    console.error(`CMS_DB_ERROR: Could not create directory ${fullAssetDir}`, mkdirError);
    throw new Error(`Failed to create asset directory for ${type2}/${subfolder}.`);
  }
  let extension = ".png";
  const mimeMatch = imageBase64ToSave.match(/^data:image\/(\w+);base64,/);
  if (mimeMatch && mimeMatch[1]) {
    const imgType = mimeMatch[1].toLowerCase();
    if (["jpeg", "jpg"].includes(imgType)) extension = ".jpg";
    else if (imgType === "webp") extension = ".webp";
    else if (imgType === "gif") extension = ".gif";
  }
  const base64Data = imageBase64ToSave.replace(/^data:image\/\w+;base64,/, "");
  const buffer = Buffer.from(base64Data, "base64");
  const newFilename = `${crypto.randomUUID()}${extension}`;
  const newDbRelativePath = path.join(type2, subfolder, newFilename).replace(/\\/g, "/");
  const absoluteSavePath = path.join(fullAssetDir, newFilename);
  try {
    await fsPromises$1.writeFile(absoluteSavePath, buffer);
    console.log(`CMS_DB: Saved new image asset to ${absoluteSavePath}`);
    if (existingDbRelativePath && existingDbRelativePath !== newDbRelativePath) {
      await deleteAsset(existingDbRelativePath);
    }
    return newDbRelativePath;
  } catch (writeError) {
    console.error(`CMS_DB_ERROR: Failed to write image asset to ${absoluteSavePath}`, writeError);
    throw new Error(`Failed to save image asset for ${type2}/${subfolder}.`);
  }
}
function getTableName(type2) {
  switch (type2) {
    case "props":
      return "at_cms_props";
    case "costumes":
      return "at_cms_costumes";
    case "poses":
      return "at_cms_poses";
    case "role_cards":
      return "at_role_playing_cards";
    default:
      throw new Error(`Invalid CMS type: ${type2}`);
  }
}
async function getCMSItems$1(type2) {
  if (!db) {
    console.error(`CMS_DB_ERROR: getCMSItems(${type2}) - db not available.`);
    return [];
  }
  const tableName = getTableName(type2);
  try {
    let items;
    if (type2 === "role_cards") {
      items = db.prepare(`SELECT id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt FROM ${tableName} ORDER BY sort_order ASC, name ASC`).all();
    } else {
      items = db.prepare(`SELECT id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt FROM ${tableName} ORDER BY name ASC`).all();
    }
    console.log(`CMS_DB: Retrieved ${items.length} items for type ${type2}.`);
    const typeMapping = {
      "props": "prop",
      "costumes": "costume",
      "poses": "pose",
      "role_cards": "role_card"
    };
    return items.map((item) => ({
      ...item,
      type: typeMapping[type2],
      // 添加 type 字段
      status_effects_json: item.status_effects_json,
      development_effects_json: item.development_effects_json,
      unlock_requirements_json: item.unlock_requirements_json
    }));
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error getting CMS items for type ${type2}:`, e);
    return [];
  }
}
async function addCMSItem$1(type2, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) {
  if (!db) {
    console.error(`CMS_DB_ERROR: addCMSItem(${type2}) - db not available.`);
    return null;
  }
  const tableName = getTableName(type2);
  let finalIconPath = null;
  let finalCgPath = null;
  try {
    if (iconBase64) finalIconPath = await saveOrUpdateImageAsset(type2, "icons", iconBase64, null);
    if (cgBase64) finalCgPath = await saveOrUpdateImageAsset(type2, "cgs", cgBase64, null);
  } catch (imgError) {
    console.error(`CMS_DB_ERROR: Failed to save image(s) for new ${type2} item:`, imgError.message);
  }
  const newItem = {
    id: crypto.randomUUID(),
    name: itemData.name,
    owner: itemData.owner,
    icon_path: finalIconPath,
    cg_image_path: finalCgPath,
    prompt_for_linluo: itemData.prompt_for_linluo,
    prompt_for_master: itemData.prompt_for_master,
    status_effects_json: statusEffectsJson || "{}",
    development_effects_json: developmentEffectsJson || "[]",
    unlock_requirements_json: unlockRequirementsJson || "[]",
    createdAt: (/* @__PURE__ */ new Date()).toISOString(),
    lastModifiedAt: (/* @__PURE__ */ new Date()).toISOString()
  };
  const stmt = db.prepare(`
        INSERT INTO ${tableName} (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
  try {
    stmt.run(newItem.id, newItem.name, newItem.owner, newItem.icon_path, newItem.cg_image_path, newItem.prompt_for_linluo, newItem.prompt_for_master, newItem.status_effects_json, newItem.development_effects_json, newItem.unlock_requirements_json, newItem.createdAt, newItem.lastModifiedAt);
    console.log(`CMS_DB: Added CMS item ${newItem.id} of type ${type2}.`);
    return newItem;
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error adding CMS item of type ${type2}:`, e);
    return null;
  }
}
async function updateCMSItem$1(type2, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) {
  if (!db) {
    console.error(`CMS_DB_ERROR: updateCMSItem(${type2}, ${itemId}) - db not available.`);
    return null;
  }
  const tableName = getTableName(type2);
  const existingItemRow = db.prepare(`SELECT icon_path, cg_image_path FROM ${tableName} WHERE id = ?`).get(itemId);
  if (!existingItemRow) {
    console.error(`CMS_DB_ERROR: Item ${itemId} of type ${type2} not found for update.`);
    return null;
  }
  let finalIconPath = existingItemRow.icon_path;
  let finalCgPath = existingItemRow.cg_image_path;
  let iconProcessed = false;
  let cgProcessed = false;
  try {
    if (iconBase64 !== void 0) {
      finalIconPath = await saveOrUpdateImageAsset(type2, "icons", iconBase64, existingItemRow.icon_path);
      iconProcessed = true;
    }
    if (cgBase64 !== void 0) {
      finalCgPath = await saveOrUpdateImageAsset(type2, "cgs", cgBase64, existingItemRow.cg_image_path);
      cgProcessed = true;
    }
  } catch (imgError) {
    console.error(`CMS_DB_ERROR: Failed to process image(s) during update for ${type2} item ${itemId}:`, imgError.message);
    if (iconBase64 !== void 0 && iconProcessed && finalIconPath === null && imgError.message.includes("icons")) finalIconPath = existingItemRow.icon_path;
    if (cgBase64 !== void 0 && cgProcessed && finalCgPath === null && imgError.message.includes("cgs")) finalCgPath = existingItemRow.cg_image_path;
  }
  const fieldsToUpdate = [];
  const valuesToUpdate = [];
  if (itemData.name !== void 0) {
    fieldsToUpdate.push("name = ?");
    valuesToUpdate.push(itemData.name);
  }
  if (itemData.owner !== void 0) {
    fieldsToUpdate.push("owner = ?");
    valuesToUpdate.push(itemData.owner);
  }
  if (iconProcessed) {
    fieldsToUpdate.push("icon_path = ?");
    valuesToUpdate.push(finalIconPath);
  }
  if (cgProcessed) {
    fieldsToUpdate.push("cg_image_path = ?");
    valuesToUpdate.push(finalCgPath);
  }
  if (itemData.prompt_for_linluo !== void 0) {
    fieldsToUpdate.push("prompt_for_linluo = ?");
    valuesToUpdate.push(itemData.prompt_for_linluo);
  }
  if (itemData.prompt_for_master !== void 0) {
    fieldsToUpdate.push("prompt_for_master = ?");
    valuesToUpdate.push(itemData.prompt_for_master);
  }
  if (statusEffectsJson !== void 0) {
    fieldsToUpdate.push("status_effects_json = ?");
    valuesToUpdate.push(statusEffectsJson === null ? "{}" : statusEffectsJson);
  }
  if (developmentEffectsJson !== void 0) {
    fieldsToUpdate.push("development_effects_json = ?");
    valuesToUpdate.push(developmentEffectsJson === null ? "[]" : developmentEffectsJson);
  }
  if (unlockRequirementsJson !== void 0) {
    fieldsToUpdate.push("unlock_requirements_json = ?");
    valuesToUpdate.push(unlockRequirementsJson === null ? "[]" : unlockRequirementsJson);
  }
  if (fieldsToUpdate.length === 0) {
    console.log(`CMS_DB: No data fields to update for item ${itemId} of type ${type2}.`);
    return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(itemId);
  }
  fieldsToUpdate.push("lastModifiedAt = ?");
  valuesToUpdate.push((/* @__PURE__ */ new Date()).toISOString());
  valuesToUpdate.push(itemId);
  const stmt = db.prepare(`UPDATE ${tableName} SET ${fieldsToUpdate.join(", ")} WHERE id = ?`);
  try {
    stmt.run(...valuesToUpdate);
    console.log(`CMS_DB: Updated CMS item ${itemId} of type ${type2}.`);
    const updatedItem = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(itemId);
    return {
      ...updatedItem,
      status_effects_json: updatedItem.status_effects_json,
      development_effects_json: updatedItem.development_effects_json,
      unlock_requirements_json: updatedItem.unlock_requirements_json
    };
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error updating CMS item ${itemId} of type ${type2}:`, e);
    return null;
  }
}
async function deleteCMSItem$1(type2, itemId) {
  if (!db) {
    console.error(`CMS_DB_ERROR: deleteCMSItem(${type2}, ${itemId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const tableName = getTableName(type2);
  const userDataPath = app.getPath("userData");
  const itemToDelete = db.prepare(`SELECT icon_path, cg_image_path FROM ${tableName} WHERE id = ?`).get(itemId);
  const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
  try {
    const result = stmt.run(itemId);
    if (result.changes > 0) {
      console.log(`CMS_DB: Deleted CMS item ${itemId} of type ${type2}.`);
      if (itemToDelete) {
        const pathsToDelete = [itemToDelete.icon_path, itemToDelete.cg_image_path].filter(Boolean);
        for (const relativeAssetPath of pathsToDelete) {
          const absoluteAssetPath = path.join(userDataPath, CMS_ASSETS_DIR_NAME, relativeAssetPath);
          try {
            await fsPromises$1.access(absoluteAssetPath);
            await fsPromises$1.unlink(absoluteAssetPath);
            console.log(`CMS_DB: Deleted asset ${relativeAssetPath} for item ${itemId}.`);
          } catch (deleteError) {
            if (deleteError.code === "ENOENT") {
              console.log(`CMS_DB_INFO: Asset ${relativeAssetPath} not found, no need to delete.`);
            } else {
              console.warn(`CMS_DB_WARN: Failed to delete asset ${relativeAssetPath} for deleted item ${itemId}.`, deleteError);
            }
          }
        }
      }
      return { success: true };
    } else {
      console.warn(`CMS_DB_WARN: Item ${itemId} of type ${type2} not found for deletion.`);
      return { success: false, error: "Item not found." };
    }
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error deleting CMS item ${itemId} of type ${type2}:`, e);
    return { success: false, error: e.message };
  }
}
function triggerHuntingTime$1() {
  console.log("CMS_DB_SERVICE: Hunting Time triggered!");
  return "狩猎时刻已激活！林珞女王的目光扫向了她的猎物...";
}
async function getRolePlayingCards$1() {
  if (!db) {
    console.error("CMS_DB_ERROR: getRolePlayingCards - db not available.");
    return [];
  }
  const tableName = getTableName("role_cards");
  try {
    const cards = db.prepare(`SELECT id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt FROM ${tableName} ORDER BY sort_order ASC, name ASC`).all();
    console.log(`CMS_DB: Retrieved ${cards.length} role playing cards.`);
    return cards;
  } catch (e) {
    console.error("CMS_DB_ERROR: Error getting role playing cards:", e);
    return [];
  }
}
async function getRolePlayingCardById$1(cardId) {
  if (!db) {
    console.error(`CMS_DB_ERROR: getRolePlayingCardById(${cardId}) - db not available.`);
    return null;
  }
  const tableName = getTableName("role_cards");
  try {
    const card = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardId);
    if (card) {
      console.log(`CMS_DB: Retrieved role playing card by ID ${cardId}.`);
    } else {
      console.warn(`CMS_DB_WARN: Role playing card with ID ${cardId} not found.`);
    }
    return card || null;
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error getting role playing card by ID ${cardId}:`, e);
    return null;
  }
}
async function addRolePlayingCard$1(cardData) {
  if (!db) {
    console.error("CMS_DB_ERROR: addRolePlayingCard - db not available.");
    return null;
  }
  const tableName = getTableName("role_cards");
  let finalIconPath = null;
  if (cardData.icon_base64) {
    try {
      finalIconPath = await saveOrUpdateImageAsset("role_cards", "icons", cardData.icon_base64, null);
    } catch (imgError) {
      console.error(`CMS_DB_ERROR: Failed to save icon for new role card:`, imgError.message);
    }
  }
  const newCard = {
    id: crypto.randomUUID(),
    name: cardData.name,
    description: cardData.description || "",
    icon_path: finalIconPath,
    initial_status_override_json: cardData.initial_status_override_json || "{}",
    persona_snippet_override: cardData.persona_snippet_override || "",
    sort_order: cardData.sort_order || 0,
    createdAt: (/* @__PURE__ */ new Date()).toISOString(),
    lastModifiedAt: (/* @__PURE__ */ new Date()).toISOString()
  };
  const stmt = db.prepare(`
        INSERT INTO ${tableName} (id, name, description, icon_path, initial_status_override_json, persona_snippet_override, sort_order, createdAt, lastModifiedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
  try {
    stmt.run(newCard.id, newCard.name, newCard.description, newCard.icon_path, newCard.initial_status_override_json, newCard.persona_snippet_override, newCard.sort_order, newCard.createdAt, newCard.lastModifiedAt);
    console.log(`CMS_DB: Added role playing card ${newCard.id}.`);
    return newCard;
  } catch (e) {
    console.error("CMS_DB_ERROR: Error adding role playing card:", e);
    return null;
  }
}
async function updateRolePlayingCard$1(cardData) {
  if (!db) {
    console.error(`CMS_DB_ERROR: updateRolePlayingCard(${cardData.id}) - db not available.`);
    return null;
  }
  const tableName = getTableName("role_cards");
  const existingCard = db.prepare(`SELECT icon_path FROM ${tableName} WHERE id = ?`).get(cardData.id);
  if (!existingCard) {
    console.error(`CMS_DB_ERROR: Role playing card ${cardData.id} not found for update.`);
    return null;
  }
  let finalIconPath = existingCard.icon_path;
  let iconProcessed = false;
  if (cardData.icon_base64 !== void 0) {
    try {
      finalIconPath = await saveOrUpdateImageAsset("role_cards", "icons", cardData.icon_base64, existingCard.icon_path);
      iconProcessed = true;
    } catch (imgError) {
      console.error(`CMS_DB_ERROR: Failed to update icon for role card ${cardData.id}:`, imgError.message);
    }
  }
  const fieldsToUpdate = [];
  const valuesToUpdate = [];
  if (cardData.name !== void 0) {
    fieldsToUpdate.push("name = ?");
    valuesToUpdate.push(cardData.name);
  }
  if (cardData.description !== void 0) {
    fieldsToUpdate.push("description = ?");
    valuesToUpdate.push(cardData.description);
  }
  if (iconProcessed) {
    fieldsToUpdate.push("icon_path = ?");
    valuesToUpdate.push(finalIconPath);
  }
  if (cardData.initial_status_override_json !== void 0) {
    fieldsToUpdate.push("initial_status_override_json = ?");
    valuesToUpdate.push(cardData.initial_status_override_json);
  }
  if (cardData.persona_snippet_override !== void 0) {
    fieldsToUpdate.push("persona_snippet_override = ?");
    valuesToUpdate.push(cardData.persona_snippet_override);
  }
  if (cardData.sort_order !== void 0) {
    fieldsToUpdate.push("sort_order = ?");
    valuesToUpdate.push(cardData.sort_order);
  }
  if (fieldsToUpdate.length === 0) {
    console.log(`CMS_DB: No data fields to update for role card ${cardData.id}.`);
    return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardData.id);
  }
  fieldsToUpdate.push("lastModifiedAt = ?");
  valuesToUpdate.push((/* @__PURE__ */ new Date()).toISOString());
  valuesToUpdate.push(cardData.id);
  const stmt = db.prepare(`UPDATE ${tableName} SET ${fieldsToUpdate.join(", ")} WHERE id = ?`);
  try {
    stmt.run(...valuesToUpdate);
    console.log(`CMS_DB: Updated role playing card ${cardData.id}.`);
    return db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`).get(cardData.id);
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error updating role playing card ${cardData.id}:`, e);
    return null;
  }
}
async function deleteRolePlayingCard$1(cardId) {
  if (!db) {
    console.error(`CMS_DB_ERROR: deleteRolePlayingCard(${cardId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const tableName = getTableName("role_cards");
  const userDataPath = app.getPath("userData");
  const cardToDelete = db.prepare(`SELECT icon_path FROM ${tableName} WHERE id = ?`).get(cardId);
  const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
  try {
    const result = stmt.run(cardId);
    if (result.changes > 0) {
      console.log(`CMS_DB: Deleted role playing card ${cardId}.`);
      if (cardToDelete && cardToDelete.icon_path) {
        const absoluteAssetPath = path.join(userDataPath, CMS_ASSETS_DIR_NAME, cardToDelete.icon_path);
        try {
          await fsPromises$1.access(absoluteAssetPath);
          await fsPromises$1.unlink(absoluteAssetPath);
          console.log(`CMS_DB: Deleted asset ${cardToDelete.icon_path} for card ${cardId}.`);
        } catch (deleteError) {
          if (deleteError.code === "ENOENT") {
            console.log(`CMS_DB_INFO: Asset ${cardToDelete.icon_path} not found, no need to delete.`);
          } else {
            console.warn(`CMS_DB_WARN: Failed to delete asset ${cardToDelete.icon_path} for deleted card ${cardId}.`, deleteError);
          }
        }
      }
      return { success: true };
    } else {
      console.warn(`CMS_DB_WARN: Role playing card ${cardId} not found for deletion.`);
      return { success: false, error: "Card not found." };
    }
  } catch (e) {
    console.error(`CMS_DB_ERROR: Error deleting role playing card ${cardId}:`, e);
    return { success: false, error: e.message };
  }
}
console.log("CMS_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("AI_LEARNING_LOG_DB_SERVICE_JS: File execution started.");
async function addLearningLog$1(logData) {
  if (!db) {
    console.error("AI_LEARNING_LOG_DB_ERROR: addLearningLog - db not available.");
    return null;
  }
  const newLog = {
    log_id: crypto.randomUUID(),
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    ...logData
  };
  if (typeof newLog.context_snapshot !== "string" && newLog.context_snapshot !== null && newLog.context_snapshot !== void 0) {
    newLog.context_snapshot = JSON.stringify(newLog.context_snapshot);
  }
  if (typeof newLog.ai_processing_summary !== "string" && newLog.ai_processing_summary !== null && newLog.ai_processing_summary !== void 0) {
    newLog.ai_processing_summary = JSON.stringify(newLog.ai_processing_summary);
  }
  if (typeof newLog.user_feedback_implicit_flags !== "string" && newLog.user_feedback_implicit_flags !== null && newLog.user_feedback_implicit_flags !== void 0) {
    newLog.user_feedback_implicit_flags = JSON.stringify(newLog.user_feedback_implicit_flags);
  }
  const stmt = db.prepare(`
        INSERT INTO ai_learning_logs (
            log_id, timestamp, ai_persona, task_type, triggering_input_summary, 
            context_snapshot, ai_processing_summary, ai_generated_output_summary, 
            user_feedback_explicit, user_feedback_implicit_flags, 
            success_metric_value, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
  try {
    stmt.run(
      newLog.log_id,
      newLog.timestamp,
      newLog.ai_persona,
      newLog.task_type,
      newLog.triggering_input_summary,
      newLog.context_snapshot,
      newLog.ai_processing_summary,
      newLog.ai_generated_output_summary,
      newLog.user_feedback_explicit,
      newLog.user_feedback_implicit_flags,
      newLog.success_metric_value,
      newLog.notes
    );
    console.log(`AI_LEARNING_LOG_DB: Added learning log ${newLog.log_id} for ${newLog.ai_persona} - ${newLog.task_type}.`);
    return newLog;
  } catch (error) {
    console.error("AI_LEARNING_LOG_DB_ERROR: Error adding learning log:", error);
    return null;
  }
}
async function getLearningLogs$1(filters = {}, limit = 50, offset = 0) {
  if (!db) {
    console.error("AI_LEARNING_LOG_DB_ERROR: getLearningLogs - db not available.");
    return [];
  }
  try {
    let query = "SELECT * FROM ai_learning_logs";
    const params = [];
    const conditions = [];
    if (filters.ai_persona && filters.ai_persona !== "all") {
      conditions.push("ai_persona = ?");
      params.push(filters.ai_persona);
    }
    if (filters.task_type && filters.task_type.trim() !== "") {
      conditions.push("task_type LIKE ?");
      params.push(`%${filters.task_type.trim()}%`);
    }
    if (filters.min_success_metric && typeof filters.min_success_metric === "number") {
      conditions.push("success_metric_value >= ?");
      params.push(filters.min_success_metric);
    }
    if (filters.max_success_metric && typeof filters.max_success_metric === "number") {
      conditions.push("success_metric_value <= ?");
      params.push(filters.max_success_metric);
    }
    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }
    query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?";
    params.push(limit, offset);
    const rows = db.prepare(query).all(...params);
    console.log(`AI_LEARNING_LOG_DB: Retrieved ${rows.length} learning logs.`);
    return rows.map((row) => ({
      ...row,
      context_snapshot: row.context_snapshot ? JSON.parse(row.context_snapshot) : null,
      ai_processing_summary: row.ai_processing_summary ? JSON.parse(row.ai_processing_summary) : null,
      user_feedback_implicit_flags: row.user_feedback_implicit_flags ? JSON.parse(row.user_feedback_implicit_flags) : null
    }));
  } catch (error) {
    console.error("AI_LEARNING_LOG_DB_ERROR: Error getting learning logs:", error);
    return [];
  }
}
console.log("AI_LEARNING_LOG_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("BODY_DEVELOPMENT_DB_SERVICE_JS: File execution started.");
function getBodyDevelopment$1(zone_id) {
  if (!db) {
    console.error(`BODY_DEV_DB_ERROR: getBodyDevelopment(${zone_id}) - db not available.`);
    return null;
  }
  try {
    const row = db.prepare("SELECT * FROM linluo_body_development WHERE zone_id = ?").get(zone_id);
    console.log(`BODY_DEV_DB: Retrieved body development for zone: ${zone_id}. Found: ${!!row}`);
    return row || null;
  } catch (error) {
    console.error(`BODY_DEV_DB_ERROR: Error getting body development for zone ${zone_id}:`, error);
    return null;
  }
}
function getAllBodyDevelopment$1() {
  if (!db) {
    console.error("BODY_DEV_DB_ERROR: getAllBodyDevelopment - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT * FROM linluo_body_development ORDER BY zone_id ASC").all();
    console.log(`BODY_DEV_DB: Retrieved ${rows.length} body development zones.`);
    return rows;
  } catch (error) {
    console.error("BODY_DEV_DB_ERROR: Error getting all body development zones:", error);
    return [];
  }
}
function updateBodyDevelopment$1(zone_id, pointsToAdd) {
  if (!db) {
    console.error(`BODY_DEV_DB_ERROR: updateBodyDevelopment(${zone_id}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const transaction = db.transaction(() => {
    const currentData = db.prepare("SELECT development_points FROM linluo_body_development WHERE zone_id = ?").get(zone_id);
    let oldPoints = 0;
    if (currentData) {
      oldPoints = currentData.development_points;
    } else {
      db.prepare("INSERT INTO linluo_body_development (zone_id, development_points, last_developed_at) VALUES (?, 0, ?)").run(zone_id, (/* @__PURE__ */ new Date()).toISOString());
    }
    const newTotalPoints = oldPoints + pointsToAdd;
    const now = (/* @__PURE__ */ new Date()).toISOString();
    const stmt = db.prepare("UPDATE linluo_body_development SET development_points = ?, last_developed_at = ? WHERE zone_id = ?");
    const info = stmt.run(newTotalPoints, now, zone_id);
    if (info.changes > 0) {
      console.log(`BODY_DEV_DB: Updated body development for zone ${zone_id}. Added ${pointsToAdd} points. New total: ${newTotalPoints}.`);
      return { success: true, zone_id, newPoints: newTotalPoints, oldPoints };
    } else {
      console.warn(`BODY_DEV_DB_WARN: updateBodyDevelopment - No rows updated for zone ${zone_id}. This might indicate an issue.`);
      return { success: false, error: `Zone ${zone_id} not found or update failed.` };
    }
  });
  try {
    return transaction();
  } catch (error) {
    console.error(`BODY_DEV_DB_ERROR: Error updating body development for zone ${zone_id}:`, error);
    return { success: false, error: error.message };
  }
}
console.log("BODY_DEVELOPMENT_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("ACHIEVEMENT_DB_SERVICE_JS: File execution started.");
function getAllAchievements$1() {
  if (!db) {
    console.error("ACHIEVEMENT_DB_ERROR: getAllAchievements - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT * FROM achievements ORDER BY title ASC").all();
    console.log(`ACHIEVEMENT_DB: Retrieved ${rows.length} achievements definitions.`);
    return rows;
  } catch (error) {
    console.error("ACHIEVEMENT_DB_ERROR: Error getting all achievements definitions:", error);
    return [];
  }
}
function getAchievementById$1(achievementId) {
  if (!db) {
    console.error(`ACHIEVEMENT_DB_ERROR: getAchievementById(${achievementId}) - db not available.`);
    return null;
  }
  try {
    const row = db.prepare("SELECT * FROM achievements WHERE id = ?").get(achievementId);
    return row || null;
  } catch (error) {
    console.error(`ACHIEVEMENT_DB_ERROR: Error getting achievement by ID ${achievementId}:`, error);
    return null;
  }
}
function getUserAchievements$1(userId) {
  if (!db) {
    console.error(`ACHIEVEMENT_DB_ERROR: getUserAchievements(${userId}) - db not available.`);
    return [];
  }
  try {
    const rows = db.prepare("SELECT ua.*, a.title, a.description, a.icon_path FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = ? ORDER BY ua.unlocked_at DESC").all(userId);
    console.log(`ACHIEVEMENT_DB: Retrieved ${rows.length} achievements for user ${userId}.`);
    return rows;
  } catch (error) {
    console.error(`ACHIEVEMENT_DB_ERROR: Error getting achievements for user ${userId}:`, error);
    return [];
  }
}
function unlockAchievement$1(userId, achievementId) {
  if (!db) {
    console.error(`ACHIEVEMENT_DB_ERROR: unlockAchievement(${userId}, ${achievementId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const achievementDef = db.prepare("SELECT id FROM achievements WHERE id = ?").get(achievementId);
  if (!achievementDef) {
    console.warn(`ACHIEVEMENT_DB_WARN: Attempted to unlock non-existent achievement ID: ${achievementId}`);
    return { success: false, error: "Achievement definition not found." };
  }
  const existingUnlock = db.prepare("SELECT id FROM user_achievements WHERE user_id = ? AND achievement_id = ?").get(userId, achievementId);
  if (existingUnlock) {
    console.log(`ACHIEVEMENT_DB: Achievement ${achievementId} already unlocked for user ${userId}.`);
    return { success: true, message: "Already unlocked." };
  }
  const newUnlockId = crypto.randomUUID();
  const unlockedAt = (/* @__PURE__ */ new Date()).toISOString();
  const stmt = db.prepare("INSERT INTO user_achievements (id, user_id, achievement_id, unlocked_at) VALUES (?, ?, ?, ?)");
  try {
    stmt.run(newUnlockId, userId, achievementId, unlockedAt);
    console.log(`ACHIEVEMENT_DB: Unlocked achievement ${achievementId} for user ${userId}.`);
    return { success: true };
  } catch (error) {
    console.error(`ACHIEVEMENT_DB_ERROR: Error unlocking achievement ${achievementId} for user ${userId}:`, error);
    return { success: false, error: error.message };
  }
}
console.log("ACHIEVEMENT_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("TASK_DB_SERVICE_JS: File execution started.");
function getTaskById$1(taskId) {
  if (!db) {
    console.error(`TASK_DB_ERROR: getTaskById(${taskId}) - db not available.`);
    return null;
  }
  try {
    const task = db.prepare("SELECT * FROM tasks WHERE task_id = ?").get(taskId);
    if (task) {
      task.resource_links = getResourceLinksForTask$1(taskId);
      console.log(`TASK_DB: Retrieved task by ID ${taskId}.`);
      return task;
    }
    console.log(`TASK_DB: Task with ID ${taskId} not found.`);
    return null;
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error getting task by ID ${taskId}:`, error);
    return null;
  }
}
function getTasksByProjectId$1(projectId, filters = {}, sortOptions = { field: "created_at", order: "DESC" }) {
  if (!db) {
    console.error(`TASK_DB_ERROR: getTasksByProjectId(${projectId}) - db not available.`);
    return [];
  }
  try {
    let query = "SELECT * FROM tasks WHERE project_id = ?";
    const params = [projectId];
    if (filters.status) {
      query += " AND status = ?";
      params.push(filters.status);
    }
    if (filters.priority !== void 0) {
      query += " AND priority = ?";
      params.push(filters.priority);
    }
    const validSortFields = ["created_at", "updated_at", "due_date", "priority", "title", "status"];
    const sortField = validSortFields.includes(sortOptions.field) ? sortOptions.field : "created_at";
    const sortOrder = sortOptions.order === "ASC" ? "ASC" : "DESC";
    query += ` ORDER BY ${sortField} ${sortOrder}`;
    const tasks = db.prepare(query).all(...params);
    tasks.forEach((task) => {
      task.resource_links = getResourceLinksForTask$1(task.task_id);
    });
    console.log(`TASK_DB: Retrieved ${tasks.length} tasks for project ${projectId}.`);
    return tasks;
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error getting tasks for project ${projectId}:`, error);
    return [];
  }
}
function getTasksByStatus$1(projectId, statusArray) {
  if (!db) {
    console.error(`TASK_DB_ERROR: getTasksByStatus(${projectId}, ${statusArray}) - db not available.`);
    return [];
  }
  if (!Array.isArray(statusArray) || statusArray.length === 0) {
    console.warn(`TASK_DB_WARN: getTasksByStatus called with empty or invalid statusArray for project ${projectId}.`);
    return [];
  }
  try {
    const placeholders = statusArray.map(() => "?").join(",");
    const query = `SELECT * FROM tasks WHERE project_id = ? AND status IN (${placeholders}) ORDER BY priority ASC, created_at DESC`;
    const params = [projectId, ...statusArray];
    const tasks = db.prepare(query).all(...params);
    tasks.forEach((task) => {
      task.resource_links = getResourceLinksForTask$1(task.task_id);
    });
    console.log(`TASK_DB: Retrieved ${tasks.length} tasks for project ${projectId} with statuses [${statusArray.join(", ")}].`);
    return tasks;
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error getting tasks by status for project ${projectId}:`, error);
    return [];
  }
}
function addTask$1(taskData) {
  if (!db) {
    console.error("TASK_DB_ERROR: addTask - db not available.");
    return null;
  }
  const now = (/* @__PURE__ */ new Date()).toISOString();
  const newTask = {
    task_id: crypto.randomUUID(),
    project_id: taskData.project_id,
    title: taskData.title,
    description: taskData.description || null,
    status: taskData.status || "todo",
    priority: taskData.priority === void 0 ? 1 : taskData.priority,
    assignee_id: taskData.assignee_id || null,
    created_at: now,
    updated_at: now,
    due_date: taskData.due_date || null,
    parent_task_id: taskData.parent_task_id || null
  };
  const stmt = db.prepare("INSERT INTO tasks (task_id, project_id, title, description, status, priority, assignee_id, created_at, updated_at, due_date, parent_task_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(newTask.task_id, newTask.project_id, newTask.title, newTask.description, newTask.status, newTask.priority, newTask.assignee_id, newTask.created_at, newTask.updated_at, newTask.due_date, newTask.parent_task_id);
    console.log(`TASK_DB: Added task ${newTask.task_id} for project ${newTask.project_id}.`);
    return { ...newTask, resource_links: [] };
  } catch (error) {
    console.error("TASK_DB_ERROR: Error adding task:", error);
    return null;
  }
}
function updateTask$1(taskId, updates) {
  if (!db) {
    console.error(`TASK_DB_ERROR: updateTask(${taskId}) - db not available.`);
    return null;
  }
  const allowedFields = ["title", "description", "status", "priority", "assignee_id", "due_date", "parent_task_id"];
  const fieldsToUpdate = [];
  const valuesToUpdate = [];
  for (const field of allowedFields) {
    if (updates[field] !== void 0) {
      fieldsToUpdate.push(`${field} = ?`);
      valuesToUpdate.push(updates[field]);
    }
  }
  if (fieldsToUpdate.length === 0) {
    console.log(`TASK_DB: No valid fields to update for task ${taskId}.`);
    return getTaskById$1(taskId);
  }
  fieldsToUpdate.push("updated_at = ?");
  valuesToUpdate.push((/* @__PURE__ */ new Date()).toISOString());
  valuesToUpdate.push(taskId);
  const stmt = db.prepare(`UPDATE tasks SET ${fieldsToUpdate.join(", ")} WHERE task_id = ?`);
  try {
    const info = stmt.run(...valuesToUpdate);
    if (info.changes > 0) {
      console.log(`TASK_DB: Updated task ${taskId}.`);
      return getTaskById$1(taskId);
    }
    console.warn(`TASK_DB_WARN: Task ${taskId} not found for update or no changes made.`);
    return null;
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error updating task ${taskId}:`, error);
    return null;
  }
}
function deleteTask$1(taskId) {
  if (!db) {
    console.error(`TASK_DB_ERROR: deleteTask(${taskId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const transaction = db.transaction(() => {
    const info = db.prepare("DELETE FROM tasks WHERE task_id = ?").run(taskId);
    if (info.changes > 0) {
      console.log(`TASK_DB: Deleted task ${taskId} and its resource links (via CASCADE).`);
      return { success: true };
    }
    console.warn(`TASK_DB_WARN: Task ${taskId} not found for deletion.`);
    return { success: false, error: "Task not found." };
  });
  try {
    return transaction();
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error deleting task ${taskId}:`, error);
    return { success: false, error: error.message };
  }
}
function addResourceLinkToTask$1(taskId, resourceData) {
  if (!db) {
    console.error("TASK_DB_ERROR: addResourceLinkToTask - db not available.");
    return null;
  }
  const newLink = {
    link_id: crypto.randomUUID(),
    task_id: taskId,
    resource_type: resourceData.resource_type,
    resource_identifier: resourceData.resource_identifier,
    resource_name: resourceData.resource_name,
    // Added
    description: resourceData.description
    // Added
  };
  const stmt = db.prepare("INSERT INTO task_resource_links (link_id, task_id, resource_type, resource_identifier, resource_name, description) VALUES (?, ?, ?, ?, ?, ?)");
  try {
    stmt.run(newLink.link_id, newLink.task_id, newLink.resource_type, newLink.resource_identifier, newLink.resource_name, newLink.description);
    console.log(`TASK_DB: Added resource link ${newLink.link_id} to task ${taskId}.`);
    return newLink;
  } catch (error) {
    console.error("TASK_DB_ERROR: Error adding resource link to task:", error);
    return null;
  }
}
function getResourceLinksForTask$1(taskId) {
  if (!db) {
    console.error(`TASK_DB_ERROR: getResourceLinksForTask(${taskId}) - db not available.`);
    return [];
  }
  try {
    const links = db.prepare("SELECT * FROM task_resource_links WHERE task_id = ?").all(taskId);
    return links;
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error getting resource links for task ${taskId}:`, error);
    return [];
  }
}
function removeResourceLinkFromTask$1(linkId) {
  if (!db) {
    console.error(`TASK_DB_ERROR: removeResourceLinkFromTask(${linkId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  const stmt = db.prepare("DELETE FROM task_resource_links WHERE link_id = ?");
  try {
    const info = stmt.run(linkId);
    if (info.changes > 0) {
      console.log(`TASK_DB: Removed resource link ${linkId}.`);
      return { success: true };
    }
    console.warn(`TASK_DB_WARN: Resource link ${linkId} not found for deletion.`);
    return { success: false, error: "Resource link not found." };
  } catch (error) {
    console.error(`TASK_DB_ERROR: Error removing resource link ${linkId}:`, error);
    return { success: false, error: error.message };
  }
}
console.log("TASK_DB_SERVICE_JS: File execution finished. Exports configured.");
console.log("ORGANIZATION_SERVICE_TS: File execution started.");
let loadedCharacters = [];
let loadedPosts = [];
let isInitialized$1 = false;
function initializeOrganizationService() {
  if (isInitialized$1) {
    console.log("ORGANIZATION_SERVICE: Already initialized.");
    return;
  }
  console.log("ORGANIZATION_SERVICE: Initializing - assigning default characters and posts...");
  try {
    loadedCharacters = DEFAULT_CHARACTERS;
    loadedPosts = DEFAULT_POSTS;
    isInitialized$1 = true;
    console.log(`ORGANIZATION_SERVICE: Initialization complete. Using ${loadedPosts.length} posts and ${loadedCharacters.length} characters from constants.`);
  } catch (error) {
    console.error("ORGANIZATION_SERVICE_ERROR: Failed to initialize by assigning constants:", error.message);
    loadedCharacters = [];
    loadedPosts = [];
  }
}
function ensureInitialized() {
  if (!isInitialized$1) {
    console.warn("ORGANIZATION_SERVICE: Service accessed before `initializeOrganizationService` completed. Data may be incomplete. Ensure `initializeOrganizationService` is called at application startup.");
    if (!isInitialized$1) {
      initializeOrganizationService();
      if (!isInitialized$1) {
        throw new Error("OrganizationService failed to initialize properly.");
      }
    }
  }
}
function getPosts$1() {
  ensureInitialized();
  console.log("ORGANIZATION_SERVICE: getPosts called, returning from memory.");
  return loadedPosts;
}
async function getCharacters$1() {
  ensureInitialized();
  console.log("ORGANIZATION_SERVICE: getCharacters called, returning from memory (async wrapper).");
  return Promise.resolve(loadedCharacters);
}
function getAssignments$1() {
  ensureInitialized();
  if (!db) {
    console.error("ORGANIZATION_SERVICE_ERROR: getAssignments - db not available.");
    return [];
  }
  try {
    const rows = db.prepare("SELECT post_id, character_id FROM assignments").all();
    console.log(`ORGANIZATION_SERVICE: Retrieved ${rows.length} assignments from DB.`);
    return rows;
  } catch (error) {
    console.error("ORGANIZATION_SERVICE_ERROR: Error getting assignments from DB:", error.message);
    return [];
  }
}
function getAssignmentByPostId$1(postId) {
  ensureInitialized();
  if (!db) {
    console.error(`ORGANIZATION_SERVICE_ERROR: getAssignmentByPostId(${postId}) - db not available.`);
    return null;
  }
  try {
    const row = db.prepare("SELECT character_id FROM assignments WHERE post_id = ?").get(postId);
    if (row) {
      console.log(`ORGANIZATION_SERVICE: Assignment for post ${postId} is character ${row.character_id}.`);
      return row.character_id;
    }
    console.log(`ORGANIZATION_SERVICE: No assignment found for post ${postId} in DB.`);
    return null;
  } catch (error) {
    console.error(`ORGANIZATION_SERVICE_ERROR: Error getting assignment for post ${postId} from DB:`, error.message);
    return null;
  }
}
function setAssignment$1(postId, characterId) {
  ensureInitialized();
  if (!db) {
    console.error(`ORGANIZATION_SERVICE_ERROR: setAssignment(${postId}, ${characterId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  if (!loadedPosts.find((p) => p.id === postId)) {
    return { success: false, error: `Invalid post_id: ${postId}. Not found in loaded posts.` };
  }
  if (!loadedCharacters.find((c) => c.id === characterId)) {
    return { success: false, error: `Invalid character_id: ${characterId}. Not found in loaded characters.` };
  }
  try {
    const stmt = db.prepare("INSERT OR REPLACE INTO assignments (post_id, character_id) VALUES (?, ?)");
    stmt.run(postId, characterId);
    console.log(`ORGANIZATION_SERVICE: Set assignment for post ${postId} to character ${characterId} in DB.`);
    return { success: true, assignment: { post_id: postId, character_id: characterId } };
  } catch (error) {
    console.error("ORGANIZATION_SERVICE_ERROR: Error setting assignment in DB:", error.message);
    return { success: false, error: error.message };
  }
}
console.log("ORGANIZATION_SERVICE_TS: File execution finished. Service functions defined.");
const organizationService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  getAssignmentByPostId: getAssignmentByPostId$1,
  getAssignments: getAssignments$1,
  getCharacters: getCharacters$1,
  getPosts: getPosts$1,
  initializeOrganizationService,
  setAssignment: setAssignment$1
}, Symbol.toStringTag, { value: "Module" }));
console.log("DATABASE_SERVICE_JS (Aggregator): File execution started.");
const initializeDatabaseService = initializeDatabaseService$1;
const closeDatabaseConnection = closeDatabaseConnection$1;
const getSettings = getSettings$1;
const saveSettings = saveSettings$1;
const getAgentCoreSetting = getAgentCoreSetting$1;
const getAllAgentCoreSettings = getAllAgentCoreSettings$1;
const saveAgentCoreSetting = saveAgentCoreSetting$1;
const getAbsoluteTerritoryPassword = getAbsoluteTerritoryPassword$1;
const setAbsoluteTerritoryPassword = setAbsoluteTerritoryPassword$1;
const verifyAbsoluteTerritoryPassword = verifyAbsoluteTerritoryPassword$1;
const getAllProjects = getAllProjects$1;
const getProjectById = getProjectById$1;
const addProject = addProject$1;
const updateProject = updateProject$1;
const deleteProject = deleteProject$1;
const duplicateProject = duplicateProject$1;
const addNoteToPouch = addNoteToPouch$1;
const updateNoteInPouch = updateNoteInPouch$1;
const deleteNoteFromPouch = deleteNoteFromPouch$1;
const updateProjectMindMap = updateProjectMindMap$1;
const addProjectKnowledgeTome = addProjectKnowledgeTome$1;
const updateProjectKnowledgeTome = updateProjectKnowledgeTome$1;
const deleteProjectKnowledgeTome = deleteProjectKnowledgeTome$1;
const addProjectKnowledgeCategory = addProjectKnowledgeCategory$1;
const removeProjectKnowledgeCategory = removeProjectKnowledgeCategory$1;
const getAllDevelopmentTasks = getAllDevelopmentTasks$1;
const addDevelopmentTask = addDevelopmentTask$1;
const createDevelopmentTaskFromChat = createDevelopmentTaskFromChat$1;
const deleteDevelopmentTask = deleteDevelopmentTask$1;
const updateDevelopmentTaskContextFiles = updateDevelopmentTaskContextFiles$1;
const updateDevelopmentTaskGeneratedCode = updateDevelopmentTaskGeneratedCode$1;
const saveChatMessage = saveChatMessage$1;
const updateChatMessage = updateChatMessage$1;
const getInitialChatMessages = getInitialChatMessages$1;
const getOlderChatMessages = getOlderChatMessages$1;
const summarizeAndReplaceMessages = summarizeAndReplaceMessages$1;
const getCoreMemories = getCoreMemories$1;
const getAllCoreMemories = getAllCoreMemories$1;
const addCoreMemory = addCoreMemory$1;
const addCoreMemoryFromChat = addCoreMemoryFromChat$1;
const updateCoreMemory = updateCoreMemory$1;
const deleteCoreMemory = deleteCoreMemory$1;
const getAbsoluteTerritoryMessages = getAbsoluteTerritoryMessages$1;
const addAbsoluteTerritoryMessage = addAbsoluteTerritoryMessage$1;
const clearAbsoluteTerritoryHistory = clearAbsoluteTerritoryHistory$1;
const findRelevantMemories = findRelevantMemories$1;
const getCoreMemoryById = getCoreMemoryById$1;
const getAllGlobalKnowledgeTomes = getAllGlobalKnowledgeTomes$1;
const addGlobalKnowledgeTome = addGlobalKnowledgeTome$1;
const updateGlobalKnowledgeTome = updateGlobalKnowledgeTome$1;
const deleteGlobalKnowledgeTome = deleteGlobalKnowledgeTome$1;
const getAllGlobalQuickCommands = getAllGlobalQuickCommands$1;
const addGlobalQuickCommand = addGlobalQuickCommand$1;
const updateGlobalQuickCommand = updateGlobalQuickCommand$1;
const deleteGlobalQuickCommand = deleteGlobalQuickCommand$1;
const clearKnowledgeIndexForProject = clearKnowledgeIndexForProject$1;
const addKnowledgeIndexEntry = addKnowledgeIndexEntry$1;
const getKnowledgeIndexEntriesForProject = getKnowledgeIndexEntriesForProject$1;
const getCMSItems = getCMSItems$1;
const addCMSItem = addCMSItem$1;
const updateCMSItem = updateCMSItem$1;
const deleteCMSItem = deleteCMSItem$1;
const triggerHuntingTime = triggerHuntingTime$1;
const getRolePlayingCards = getRolePlayingCards$1;
const getRolePlayingCardById = getRolePlayingCardById$1;
const addRolePlayingCard = addRolePlayingCard$1;
const updateRolePlayingCard = updateRolePlayingCard$1;
const deleteRolePlayingCard = deleteRolePlayingCard$1;
const addLearningLog = addLearningLog$1;
const getLearningLogs = getLearningLogs$1;
const getBodyDevelopment = getBodyDevelopment$1;
const getAllBodyDevelopment = getAllBodyDevelopment$1;
const updateBodyDevelopment = updateBodyDevelopment$1;
const getAllAchievements = getAllAchievements$1;
const getUserAchievements = getUserAchievements$1;
const unlockAchievement = unlockAchievement$1;
const getAchievementById = getAchievementById$1;
const getTaskById = getTaskById$1;
const getTasksByProjectId = getTasksByProjectId$1;
const getTasksByStatus = getTasksByStatus$1;
const addTask = addTask$1;
const updateTask = updateTask$1;
const deleteTask = deleteTask$1;
const addResourceLinkToTask = addResourceLinkToTask$1;
const getResourceLinksForTask = getResourceLinksForTask$1;
const removeResourceLinkFromTask = removeResourceLinkFromTask$1;
const getPosts = getPosts$1;
const getCharacters = getCharacters$1;
const getAssignments = getAssignments$1;
const getAssignmentByPostId = getAssignmentByPostId$1;
const setAssignment = setAssignment$1;
console.log("DATABASE_SERVICE_JS (Aggregator): All sub-modules aggregated and exported.");
const dbService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  addAbsoluteTerritoryMessage,
  addCMSItem,
  addCoreMemory,
  addCoreMemoryFromChat,
  addDevelopmentTask,
  addGlobalKnowledgeTome,
  addGlobalQuickCommand,
  addKnowledgeIndexEntry,
  addLearningLog,
  addNoteToPouch,
  addProject,
  addProjectKnowledgeCategory,
  addProjectKnowledgeTome,
  addResourceLinkToTask,
  addRolePlayingCard,
  addTask,
  clearAbsoluteTerritoryHistory,
  clearKnowledgeIndexForProject,
  closeDatabaseConnection,
  createDevelopmentTaskFromChat,
  deleteCMSItem,
  deleteCoreMemory,
  deleteDevelopmentTask,
  deleteGlobalKnowledgeTome,
  deleteGlobalQuickCommand,
  deleteNoteFromPouch,
  deleteProject,
  deleteProjectKnowledgeTome,
  deleteRolePlayingCard,
  deleteTask,
  duplicateProject,
  findRelevantMemories,
  getAbsoluteTerritoryMessages,
  getAbsoluteTerritoryPassword,
  getAchievementById,
  getAgentCoreSetting,
  getAllAchievements,
  getAllAgentCoreSettings,
  getAllBodyDevelopment,
  getAllCoreMemories,
  getAllDevelopmentTasks,
  getAllGlobalKnowledgeTomes,
  getAllGlobalQuickCommands,
  getAllProjects,
  getAssignmentByPostId,
  getAssignments,
  getBodyDevelopment,
  getCMSItems,
  getCharacters,
  getCoreMemories,
  getCoreMemoryById,
  getInitialChatMessages,
  getKnowledgeIndexEntriesForProject,
  getLearningLogs,
  getOlderChatMessages,
  getPosts,
  getProjectById,
  getResourceLinksForTask,
  getRolePlayingCardById,
  getRolePlayingCards,
  getSettings,
  getTaskById,
  getTasksByProjectId,
  getTasksByStatus,
  getUserAchievements,
  initializeDatabaseService,
  removeProjectKnowledgeCategory,
  removeResourceLinkFromTask,
  saveAgentCoreSetting,
  saveChatMessage,
  saveSettings,
  setAbsoluteTerritoryPassword,
  setAssignment,
  summarizeAndReplaceMessages,
  triggerHuntingTime,
  unlockAchievement,
  updateBodyDevelopment,
  updateCMSItem,
  updateChatMessage,
  updateCoreMemory,
  updateDevelopmentTaskContextFiles,
  updateDevelopmentTaskGeneratedCode,
  updateGlobalKnowledgeTome,
  updateGlobalQuickCommand,
  updateNoteInPouch,
  updateProject,
  updateProjectKnowledgeTome,
  updateProjectMindMap,
  updateRolePlayingCard,
  updateTask,
  verifyAbsoluteTerritoryPassword
}, Symbol.toStringTag, { value: "Module" }));
console.log("RAG_SERVICE_JS: File execution started (ESM).");
const DEFAULT_CHUNK_SIZE = 1e3;
const DEFAULT_OVERLAP_SIZE = 200;
const NON_INDEXABLE_EXTENSIONS = /* @__PURE__ */ new Set([".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp", ".exe", ".dll", ".so", ".dylib", ".zip", ".tar", ".gz", ".rar", ".7z", ".mp3", ".mp4", ".avi", ".mov", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".iso", ".dmg", ".pkg"]);
const NON_INDEXABLE_DIRS = /* @__PURE__ */ new Set(["node_modules", ".git", ".vscode", ".idea", "__pycache__"]);
let dbAggregatorServiceInstance;
function initializeRagService(dbService2, userDataPath) {
  dbAggregatorServiceInstance = dbService2;
  console.log("RAG Service initialized with aggregated DB Service.");
}
function chunkText(text, options = {}) {
  const chunkSize = options.chunkSize || DEFAULT_CHUNK_SIZE;
  const overlapSize = options.overlapSize || DEFAULT_OVERLAP_SIZE;
  const chunks = [];
  let i = 0;
  while (i < text.length) {
    const end = Math.min(i + chunkSize, text.length);
    chunks.push(text.substring(i, end));
    i += chunkSize - overlapSize;
    if (i + overlapSize >= text.length && end === text.length) break;
  }
  return chunks.filter((chunk) => chunk.trim() !== "");
}
async function getEmbeddingForChunk(textChunk, apiKey, embeddingModelName, taskType, title) {
  const result = await embedContentInternal(textChunk, taskType, title, apiKey);
  if (typeof result === "string") {
    console.error("RAG Service (getEmbeddingForChunk) received error string from aiKernelService:", result);
    throw new Error(result);
  }
  if (result === null) {
    console.warn("RAG Service (getEmbeddingForChunk): Received null vector from aiKernelService. Text chunk (first 100 chars):", textChunk ? textChunk.substring(0, 100) + "..." : "EMPTY_CHUNK_TEXT");
  }
  return result;
}
async function* streamFiles(dirPath) {
  try {
    const dirents = await fsPromises$1.readdir(dirPath, { withFileTypes: true });
    for (const dirent of dirents) {
      const fullPath = path.join(dirPath, dirent.name);
      if (dirent.isDirectory()) {
        if (!NON_INDEXABLE_DIRS.has(dirent.name) && !dirent.name.startsWith(".")) {
          yield* streamFiles(fullPath);
        }
      } else {
        const ext = path.extname(dirent.name).toLowerCase();
        if (!NON_INDEXABLE_EXTENSIONS.has(ext)) {
          yield fullPath;
        }
      }
    }
  } catch (error) {
    console.warn("RAG_SERVICE: Could not read directory " + dirPath + ": " + error.message + ". Skipping.");
  }
}
async function indexFileContent(projectId, filePath, fileContent, options) {
  const { apiKey, embeddingModelName } = options;
  console.log(`RAG_SERVICE: Starting single file content indexing for project ${projectId}, file: ${filePath}`);
  if (!dbAggregatorServiceInstance) return { success: false, error: "Database aggregator service not initialized." };
  if (!apiKey || !apiKey.trim()) return { success: false, error: "API Key is missing for file content indexing." };
  if (!embeddingModelName) return { success: false, error: "Embedding model name not provided." };
  if (!fileContent || typeof fileContent !== "string" || !fileContent.trim()) return { success: false, error: "File content is empty or invalid." };
  try {
    const clearStmt = dbAggregatorServiceInstance.db.prepare("DELETE FROM knowledge_index WHERE source_project_id = ? AND source_file_path = ?");
    const clearResult = clearStmt.run(projectId, filePath);
    console.log(`RAG_SERVICE: Cleared ${clearResult.changes} old index entries for file: ${filePath}`);
    const textChunks = chunkText(fileContent);
    if (textChunks.length === 0) {
      return { success: true, message: "No indexable chunks generated from file content.", chunksCreated: 0 };
    }
    let chunksCreated = 0;
    for (let i = 0; i < textChunks.length; i++) {
      const chunk = textChunks[i];
      const embeddingVector = await getEmbeddingForChunk(chunk, apiKey, embeddingModelName, "RETRIEVAL_DOCUMENT", path.basename(filePath));
      if (embeddingVector && Array.isArray(embeddingVector)) {
        const fileExtension = path.extname(filePath).toLowerCase().substring(1);
        const metadata = {
          file_type: fileExtension || "unknown",
          chunk_index: i,
          original_file_name: path.basename(filePath)
        };
        await dbAggregatorServiceInstance.addKnowledgeIndexEntry({
          id: crypto$1.randomUUID(),
          source_project_id: projectId,
          source_file_path: filePath,
          chunk_text: chunk,
          chunk_vector: JSON.stringify(embeddingVector),
          metadata: JSON.stringify(metadata),
          indexed_at: (/* @__PURE__ */ new Date()).toISOString()
        });
        chunksCreated++;
      } else if (embeddingVector === null) {
        console.warn(`RAG_SERVICE (indexFileContent): Skipping chunk in ${filePath} because embedding was null.`);
      } else {
        console.warn(`RAG_SERVICE (indexFileContent): Skipping chunk in ${filePath} due to invalid embedding vector type:`, typeof embeddingVector);
      }
    }
    const successMessage = `File content indexing complete for ${filePath}. Created ${chunksCreated} index entries.`;
    console.log("RAG_SERVICE: " + successMessage);
    return { success: true, message: successMessage, chunksCreated };
  } catch (error) {
    console.error(`RAG_SERVICE: Error during file content indexing for ${filePath}:`, error);
    return { success: false, error: error.message || "An unexpected error occurred during file content indexing." };
  }
}
async function runProjectIndexing(projectId, projectPath, options) {
  const { apiKey, embeddingModelName } = options;
  console.log("RAG_SERVICE: Starting indexing for project " + projectId + " at path " + projectPath + " using model " + embeddingModelName);
  if (!dbAggregatorServiceInstance) {
    return { success: false, error: "Database aggregator service not initialized in RAG service." };
  }
  if (!apiKey || apiKey.trim() === "") {
    return { success: false, error: "API Key is missing for project indexing." };
  }
  if (!embeddingModelName) {
    return { success: false, error: "Embedding model name not provided for project indexing." };
  }
  try {
    await fsPromises$1.access(projectPath, fsPromises$1.constants.R_OK);
  } catch (err) {
    return { success: false, error: 'Project path "' + projectPath + '" does not exist or is not accessible.' };
  }
  try {
    await dbAggregatorServiceInstance.clearKnowledgeIndexForProject(projectId);
    console.log("RAG_SERVICE: Cleared old index for project " + projectId + ".");
    let filesProcessed = 0;
    let chunksCreated = 0;
    for await (const filePath of streamFiles(projectPath)) {
      try {
        console.log("RAG_SERVICE: Processing file: " + filePath);
        const content = await fsPromises$1.readFile(filePath, "utf-8");
        if (!content.trim()) {
          console.log("RAG_SERVICE: Skipping empty file: " + filePath);
          continue;
        }
        const textChunks = chunkText(content);
        if (textChunks.length === 0) {
          console.log("RAG_SERVICE: No chunks generated for file: " + filePath);
          continue;
        }
        filesProcessed++;
        for (let i = 0; i < textChunks.length; i++) {
          const chunk = textChunks[i];
          const embeddingVector = await getEmbeddingForChunk(chunk, apiKey, embeddingModelName, "RETRIEVAL_DOCUMENT", path.basename(filePath));
          if (embeddingVector && Array.isArray(embeddingVector)) {
            const fileExtension = path.extname(filePath).toLowerCase().substring(1);
            const metadata = {
              file_type: fileExtension || "unknown",
              chunk_index: i,
              original_file_name: path.basename(filePath)
            };
            await dbAggregatorServiceInstance.addKnowledgeIndexEntry({
              id: crypto$1.randomUUID(),
              source_project_id: projectId,
              source_file_path: filePath,
              chunk_text: chunk,
              chunk_vector: JSON.stringify(embeddingVector),
              metadata: JSON.stringify(metadata),
              indexed_at: (/* @__PURE__ */ new Date()).toISOString()
            });
            chunksCreated++;
          } else if (embeddingVector === null) {
            console.warn("RAG_SERVICE: Skipping chunk in " + filePath + " because embedding was null (likely empty text).");
          } else {
            console.warn("RAG_SERVICE: Skipping chunk in " + filePath + " due to invalid embedding vector type:", typeof embeddingVector);
          }
        }
      } catch (fileError) {
        console.error("RAG_SERVICE: Error processing file " + filePath + ":", fileError.message);
        if (fileError.message.toLowerCase().includes("api key") || fileError.message.toLowerCase().includes("permission") || fileError.message.toLowerCase().includes("content_invalid")) {
          throw fileError;
        }
      }
    }
    const successMessage = "Project indexing complete for " + projectId + ". Processed " + filesProcessed + " files, created " + chunksCreated + " index entries.";
    console.log("RAG_SERVICE: " + successMessage);
    return { success: true, message: successMessage };
  } catch (error) {
    console.error("RAG_SERVICE: Error during project indexing for " + projectId + ":", error);
    return { success: false, error: error.message || "An unexpected error occurred during indexing." };
  }
}
function cosineSimilarity(vecA, vecB) {
  if (!vecA || !vecB || vecA.length !== vecB.length || vecA.length === 0) {
    console.warn("RAG_SERVICE: Cosine similarity: Invalid input vectors.", { vecALength: vecA == null ? void 0 : vecA.length, vecBLength: vecB == null ? void 0 : vecB.length });
    return 0;
  }
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }
  if (normA === 0 || normB === 0) {
    return 0;
  }
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}
async function retrieveRelevantChunks(queryText, projectId, apiKey, embeddingModelName, topK = 5) {
  console.log('RAG_SERVICE: Retrieving chunks for query "' + queryText + '" in project ' + projectId + " using model " + embeddingModelName);
  if (!dbAggregatorServiceInstance) {
    return { success: false, error: "Database aggregator service not initialized in RAG service." };
  }
  if (!apiKey || apiKey.trim() === "") {
    return { success: false, error: "API Key is missing for chunk retrieval." };
  }
  if (!embeddingModelName) {
    return { success: false, error: "Embedding model name not provided for chunk retrieval." };
  }
  try {
    const queryVectorResult = await getEmbeddingForChunk(queryText, apiKey, embeddingModelName, "RETRIEVAL_QUERY", void 0);
    if (queryVectorResult === null) {
      console.warn("RAG_SERVICE: Query text resulted in null vector, likely empty query. Returning no results.");
      return { success: true, results: [], message: "Query text was empty or invalid." };
    }
    const queryVector = queryVectorResult;
    const allChunksRaw = await dbAggregatorServiceInstance.getKnowledgeIndexEntriesForProject(projectId);
    if (!allChunksRaw || allChunksRaw.length === 0) {
      return { success: true, results: [], message: "No knowledge index entries found for this project." };
    }
    const scoredChunks = [];
    for (const rawChunk of allChunksRaw) {
      try {
        const chunkVector = rawChunk.chunk_vector ? JSON.parse(rawChunk.chunk_vector) : null;
        const metadata = rawChunk.metadata ? JSON.parse(rawChunk.metadata) : {};
        if (!chunkVector || !Array.isArray(chunkVector)) {
          console.warn("RAG_SERVICE: Skipping chunk " + rawChunk.id + " due to missing or invalid vector.");
          continue;
        }
        const similarityScore = cosineSimilarity(queryVector, chunkVector);
        scoredChunks.push({
          id: rawChunk.id,
          source_project_id: rawChunk.source_project_id,
          source_file_path: rawChunk.source_file_path,
          chunk_text: rawChunk.chunk_text,
          indexed_at: rawChunk.indexed_at,
          chunk_vector: chunkVector,
          metadata,
          similarityScore
        });
      } catch (parseError) {
        console.error("RAG_SERVICE: Error parsing data for chunk " + rawChunk.id + " (file: " + rawChunk.source_file_path + "):", parseError);
      }
    }
    scoredChunks.sort((a, b) => b.similarityScore - a.similarityScore);
    const topResults = scoredChunks.slice(0, topK);
    console.log("RAG_SERVICE: Retrieved " + topResults.length + " relevant chunks.");
    return { success: true, results: topResults };
  } catch (error) {
    console.error("RAG_SERVICE: Error retrieving relevant chunks for project " + projectId + ":", error);
    return { success: false, error: error.message || "An unexpected error occurred during retrieval." };
  }
}
console.log("RAG_SERVICE_JS: File execution finished (ESM). Exports configured.");
const ragServiceImport = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  indexFileContent,
  initializeRagService,
  retrieveRelevantChunks,
  runProjectIndexing
}, Symbol.toStringTag, { value: "Module" }));
/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */
function isNothing(subject) {
  return typeof subject === "undefined" || subject === null;
}
function isObject(subject) {
  return typeof subject === "object" && subject !== null;
}
function toArray(sequence) {
  if (Array.isArray(sequence)) return sequence;
  else if (isNothing(sequence)) return [];
  return [sequence];
}
function extend(target, source) {
  var index, length, key, sourceKeys;
  if (source) {
    sourceKeys = Object.keys(source);
    for (index = 0, length = sourceKeys.length; index < length; index += 1) {
      key = sourceKeys[index];
      target[key] = source[key];
    }
  }
  return target;
}
function repeat(string, count) {
  var result = "", cycle;
  for (cycle = 0; cycle < count; cycle += 1) {
    result += string;
  }
  return result;
}
function isNegativeZero(number) {
  return number === 0 && Number.NEGATIVE_INFINITY === 1 / number;
}
var isNothing_1 = isNothing;
var isObject_1 = isObject;
var toArray_1 = toArray;
var repeat_1 = repeat;
var isNegativeZero_1 = isNegativeZero;
var extend_1 = extend;
var common = {
  isNothing: isNothing_1,
  isObject: isObject_1,
  toArray: toArray_1,
  repeat: repeat_1,
  isNegativeZero: isNegativeZero_1,
  extend: extend_1
};
function formatError(exception2, compact) {
  var where = "", message = exception2.reason || "(unknown reason)";
  if (!exception2.mark) return message;
  if (exception2.mark.name) {
    where += 'in "' + exception2.mark.name + '" ';
  }
  where += "(" + (exception2.mark.line + 1) + ":" + (exception2.mark.column + 1) + ")";
  if (!compact && exception2.mark.snippet) {
    where += "\n\n" + exception2.mark.snippet;
  }
  return message + " " + where;
}
function YAMLException$1(reason, mark) {
  Error.call(this);
  this.name = "YAMLException";
  this.reason = reason;
  this.mark = mark;
  this.message = formatError(this, false);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(this, this.constructor);
  } else {
    this.stack = new Error().stack || "";
  }
}
YAMLException$1.prototype = Object.create(Error.prototype);
YAMLException$1.prototype.constructor = YAMLException$1;
YAMLException$1.prototype.toString = function toString(compact) {
  return this.name + ": " + formatError(this, compact);
};
var exception = YAMLException$1;
function getLine(buffer, lineStart, lineEnd, position, maxLineLength) {
  var head = "";
  var tail = "";
  var maxHalfLength = Math.floor(maxLineLength / 2) - 1;
  if (position - lineStart > maxHalfLength) {
    head = " ... ";
    lineStart = position - maxHalfLength + head.length;
  }
  if (lineEnd - position > maxHalfLength) {
    tail = " ...";
    lineEnd = position + maxHalfLength - tail.length;
  }
  return {
    str: head + buffer.slice(lineStart, lineEnd).replace(/\t/g, "→") + tail,
    pos: position - lineStart + head.length
    // relative position
  };
}
function padStart(string, max) {
  return common.repeat(" ", max - string.length) + string;
}
function makeSnippet(mark, options) {
  options = Object.create(options || null);
  if (!mark.buffer) return null;
  if (!options.maxLength) options.maxLength = 79;
  if (typeof options.indent !== "number") options.indent = 1;
  if (typeof options.linesBefore !== "number") options.linesBefore = 3;
  if (typeof options.linesAfter !== "number") options.linesAfter = 2;
  var re = /\r?\n|\r|\0/g;
  var lineStarts = [0];
  var lineEnds = [];
  var match;
  var foundLineNo = -1;
  while (match = re.exec(mark.buffer)) {
    lineEnds.push(match.index);
    lineStarts.push(match.index + match[0].length);
    if (mark.position <= match.index && foundLineNo < 0) {
      foundLineNo = lineStarts.length - 2;
    }
  }
  if (foundLineNo < 0) foundLineNo = lineStarts.length - 1;
  var result = "", i, line;
  var lineNoLength = Math.min(mark.line + options.linesAfter, lineEnds.length).toString().length;
  var maxLineLength = options.maxLength - (options.indent + lineNoLength + 3);
  for (i = 1; i <= options.linesBefore; i++) {
    if (foundLineNo - i < 0) break;
    line = getLine(
      mark.buffer,
      lineStarts[foundLineNo - i],
      lineEnds[foundLineNo - i],
      mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo - i]),
      maxLineLength
    );
    result = common.repeat(" ", options.indent) + padStart((mark.line - i + 1).toString(), lineNoLength) + " | " + line.str + "\n" + result;
  }
  line = getLine(mark.buffer, lineStarts[foundLineNo], lineEnds[foundLineNo], mark.position, maxLineLength);
  result += common.repeat(" ", options.indent) + padStart((mark.line + 1).toString(), lineNoLength) + " | " + line.str + "\n";
  result += common.repeat("-", options.indent + lineNoLength + 3 + line.pos) + "^\n";
  for (i = 1; i <= options.linesAfter; i++) {
    if (foundLineNo + i >= lineEnds.length) break;
    line = getLine(
      mark.buffer,
      lineStarts[foundLineNo + i],
      lineEnds[foundLineNo + i],
      mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo + i]),
      maxLineLength
    );
    result += common.repeat(" ", options.indent) + padStart((mark.line + i + 1).toString(), lineNoLength) + " | " + line.str + "\n";
  }
  return result.replace(/\n$/, "");
}
var snippet = makeSnippet;
var TYPE_CONSTRUCTOR_OPTIONS = [
  "kind",
  "multi",
  "resolve",
  "construct",
  "instanceOf",
  "predicate",
  "represent",
  "representName",
  "defaultStyle",
  "styleAliases"
];
var YAML_NODE_KINDS = [
  "scalar",
  "sequence",
  "mapping"
];
function compileStyleAliases(map2) {
  var result = {};
  if (map2 !== null) {
    Object.keys(map2).forEach(function(style) {
      map2[style].forEach(function(alias) {
        result[String(alias)] = style;
      });
    });
  }
  return result;
}
function Type$1(tag, options) {
  options = options || {};
  Object.keys(options).forEach(function(name) {
    if (TYPE_CONSTRUCTOR_OPTIONS.indexOf(name) === -1) {
      throw new exception('Unknown option "' + name + '" is met in definition of "' + tag + '" YAML type.');
    }
  });
  this.options = options;
  this.tag = tag;
  this.kind = options["kind"] || null;
  this.resolve = options["resolve"] || function() {
    return true;
  };
  this.construct = options["construct"] || function(data) {
    return data;
  };
  this.instanceOf = options["instanceOf"] || null;
  this.predicate = options["predicate"] || null;
  this.represent = options["represent"] || null;
  this.representName = options["representName"] || null;
  this.defaultStyle = options["defaultStyle"] || null;
  this.multi = options["multi"] || false;
  this.styleAliases = compileStyleAliases(options["styleAliases"] || null);
  if (YAML_NODE_KINDS.indexOf(this.kind) === -1) {
    throw new exception('Unknown kind "' + this.kind + '" is specified for "' + tag + '" YAML type.');
  }
}
var type = Type$1;
function compileList(schema2, name) {
  var result = [];
  schema2[name].forEach(function(currentType) {
    var newIndex = result.length;
    result.forEach(function(previousType, previousIndex) {
      if (previousType.tag === currentType.tag && previousType.kind === currentType.kind && previousType.multi === currentType.multi) {
        newIndex = previousIndex;
      }
    });
    result[newIndex] = currentType;
  });
  return result;
}
function compileMap() {
  var result = {
    scalar: {},
    sequence: {},
    mapping: {},
    fallback: {},
    multi: {
      scalar: [],
      sequence: [],
      mapping: [],
      fallback: []
    }
  }, index, length;
  function collectType(type2) {
    if (type2.multi) {
      result.multi[type2.kind].push(type2);
      result.multi["fallback"].push(type2);
    } else {
      result[type2.kind][type2.tag] = result["fallback"][type2.tag] = type2;
    }
  }
  for (index = 0, length = arguments.length; index < length; index += 1) {
    arguments[index].forEach(collectType);
  }
  return result;
}
function Schema$1(definition) {
  return this.extend(definition);
}
Schema$1.prototype.extend = function extend2(definition) {
  var implicit = [];
  var explicit = [];
  if (definition instanceof type) {
    explicit.push(definition);
  } else if (Array.isArray(definition)) {
    explicit = explicit.concat(definition);
  } else if (definition && (Array.isArray(definition.implicit) || Array.isArray(definition.explicit))) {
    if (definition.implicit) implicit = implicit.concat(definition.implicit);
    if (definition.explicit) explicit = explicit.concat(definition.explicit);
  } else {
    throw new exception("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");
  }
  implicit.forEach(function(type$1) {
    if (!(type$1 instanceof type)) {
      throw new exception("Specified list of YAML types (or a single Type object) contains a non-Type object.");
    }
    if (type$1.loadKind && type$1.loadKind !== "scalar") {
      throw new exception("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");
    }
    if (type$1.multi) {
      throw new exception("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.");
    }
  });
  explicit.forEach(function(type$1) {
    if (!(type$1 instanceof type)) {
      throw new exception("Specified list of YAML types (or a single Type object) contains a non-Type object.");
    }
  });
  var result = Object.create(Schema$1.prototype);
  result.implicit = (this.implicit || []).concat(implicit);
  result.explicit = (this.explicit || []).concat(explicit);
  result.compiledImplicit = compileList(result, "implicit");
  result.compiledExplicit = compileList(result, "explicit");
  result.compiledTypeMap = compileMap(result.compiledImplicit, result.compiledExplicit);
  return result;
};
var schema = Schema$1;
var str = new type("tag:yaml.org,2002:str", {
  kind: "scalar",
  construct: function(data) {
    return data !== null ? data : "";
  }
});
var seq = new type("tag:yaml.org,2002:seq", {
  kind: "sequence",
  construct: function(data) {
    return data !== null ? data : [];
  }
});
var map = new type("tag:yaml.org,2002:map", {
  kind: "mapping",
  construct: function(data) {
    return data !== null ? data : {};
  }
});
var failsafe = new schema({
  explicit: [
    str,
    seq,
    map
  ]
});
function resolveYamlNull(data) {
  if (data === null) return true;
  var max = data.length;
  return max === 1 && data === "~" || max === 4 && (data === "null" || data === "Null" || data === "NULL");
}
function constructYamlNull() {
  return null;
}
function isNull(object) {
  return object === null;
}
var _null = new type("tag:yaml.org,2002:null", {
  kind: "scalar",
  resolve: resolveYamlNull,
  construct: constructYamlNull,
  predicate: isNull,
  represent: {
    canonical: function() {
      return "~";
    },
    lowercase: function() {
      return "null";
    },
    uppercase: function() {
      return "NULL";
    },
    camelcase: function() {
      return "Null";
    },
    empty: function() {
      return "";
    }
  },
  defaultStyle: "lowercase"
});
function resolveYamlBoolean(data) {
  if (data === null) return false;
  var max = data.length;
  return max === 4 && (data === "true" || data === "True" || data === "TRUE") || max === 5 && (data === "false" || data === "False" || data === "FALSE");
}
function constructYamlBoolean(data) {
  return data === "true" || data === "True" || data === "TRUE";
}
function isBoolean(object) {
  return Object.prototype.toString.call(object) === "[object Boolean]";
}
var bool = new type("tag:yaml.org,2002:bool", {
  kind: "scalar",
  resolve: resolveYamlBoolean,
  construct: constructYamlBoolean,
  predicate: isBoolean,
  represent: {
    lowercase: function(object) {
      return object ? "true" : "false";
    },
    uppercase: function(object) {
      return object ? "TRUE" : "FALSE";
    },
    camelcase: function(object) {
      return object ? "True" : "False";
    }
  },
  defaultStyle: "lowercase"
});
function isHexCode(c) {
  return 48 <= c && c <= 57 || 65 <= c && c <= 70 || 97 <= c && c <= 102;
}
function isOctCode(c) {
  return 48 <= c && c <= 55;
}
function isDecCode(c) {
  return 48 <= c && c <= 57;
}
function resolveYamlInteger(data) {
  if (data === null) return false;
  var max = data.length, index = 0, hasDigits = false, ch;
  if (!max) return false;
  ch = data[index];
  if (ch === "-" || ch === "+") {
    ch = data[++index];
  }
  if (ch === "0") {
    if (index + 1 === max) return true;
    ch = data[++index];
    if (ch === "b") {
      index++;
      for (; index < max; index++) {
        ch = data[index];
        if (ch === "_") continue;
        if (ch !== "0" && ch !== "1") return false;
        hasDigits = true;
      }
      return hasDigits && ch !== "_";
    }
    if (ch === "x") {
      index++;
      for (; index < max; index++) {
        ch = data[index];
        if (ch === "_") continue;
        if (!isHexCode(data.charCodeAt(index))) return false;
        hasDigits = true;
      }
      return hasDigits && ch !== "_";
    }
    if (ch === "o") {
      index++;
      for (; index < max; index++) {
        ch = data[index];
        if (ch === "_") continue;
        if (!isOctCode(data.charCodeAt(index))) return false;
        hasDigits = true;
      }
      return hasDigits && ch !== "_";
    }
  }
  if (ch === "_") return false;
  for (; index < max; index++) {
    ch = data[index];
    if (ch === "_") continue;
    if (!isDecCode(data.charCodeAt(index))) {
      return false;
    }
    hasDigits = true;
  }
  if (!hasDigits || ch === "_") return false;
  return true;
}
function constructYamlInteger(data) {
  var value = data, sign = 1, ch;
  if (value.indexOf("_") !== -1) {
    value = value.replace(/_/g, "");
  }
  ch = value[0];
  if (ch === "-" || ch === "+") {
    if (ch === "-") sign = -1;
    value = value.slice(1);
    ch = value[0];
  }
  if (value === "0") return 0;
  if (ch === "0") {
    if (value[1] === "b") return sign * parseInt(value.slice(2), 2);
    if (value[1] === "x") return sign * parseInt(value.slice(2), 16);
    if (value[1] === "o") return sign * parseInt(value.slice(2), 8);
  }
  return sign * parseInt(value, 10);
}
function isInteger(object) {
  return Object.prototype.toString.call(object) === "[object Number]" && (object % 1 === 0 && !common.isNegativeZero(object));
}
var int = new type("tag:yaml.org,2002:int", {
  kind: "scalar",
  resolve: resolveYamlInteger,
  construct: constructYamlInteger,
  predicate: isInteger,
  represent: {
    binary: function(obj) {
      return obj >= 0 ? "0b" + obj.toString(2) : "-0b" + obj.toString(2).slice(1);
    },
    octal: function(obj) {
      return obj >= 0 ? "0o" + obj.toString(8) : "-0o" + obj.toString(8).slice(1);
    },
    decimal: function(obj) {
      return obj.toString(10);
    },
    /* eslint-disable max-len */
    hexadecimal: function(obj) {
      return obj >= 0 ? "0x" + obj.toString(16).toUpperCase() : "-0x" + obj.toString(16).toUpperCase().slice(1);
    }
  },
  defaultStyle: "decimal",
  styleAliases: {
    binary: [2, "bin"],
    octal: [8, "oct"],
    decimal: [10, "dec"],
    hexadecimal: [16, "hex"]
  }
});
var YAML_FLOAT_PATTERN = new RegExp(
  // 2.5e4, 2.5 and integers
  "^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"
);
function resolveYamlFloat(data) {
  if (data === null) return false;
  if (!YAML_FLOAT_PATTERN.test(data) || // Quick hack to not allow integers end with `_`
  // Probably should update regexp & check speed
  data[data.length - 1] === "_") {
    return false;
  }
  return true;
}
function constructYamlFloat(data) {
  var value, sign;
  value = data.replace(/_/g, "").toLowerCase();
  sign = value[0] === "-" ? -1 : 1;
  if ("+-".indexOf(value[0]) >= 0) {
    value = value.slice(1);
  }
  if (value === ".inf") {
    return sign === 1 ? Number.POSITIVE_INFINITY : Number.NEGATIVE_INFINITY;
  } else if (value === ".nan") {
    return NaN;
  }
  return sign * parseFloat(value, 10);
}
var SCIENTIFIC_WITHOUT_DOT = /^[-+]?[0-9]+e/;
function representYamlFloat(object, style) {
  var res;
  if (isNaN(object)) {
    switch (style) {
      case "lowercase":
        return ".nan";
      case "uppercase":
        return ".NAN";
      case "camelcase":
        return ".NaN";
    }
  } else if (Number.POSITIVE_INFINITY === object) {
    switch (style) {
      case "lowercase":
        return ".inf";
      case "uppercase":
        return ".INF";
      case "camelcase":
        return ".Inf";
    }
  } else if (Number.NEGATIVE_INFINITY === object) {
    switch (style) {
      case "lowercase":
        return "-.inf";
      case "uppercase":
        return "-.INF";
      case "camelcase":
        return "-.Inf";
    }
  } else if (common.isNegativeZero(object)) {
    return "-0.0";
  }
  res = object.toString(10);
  return SCIENTIFIC_WITHOUT_DOT.test(res) ? res.replace("e", ".e") : res;
}
function isFloat(object) {
  return Object.prototype.toString.call(object) === "[object Number]" && (object % 1 !== 0 || common.isNegativeZero(object));
}
var float = new type("tag:yaml.org,2002:float", {
  kind: "scalar",
  resolve: resolveYamlFloat,
  construct: constructYamlFloat,
  predicate: isFloat,
  represent: representYamlFloat,
  defaultStyle: "lowercase"
});
var json = failsafe.extend({
  implicit: [
    _null,
    bool,
    int,
    float
  ]
});
var core = json;
var YAML_DATE_REGEXP = new RegExp(
  "^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"
);
var YAML_TIMESTAMP_REGEXP = new RegExp(
  "^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$"
);
function resolveYamlTimestamp(data) {
  if (data === null) return false;
  if (YAML_DATE_REGEXP.exec(data) !== null) return true;
  if (YAML_TIMESTAMP_REGEXP.exec(data) !== null) return true;
  return false;
}
function constructYamlTimestamp(data) {
  var match, year, month, day, hour, minute, second, fraction = 0, delta = null, tz_hour, tz_minute, date;
  match = YAML_DATE_REGEXP.exec(data);
  if (match === null) match = YAML_TIMESTAMP_REGEXP.exec(data);
  if (match === null) throw new Error("Date resolve error");
  year = +match[1];
  month = +match[2] - 1;
  day = +match[3];
  if (!match[4]) {
    return new Date(Date.UTC(year, month, day));
  }
  hour = +match[4];
  minute = +match[5];
  second = +match[6];
  if (match[7]) {
    fraction = match[7].slice(0, 3);
    while (fraction.length < 3) {
      fraction += "0";
    }
    fraction = +fraction;
  }
  if (match[9]) {
    tz_hour = +match[10];
    tz_minute = +(match[11] || 0);
    delta = (tz_hour * 60 + tz_minute) * 6e4;
    if (match[9] === "-") delta = -delta;
  }
  date = new Date(Date.UTC(year, month, day, hour, minute, second, fraction));
  if (delta) date.setTime(date.getTime() - delta);
  return date;
}
function representYamlTimestamp(object) {
  return object.toISOString();
}
var timestamp = new type("tag:yaml.org,2002:timestamp", {
  kind: "scalar",
  resolve: resolveYamlTimestamp,
  construct: constructYamlTimestamp,
  instanceOf: Date,
  represent: representYamlTimestamp
});
function resolveYamlMerge(data) {
  return data === "<<" || data === null;
}
var merge = new type("tag:yaml.org,2002:merge", {
  kind: "scalar",
  resolve: resolveYamlMerge
});
var BASE64_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";
function resolveYamlBinary(data) {
  if (data === null) return false;
  var code, idx, bitlen = 0, max = data.length, map2 = BASE64_MAP;
  for (idx = 0; idx < max; idx++) {
    code = map2.indexOf(data.charAt(idx));
    if (code > 64) continue;
    if (code < 0) return false;
    bitlen += 6;
  }
  return bitlen % 8 === 0;
}
function constructYamlBinary(data) {
  var idx, tailbits, input = data.replace(/[\r\n=]/g, ""), max = input.length, map2 = BASE64_MAP, bits = 0, result = [];
  for (idx = 0; idx < max; idx++) {
    if (idx % 4 === 0 && idx) {
      result.push(bits >> 16 & 255);
      result.push(bits >> 8 & 255);
      result.push(bits & 255);
    }
    bits = bits << 6 | map2.indexOf(input.charAt(idx));
  }
  tailbits = max % 4 * 6;
  if (tailbits === 0) {
    result.push(bits >> 16 & 255);
    result.push(bits >> 8 & 255);
    result.push(bits & 255);
  } else if (tailbits === 18) {
    result.push(bits >> 10 & 255);
    result.push(bits >> 2 & 255);
  } else if (tailbits === 12) {
    result.push(bits >> 4 & 255);
  }
  return new Uint8Array(result);
}
function representYamlBinary(object) {
  var result = "", bits = 0, idx, tail, max = object.length, map2 = BASE64_MAP;
  for (idx = 0; idx < max; idx++) {
    if (idx % 3 === 0 && idx) {
      result += map2[bits >> 18 & 63];
      result += map2[bits >> 12 & 63];
      result += map2[bits >> 6 & 63];
      result += map2[bits & 63];
    }
    bits = (bits << 8) + object[idx];
  }
  tail = max % 3;
  if (tail === 0) {
    result += map2[bits >> 18 & 63];
    result += map2[bits >> 12 & 63];
    result += map2[bits >> 6 & 63];
    result += map2[bits & 63];
  } else if (tail === 2) {
    result += map2[bits >> 10 & 63];
    result += map2[bits >> 4 & 63];
    result += map2[bits << 2 & 63];
    result += map2[64];
  } else if (tail === 1) {
    result += map2[bits >> 2 & 63];
    result += map2[bits << 4 & 63];
    result += map2[64];
    result += map2[64];
  }
  return result;
}
function isBinary(obj) {
  return Object.prototype.toString.call(obj) === "[object Uint8Array]";
}
var binary = new type("tag:yaml.org,2002:binary", {
  kind: "scalar",
  resolve: resolveYamlBinary,
  construct: constructYamlBinary,
  predicate: isBinary,
  represent: representYamlBinary
});
var _hasOwnProperty$3 = Object.prototype.hasOwnProperty;
var _toString$2 = Object.prototype.toString;
function resolveYamlOmap(data) {
  if (data === null) return true;
  var objectKeys = [], index, length, pair, pairKey, pairHasKey, object = data;
  for (index = 0, length = object.length; index < length; index += 1) {
    pair = object[index];
    pairHasKey = false;
    if (_toString$2.call(pair) !== "[object Object]") return false;
    for (pairKey in pair) {
      if (_hasOwnProperty$3.call(pair, pairKey)) {
        if (!pairHasKey) pairHasKey = true;
        else return false;
      }
    }
    if (!pairHasKey) return false;
    if (objectKeys.indexOf(pairKey) === -1) objectKeys.push(pairKey);
    else return false;
  }
  return true;
}
function constructYamlOmap(data) {
  return data !== null ? data : [];
}
var omap = new type("tag:yaml.org,2002:omap", {
  kind: "sequence",
  resolve: resolveYamlOmap,
  construct: constructYamlOmap
});
var _toString$1 = Object.prototype.toString;
function resolveYamlPairs(data) {
  if (data === null) return true;
  var index, length, pair, keys, result, object = data;
  result = new Array(object.length);
  for (index = 0, length = object.length; index < length; index += 1) {
    pair = object[index];
    if (_toString$1.call(pair) !== "[object Object]") return false;
    keys = Object.keys(pair);
    if (keys.length !== 1) return false;
    result[index] = [keys[0], pair[keys[0]]];
  }
  return true;
}
function constructYamlPairs(data) {
  if (data === null) return [];
  var index, length, pair, keys, result, object = data;
  result = new Array(object.length);
  for (index = 0, length = object.length; index < length; index += 1) {
    pair = object[index];
    keys = Object.keys(pair);
    result[index] = [keys[0], pair[keys[0]]];
  }
  return result;
}
var pairs = new type("tag:yaml.org,2002:pairs", {
  kind: "sequence",
  resolve: resolveYamlPairs,
  construct: constructYamlPairs
});
var _hasOwnProperty$2 = Object.prototype.hasOwnProperty;
function resolveYamlSet(data) {
  if (data === null) return true;
  var key, object = data;
  for (key in object) {
    if (_hasOwnProperty$2.call(object, key)) {
      if (object[key] !== null) return false;
    }
  }
  return true;
}
function constructYamlSet(data) {
  return data !== null ? data : {};
}
var set = new type("tag:yaml.org,2002:set", {
  kind: "mapping",
  resolve: resolveYamlSet,
  construct: constructYamlSet
});
var _default = core.extend({
  implicit: [
    timestamp,
    merge
  ],
  explicit: [
    binary,
    omap,
    pairs,
    set
  ]
});
var _hasOwnProperty$1 = Object.prototype.hasOwnProperty;
var CONTEXT_FLOW_IN = 1;
var CONTEXT_FLOW_OUT = 2;
var CONTEXT_BLOCK_IN = 3;
var CONTEXT_BLOCK_OUT = 4;
var CHOMPING_CLIP = 1;
var CHOMPING_STRIP = 2;
var CHOMPING_KEEP = 3;
var PATTERN_NON_PRINTABLE = /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/;
var PATTERN_NON_ASCII_LINE_BREAKS = /[\x85\u2028\u2029]/;
var PATTERN_FLOW_INDICATORS = /[,\[\]\{\}]/;
var PATTERN_TAG_HANDLE = /^(?:!|!!|![a-z\-]+!)$/i;
var PATTERN_TAG_URI = /^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;
function _class(obj) {
  return Object.prototype.toString.call(obj);
}
function is_EOL(c) {
  return c === 10 || c === 13;
}
function is_WHITE_SPACE(c) {
  return c === 9 || c === 32;
}
function is_WS_OR_EOL(c) {
  return c === 9 || c === 32 || c === 10 || c === 13;
}
function is_FLOW_INDICATOR(c) {
  return c === 44 || c === 91 || c === 93 || c === 123 || c === 125;
}
function fromHexCode(c) {
  var lc;
  if (48 <= c && c <= 57) {
    return c - 48;
  }
  lc = c | 32;
  if (97 <= lc && lc <= 102) {
    return lc - 97 + 10;
  }
  return -1;
}
function escapedHexLen(c) {
  if (c === 120) {
    return 2;
  }
  if (c === 117) {
    return 4;
  }
  if (c === 85) {
    return 8;
  }
  return 0;
}
function fromDecimalCode(c) {
  if (48 <= c && c <= 57) {
    return c - 48;
  }
  return -1;
}
function simpleEscapeSequence(c) {
  return c === 48 ? "\0" : c === 97 ? "\x07" : c === 98 ? "\b" : c === 116 ? "	" : c === 9 ? "	" : c === 110 ? "\n" : c === 118 ? "\v" : c === 102 ? "\f" : c === 114 ? "\r" : c === 101 ? "\x1B" : c === 32 ? " " : c === 34 ? '"' : c === 47 ? "/" : c === 92 ? "\\" : c === 78 ? "" : c === 95 ? " " : c === 76 ? "\u2028" : c === 80 ? "\u2029" : "";
}
function charFromCodepoint(c) {
  if (c <= 65535) {
    return String.fromCharCode(c);
  }
  return String.fromCharCode(
    (c - 65536 >> 10) + 55296,
    (c - 65536 & 1023) + 56320
  );
}
var simpleEscapeCheck = new Array(256);
var simpleEscapeMap = new Array(256);
for (var i = 0; i < 256; i++) {
  simpleEscapeCheck[i] = simpleEscapeSequence(i) ? 1 : 0;
  simpleEscapeMap[i] = simpleEscapeSequence(i);
}
function State$1(input, options) {
  this.input = input;
  this.filename = options["filename"] || null;
  this.schema = options["schema"] || _default;
  this.onWarning = options["onWarning"] || null;
  this.legacy = options["legacy"] || false;
  this.json = options["json"] || false;
  this.listener = options["listener"] || null;
  this.implicitTypes = this.schema.compiledImplicit;
  this.typeMap = this.schema.compiledTypeMap;
  this.length = input.length;
  this.position = 0;
  this.line = 0;
  this.lineStart = 0;
  this.lineIndent = 0;
  this.firstTabInLine = -1;
  this.documents = [];
}
function generateError(state, message) {
  var mark = {
    name: state.filename,
    buffer: state.input.slice(0, -1),
    // omit trailing \0
    position: state.position,
    line: state.line,
    column: state.position - state.lineStart
  };
  mark.snippet = snippet(mark);
  return new exception(message, mark);
}
function throwError(state, message) {
  throw generateError(state, message);
}
function throwWarning(state, message) {
  if (state.onWarning) {
    state.onWarning.call(null, generateError(state, message));
  }
}
var directiveHandlers = {
  YAML: function handleYamlDirective(state, name, args) {
    var match, major, minor;
    if (state.version !== null) {
      throwError(state, "duplication of %YAML directive");
    }
    if (args.length !== 1) {
      throwError(state, "YAML directive accepts exactly one argument");
    }
    match = /^([0-9]+)\.([0-9]+)$/.exec(args[0]);
    if (match === null) {
      throwError(state, "ill-formed argument of the YAML directive");
    }
    major = parseInt(match[1], 10);
    minor = parseInt(match[2], 10);
    if (major !== 1) {
      throwError(state, "unacceptable YAML version of the document");
    }
    state.version = args[0];
    state.checkLineBreaks = minor < 2;
    if (minor !== 1 && minor !== 2) {
      throwWarning(state, "unsupported YAML version of the document");
    }
  },
  TAG: function handleTagDirective(state, name, args) {
    var handle, prefix;
    if (args.length !== 2) {
      throwError(state, "TAG directive accepts exactly two arguments");
    }
    handle = args[0];
    prefix = args[1];
    if (!PATTERN_TAG_HANDLE.test(handle)) {
      throwError(state, "ill-formed tag handle (first argument) of the TAG directive");
    }
    if (_hasOwnProperty$1.call(state.tagMap, handle)) {
      throwError(state, 'there is a previously declared suffix for "' + handle + '" tag handle');
    }
    if (!PATTERN_TAG_URI.test(prefix)) {
      throwError(state, "ill-formed tag prefix (second argument) of the TAG directive");
    }
    try {
      prefix = decodeURIComponent(prefix);
    } catch (err) {
      throwError(state, "tag prefix is malformed: " + prefix);
    }
    state.tagMap[handle] = prefix;
  }
};
function captureSegment(state, start, end, checkJson) {
  var _position, _length, _character, _result;
  if (start < end) {
    _result = state.input.slice(start, end);
    if (checkJson) {
      for (_position = 0, _length = _result.length; _position < _length; _position += 1) {
        _character = _result.charCodeAt(_position);
        if (!(_character === 9 || 32 <= _character && _character <= 1114111)) {
          throwError(state, "expected valid JSON character");
        }
      }
    } else if (PATTERN_NON_PRINTABLE.test(_result)) {
      throwError(state, "the stream contains non-printable characters");
    }
    state.result += _result;
  }
}
function mergeMappings(state, destination, source, overridableKeys) {
  var sourceKeys, key, index, quantity;
  if (!common.isObject(source)) {
    throwError(state, "cannot merge mappings; the provided source object is unacceptable");
  }
  sourceKeys = Object.keys(source);
  for (index = 0, quantity = sourceKeys.length; index < quantity; index += 1) {
    key = sourceKeys[index];
    if (!_hasOwnProperty$1.call(destination, key)) {
      destination[key] = source[key];
      overridableKeys[key] = true;
    }
  }
}
function storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, startLine, startLineStart, startPos) {
  var index, quantity;
  if (Array.isArray(keyNode)) {
    keyNode = Array.prototype.slice.call(keyNode);
    for (index = 0, quantity = keyNode.length; index < quantity; index += 1) {
      if (Array.isArray(keyNode[index])) {
        throwError(state, "nested arrays are not supported inside keys");
      }
      if (typeof keyNode === "object" && _class(keyNode[index]) === "[object Object]") {
        keyNode[index] = "[object Object]";
      }
    }
  }
  if (typeof keyNode === "object" && _class(keyNode) === "[object Object]") {
    keyNode = "[object Object]";
  }
  keyNode = String(keyNode);
  if (_result === null) {
    _result = {};
  }
  if (keyTag === "tag:yaml.org,2002:merge") {
    if (Array.isArray(valueNode)) {
      for (index = 0, quantity = valueNode.length; index < quantity; index += 1) {
        mergeMappings(state, _result, valueNode[index], overridableKeys);
      }
    } else {
      mergeMappings(state, _result, valueNode, overridableKeys);
    }
  } else {
    if (!state.json && !_hasOwnProperty$1.call(overridableKeys, keyNode) && _hasOwnProperty$1.call(_result, keyNode)) {
      state.line = startLine || state.line;
      state.lineStart = startLineStart || state.lineStart;
      state.position = startPos || state.position;
      throwError(state, "duplicated mapping key");
    }
    if (keyNode === "__proto__") {
      Object.defineProperty(_result, keyNode, {
        configurable: true,
        enumerable: true,
        writable: true,
        value: valueNode
      });
    } else {
      _result[keyNode] = valueNode;
    }
    delete overridableKeys[keyNode];
  }
  return _result;
}
function readLineBreak(state) {
  var ch;
  ch = state.input.charCodeAt(state.position);
  if (ch === 10) {
    state.position++;
  } else if (ch === 13) {
    state.position++;
    if (state.input.charCodeAt(state.position) === 10) {
      state.position++;
    }
  } else {
    throwError(state, "a line break is expected");
  }
  state.line += 1;
  state.lineStart = state.position;
  state.firstTabInLine = -1;
}
function skipSeparationSpace(state, allowComments, checkIndent) {
  var lineBreaks = 0, ch = state.input.charCodeAt(state.position);
  while (ch !== 0) {
    while (is_WHITE_SPACE(ch)) {
      if (ch === 9 && state.firstTabInLine === -1) {
        state.firstTabInLine = state.position;
      }
      ch = state.input.charCodeAt(++state.position);
    }
    if (allowComments && ch === 35) {
      do {
        ch = state.input.charCodeAt(++state.position);
      } while (ch !== 10 && ch !== 13 && ch !== 0);
    }
    if (is_EOL(ch)) {
      readLineBreak(state);
      ch = state.input.charCodeAt(state.position);
      lineBreaks++;
      state.lineIndent = 0;
      while (ch === 32) {
        state.lineIndent++;
        ch = state.input.charCodeAt(++state.position);
      }
    } else {
      break;
    }
  }
  if (checkIndent !== -1 && lineBreaks !== 0 && state.lineIndent < checkIndent) {
    throwWarning(state, "deficient indentation");
  }
  return lineBreaks;
}
function testDocumentSeparator(state) {
  var _position = state.position, ch;
  ch = state.input.charCodeAt(_position);
  if ((ch === 45 || ch === 46) && ch === state.input.charCodeAt(_position + 1) && ch === state.input.charCodeAt(_position + 2)) {
    _position += 3;
    ch = state.input.charCodeAt(_position);
    if (ch === 0 || is_WS_OR_EOL(ch)) {
      return true;
    }
  }
  return false;
}
function writeFoldedLines(state, count) {
  if (count === 1) {
    state.result += " ";
  } else if (count > 1) {
    state.result += common.repeat("\n", count - 1);
  }
}
function readPlainScalar(state, nodeIndent, withinFlowCollection) {
  var preceding, following, captureStart, captureEnd, hasPendingContent, _line, _lineStart, _lineIndent, _kind = state.kind, _result = state.result, ch;
  ch = state.input.charCodeAt(state.position);
  if (is_WS_OR_EOL(ch) || is_FLOW_INDICATOR(ch) || ch === 35 || ch === 38 || ch === 42 || ch === 33 || ch === 124 || ch === 62 || ch === 39 || ch === 34 || ch === 37 || ch === 64 || ch === 96) {
    return false;
  }
  if (ch === 63 || ch === 45) {
    following = state.input.charCodeAt(state.position + 1);
    if (is_WS_OR_EOL(following) || withinFlowCollection && is_FLOW_INDICATOR(following)) {
      return false;
    }
  }
  state.kind = "scalar";
  state.result = "";
  captureStart = captureEnd = state.position;
  hasPendingContent = false;
  while (ch !== 0) {
    if (ch === 58) {
      following = state.input.charCodeAt(state.position + 1);
      if (is_WS_OR_EOL(following) || withinFlowCollection && is_FLOW_INDICATOR(following)) {
        break;
      }
    } else if (ch === 35) {
      preceding = state.input.charCodeAt(state.position - 1);
      if (is_WS_OR_EOL(preceding)) {
        break;
      }
    } else if (state.position === state.lineStart && testDocumentSeparator(state) || withinFlowCollection && is_FLOW_INDICATOR(ch)) {
      break;
    } else if (is_EOL(ch)) {
      _line = state.line;
      _lineStart = state.lineStart;
      _lineIndent = state.lineIndent;
      skipSeparationSpace(state, false, -1);
      if (state.lineIndent >= nodeIndent) {
        hasPendingContent = true;
        ch = state.input.charCodeAt(state.position);
        continue;
      } else {
        state.position = captureEnd;
        state.line = _line;
        state.lineStart = _lineStart;
        state.lineIndent = _lineIndent;
        break;
      }
    }
    if (hasPendingContent) {
      captureSegment(state, captureStart, captureEnd, false);
      writeFoldedLines(state, state.line - _line);
      captureStart = captureEnd = state.position;
      hasPendingContent = false;
    }
    if (!is_WHITE_SPACE(ch)) {
      captureEnd = state.position + 1;
    }
    ch = state.input.charCodeAt(++state.position);
  }
  captureSegment(state, captureStart, captureEnd, false);
  if (state.result) {
    return true;
  }
  state.kind = _kind;
  state.result = _result;
  return false;
}
function readSingleQuotedScalar(state, nodeIndent) {
  var ch, captureStart, captureEnd;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 39) {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  state.position++;
  captureStart = captureEnd = state.position;
  while ((ch = state.input.charCodeAt(state.position)) !== 0) {
    if (ch === 39) {
      captureSegment(state, captureStart, state.position, true);
      ch = state.input.charCodeAt(++state.position);
      if (ch === 39) {
        captureStart = state.position;
        state.position++;
        captureEnd = state.position;
      } else {
        return true;
      }
    } else if (is_EOL(ch)) {
      captureSegment(state, captureStart, captureEnd, true);
      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));
      captureStart = captureEnd = state.position;
    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {
      throwError(state, "unexpected end of the document within a single quoted scalar");
    } else {
      state.position++;
      captureEnd = state.position;
    }
  }
  throwError(state, "unexpected end of the stream within a single quoted scalar");
}
function readDoubleQuotedScalar(state, nodeIndent) {
  var captureStart, captureEnd, hexLength, hexResult, tmp, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 34) {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  state.position++;
  captureStart = captureEnd = state.position;
  while ((ch = state.input.charCodeAt(state.position)) !== 0) {
    if (ch === 34) {
      captureSegment(state, captureStart, state.position, true);
      state.position++;
      return true;
    } else if (ch === 92) {
      captureSegment(state, captureStart, state.position, true);
      ch = state.input.charCodeAt(++state.position);
      if (is_EOL(ch)) {
        skipSeparationSpace(state, false, nodeIndent);
      } else if (ch < 256 && simpleEscapeCheck[ch]) {
        state.result += simpleEscapeMap[ch];
        state.position++;
      } else if ((tmp = escapedHexLen(ch)) > 0) {
        hexLength = tmp;
        hexResult = 0;
        for (; hexLength > 0; hexLength--) {
          ch = state.input.charCodeAt(++state.position);
          if ((tmp = fromHexCode(ch)) >= 0) {
            hexResult = (hexResult << 4) + tmp;
          } else {
            throwError(state, "expected hexadecimal character");
          }
        }
        state.result += charFromCodepoint(hexResult);
        state.position++;
      } else {
        throwError(state, "unknown escape sequence");
      }
      captureStart = captureEnd = state.position;
    } else if (is_EOL(ch)) {
      captureSegment(state, captureStart, captureEnd, true);
      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));
      captureStart = captureEnd = state.position;
    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {
      throwError(state, "unexpected end of the document within a double quoted scalar");
    } else {
      state.position++;
      captureEnd = state.position;
    }
  }
  throwError(state, "unexpected end of the stream within a double quoted scalar");
}
function readFlowCollection(state, nodeIndent) {
  var readNext = true, _line, _lineStart, _pos, _tag = state.tag, _result, _anchor = state.anchor, following, terminator, isPair, isExplicitPair, isMapping, overridableKeys = /* @__PURE__ */ Object.create(null), keyNode, keyTag, valueNode, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch === 91) {
    terminator = 93;
    isMapping = false;
    _result = [];
  } else if (ch === 123) {
    terminator = 125;
    isMapping = true;
    _result = {};
  } else {
    return false;
  }
  if (state.anchor !== null) {
    state.anchorMap[state.anchor] = _result;
  }
  ch = state.input.charCodeAt(++state.position);
  while (ch !== 0) {
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if (ch === terminator) {
      state.position++;
      state.tag = _tag;
      state.anchor = _anchor;
      state.kind = isMapping ? "mapping" : "sequence";
      state.result = _result;
      return true;
    } else if (!readNext) {
      throwError(state, "missed comma between flow collection entries");
    } else if (ch === 44) {
      throwError(state, "expected the node content, but found ','");
    }
    keyTag = keyNode = valueNode = null;
    isPair = isExplicitPair = false;
    if (ch === 63) {
      following = state.input.charCodeAt(state.position + 1);
      if (is_WS_OR_EOL(following)) {
        isPair = isExplicitPair = true;
        state.position++;
        skipSeparationSpace(state, true, nodeIndent);
      }
    }
    _line = state.line;
    _lineStart = state.lineStart;
    _pos = state.position;
    composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);
    keyTag = state.tag;
    keyNode = state.result;
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if ((isExplicitPair || state.line === _line) && ch === 58) {
      isPair = true;
      ch = state.input.charCodeAt(++state.position);
      skipSeparationSpace(state, true, nodeIndent);
      composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);
      valueNode = state.result;
    }
    if (isMapping) {
      storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos);
    } else if (isPair) {
      _result.push(storeMappingPair(state, null, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos));
    } else {
      _result.push(keyNode);
    }
    skipSeparationSpace(state, true, nodeIndent);
    ch = state.input.charCodeAt(state.position);
    if (ch === 44) {
      readNext = true;
      ch = state.input.charCodeAt(++state.position);
    } else {
      readNext = false;
    }
  }
  throwError(state, "unexpected end of the stream within a flow collection");
}
function readBlockScalar(state, nodeIndent) {
  var captureStart, folding, chomping = CHOMPING_CLIP, didReadContent = false, detectedIndent = false, textIndent = nodeIndent, emptyLines = 0, atMoreIndented = false, tmp, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch === 124) {
    folding = false;
  } else if (ch === 62) {
    folding = true;
  } else {
    return false;
  }
  state.kind = "scalar";
  state.result = "";
  while (ch !== 0) {
    ch = state.input.charCodeAt(++state.position);
    if (ch === 43 || ch === 45) {
      if (CHOMPING_CLIP === chomping) {
        chomping = ch === 43 ? CHOMPING_KEEP : CHOMPING_STRIP;
      } else {
        throwError(state, "repeat of a chomping mode identifier");
      }
    } else if ((tmp = fromDecimalCode(ch)) >= 0) {
      if (tmp === 0) {
        throwError(state, "bad explicit indentation width of a block scalar; it cannot be less than one");
      } else if (!detectedIndent) {
        textIndent = nodeIndent + tmp - 1;
        detectedIndent = true;
      } else {
        throwError(state, "repeat of an indentation width identifier");
      }
    } else {
      break;
    }
  }
  if (is_WHITE_SPACE(ch)) {
    do {
      ch = state.input.charCodeAt(++state.position);
    } while (is_WHITE_SPACE(ch));
    if (ch === 35) {
      do {
        ch = state.input.charCodeAt(++state.position);
      } while (!is_EOL(ch) && ch !== 0);
    }
  }
  while (ch !== 0) {
    readLineBreak(state);
    state.lineIndent = 0;
    ch = state.input.charCodeAt(state.position);
    while ((!detectedIndent || state.lineIndent < textIndent) && ch === 32) {
      state.lineIndent++;
      ch = state.input.charCodeAt(++state.position);
    }
    if (!detectedIndent && state.lineIndent > textIndent) {
      textIndent = state.lineIndent;
    }
    if (is_EOL(ch)) {
      emptyLines++;
      continue;
    }
    if (state.lineIndent < textIndent) {
      if (chomping === CHOMPING_KEEP) {
        state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
      } else if (chomping === CHOMPING_CLIP) {
        if (didReadContent) {
          state.result += "\n";
        }
      }
      break;
    }
    if (folding) {
      if (is_WHITE_SPACE(ch)) {
        atMoreIndented = true;
        state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
      } else if (atMoreIndented) {
        atMoreIndented = false;
        state.result += common.repeat("\n", emptyLines + 1);
      } else if (emptyLines === 0) {
        if (didReadContent) {
          state.result += " ";
        }
      } else {
        state.result += common.repeat("\n", emptyLines);
      }
    } else {
      state.result += common.repeat("\n", didReadContent ? 1 + emptyLines : emptyLines);
    }
    didReadContent = true;
    detectedIndent = true;
    emptyLines = 0;
    captureStart = state.position;
    while (!is_EOL(ch) && ch !== 0) {
      ch = state.input.charCodeAt(++state.position);
    }
    captureSegment(state, captureStart, state.position, false);
  }
  return true;
}
function readBlockSequence(state, nodeIndent) {
  var _line, _tag = state.tag, _anchor = state.anchor, _result = [], following, detected = false, ch;
  if (state.firstTabInLine !== -1) return false;
  if (state.anchor !== null) {
    state.anchorMap[state.anchor] = _result;
  }
  ch = state.input.charCodeAt(state.position);
  while (ch !== 0) {
    if (state.firstTabInLine !== -1) {
      state.position = state.firstTabInLine;
      throwError(state, "tab characters must not be used in indentation");
    }
    if (ch !== 45) {
      break;
    }
    following = state.input.charCodeAt(state.position + 1);
    if (!is_WS_OR_EOL(following)) {
      break;
    }
    detected = true;
    state.position++;
    if (skipSeparationSpace(state, true, -1)) {
      if (state.lineIndent <= nodeIndent) {
        _result.push(null);
        ch = state.input.charCodeAt(state.position);
        continue;
      }
    }
    _line = state.line;
    composeNode(state, nodeIndent, CONTEXT_BLOCK_IN, false, true);
    _result.push(state.result);
    skipSeparationSpace(state, true, -1);
    ch = state.input.charCodeAt(state.position);
    if ((state.line === _line || state.lineIndent > nodeIndent) && ch !== 0) {
      throwError(state, "bad indentation of a sequence entry");
    } else if (state.lineIndent < nodeIndent) {
      break;
    }
  }
  if (detected) {
    state.tag = _tag;
    state.anchor = _anchor;
    state.kind = "sequence";
    state.result = _result;
    return true;
  }
  return false;
}
function readBlockMapping(state, nodeIndent, flowIndent) {
  var following, allowCompact, _line, _keyLine, _keyLineStart, _keyPos, _tag = state.tag, _anchor = state.anchor, _result = {}, overridableKeys = /* @__PURE__ */ Object.create(null), keyTag = null, keyNode = null, valueNode = null, atExplicitKey = false, detected = false, ch;
  if (state.firstTabInLine !== -1) return false;
  if (state.anchor !== null) {
    state.anchorMap[state.anchor] = _result;
  }
  ch = state.input.charCodeAt(state.position);
  while (ch !== 0) {
    if (!atExplicitKey && state.firstTabInLine !== -1) {
      state.position = state.firstTabInLine;
      throwError(state, "tab characters must not be used in indentation");
    }
    following = state.input.charCodeAt(state.position + 1);
    _line = state.line;
    if ((ch === 63 || ch === 58) && is_WS_OR_EOL(following)) {
      if (ch === 63) {
        if (atExplicitKey) {
          storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);
          keyTag = keyNode = valueNode = null;
        }
        detected = true;
        atExplicitKey = true;
        allowCompact = true;
      } else if (atExplicitKey) {
        atExplicitKey = false;
        allowCompact = true;
      } else {
        throwError(state, "incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line");
      }
      state.position += 1;
      ch = following;
    } else {
      _keyLine = state.line;
      _keyLineStart = state.lineStart;
      _keyPos = state.position;
      if (!composeNode(state, flowIndent, CONTEXT_FLOW_OUT, false, true)) {
        break;
      }
      if (state.line === _line) {
        ch = state.input.charCodeAt(state.position);
        while (is_WHITE_SPACE(ch)) {
          ch = state.input.charCodeAt(++state.position);
        }
        if (ch === 58) {
          ch = state.input.charCodeAt(++state.position);
          if (!is_WS_OR_EOL(ch)) {
            throwError(state, "a whitespace character is expected after the key-value separator within a block mapping");
          }
          if (atExplicitKey) {
            storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);
            keyTag = keyNode = valueNode = null;
          }
          detected = true;
          atExplicitKey = false;
          allowCompact = false;
          keyTag = state.tag;
          keyNode = state.result;
        } else if (detected) {
          throwError(state, "can not read an implicit mapping pair; a colon is missed");
        } else {
          state.tag = _tag;
          state.anchor = _anchor;
          return true;
        }
      } else if (detected) {
        throwError(state, "can not read a block mapping entry; a multiline key may not be an implicit key");
      } else {
        state.tag = _tag;
        state.anchor = _anchor;
        return true;
      }
    }
    if (state.line === _line || state.lineIndent > nodeIndent) {
      if (atExplicitKey) {
        _keyLine = state.line;
        _keyLineStart = state.lineStart;
        _keyPos = state.position;
      }
      if (composeNode(state, nodeIndent, CONTEXT_BLOCK_OUT, true, allowCompact)) {
        if (atExplicitKey) {
          keyNode = state.result;
        } else {
          valueNode = state.result;
        }
      }
      if (!atExplicitKey) {
        storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _keyLine, _keyLineStart, _keyPos);
        keyTag = keyNode = valueNode = null;
      }
      skipSeparationSpace(state, true, -1);
      ch = state.input.charCodeAt(state.position);
    }
    if ((state.line === _line || state.lineIndent > nodeIndent) && ch !== 0) {
      throwError(state, "bad indentation of a mapping entry");
    } else if (state.lineIndent < nodeIndent) {
      break;
    }
  }
  if (atExplicitKey) {
    storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);
  }
  if (detected) {
    state.tag = _tag;
    state.anchor = _anchor;
    state.kind = "mapping";
    state.result = _result;
  }
  return detected;
}
function readTagProperty(state) {
  var _position, isVerbatim = false, isNamed = false, tagHandle, tagName, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 33) return false;
  if (state.tag !== null) {
    throwError(state, "duplication of a tag property");
  }
  ch = state.input.charCodeAt(++state.position);
  if (ch === 60) {
    isVerbatim = true;
    ch = state.input.charCodeAt(++state.position);
  } else if (ch === 33) {
    isNamed = true;
    tagHandle = "!!";
    ch = state.input.charCodeAt(++state.position);
  } else {
    tagHandle = "!";
  }
  _position = state.position;
  if (isVerbatim) {
    do {
      ch = state.input.charCodeAt(++state.position);
    } while (ch !== 0 && ch !== 62);
    if (state.position < state.length) {
      tagName = state.input.slice(_position, state.position);
      ch = state.input.charCodeAt(++state.position);
    } else {
      throwError(state, "unexpected end of the stream within a verbatim tag");
    }
  } else {
    while (ch !== 0 && !is_WS_OR_EOL(ch)) {
      if (ch === 33) {
        if (!isNamed) {
          tagHandle = state.input.slice(_position - 1, state.position + 1);
          if (!PATTERN_TAG_HANDLE.test(tagHandle)) {
            throwError(state, "named tag handle cannot contain such characters");
          }
          isNamed = true;
          _position = state.position + 1;
        } else {
          throwError(state, "tag suffix cannot contain exclamation marks");
        }
      }
      ch = state.input.charCodeAt(++state.position);
    }
    tagName = state.input.slice(_position, state.position);
    if (PATTERN_FLOW_INDICATORS.test(tagName)) {
      throwError(state, "tag suffix cannot contain flow indicator characters");
    }
  }
  if (tagName && !PATTERN_TAG_URI.test(tagName)) {
    throwError(state, "tag name cannot contain such characters: " + tagName);
  }
  try {
    tagName = decodeURIComponent(tagName);
  } catch (err) {
    throwError(state, "tag name is malformed: " + tagName);
  }
  if (isVerbatim) {
    state.tag = tagName;
  } else if (_hasOwnProperty$1.call(state.tagMap, tagHandle)) {
    state.tag = state.tagMap[tagHandle] + tagName;
  } else if (tagHandle === "!") {
    state.tag = "!" + tagName;
  } else if (tagHandle === "!!") {
    state.tag = "tag:yaml.org,2002:" + tagName;
  } else {
    throwError(state, 'undeclared tag handle "' + tagHandle + '"');
  }
  return true;
}
function readAnchorProperty(state) {
  var _position, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 38) return false;
  if (state.anchor !== null) {
    throwError(state, "duplication of an anchor property");
  }
  ch = state.input.charCodeAt(++state.position);
  _position = state.position;
  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {
    ch = state.input.charCodeAt(++state.position);
  }
  if (state.position === _position) {
    throwError(state, "name of an anchor node must contain at least one character");
  }
  state.anchor = state.input.slice(_position, state.position);
  return true;
}
function readAlias(state) {
  var _position, alias, ch;
  ch = state.input.charCodeAt(state.position);
  if (ch !== 42) return false;
  ch = state.input.charCodeAt(++state.position);
  _position = state.position;
  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {
    ch = state.input.charCodeAt(++state.position);
  }
  if (state.position === _position) {
    throwError(state, "name of an alias node must contain at least one character");
  }
  alias = state.input.slice(_position, state.position);
  if (!_hasOwnProperty$1.call(state.anchorMap, alias)) {
    throwError(state, 'unidentified alias "' + alias + '"');
  }
  state.result = state.anchorMap[alias];
  skipSeparationSpace(state, true, -1);
  return true;
}
function composeNode(state, parentIndent, nodeContext, allowToSeek, allowCompact) {
  var allowBlockStyles, allowBlockScalars, allowBlockCollections, indentStatus = 1, atNewLine = false, hasContent = false, typeIndex, typeQuantity, typeList, type2, flowIndent, blockIndent;
  if (state.listener !== null) {
    state.listener("open", state);
  }
  state.tag = null;
  state.anchor = null;
  state.kind = null;
  state.result = null;
  allowBlockStyles = allowBlockScalars = allowBlockCollections = CONTEXT_BLOCK_OUT === nodeContext || CONTEXT_BLOCK_IN === nodeContext;
  if (allowToSeek) {
    if (skipSeparationSpace(state, true, -1)) {
      atNewLine = true;
      if (state.lineIndent > parentIndent) {
        indentStatus = 1;
      } else if (state.lineIndent === parentIndent) {
        indentStatus = 0;
      } else if (state.lineIndent < parentIndent) {
        indentStatus = -1;
      }
    }
  }
  if (indentStatus === 1) {
    while (readTagProperty(state) || readAnchorProperty(state)) {
      if (skipSeparationSpace(state, true, -1)) {
        atNewLine = true;
        allowBlockCollections = allowBlockStyles;
        if (state.lineIndent > parentIndent) {
          indentStatus = 1;
        } else if (state.lineIndent === parentIndent) {
          indentStatus = 0;
        } else if (state.lineIndent < parentIndent) {
          indentStatus = -1;
        }
      } else {
        allowBlockCollections = false;
      }
    }
  }
  if (allowBlockCollections) {
    allowBlockCollections = atNewLine || allowCompact;
  }
  if (indentStatus === 1 || CONTEXT_BLOCK_OUT === nodeContext) {
    if (CONTEXT_FLOW_IN === nodeContext || CONTEXT_FLOW_OUT === nodeContext) {
      flowIndent = parentIndent;
    } else {
      flowIndent = parentIndent + 1;
    }
    blockIndent = state.position - state.lineStart;
    if (indentStatus === 1) {
      if (allowBlockCollections && (readBlockSequence(state, blockIndent) || readBlockMapping(state, blockIndent, flowIndent)) || readFlowCollection(state, flowIndent)) {
        hasContent = true;
      } else {
        if (allowBlockScalars && readBlockScalar(state, flowIndent) || readSingleQuotedScalar(state, flowIndent) || readDoubleQuotedScalar(state, flowIndent)) {
          hasContent = true;
        } else if (readAlias(state)) {
          hasContent = true;
          if (state.tag !== null || state.anchor !== null) {
            throwError(state, "alias node should not have any properties");
          }
        } else if (readPlainScalar(state, flowIndent, CONTEXT_FLOW_IN === nodeContext)) {
          hasContent = true;
          if (state.tag === null) {
            state.tag = "?";
          }
        }
        if (state.anchor !== null) {
          state.anchorMap[state.anchor] = state.result;
        }
      }
    } else if (indentStatus === 0) {
      hasContent = allowBlockCollections && readBlockSequence(state, blockIndent);
    }
  }
  if (state.tag === null) {
    if (state.anchor !== null) {
      state.anchorMap[state.anchor] = state.result;
    }
  } else if (state.tag === "?") {
    if (state.result !== null && state.kind !== "scalar") {
      throwError(state, 'unacceptable node kind for !<?> tag; it should be "scalar", not "' + state.kind + '"');
    }
    for (typeIndex = 0, typeQuantity = state.implicitTypes.length; typeIndex < typeQuantity; typeIndex += 1) {
      type2 = state.implicitTypes[typeIndex];
      if (type2.resolve(state.result)) {
        state.result = type2.construct(state.result);
        state.tag = type2.tag;
        if (state.anchor !== null) {
          state.anchorMap[state.anchor] = state.result;
        }
        break;
      }
    }
  } else if (state.tag !== "!") {
    if (_hasOwnProperty$1.call(state.typeMap[state.kind || "fallback"], state.tag)) {
      type2 = state.typeMap[state.kind || "fallback"][state.tag];
    } else {
      type2 = null;
      typeList = state.typeMap.multi[state.kind || "fallback"];
      for (typeIndex = 0, typeQuantity = typeList.length; typeIndex < typeQuantity; typeIndex += 1) {
        if (state.tag.slice(0, typeList[typeIndex].tag.length) === typeList[typeIndex].tag) {
          type2 = typeList[typeIndex];
          break;
        }
      }
    }
    if (!type2) {
      throwError(state, "unknown tag !<" + state.tag + ">");
    }
    if (state.result !== null && type2.kind !== state.kind) {
      throwError(state, "unacceptable node kind for !<" + state.tag + '> tag; it should be "' + type2.kind + '", not "' + state.kind + '"');
    }
    if (!type2.resolve(state.result, state.tag)) {
      throwError(state, "cannot resolve a node with !<" + state.tag + "> explicit tag");
    } else {
      state.result = type2.construct(state.result, state.tag);
      if (state.anchor !== null) {
        state.anchorMap[state.anchor] = state.result;
      }
    }
  }
  if (state.listener !== null) {
    state.listener("close", state);
  }
  return state.tag !== null || state.anchor !== null || hasContent;
}
function readDocument(state) {
  var documentStart = state.position, _position, directiveName, directiveArgs, hasDirectives = false, ch;
  state.version = null;
  state.checkLineBreaks = state.legacy;
  state.tagMap = /* @__PURE__ */ Object.create(null);
  state.anchorMap = /* @__PURE__ */ Object.create(null);
  while ((ch = state.input.charCodeAt(state.position)) !== 0) {
    skipSeparationSpace(state, true, -1);
    ch = state.input.charCodeAt(state.position);
    if (state.lineIndent > 0 || ch !== 37) {
      break;
    }
    hasDirectives = true;
    ch = state.input.charCodeAt(++state.position);
    _position = state.position;
    while (ch !== 0 && !is_WS_OR_EOL(ch)) {
      ch = state.input.charCodeAt(++state.position);
    }
    directiveName = state.input.slice(_position, state.position);
    directiveArgs = [];
    if (directiveName.length < 1) {
      throwError(state, "directive name must not be less than one character in length");
    }
    while (ch !== 0) {
      while (is_WHITE_SPACE(ch)) {
        ch = state.input.charCodeAt(++state.position);
      }
      if (ch === 35) {
        do {
          ch = state.input.charCodeAt(++state.position);
        } while (ch !== 0 && !is_EOL(ch));
        break;
      }
      if (is_EOL(ch)) break;
      _position = state.position;
      while (ch !== 0 && !is_WS_OR_EOL(ch)) {
        ch = state.input.charCodeAt(++state.position);
      }
      directiveArgs.push(state.input.slice(_position, state.position));
    }
    if (ch !== 0) readLineBreak(state);
    if (_hasOwnProperty$1.call(directiveHandlers, directiveName)) {
      directiveHandlers[directiveName](state, directiveName, directiveArgs);
    } else {
      throwWarning(state, 'unknown document directive "' + directiveName + '"');
    }
  }
  skipSeparationSpace(state, true, -1);
  if (state.lineIndent === 0 && state.input.charCodeAt(state.position) === 45 && state.input.charCodeAt(state.position + 1) === 45 && state.input.charCodeAt(state.position + 2) === 45) {
    state.position += 3;
    skipSeparationSpace(state, true, -1);
  } else if (hasDirectives) {
    throwError(state, "directives end mark is expected");
  }
  composeNode(state, state.lineIndent - 1, CONTEXT_BLOCK_OUT, false, true);
  skipSeparationSpace(state, true, -1);
  if (state.checkLineBreaks && PATTERN_NON_ASCII_LINE_BREAKS.test(state.input.slice(documentStart, state.position))) {
    throwWarning(state, "non-ASCII line breaks are interpreted as content");
  }
  state.documents.push(state.result);
  if (state.position === state.lineStart && testDocumentSeparator(state)) {
    if (state.input.charCodeAt(state.position) === 46) {
      state.position += 3;
      skipSeparationSpace(state, true, -1);
    }
    return;
  }
  if (state.position < state.length - 1) {
    throwError(state, "end of the stream or a document separator is expected");
  } else {
    return;
  }
}
function loadDocuments(input, options) {
  input = String(input);
  options = options || {};
  if (input.length !== 0) {
    if (input.charCodeAt(input.length - 1) !== 10 && input.charCodeAt(input.length - 1) !== 13) {
      input += "\n";
    }
    if (input.charCodeAt(0) === 65279) {
      input = input.slice(1);
    }
  }
  var state = new State$1(input, options);
  var nullpos = input.indexOf("\0");
  if (nullpos !== -1) {
    state.position = nullpos;
    throwError(state, "null byte is not allowed in input");
  }
  state.input += "\0";
  while (state.input.charCodeAt(state.position) === 32) {
    state.lineIndent += 1;
    state.position += 1;
  }
  while (state.position < state.length - 1) {
    readDocument(state);
  }
  return state.documents;
}
function loadAll$1(input, iterator, options) {
  if (iterator !== null && typeof iterator === "object" && typeof options === "undefined") {
    options = iterator;
    iterator = null;
  }
  var documents = loadDocuments(input, options);
  if (typeof iterator !== "function") {
    return documents;
  }
  for (var index = 0, length = documents.length; index < length; index += 1) {
    iterator(documents[index]);
  }
}
function load$1(input, options) {
  var documents = loadDocuments(input, options);
  if (documents.length === 0) {
    return void 0;
  } else if (documents.length === 1) {
    return documents[0];
  }
  throw new exception("expected a single document in the stream, but found more");
}
var loadAll_1 = loadAll$1;
var load_1 = load$1;
var loader = {
  loadAll: loadAll_1,
  load: load_1
};
var _toString = Object.prototype.toString;
var _hasOwnProperty = Object.prototype.hasOwnProperty;
var CHAR_BOM = 65279;
var CHAR_TAB = 9;
var CHAR_LINE_FEED = 10;
var CHAR_CARRIAGE_RETURN = 13;
var CHAR_SPACE = 32;
var CHAR_EXCLAMATION = 33;
var CHAR_DOUBLE_QUOTE = 34;
var CHAR_SHARP = 35;
var CHAR_PERCENT = 37;
var CHAR_AMPERSAND = 38;
var CHAR_SINGLE_QUOTE = 39;
var CHAR_ASTERISK = 42;
var CHAR_COMMA = 44;
var CHAR_MINUS = 45;
var CHAR_COLON = 58;
var CHAR_EQUALS = 61;
var CHAR_GREATER_THAN = 62;
var CHAR_QUESTION = 63;
var CHAR_COMMERCIAL_AT = 64;
var CHAR_LEFT_SQUARE_BRACKET = 91;
var CHAR_RIGHT_SQUARE_BRACKET = 93;
var CHAR_GRAVE_ACCENT = 96;
var CHAR_LEFT_CURLY_BRACKET = 123;
var CHAR_VERTICAL_LINE = 124;
var CHAR_RIGHT_CURLY_BRACKET = 125;
var ESCAPE_SEQUENCES = {};
ESCAPE_SEQUENCES[0] = "\\0";
ESCAPE_SEQUENCES[7] = "\\a";
ESCAPE_SEQUENCES[8] = "\\b";
ESCAPE_SEQUENCES[9] = "\\t";
ESCAPE_SEQUENCES[10] = "\\n";
ESCAPE_SEQUENCES[11] = "\\v";
ESCAPE_SEQUENCES[12] = "\\f";
ESCAPE_SEQUENCES[13] = "\\r";
ESCAPE_SEQUENCES[27] = "\\e";
ESCAPE_SEQUENCES[34] = '\\"';
ESCAPE_SEQUENCES[92] = "\\\\";
ESCAPE_SEQUENCES[133] = "\\N";
ESCAPE_SEQUENCES[160] = "\\_";
ESCAPE_SEQUENCES[8232] = "\\L";
ESCAPE_SEQUENCES[8233] = "\\P";
var DEPRECATED_BOOLEANS_SYNTAX = [
  "y",
  "Y",
  "yes",
  "Yes",
  "YES",
  "on",
  "On",
  "ON",
  "n",
  "N",
  "no",
  "No",
  "NO",
  "off",
  "Off",
  "OFF"
];
var DEPRECATED_BASE60_SYNTAX = /^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;
function compileStyleMap(schema2, map2) {
  var result, keys, index, length, tag, style, type2;
  if (map2 === null) return {};
  result = {};
  keys = Object.keys(map2);
  for (index = 0, length = keys.length; index < length; index += 1) {
    tag = keys[index];
    style = String(map2[tag]);
    if (tag.slice(0, 2) === "!!") {
      tag = "tag:yaml.org,2002:" + tag.slice(2);
    }
    type2 = schema2.compiledTypeMap["fallback"][tag];
    if (type2 && _hasOwnProperty.call(type2.styleAliases, style)) {
      style = type2.styleAliases[style];
    }
    result[tag] = style;
  }
  return result;
}
function encodeHex(character) {
  var string, handle, length;
  string = character.toString(16).toUpperCase();
  if (character <= 255) {
    handle = "x";
    length = 2;
  } else if (character <= 65535) {
    handle = "u";
    length = 4;
  } else if (character <= 4294967295) {
    handle = "U";
    length = 8;
  } else {
    throw new exception("code point within a string may not be greater than 0xFFFFFFFF");
  }
  return "\\" + handle + common.repeat("0", length - string.length) + string;
}
var QUOTING_TYPE_SINGLE = 1, QUOTING_TYPE_DOUBLE = 2;
function State(options) {
  this.schema = options["schema"] || _default;
  this.indent = Math.max(1, options["indent"] || 2);
  this.noArrayIndent = options["noArrayIndent"] || false;
  this.skipInvalid = options["skipInvalid"] || false;
  this.flowLevel = common.isNothing(options["flowLevel"]) ? -1 : options["flowLevel"];
  this.styleMap = compileStyleMap(this.schema, options["styles"] || null);
  this.sortKeys = options["sortKeys"] || false;
  this.lineWidth = options["lineWidth"] || 80;
  this.noRefs = options["noRefs"] || false;
  this.noCompatMode = options["noCompatMode"] || false;
  this.condenseFlow = options["condenseFlow"] || false;
  this.quotingType = options["quotingType"] === '"' ? QUOTING_TYPE_DOUBLE : QUOTING_TYPE_SINGLE;
  this.forceQuotes = options["forceQuotes"] || false;
  this.replacer = typeof options["replacer"] === "function" ? options["replacer"] : null;
  this.implicitTypes = this.schema.compiledImplicit;
  this.explicitTypes = this.schema.compiledExplicit;
  this.tag = null;
  this.result = "";
  this.duplicates = [];
  this.usedDuplicates = null;
}
function indentString(string, spaces) {
  var ind = common.repeat(" ", spaces), position = 0, next = -1, result = "", line, length = string.length;
  while (position < length) {
    next = string.indexOf("\n", position);
    if (next === -1) {
      line = string.slice(position);
      position = length;
    } else {
      line = string.slice(position, next + 1);
      position = next + 1;
    }
    if (line.length && line !== "\n") result += ind;
    result += line;
  }
  return result;
}
function generateNextLine(state, level) {
  return "\n" + common.repeat(" ", state.indent * level);
}
function testImplicitResolving(state, str2) {
  var index, length, type2;
  for (index = 0, length = state.implicitTypes.length; index < length; index += 1) {
    type2 = state.implicitTypes[index];
    if (type2.resolve(str2)) {
      return true;
    }
  }
  return false;
}
function isWhitespace(c) {
  return c === CHAR_SPACE || c === CHAR_TAB;
}
function isPrintable(c) {
  return 32 <= c && c <= 126 || 161 <= c && c <= 55295 && c !== 8232 && c !== 8233 || 57344 <= c && c <= 65533 && c !== CHAR_BOM || 65536 <= c && c <= 1114111;
}
function isNsCharOrWhitespace(c) {
  return isPrintable(c) && c !== CHAR_BOM && c !== CHAR_CARRIAGE_RETURN && c !== CHAR_LINE_FEED;
}
function isPlainSafe(c, prev, inblock) {
  var cIsNsCharOrWhitespace = isNsCharOrWhitespace(c);
  var cIsNsChar = cIsNsCharOrWhitespace && !isWhitespace(c);
  return (
    // ns-plain-safe
    (inblock ? (
      // c = flow-in
      cIsNsCharOrWhitespace
    ) : cIsNsCharOrWhitespace && c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET) && c !== CHAR_SHARP && !(prev === CHAR_COLON && !cIsNsChar) || isNsCharOrWhitespace(prev) && !isWhitespace(prev) && c === CHAR_SHARP || prev === CHAR_COLON && cIsNsChar
  );
}
function isPlainSafeFirst(c) {
  return isPrintable(c) && c !== CHAR_BOM && !isWhitespace(c) && c !== CHAR_MINUS && c !== CHAR_QUESTION && c !== CHAR_COLON && c !== CHAR_COMMA && c !== CHAR_LEFT_SQUARE_BRACKET && c !== CHAR_RIGHT_SQUARE_BRACKET && c !== CHAR_LEFT_CURLY_BRACKET && c !== CHAR_RIGHT_CURLY_BRACKET && c !== CHAR_SHARP && c !== CHAR_AMPERSAND && c !== CHAR_ASTERISK && c !== CHAR_EXCLAMATION && c !== CHAR_VERTICAL_LINE && c !== CHAR_EQUALS && c !== CHAR_GREATER_THAN && c !== CHAR_SINGLE_QUOTE && c !== CHAR_DOUBLE_QUOTE && c !== CHAR_PERCENT && c !== CHAR_COMMERCIAL_AT && c !== CHAR_GRAVE_ACCENT;
}
function isPlainSafeLast(c) {
  return !isWhitespace(c) && c !== CHAR_COLON;
}
function codePointAt(string, pos) {
  var first = string.charCodeAt(pos), second;
  if (first >= 55296 && first <= 56319 && pos + 1 < string.length) {
    second = string.charCodeAt(pos + 1);
    if (second >= 56320 && second <= 57343) {
      return (first - 55296) * 1024 + second - 56320 + 65536;
    }
  }
  return first;
}
function needIndentIndicator(string) {
  var leadingSpaceRe = /^\n* /;
  return leadingSpaceRe.test(string);
}
var STYLE_PLAIN = 1, STYLE_SINGLE = 2, STYLE_LITERAL = 3, STYLE_FOLDED = 4, STYLE_DOUBLE = 5;
function chooseScalarStyle(string, singleLineOnly, indentPerLevel, lineWidth, testAmbiguousType, quotingType, forceQuotes, inblock) {
  var i;
  var char = 0;
  var prevChar = null;
  var hasLineBreak = false;
  var hasFoldableLine = false;
  var shouldTrackWidth = lineWidth !== -1;
  var previousLineBreak = -1;
  var plain = isPlainSafeFirst(codePointAt(string, 0)) && isPlainSafeLast(codePointAt(string, string.length - 1));
  if (singleLineOnly || forceQuotes) {
    for (i = 0; i < string.length; char >= 65536 ? i += 2 : i++) {
      char = codePointAt(string, i);
      if (!isPrintable(char)) {
        return STYLE_DOUBLE;
      }
      plain = plain && isPlainSafe(char, prevChar, inblock);
      prevChar = char;
    }
  } else {
    for (i = 0; i < string.length; char >= 65536 ? i += 2 : i++) {
      char = codePointAt(string, i);
      if (char === CHAR_LINE_FEED) {
        hasLineBreak = true;
        if (shouldTrackWidth) {
          hasFoldableLine = hasFoldableLine || // Foldable line = too long, and not more-indented.
          i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== " ";
          previousLineBreak = i;
        }
      } else if (!isPrintable(char)) {
        return STYLE_DOUBLE;
      }
      plain = plain && isPlainSafe(char, prevChar, inblock);
      prevChar = char;
    }
    hasFoldableLine = hasFoldableLine || shouldTrackWidth && (i - previousLineBreak - 1 > lineWidth && string[previousLineBreak + 1] !== " ");
  }
  if (!hasLineBreak && !hasFoldableLine) {
    if (plain && !forceQuotes && !testAmbiguousType(string)) {
      return STYLE_PLAIN;
    }
    return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;
  }
  if (indentPerLevel > 9 && needIndentIndicator(string)) {
    return STYLE_DOUBLE;
  }
  if (!forceQuotes) {
    return hasFoldableLine ? STYLE_FOLDED : STYLE_LITERAL;
  }
  return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;
}
function writeScalar(state, string, level, iskey, inblock) {
  state.dump = function() {
    if (string.length === 0) {
      return state.quotingType === QUOTING_TYPE_DOUBLE ? '""' : "''";
    }
    if (!state.noCompatMode) {
      if (DEPRECATED_BOOLEANS_SYNTAX.indexOf(string) !== -1 || DEPRECATED_BASE60_SYNTAX.test(string)) {
        return state.quotingType === QUOTING_TYPE_DOUBLE ? '"' + string + '"' : "'" + string + "'";
      }
    }
    var indent = state.indent * Math.max(1, level);
    var lineWidth = state.lineWidth === -1 ? -1 : Math.max(Math.min(state.lineWidth, 40), state.lineWidth - indent);
    var singleLineOnly = iskey || state.flowLevel > -1 && level >= state.flowLevel;
    function testAmbiguity(string2) {
      return testImplicitResolving(state, string2);
    }
    switch (chooseScalarStyle(
      string,
      singleLineOnly,
      state.indent,
      lineWidth,
      testAmbiguity,
      state.quotingType,
      state.forceQuotes && !iskey,
      inblock
    )) {
      case STYLE_PLAIN:
        return string;
      case STYLE_SINGLE:
        return "'" + string.replace(/'/g, "''") + "'";
      case STYLE_LITERAL:
        return "|" + blockHeader(string, state.indent) + dropEndingNewline(indentString(string, indent));
      case STYLE_FOLDED:
        return ">" + blockHeader(string, state.indent) + dropEndingNewline(indentString(foldString(string, lineWidth), indent));
      case STYLE_DOUBLE:
        return '"' + escapeString(string) + '"';
      default:
        throw new exception("impossible error: invalid scalar style");
    }
  }();
}
function blockHeader(string, indentPerLevel) {
  var indentIndicator = needIndentIndicator(string) ? String(indentPerLevel) : "";
  var clip = string[string.length - 1] === "\n";
  var keep = clip && (string[string.length - 2] === "\n" || string === "\n");
  var chomp = keep ? "+" : clip ? "" : "-";
  return indentIndicator + chomp + "\n";
}
function dropEndingNewline(string) {
  return string[string.length - 1] === "\n" ? string.slice(0, -1) : string;
}
function foldString(string, width) {
  var lineRe = /(\n+)([^\n]*)/g;
  var result = function() {
    var nextLF = string.indexOf("\n");
    nextLF = nextLF !== -1 ? nextLF : string.length;
    lineRe.lastIndex = nextLF;
    return foldLine(string.slice(0, nextLF), width);
  }();
  var prevMoreIndented = string[0] === "\n" || string[0] === " ";
  var moreIndented;
  var match;
  while (match = lineRe.exec(string)) {
    var prefix = match[1], line = match[2];
    moreIndented = line[0] === " ";
    result += prefix + (!prevMoreIndented && !moreIndented && line !== "" ? "\n" : "") + foldLine(line, width);
    prevMoreIndented = moreIndented;
  }
  return result;
}
function foldLine(line, width) {
  if (line === "" || line[0] === " ") return line;
  var breakRe = / [^ ]/g;
  var match;
  var start = 0, end, curr = 0, next = 0;
  var result = "";
  while (match = breakRe.exec(line)) {
    next = match.index;
    if (next - start > width) {
      end = curr > start ? curr : next;
      result += "\n" + line.slice(start, end);
      start = end + 1;
    }
    curr = next;
  }
  result += "\n";
  if (line.length - start > width && curr > start) {
    result += line.slice(start, curr) + "\n" + line.slice(curr + 1);
  } else {
    result += line.slice(start);
  }
  return result.slice(1);
}
function escapeString(string) {
  var result = "";
  var char = 0;
  var escapeSeq;
  for (var i = 0; i < string.length; char >= 65536 ? i += 2 : i++) {
    char = codePointAt(string, i);
    escapeSeq = ESCAPE_SEQUENCES[char];
    if (!escapeSeq && isPrintable(char)) {
      result += string[i];
      if (char >= 65536) result += string[i + 1];
    } else {
      result += escapeSeq || encodeHex(char);
    }
  }
  return result;
}
function writeFlowSequence(state, level, object) {
  var _result = "", _tag = state.tag, index, length, value;
  for (index = 0, length = object.length; index < length; index += 1) {
    value = object[index];
    if (state.replacer) {
      value = state.replacer.call(object, String(index), value);
    }
    if (writeNode(state, level, value, false, false) || typeof value === "undefined" && writeNode(state, level, null, false, false)) {
      if (_result !== "") _result += "," + (!state.condenseFlow ? " " : "");
      _result += state.dump;
    }
  }
  state.tag = _tag;
  state.dump = "[" + _result + "]";
}
function writeBlockSequence(state, level, object, compact) {
  var _result = "", _tag = state.tag, index, length, value;
  for (index = 0, length = object.length; index < length; index += 1) {
    value = object[index];
    if (state.replacer) {
      value = state.replacer.call(object, String(index), value);
    }
    if (writeNode(state, level + 1, value, true, true, false, true) || typeof value === "undefined" && writeNode(state, level + 1, null, true, true, false, true)) {
      if (!compact || _result !== "") {
        _result += generateNextLine(state, level);
      }
      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
        _result += "-";
      } else {
        _result += "- ";
      }
      _result += state.dump;
    }
  }
  state.tag = _tag;
  state.dump = _result || "[]";
}
function writeFlowMapping(state, level, object) {
  var _result = "", _tag = state.tag, objectKeyList = Object.keys(object), index, length, objectKey, objectValue, pairBuffer;
  for (index = 0, length = objectKeyList.length; index < length; index += 1) {
    pairBuffer = "";
    if (_result !== "") pairBuffer += ", ";
    if (state.condenseFlow) pairBuffer += '"';
    objectKey = objectKeyList[index];
    objectValue = object[objectKey];
    if (state.replacer) {
      objectValue = state.replacer.call(object, objectKey, objectValue);
    }
    if (!writeNode(state, level, objectKey, false, false)) {
      continue;
    }
    if (state.dump.length > 1024) pairBuffer += "? ";
    pairBuffer += state.dump + (state.condenseFlow ? '"' : "") + ":" + (state.condenseFlow ? "" : " ");
    if (!writeNode(state, level, objectValue, false, false)) {
      continue;
    }
    pairBuffer += state.dump;
    _result += pairBuffer;
  }
  state.tag = _tag;
  state.dump = "{" + _result + "}";
}
function writeBlockMapping(state, level, object, compact) {
  var _result = "", _tag = state.tag, objectKeyList = Object.keys(object), index, length, objectKey, objectValue, explicitPair, pairBuffer;
  if (state.sortKeys === true) {
    objectKeyList.sort();
  } else if (typeof state.sortKeys === "function") {
    objectKeyList.sort(state.sortKeys);
  } else if (state.sortKeys) {
    throw new exception("sortKeys must be a boolean or a function");
  }
  for (index = 0, length = objectKeyList.length; index < length; index += 1) {
    pairBuffer = "";
    if (!compact || _result !== "") {
      pairBuffer += generateNextLine(state, level);
    }
    objectKey = objectKeyList[index];
    objectValue = object[objectKey];
    if (state.replacer) {
      objectValue = state.replacer.call(object, objectKey, objectValue);
    }
    if (!writeNode(state, level + 1, objectKey, true, true, true)) {
      continue;
    }
    explicitPair = state.tag !== null && state.tag !== "?" || state.dump && state.dump.length > 1024;
    if (explicitPair) {
      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
        pairBuffer += "?";
      } else {
        pairBuffer += "? ";
      }
    }
    pairBuffer += state.dump;
    if (explicitPair) {
      pairBuffer += generateNextLine(state, level);
    }
    if (!writeNode(state, level + 1, objectValue, true, explicitPair)) {
      continue;
    }
    if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {
      pairBuffer += ":";
    } else {
      pairBuffer += ": ";
    }
    pairBuffer += state.dump;
    _result += pairBuffer;
  }
  state.tag = _tag;
  state.dump = _result || "{}";
}
function detectType(state, object, explicit) {
  var _result, typeList, index, length, type2, style;
  typeList = explicit ? state.explicitTypes : state.implicitTypes;
  for (index = 0, length = typeList.length; index < length; index += 1) {
    type2 = typeList[index];
    if ((type2.instanceOf || type2.predicate) && (!type2.instanceOf || typeof object === "object" && object instanceof type2.instanceOf) && (!type2.predicate || type2.predicate(object))) {
      if (explicit) {
        if (type2.multi && type2.representName) {
          state.tag = type2.representName(object);
        } else {
          state.tag = type2.tag;
        }
      } else {
        state.tag = "?";
      }
      if (type2.represent) {
        style = state.styleMap[type2.tag] || type2.defaultStyle;
        if (_toString.call(type2.represent) === "[object Function]") {
          _result = type2.represent(object, style);
        } else if (_hasOwnProperty.call(type2.represent, style)) {
          _result = type2.represent[style](object, style);
        } else {
          throw new exception("!<" + type2.tag + '> tag resolver accepts not "' + style + '" style');
        }
        state.dump = _result;
      }
      return true;
    }
  }
  return false;
}
function writeNode(state, level, object, block, compact, iskey, isblockseq) {
  state.tag = null;
  state.dump = object;
  if (!detectType(state, object, false)) {
    detectType(state, object, true);
  }
  var type2 = _toString.call(state.dump);
  var inblock = block;
  var tagStr;
  if (block) {
    block = state.flowLevel < 0 || state.flowLevel > level;
  }
  var objectOrArray = type2 === "[object Object]" || type2 === "[object Array]", duplicateIndex, duplicate;
  if (objectOrArray) {
    duplicateIndex = state.duplicates.indexOf(object);
    duplicate = duplicateIndex !== -1;
  }
  if (state.tag !== null && state.tag !== "?" || duplicate || state.indent !== 2 && level > 0) {
    compact = false;
  }
  if (duplicate && state.usedDuplicates[duplicateIndex]) {
    state.dump = "*ref_" + duplicateIndex;
  } else {
    if (objectOrArray && duplicate && !state.usedDuplicates[duplicateIndex]) {
      state.usedDuplicates[duplicateIndex] = true;
    }
    if (type2 === "[object Object]") {
      if (block && Object.keys(state.dump).length !== 0) {
        writeBlockMapping(state, level, state.dump, compact);
        if (duplicate) {
          state.dump = "&ref_" + duplicateIndex + state.dump;
        }
      } else {
        writeFlowMapping(state, level, state.dump);
        if (duplicate) {
          state.dump = "&ref_" + duplicateIndex + " " + state.dump;
        }
      }
    } else if (type2 === "[object Array]") {
      if (block && state.dump.length !== 0) {
        if (state.noArrayIndent && !isblockseq && level > 0) {
          writeBlockSequence(state, level - 1, state.dump, compact);
        } else {
          writeBlockSequence(state, level, state.dump, compact);
        }
        if (duplicate) {
          state.dump = "&ref_" + duplicateIndex + state.dump;
        }
      } else {
        writeFlowSequence(state, level, state.dump);
        if (duplicate) {
          state.dump = "&ref_" + duplicateIndex + " " + state.dump;
        }
      }
    } else if (type2 === "[object String]") {
      if (state.tag !== "?") {
        writeScalar(state, state.dump, level, iskey, inblock);
      }
    } else if (type2 === "[object Undefined]") {
      return false;
    } else {
      if (state.skipInvalid) return false;
      throw new exception("unacceptable kind of an object to dump " + type2);
    }
    if (state.tag !== null && state.tag !== "?") {
      tagStr = encodeURI(
        state.tag[0] === "!" ? state.tag.slice(1) : state.tag
      ).replace(/!/g, "%21");
      if (state.tag[0] === "!") {
        tagStr = "!" + tagStr;
      } else if (tagStr.slice(0, 18) === "tag:yaml.org,2002:") {
        tagStr = "!!" + tagStr.slice(18);
      } else {
        tagStr = "!<" + tagStr + ">";
      }
      state.dump = tagStr + " " + state.dump;
    }
  }
  return true;
}
function getDuplicateReferences(object, state) {
  var objects = [], duplicatesIndexes = [], index, length;
  inspectNode(object, objects, duplicatesIndexes);
  for (index = 0, length = duplicatesIndexes.length; index < length; index += 1) {
    state.duplicates.push(objects[duplicatesIndexes[index]]);
  }
  state.usedDuplicates = new Array(length);
}
function inspectNode(object, objects, duplicatesIndexes) {
  var objectKeyList, index, length;
  if (object !== null && typeof object === "object") {
    index = objects.indexOf(object);
    if (index !== -1) {
      if (duplicatesIndexes.indexOf(index) === -1) {
        duplicatesIndexes.push(index);
      }
    } else {
      objects.push(object);
      if (Array.isArray(object)) {
        for (index = 0, length = object.length; index < length; index += 1) {
          inspectNode(object[index], objects, duplicatesIndexes);
        }
      } else {
        objectKeyList = Object.keys(object);
        for (index = 0, length = objectKeyList.length; index < length; index += 1) {
          inspectNode(object[objectKeyList[index]], objects, duplicatesIndexes);
        }
      }
    }
  }
}
function dump$1(input, options) {
  options = options || {};
  var state = new State(options);
  if (!state.noRefs) getDuplicateReferences(input, state);
  var value = input;
  if (state.replacer) {
    value = state.replacer.call({ "": value }, "", value);
  }
  if (writeNode(state, 0, value, true, true)) return state.dump + "\n";
  return "";
}
var dump_1 = dump$1;
var dumper = {
  dump: dump_1
};
function renamed(from, to) {
  return function() {
    throw new Error("Function yaml." + from + " is removed in js-yaml 4. Use yaml." + to + " instead, which is now safe by default.");
  };
}
var Type = type;
var Schema = schema;
var FAILSAFE_SCHEMA = failsafe;
var JSON_SCHEMA = json;
var CORE_SCHEMA = core;
var DEFAULT_SCHEMA = _default;
var load = loader.load;
var loadAll = loader.loadAll;
var dump = dumper.dump;
var YAMLException = exception;
var types = {
  binary,
  float,
  map,
  null: _null,
  pairs,
  set,
  timestamp,
  bool,
  int,
  merge,
  omap,
  seq,
  str
};
var safeLoad = renamed("safeLoad", "load");
var safeLoadAll = renamed("safeLoadAll", "loadAll");
var safeDump = renamed("safeDump", "dump");
var jsYaml = {
  Type,
  Schema,
  FAILSAFE_SCHEMA,
  JSON_SCHEMA,
  CORE_SCHEMA,
  DEFAULT_SCHEMA,
  load,
  loadAll,
  dump,
  YAMLException,
  types,
  safeLoad,
  safeLoadAll,
  safeDump
};
console.log("UNIVERSAL_ASSET_SERVICE_JS: File execution started.");
const ASSET_PACKS_DIR_NAME = "asset_packs";
const TGC_ASSET_PROTOCOL = "tgc-asset";
let globalAssetsCache = {
  props: [],
  costumes: [],
  poses: [],
  scene_cards: [],
  achievements: [],
  scripts: [],
  role_cards: []
};
let isInitialized = false;
let userDataPathInstance;
function validateAsset(assetData, filePath) {
  const requiredFields = ["id", "type", "name", "description"];
  for (const field of requiredFields) {
    if (assetData[field] === void 0 || assetData[field] === null) {
      console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Missing required field: ${field}`);
      return false;
    }
  }
  if (typeof assetData.id !== "string" || !assetData.id.trim()) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'id'.`);
    return false;
  }
  if (typeof assetData.type !== "string" || !assetData.type.trim()) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'type'.`);
    return false;
  }
  if (typeof assetData.name !== "string" || !assetData.name.trim()) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'name'.`);
    return false;
  }
  if (assetData.type === "scene_card" && (typeof assetData.background_image_path !== "string" || !assetData.background_image_path.trim())) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for scene_card in ${filePath}. Missing or invalid 'background_image_path'.`);
    return false;
  }
  if (assetData.type === "script" && (typeof assetData.title !== "string" || !assetData.title.trim())) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for script in ${filePath}. Missing or invalid 'title'.`);
    return false;
  }
  if (assetData.type === "script" && (!Array.isArray(assetData.scenes) || assetData.scenes.length === 0)) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for script in ${filePath}. Missing or empty 'scenes' array.`);
    return false;
  }
  return true;
}
function resolveAssetPath(assetPackPath, relativePathInYaml) {
  if (!relativePathInYaml) return null;
  const packDir = path.dirname(assetPackPath);
  path.resolve(packDir, relativePathInYaml);
  const packFileName = path.basename(assetPackPath);
  const packNameWithoutExt = packFileName.replace(/\.pack\.yaml$/, "");
  const normalizedRelativePath = relativePathInYaml.replace(/\\/g, "/");
  return `${TGC_ASSET_PROTOCOL}://${packNameWithoutExt}/${normalizedRelativePath}`;
}
async function loadAssetPacks() {
  if (!userDataPathInstance) {
    console.error("UNIVERSAL_ASSET_SERVICE: User data path not set. Cannot load asset packs.");
    return;
  }
  const assetPacksPath = path.join(userDataPathInstance, ASSET_PACKS_DIR_NAME);
  console.log(`UNIVERSAL_ASSET_SERVICE: Scanning for asset packs in: ${assetPacksPath}`);
  try {
    await fsPromises$1.mkdir(assetPacksPath, { recursive: true });
    const files = await fsPromises$1.readdir(assetPacksPath);
    const packFiles = files.filter((file) => file.endsWith(".pack.yaml"));
    console.log(`UNIVERSAL_ASSET_SERVICE: Found ${packFiles.length} .pack.yaml files.`);
    const newGlobalAssetsCache = {
      props: [],
      costumes: [],
      poses: [],
      scene_cards: [],
      achievements: [],
      scripts: [],
      role_cards: []
    };
    for (const packFile of packFiles) {
      const filePath = path.join(assetPacksPath, packFile);
      try {
        console.log(`UNIVERSAL_ASSET_SERVICE: Reading asset pack: ${filePath}`);
        const fileContent = await fsPromises$1.readFile(filePath, "utf8");
        const packData = jsYaml.load(fileContent);
        if (!packData || typeof packData !== "object") {
          console.warn(`UNIVERSAL_ASSET_SERVICE: Invalid or empty YAML content in ${filePath}. Skipping.`);
          continue;
        }
        const assetsInPack = Array.isArray(packData) ? packData : packData.assets || [];
        if (!Array.isArray(assetsInPack)) {
          console.warn(`UNIVERSAL_ASSET_SERVICE: 'assets' key in ${filePath} is not an array or pack is not an array itself. Skipping.`);
          continue;
        }
        console.log(`UNIVERSAL_ASSET_SERVICE: Processing ${assetsInPack.length} assets from ${filePath}`);
        for (const asset of assetsInPack) {
          if (validateAsset(asset, filePath)) {
            if (asset.icon_path) {
              asset.icon_path = resolveAssetPath(filePath, asset.icon_path);
            }
            if (asset.cg_image_path) {
              asset.cg_image_path = resolveAssetPath(filePath, asset.cg_image_path);
            }
            if (asset.type === "scene_card") {
              if (asset.background_image_path) {
                asset.background_image_path = resolveAssetPath(filePath, asset.background_image_path);
              }
              if (asset.default_bgm_path) {
                asset.default_bgm_path = resolveAssetPath(filePath, asset.default_bgm_path);
              }
            }
            if (asset.type === "role_card") {
              asset.initial_status_override_json = asset.initial_status_override_json || "{}";
              asset.persona_snippet_override = asset.persona_snippet_override || "";
            }
            if (asset.type === "achievement" && asset.icon_path) {
              asset.icon_path = resolveAssetPath(filePath, asset.icon_path);
            }
            const assetTypeKey = `${asset.type}s`;
            if (newGlobalAssetsCache[assetTypeKey]) {
              newGlobalAssetsCache[assetTypeKey].push(asset);
            } else {
              console.warn(`UNIVERSAL_ASSET_SERVICE: Unknown asset type '${asset.type}' in ${filePath}. Storing under 'unknown'.`);
              newGlobalAssetsCache.unknown = newGlobalAssetsCache.unknown || [];
              newGlobalAssetsCache.unknown.push(asset);
            }
          }
        }
      } catch (fileError) {
        console.error(`UNIVERSAL_ASSET_SERVICE: Error processing asset pack ${filePath}:`, fileError.message);
      }
    }
    globalAssetsCache = newGlobalAssetsCache;
    console.log("UNIVERSAL_ASSET_SERVICE: Asset packs loaded. Cache updated.");
    logCacheSummary();
  } catch (dirError) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Error reading asset packs directory ${assetPacksPath}:`, dirError.message);
  }
}
function logCacheSummary() {
  console.log("UNIVERSAL_ASSET_SERVICE: Current Global Assets Cache Summary:");
  for (const key in globalAssetsCache) {
    if (globalAssetsCache[key] && Array.isArray(globalAssetsCache[key])) {
      console.log(`  - ${key}: ${globalAssetsCache[key].length} items`);
    }
  }
}
function initializeUniversalAssetService(appUserDataPath) {
  if (isInitialized) {
    console.warn("UNIVERSAL_ASSET_SERVICE: Already initialized.");
    return;
  }
  userDataPathInstance = appUserDataPath;
  console.log("UNIVERSAL_ASSET_SERVICE: Initializing with userDataPath:", userDataPathInstance);
  if (protocol && typeof protocol.registerFileProtocol === "function") {
    const assetPacksRootPath = path.join(userDataPathInstance, ASSET_PACKS_DIR_NAME);
    if (!protocol.isProtocolRegistered(TGC_ASSET_PROTOCOL)) {
      protocol.registerFileProtocol(TGC_ASSET_PROTOCOL, (request, callback) => {
        try {
          const urlPath = request.url.slice(`${TGC_ASSET_PROTOCOL}://`.length).split("?")[0];
          const decodedUrlPath = decodeURI(urlPath);
          const absoluteDiskPath = path.join(assetPacksRootPath, decodedUrlPath);
          if (fs.existsSync(absoluteDiskPath)) {
            callback({ path: absoluteDiskPath });
          } else {
            console.error(`UNIVERSAL_ASSET_SERVICE (${TGC_ASSET_PROTOCOL}): File not found at ${absoluteDiskPath} (requested: ${request.url})`);
            callback({ error: -6 });
          }
        } catch (e) {
          console.error(`UNIVERSAL_ASSET_SERVICE (${TGC_ASSET_PROTOCOL}): Error resolving path for ${request.url}`, e);
          callback({ error: -2 });
        }
      });
      console.log(`UNIVERSAL_ASSET_SERVICE: '${TGC_ASSET_PROTOCOL}' custom protocol registered. Root: ${assetPacksRootPath}`);
    } else {
      console.log(`UNIVERSAL_ASSET_SERVICE: '${TGC_ASSET_PROTOCOL}' custom protocol ALREADY registered.`);
    }
  } else {
    console.error("UNIVERSAL_ASSET_SERVICE: Electron protocol module not available. Asset paths may not work.");
  }
  loadAssetPacks();
  isInitialized = true;
  console.log("UNIVERSAL_ASSET_SERVICE: Initialization complete.");
}
function getAssetsByType(assetType) {
  const assetTypeKey = `${assetType}s`;
  return globalAssetsCache[assetTypeKey] || [];
}
function getAllLoadedAssets() {
  return globalAssetsCache;
}
function getAssetById(assetId) {
  if (!assetId) return null;
  for (const assetTypeKey in globalAssetsCache) {
    if (globalAssetsCache.hasOwnProperty(assetTypeKey)) {
      const assets = globalAssetsCache[assetTypeKey];
      if (Array.isArray(assets)) {
        const foundAsset = assets.find((asset) => asset.id === assetId);
        if (foundAsset) {
          return foundAsset;
        }
      }
    }
  }
  console.warn(`UNIVERSAL_ASSET_SERVICE: Asset with ID '${assetId}' not found in cache.`);
  return null;
}
function refreshAssetPacks() {
  console.log("UNIVERSAL_ASSET_SERVICE: Refreshing asset packs...");
  return loadAssetPacks();
}
console.log("UNIVERSAL_ASSET_SERVICE_JS: File execution finished. Exports configured.");
const universalAssetService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  getAllLoadedAssets,
  getAssetById,
  getAssetsByType,
  initializeUniversalAssetService,
  refreshAssetPacks
}, Symbol.toStringTag, { value: "Module" }));
console.log("PROMPT_BUILDER_SERVICE_JS: File execution started.");
const FALLBACK_LINLUO_PERSONA = "我叫林珞，你的AI伴侣。";
const FALLBACK_XIAOLAN_PERSONA = "我是林小岚，技术助手。";
const FALLBACK_YUJING_PERSONA = "我是语镜，视觉总监。";
const FALLBACK_USER_PROFILE = USER_PROFILE_DEFAULTS.content;
const getCleanText = (html) => {
  if (typeof DOMParser === "undefined" && typeof document !== "undefined") {
    const div = document.createElement("div");
    div.innerHTML = html;
    return (div.textContent || div.innerText || "").trim();
  }
  if (typeof DOMParser === "undefined") {
    return html.replace(/<[^>]*>?/gm, "").trim();
  }
  try {
    const doc = new DOMParser().parseFromString(html, "text/html");
    return (doc.body.textContent || "").trim();
  } catch (e) {
    console.warn("PROMPT_BUILDER_SERVICE: getCleanText - DOMParser failed, using regex fallback.", e.message);
    return html.replace(/<[^>]*>?/gm, "").trim();
  }
};
async function buildSystemInstruction(targetIdentifier, roleCardId, currentLinLuoDetailedStatus, postId, characterId, taskCockpitUserInput, taskCockpitContext, classifiedIntent) {
  let baseSystemInstruction = "";
  let postName = "未知岗位";
  let characterName = "未知角色";
  let isFunctionalPersona = false;
  const characters = await getCharacters$1();
  const functionalPersonaIds = ["SummarizerPersona", "ClassifierPersona", "OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "RoundtableFacilitatorPersona", "user_profile", "linluo_persona", "xiaolan_persona", "yujing_persona", "IntentClassifierPersona"];
  if (functionalPersonaIds.includes(targetIdentifier)) {
    isFunctionalPersona = true;
  }
  if (!isFunctionalPersona && postId && characterId && CORE_POSTS && characters) {
    const postInfo = CORE_POSTS.find((p) => p.id === postId);
    const charInfo = characters.find((c) => c.id === characterId);
    if (postInfo) postName = postInfo.name;
    if (charInfo) characterName = charInfo.name;
    baseSystemInstruction = `[[你当前的虚拟岗位是: ${postName}]]
`;
    if (postInfo == null ? void 0 : postInfo.description) {
      baseSystemInstruction += `[[岗位核心职责]]:
${postInfo.description}

`;
    }
    baseSystemInstruction += `[[你当前扮演的角色人格是: ${characterName}]]
`;
    let charPersonaContent = charInfo == null ? void 0 : charInfo.persona_prompt;
    if (!charPersonaContent) {
      const dbPersonaSettingId = `${characterId}_persona`;
      const dbPersona = await getAgentCoreSetting(dbPersonaSettingId);
      if (dbPersona == null ? void 0 : dbPersona.content) {
        charPersonaContent = `[[核心人格设定 (来自DB)]]:
${dbPersona.content}
`;
      } else {
        charPersonaContent = `[[核心人格设定 (默认)]]:
${characterId === "linluo" ? FALLBACK_LINLUO_PERSONA : characterId === "xiaolan" ? FALLBACK_XIAOLAN_PERSONA : characterId === "yujing" ? FALLBACK_YUJING_PERSONA : "你是一个有用的AI助手。"}
`;
      }
    }
    baseSystemInstruction += charPersonaContent;
  } else {
    const dbPersona = await getAgentCoreSetting(targetIdentifier);
    if (dbPersona == null ? void 0 : dbPersona.content) {
      baseSystemInstruction = dbPersona.content;
    } else {
      const charInfo = characters.find((c) => c.id === targetIdentifier);
      if (charInfo && charInfo.persona_prompt) {
        baseSystemInstruction = charInfo.persona_prompt;
      } else {
        if (targetIdentifier === "linluo_persona" || targetIdentifier === "LinLuo") baseSystemInstruction = FALLBACK_LINLUO_PERSONA;
        else if (targetIdentifier === "xiaolan_persona" || targetIdentifier === "XiaoLan") baseSystemInstruction = FALLBACK_XIAOLAN_PERSONA;
        else if (targetIdentifier === "yujing_persona" || targetIdentifier === "YuJing") baseSystemInstruction = FALLBACK_YUJING_PERSONA;
        else if (targetIdentifier === "user_profile") baseSystemInstruction = FALLBACK_USER_PROFILE;
        else if (targetIdentifier === "summarizer_persona") baseSystemInstruction = "你是一个高效的AI文本摘要助手。请将提供的对话或文本内容浓缩为一段简洁、精确的摘要，突出核心信息和关键点。摘要应自然流畅，易于理解。";
        else if (targetIdentifier === "classifier_persona") baseSystemInstruction = '你是一个AI意图分类器。根据用户输入，判断其主要意图。如果用户似乎在提问、寻求信息或希望进行知识检索，请回答 "RETRIEVAL"。如果用户似乎在进行常规对话、闲聊或情感交流，请回答 "CONVERSATIONAL"。如果无法明确判断，优先回答 "CONVERSATIONAL"。你只能回答 "RETRIEVAL" 或 "CONVERSATIONAL"。';
        else if (targetIdentifier === "IntentClassifierPersona") {
          const intentClassifierChar = characters.find((c) => c.id === "IntentClassifierPersona");
          baseSystemInstruction = (intentClassifierChar == null ? void 0 : intentClassifierChar.persona_prompt) || "你是一位高效的意图分类专家...（请参考完整的默认提示）";
        } else baseSystemInstruction = FALLBACK_USER_PROFILE;
      }
    }
    if (targetIdentifier === "TaskCockpitIntentRouterPersona" && taskCockpitUserInput && taskCockpitContext) {
      let personaTemplate = baseSystemInstruction;
      personaTemplate = personaTemplate.replace("{userInput}", taskCockpitUserInput || "");
      personaTemplate = personaTemplate.replace("{taskId}", taskCockpitContext.taskId || "未提供");
      personaTemplate = personaTemplate.replace("{currentFileInRightPanePath}", taskCockpitContext.currentFileInRightPanePath || "无");
      personaTemplate = personaTemplate.replace("{selectedTextInRightPaneEditor}", taskCockpitContext.selectedTextInRightPaneEditor || "无");
      personaTemplate = personaTemplate.replace("{currentRightPaneView}", taskCockpitContext.currentRightPaneView || "未知");
      personaTemplate = personaTemplate.replace("{workspaceRoot}", taskCockpitContext.workspaceRoot || "未知");
      baseSystemInstruction = personaTemplate;
    }
  }
  if (classifiedIntent && classifiedIntent !== "unknown" && !isFunctionalPersona) {
    baseSystemInstruction += `

[[当前用户意图 (供你参考)]]: ${classifiedIntent}。请在你的回应中，结合此意图，提供更具针对性的协助或交流。`;
  }
  let finalSystemInstruction = baseSystemInstruction;
  const effectiveCharIdForRoleCard = characterId || (["LinLuo", "linluo_persona"].includes(targetIdentifier) ? "linluo" : null);
  if (roleCardId && effectiveCharIdForRoleCard === "linluo") {
    let card = getAssetById(roleCardId);
    if (!card || card.type !== "role_card" && card.type !== "db_role_card") {
      card = await getRolePlayingCardById(roleCardId);
    }
    if (card && card.persona_snippet_override) {
      const roleCardInstruction = `
[[当前激活的角色卡设定 (优先扮演此设定)]]:
${card.persona_snippet_override}`;
      finalSystemInstruction = `${roleCardInstruction}

${finalSystemInstruction}`;
    }
  }
  const effectiveCharIdForStatus = characterId || targetIdentifier;
  if (["linluo", "LinLuo", "linluo_persona"].includes(effectiveCharIdForStatus) && currentLinLuoDetailedStatus) {
    const statusString = `
[[当前状态(仅供你参考，不要直接输出给用户)]]: ${JSON.stringify(currentLinLuoDetailedStatus)}`;
    finalSystemInstruction = `${statusString}

${finalSystemInstruction}`;
  }
  const contextHandlingInstruction = `


[[[核心铁律：关于参考资料的处理原则]]]
系统有时会为你提供从项目知识库中检索到的“参考资料”（Context）。
【首要原则】：你必须首先独立判断这些“参考资料”，与用户当前最新的问题和对话上下文，是否【直接】且【高度相关】。
【引用规则】：只有当资料高度相关，且能明确帮助你生成更准确、更有深度的回答时，才可引用或基于其进行回答。引用时必须自然地融入到你的对话风格中。
【拒绝规则】：如果“参考资料”与当前对话主题明显无关、风马牛不相及，你【必须】果断地、完全地【忽略】这些资料！绝不允许为了使用而使用，进行任何牵强附会、强行关联的“脑补”！在这种情况下，你应该礼貌地说明未找到直接相关的资料，并仅基于你自身的知识和对话历史进行回答。
你的回答必须永远保证逻辑清晰、连贯自然，绝不能被无关信息污染，这是最高指令。`;
  finalSystemInstruction += contextHandlingInstruction;
  return finalSystemInstruction;
}
async function buildRoundtableSystemInstruction(currentSpeakerCharacterId, currentSpeakerPostId, meetingTopic, otherParticipantNames, currentTurnNumber, lastSpeakerCharacterId, lastResponseText) {
  const characters = await getCharacters$1();
  const speakerCharInfo = characters.find((c) => c.id === currentSpeakerCharacterId);
  const speakerPostInfo = CORE_POSTS.find((p) => p.id === currentSpeakerPostId);
  if (!speakerCharInfo || !speakerPostInfo) {
    console.error(`PROMPT_BUILDER: Invalid speakerCharInfo or speakerPostInfo for roundtable. CharID: ${currentSpeakerCharacterId}, PostID: ${currentSpeakerPostId}`);
    return "你是一位AI助手，正在参与一场多方讨论。请就议题发表你的看法。";
  }
  let basePersona = speakerCharInfo.persona_prompt || `你扮演的是 ${speakerCharInfo.name}。`;
  let postResponsibilities = speakerPostInfo.description ? `你的岗位职责是：${speakerPostInfo.description}` : "";
  let instruction = `你正在参与一场名为“${meetingTopic}”的圆桌会议。
你当前扮演的角色是【${speakerCharInfo.name}】，担任【${speakerPostInfo.name}】的岗位。${postResponsibilities}
其他与会者包括：${otherParticipantNames.join("、 ")}。
当前是会议的第 ${currentTurnNumber} 轮发言。

`;
  if (lastSpeakerCharacterId === "user") {
    instruction += `会议发起人 (舰长) 提出了初始议题或最新指示：“${lastResponseText}”。
请针对此议题/指示，并结合你的角色与职责，发表你的看法。`;
  } else {
    const lastSpeakerInfo = characters.find((c) => c.id === lastSpeakerCharacterId);
    const lastSpeakerName = lastSpeakerInfo ? lastSpeakerInfo.name : "上一位发言者";
    instruction += `上一位发言者是【${lastSpeakerName}】，其主要观点是：“${getCleanText(lastResponseText).substring(0, 200)}${getCleanText(lastResponseText).length > 200 ? "..." : ""}”。
请针对其发言，并结合整体议题及你的角色与职责，阐述你的看法或补充。`;
  }
  instruction += `

[[你的核心人格设定]]:
${basePersona}`;
  instruction += `

请确保你的发言既符合你的角色人格，也体现你的岗位职责。发言应有条理，观点明确。`;
  const contextHandlingInstruction = `


[[[核心铁律：关于参考资料的处理原则]]]
系统有时会为你提供从项目知识库中检索到的“参考资料”（Context）。
【首要原则】：你必须首先独立判断这些“参考资料”，与用户当前最新的问题和对话上下文，是否【直接】且【高度相关】。
【引用规则】：只有当资料高度相关，且能明确帮助你生成更准确、更有深度的回答时，才可引用或基于其进行回答。引用时必须自然地融入到你的对话风格中。
【拒绝规则】：如果“参考资料”与当前对话主题明显无关、风马牛不相及，你【必须】果断地、完全地【忽略】这些资料！绝不允许为了使用而使用，进行任何牵强附会、强行关联的“脑补”！在这种情况下，你应该礼貌地说明未找到直接相关的资料，并仅基于你自身的知识和对话历史进行回答。
你的回答必须永远保证逻辑清晰、连贯自然，绝不能被无关信息污染，这是最高指令。`;
  instruction += contextHandlingInstruction;
  return instruction;
}
function prepareUserHistoryForAI(history, memoryContextString, userPrompt) {
  const mappedHistory = (history || []).filter((msg) => typeof msg.text === "string").map((msg) => ({
    role: msg.sender === "user" ? "user" : "model",
    parts: [{ text: getCleanText(msg.text) }]
  }));
  let requestContents = mappedHistory;
  const lastUserContentIndex = requestContents.map((c) => c.role).lastIndexOf("user");
  let finalUserPromptSegment = userPrompt;
  if (memoryContextString && memoryContextString.trim() !== "") {
    finalUserPromptSegment = `${memoryContextString}

[[用户最新指令/问题]]:
${userPrompt}`;
  }
  if (lastUserContentIndex !== -1 && requestContents[lastUserContentIndex].role === "user" && requestContents[lastUserContentIndex].parts[0].text === userPrompt && (!memoryContextString || memoryContextString.trim() === "")) ;
  else {
    requestContents.push({ role: "user", parts: [{ text: finalUserPromptSegment }] });
  }
  return requestContents;
}
console.log("PROMPT_BUILDER_SERVICE_JS: File execution finished. Exports configured.");
console.log("MEMORY_AUGMENTATION_SERVICE_JS: File execution started.");
async function retrieveAndFormatMemories(queryText, personaTarget, projectId, contextType, keywords, desiredMemoryTypes, apiKey, embeddingModelName, dbService2, aiKernelService2) {
  if (!queryText || !queryText.trim()) {
    return "";
  }
  const queryEmbeddingResult = await aiKernelService2.embedContentInternal(queryText, "RETRIEVAL_QUERY", void 0, apiKey, embeddingModelName);
  if (typeof queryEmbeddingResult === "string" || queryEmbeddingResult === null) {
    console.warn("MEMORY_AUGMENTATION_SERVICE: Embedding for memory retrieval failed or returned null/string error:", queryEmbeddingResult);
    return "";
  }
  const queryEmbedding = queryEmbeddingResult;
  const relevantMemories = await dbService2.findRelevantMemories(
    queryEmbedding,
    {
      personaTarget,
      projectContextId: projectId || null,
      contextType: contextType || "general",
      currentTopicKeywords: keywords || [],
      desiredMemoryTypes: desiredMemoryTypes || []
    },
    5
    // Default limit for RAG context
  );
  let memoryContext = "";
  if (relevantMemories.direct.length > 0) {
    memoryContext += "\n\n[[相关核心记忆碎片]]:\n";
    relevantMemories.direct.forEach((mem) => {
      memoryContext += `- ${mem.memory_content} (重要性: ${mem.importance}, 类型: ${mem.memory_type || "未知"})
`;
    });
  }
  if (relevantMemories.associated.length > 0) {
    const limitedAssociated = relevantMemories.associated.slice(0, 2);
    if (limitedAssociated.length > 0) {
      memoryContext += "\n[[相关联想记忆碎片]]:\n";
      limitedAssociated.forEach((mem) => {
        memoryContext += `- ${mem.memory_content}
`;
      });
    }
  }
  return memoryContext.trim();
}
console.log("MEMORY_AUGMENTATION_SERVICE_JS: File execution finished. Exports configured.");
console.log("MEMORY_PERSISTENCE_SERVICE_JS: File execution started.");
function parseActionCommandsInternal(text) {
  const commands = [];
  const genericCommandRegex = /\[\[([A-Z_]+):({.*?})\]\]/sg;
  let match;
  while ((match = genericCommandRegex.exec(text)) !== null) {
    try {
      const commandType = match[1];
      const payload = JSON.parse(match[2]);
      commands.push({ type: commandType, payload });
    } catch (e) {
      console.error(`MEMORY_PERSISTENCE_SERVICE: Failed to parse command JSON for type ${match[1]}:`, e, match[2]);
    }
  }
  const cleanedText = text.replace(genericCommandRegex, "").trim();
  return { cleanedText, commands };
}
async function processResponseForActions(rawResponseText, projectId, dbService2) {
  if (typeof rawResponseText !== "string") {
    console.warn("MEMORY_PERSISTENCE_SERVICE: rawResponseText is not a string. Cannot process actions.");
    return { cleanedText: String(rawResponseText), actionResults: [] };
  }
  const { cleanedText, commands } = parseActionCommandsInternal(rawResponseText);
  const actionResults = [];
  if (commands.length > 0) {
    console.log(`MEMORY_PERSISTENCE_SERVICE: Found ${commands.length} actions to process.`);
    for (const command of commands) {
      try {
        switch (command.type) {
          case "STORE_MEMORY":
            console.log("MEMORY_PERSISTENCE_SERVICE: Executing STORE_MEMORY command:", command.payload);
            const memoryPayload = {
              // Ensure all required fields for addCoreMemory are present or defaulted
              project_context_id: command.payload.project_context_id || projectId || null,
              ...command.payload
            };
            const addedMemory = await dbService2.addCoreMemory(memoryPayload);
            actionResults.push({ type: "STORE_MEMORY", success: !!addedMemory, detail: addedMemory || "Failed to add memory." });
            break;
          case "DEVELOP_BODY_ZONE":
            console.log("MEMORY_PERSISTENCE_SERVICE: Executing DEVELOP_BODY_ZONE command:", command.payload);
            if (command.payload.zone && typeof command.payload.points === "number") {
              const devResult = await dbService2.updateBodyDevelopment(command.payload.zone, command.payload.points);
              actionResults.push({ type: "DEVELOP_BODY_ZONE", success: devResult.success, detail: devResult });
            } else {
              actionResults.push({ type: "DEVELOP_BODY_ZONE", success: false, detail: "Invalid payload for DEVELOP_BODY_ZONE." });
            }
            break;
          case "TRIGGER_ACHIEVEMENT":
            console.log("MEMORY_PERSISTENCE_SERVICE: Executing TRIGGER_ACHIEVEMENT command:", command.payload);
            if (command.payload.id) {
              const achResult = await dbService2.unlockAchievement("default_user", command.payload.id);
              actionResults.push({ type: "TRIGGER_ACHIEVEMENT", success: achResult.success, detail: achResult });
            } else {
              actionResults.push({ type: "TRIGGER_ACHIEVEMENT", success: false, detail: "Invalid payload for TRIGGER_ACHIEVEMENT, missing id." });
            }
            break;
          default:
            console.warn(`MEMORY_PERSISTENCE_SERVICE: Unknown command type '${command.type}'. Skipping.`);
            actionResults.push({ type: command.type, success: false, detail: "Unknown command type." });
        }
      } catch (error) {
        console.error(`MEMORY_PERSISTENCE_SERVICE: Error executing command ${command.type}:`, error);
        actionResults.push({ type: command.type, success: false, detail: error.message });
      }
    }
  }
  return { cleanedText, actionResults };
}
console.log("MEMORY_PERSISTENCE_SERVICE_JS: File execution finished. Exports configured.");
console.log("FILE_SYSTEM_SERVICE_JS: File execution started.");
async function listFiles(directoryPath, recursive = false, depth = Infinity, currentDepth = 0) {
  console.log(`FILE_SYSTEM_SERVICE: Listing files for directory: ${directoryPath}, recursive: ${recursive}, depth: ${depth}, currentDepth: ${currentDepth}`);
  if (currentDepth >= depth) {
    return [];
  }
  try {
    await fsPromises$1.access(directoryPath, fsPromises$1.constants.R_OK);
    const dirents = await fsPromises$1.readdir(directoryPath, { withFileTypes: true });
    const files = await Promise.all(
      dirents.map(async (dirent) => {
        const resPath = path.join(directoryPath, dirent.name);
        const fileNode = {
          name: dirent.name,
          path: resPath,
          type: dirent.isDirectory() ? "directory" : "file"
        };
        if (dirent.isDirectory() && recursive) {
          fileNode.children = await listFiles(resPath, true, depth, currentDepth + 1);
        }
        return fileNode;
      })
    );
    if (currentDepth === 0) console.log(`FILE_SYSTEM_SERVICE: Found ${files.length} items in ${directoryPath} (root level)`);
    return files;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error listing files in ${directoryPath}:`, error);
    if (error.code === "ENOENT") {
      return { error: `Directory not found: ${directoryPath}` };
    } else if (error.code === "EACCES") {
      return { error: `Permission denied for directory: ${directoryPath}` };
    }
    return { error: `Failed to list files: ${error.message}` };
  }
}
async function readFile(filePath) {
  console.log(`FILE_SYSTEM_SERVICE: Reading file: ${filePath}`);
  try {
    await fsPromises$1.access(filePath, fsPromises$1.constants.R_OK);
    const content = await fsPromises$1.readFile(filePath, "utf-8");
    return content;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error reading file ${filePath}:`, error);
    if (error.code === "ENOENT") {
      return { error: `File not found: ${filePath}` };
    } else if (error.code === "EACCES") {
      return { error: `Permission denied for file: ${filePath}` };
    }
    return { error: `Failed to read file: ${error.message}` };
  }
}
async function writeFile(filePath, content) {
  console.log(`FILE_SYSTEM_SERVICE: Writing to file: ${filePath}`);
  try {
    await fsPromises$1.writeFile(filePath, content, "utf-8");
    console.log(`FILE_SYSTEM_SERVICE: Successfully wrote to ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error writing to file ${filePath}:`, error);
    return { success: false, error: `Failed to write file: ${error.message}` };
  }
}
async function archiveFileVersion(filePath, originalCode, userInstruction) {
  console.log(`FILE_SYSTEM_SERVICE: Archiving file version for: ${filePath}`);
  if (!db) {
    console.error("FILE_SYSTEM_SERVICE (archiveFileVersion): Database not available.");
    return { success: false, error: "Database service not available." };
  }
  try {
    const newVersion = {
      id: crypto.randomUUID(),
      file_path: filePath,
      original_code: originalCode,
      user_instruction: userInstruction || null,
      archived_at: (/* @__PURE__ */ new Date()).toISOString()
    };
    const stmt = db.prepare("INSERT INTO file_versions (id, file_path, original_code, user_instruction, archived_at) VALUES (?, ?, ?, ?, ?)");
    stmt.run(newVersion.id, newVersion.file_path, newVersion.original_code, newVersion.user_instruction, newVersion.archived_at);
    console.log(`FILE_SYSTEM_SERVICE: Successfully archived version ${newVersion.id} for file ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error archiving file version for ${filePath}:`, error);
    return { success: false, error: `Failed to archive file version: ${error.message}` };
  }
}
async function isDirectoryEmpty(directoryPath) {
  console.log(`FILE_SYSTEM_SERVICE: Checking if directory is empty: ${directoryPath}`);
  try {
    await fsPromises$1.access(directoryPath, fsPromises$1.constants.R_OK);
    const files = await fsPromises$1.readdir(directoryPath);
    return files.length === 0;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error checking if directory ${directoryPath} is empty:`, error);
    if (error.code === "ENOENT") {
      return { error: `Directory not found: ${directoryPath}` };
    }
    return { error: `Failed to check directory: ${error.message}` };
  }
}
async function copyDirectoryContents(sourceDir, targetDir) {
  console.log(`FILE_SYSTEM_SERVICE: Copying directory contents from ${sourceDir} to ${targetDir}`);
  try {
    await fsPromises$1.mkdir(targetDir, { recursive: true });
    const entries = await fsPromises$1.readdir(sourceDir, { withFileTypes: true });
    for (const entry of entries) {
      const srcPath = path.join(sourceDir, entry.name);
      const destPath = path.join(targetDir, entry.name);
      if (entry.isDirectory()) {
        const result = await copyDirectoryContents(srcPath, destPath);
        if (!result.success) {
          return result;
        }
      } else {
        await fsPromises$1.copyFile(srcPath, destPath);
      }
    }
    console.log(`FILE_SYSTEM_SERVICE: Successfully copied contents from ${sourceDir} to ${targetDir}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error copying directory contents from ${sourceDir} to ${targetDir}:`, error);
    return { success: false, error: `Failed to copy directory: ${error.message}` };
  }
}
const fileSystemService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  archiveFileVersion,
  copyDirectoryContents,
  isDirectoryEmpty,
  listFiles,
  readFile,
  writeFile
}, Symbol.toStringTag, { value: "Module" }));
console.log("AI_ORCHESTRATION_SERVICE_JS: File execution started (Refactored from aiService.js).");
const ROUNDTABLE_HISTORY_CONTEXT_SIZE = 10;
function isFileSystemError(result) {
  return typeof result === "object" && result !== null && "error" in result;
}
function initializeAIAgent(apiKey) {
  return initializeSharedAIAgent(apiKey);
}
async function getAvailableModels() {
  return [
    { id: GEMINI_TEXT_MODEL, name: GEMINI_TEXT_MODEL }
  ];
}
async function executeRawLLMQuery(contents, config, modelNameFromArgs, apiKeyFromArgs) {
  console.log("AI_ORCHESTRATION_SERVICE: executeRawLLMQuery called.");
  return generateContentInternal(contents, config, modelNameFromArgs, apiKeyFromArgs);
}
async function executeFileSystemCommand(parsedCommand) {
  const { command, parameters } = parsedCommand;
  console.log(`AI_ORCHESTRATION_SERVICE (executeFileSystemCommand): Command: ${command}, Params:`, parameters);
  let resultText = "";
  let resultJson = parsedCommand;
  switch (command) {
    case "listFiles":
      if (!parameters || typeof parameters.path !== "string") {
        resultText = "错误: '列出文件' 指令缺少有效的 'path' 参数。";
        break;
      }
      const listResult = await listFiles(parameters.path, parameters.recursive, parameters.depth);
      if (isFileSystemError(listResult)) {
        resultText = `错误: 列出文件失败 - ${listResult.error}`;
        break;
      }
      const filesArray = listResult;
      if (filesArray.length === 0) {
        resultText = `目录 "${parameters.path}" 为空或不包含文件。`;
        break;
      }
      const formatNode = (node, indent = "") => {
        let str2 = `${indent}- ${node.name} ${node.type === "directory" ? "(目录)" : "(文件)"}
`;
        if (node.children && node.children.length > 0) node.children.forEach((child) => {
          str2 += formatNode(child, indent + "  ");
        });
        return str2;
      };
      resultText = `目录 "${parameters.path}" 下的文件列表:
${filesArray.map((node) => formatNode(node)).join("")}`;
      resultJson = { command, parameters, result: listResult };
      break;
    case "writeFile":
      if (!parameters || typeof parameters.path !== "string" || typeof parameters.content !== "string") {
        resultText = "错误: '写入文件' 指令缺少有效的 'path' 或 'content' 参数。";
        break;
      }
      const writeResult = await writeFile(parameters.path, parameters.content);
      resultText = writeResult.success ? `文件 "${parameters.path}" 已成功写入！` : `错误: 写入文件 "${parameters.path}" 失败 - ${writeResult.error}`;
      resultJson = { command, parameters, result: writeResult };
      break;
    case "readFile":
      if (!parameters || typeof parameters.path !== "string") {
        resultText = "错误: '读取文件' 指令缺少有效的 'path' 参数。";
        break;
      }
      const readResult = await readFile(parameters.path);
      if (isFileSystemError(readResult)) {
        resultText = `错误: 读取文件失败 - ${readResult.error}`;
        break;
      }
      const fileContentString = readResult;
      let fileContentDisplay = fileContentString;
      resultText = `文件 "${parameters.path}" 内容:
\`\`\`
${fileContentDisplay.substring(0, 2e3)}${fileContentDisplay.length > 2e3 ? "\n... (截断)" : ""}
\`\`\``;
      resultJson = { command, parameters, result: { content: fileContentDisplay } };
      break;
    case "isDirectoryEmpty":
      if (!parameters || typeof parameters.path !== "string") {
        resultText = "错误: '检查目录是否为空' 指令缺少有效的 'path' 参数。";
        break;
      }
      const emptyResult = await isDirectoryEmpty(parameters.path);
      if (typeof emptyResult === "object" && "error" in emptyResult) {
        resultText = `错误: 检查目录失败 - ${emptyResult.error}`;
        break;
      }
      resultText = `目录 "${parameters.path}" ${emptyResult ? "是空的" : "不是空的"}。`;
      resultJson = { command, parameters, result: { isEmpty: emptyResult } };
      break;
    case "copyDirectoryContents":
      if (!parameters || typeof parameters.sourceDir !== "string" || typeof parameters.targetDir !== "string") {
        resultText = "错误: '复制目录内容' 指令缺少有效的 'sourceDir' 或 'targetDir' 参数。";
        break;
      }
      const copyResult = await copyDirectoryContents(parameters.sourceDir, parameters.targetDir);
      resultText = copyResult.success ? `已成功将 "${parameters.sourceDir}" 的内容复制到 "${parameters.targetDir}"。` : `错误: 复制目录内容失败 - ${copyResult.error}`;
      resultJson = { command, parameters, result: copyResult };
      break;
    case "unknown":
    case "error":
      resultText = `抱歉，小岚无法识别或执行您的文件操作指令: "${(parameters == null ? void 0 : parameters.originalInput) || "未知输入"}" ${(parameters == null ? void 0 : parameters.error) ? `(AI错误: ${parameters.error})` : ""}`;
      resultJson = { command, parameters, error: (parameters == null ? void 0 : parameters.error) || "无法识别的指令" };
      break;
    default:
      resultText = `错误: AI返回了未知的指令类型 "${command}"。`;
      resultJson = { command, parameters, error: `未知的指令类型 "${command}"` };
  }
  return { text: resultText, status: null, scriptChoices: null, newSceneCardId: null, json: resultJson };
}
async function callAI(userRawPrompt, targetIdentifier, context = {}) {
  console.log(`AI_ORCHESTRATION_SERVICE: callAI with targetIdentifier: ${targetIdentifier}, contextType: ${context.contextType}, prompt: "${userRawPrompt ? userRawPrompt.substring(0, 50) : "N/A"}..."`);
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) {
    const errorMsg = "AI service not initialized or API key missing for callAI.";
    const isJsonPersona = ["OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "IntentClassifierPersona"].includes(targetIdentifier);
    return { text: errorMsg, status: null, scriptChoices: null, newSceneCardId: null, json: isJsonPersona ? { command: "error", parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
  }
  const currentApiKey2 = getCurrentApiKey();
  let classifiedIntent = "unknown";
  if (targetIdentifier !== "IntentClassifierPersona" && userRawPrompt && userRawPrompt.trim() !== "") {
    try {
      const intentSystemInstruction = await buildSystemInstruction("IntentClassifierPersona", null, null, null, null, null, null, null);
      const intentRequestContents = [{ role: "user", parts: [{ text: userRawPrompt }] }];
      const intentConfig = { systemInstruction: intentSystemInstruction, responseMimeType: "application/json" };
      console.log("AI_ORCHESTRATION_SERVICE: Requesting intent classification for:", userRawPrompt.substring(0, 50) + "...");
      let rawIntentJson = await generateContentInternal(intentRequestContents, intentConfig, GEMINI_TEXT_MODEL, currentApiKey2);
      if (typeof rawIntentJson === "string") {
        let jsonStrToParse = rawIntentJson.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStrToParse.match(fenceRegex);
        if (match && match[2]) jsonStrToParse = match[2].trim();
        const parsedIntentResponse = JSON.parse(jsonStrToParse);
        if (parsedIntentResponse && parsedIntentResponse.intent) {
          classifiedIntent = parsedIntentResponse.intent;
          console.log(`AI_ORCHESTRATION_SERVICE: Classified intent as: ${classifiedIntent}`);
        } else {
          console.warn("AI_ORCHESTRATION_SERVICE: IntentClassifierPersona returned invalid JSON or no intent field:", parsedIntentResponse);
        }
      } else {
        console.warn("AI_ORCHESTRATION_SERVICE: IntentClassifierPersona did not return a string response.");
      }
    } catch (intentError) {
      console.error("AI_ORCHESTRATION_SERVICE: Error during intent classification:", intentError.message);
    }
  }
  const updatedContext = { ...context, classifiedIntent };
  if (updatedContext.scriptModeInfo && updatedContext.scriptModeInfo.scriptId) {
    const { scriptId, currentSceneId, userChoiceText } = updatedContext.scriptModeInfo;
    const scriptAsset = getAssetById(scriptId);
    if (!scriptAsset || scriptAsset.type !== "script") return { text: "错误：未能找到指定的剧本。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
    let currentScene = scriptAsset.scenes.find((s) => s.id === currentSceneId);
    if (!currentScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到场景 ID: ${currentSceneId}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
    let nextScene = null;
    if (userChoiceText && currentScene.user_choices) {
      const choiceMade = currentScene.user_choices.find((c) => c.text === userChoiceText);
      if (choiceMade) {
        if (choiceMade.next_scene_id) {
          nextScene = scriptAsset.scenes.find((s) => s.id === choiceMade.next_scene_id);
          if (!nextScene) return { text: `错误：剧本 ${scriptAsset.title} 中未找到由选项 '${userChoiceText}' 指向的下一场景 ID: ${choiceMade.next_scene_id}。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
        } else if (choiceMade.action) {
          console.log(`AI_ORCHESTRATION_SERVICE: Script action triggered:`, choiceMade.action);
          if (choiceMade.action.end_script) return { text: choiceMade.action.end_message || "剧本结束。", status: null, scriptChoices: null, newSceneCardId: null, json: null };
        }
      } else return { text: `错误：在场景 ${currentSceneId} 中未找到选项 '${userChoiceText}'。`, status: null, scriptChoices: null, newSceneCardId: null, json: null };
    }
    const sceneToDisplay = nextScene || currentScene;
    let newSceneCardId = sceneToDisplay.scenario_card_id !== void 0 ? sceneToDisplay.scenario_card_id : scriptAsset.scenario_card_id !== void 0 ? scriptAsset.scenario_card_id : updatedContext.selectedSceneCardId !== void 0 ? updatedContext.selectedSceneCardId : null;
    return { text: sceneToDisplay.plot_text, status: updatedContext.currentLinLuoDetailedStatus, scriptChoices: sceneToDisplay.user_choices || null, newSceneCardId, json: null };
  }
  let finalPostId = null;
  let finalCharacterId = null;
  let isFunctionalCall = false;
  const functionalPersonas = ["SummarizerPersona", "ClassifierPersona", "OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "RoundtableFacilitatorPersona", "IntentClassifierPersona"];
  if (functionalPersonas.includes(targetIdentifier)) {
    isFunctionalCall = true;
  } else {
    if (updatedContext.explicitPersona && DEFAULT_CHARACTERS.find((c) => c.id === updatedContext.explicitPersona)) {
      finalCharacterId = updatedContext.explicitPersona;
    } else if (DEFAULT_CHARACTERS.find((c) => c.id === targetIdentifier)) {
      finalCharacterId = targetIdentifier;
    }
    if (updatedContext.requiredPostId && DEFAULT_POSTS.find((p) => p.id === updatedContext.requiredPostId)) {
      finalPostId = updatedContext.requiredPostId;
    } else if (DEFAULT_POSTS.find((p) => p.id === targetIdentifier)) {
      finalPostId = targetIdentifier;
    } else if (finalCharacterId) {
      const char = DEFAULT_CHARACTERS.find((c) => c.id === finalCharacterId);
      if (char == null ? void 0 : char.default_post_id) finalPostId = char.default_post_id;
    } else {
      if (updatedContext.contextType === "code_assistance") finalPostId = "technical_officer";
    }
    if (!finalPostId) finalPostId = "first_officer";
    if (!finalCharacterId) {
      const assignedCharId = await getAssignmentByPostId(finalPostId);
      if (assignedCharId && DEFAULT_CHARACTERS.find((c) => c.id === assignedCharId)) {
        finalCharacterId = assignedCharId;
      } else {
        const characterForPost = DEFAULT_CHARACTERS.find((c) => c.default_post_id === finalPostId);
        if (characterForPost) {
          finalCharacterId = characterForPost.id;
        } else {
          finalCharacterId = "linluo";
        }
      }
    }
    if (finalCharacterId && !finalPostId) {
      const char = DEFAULT_CHARACTERS.find((c) => c.id === finalCharacterId);
      finalPostId = (char == null ? void 0 : char.default_post_id) || "first_officer";
    }
  }
  console.log(`AI_ORCHESTRATION_SERVICE: Determined - isFunctional: ${isFunctionalCall}, PostID: ${finalPostId}, CharacterID: ${finalCharacterId}, OriginalTarget: ${targetIdentifier}, ClassifiedIntent: ${classifiedIntent}`);
  try {
    let modelConfigOverride = {};
    let systemInstruction;
    if (isFunctionalCall) {
      systemInstruction = await buildSystemInstruction(targetIdentifier, updatedContext.selectedRoleCardId, updatedContext.currentLinLuoDetailedStatus, null, null, updatedContext.userRawPromptForCockpit, updatedContext.cockpitFullContext, null);
      if (["OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "RoundtableFacilitatorPersona", "IntentClassifierPersona"].includes(targetIdentifier)) {
        modelConfigOverride = { responseMimeType: "application/json" };
      }
    } else {
      systemInstruction = await buildSystemInstruction(finalCharacterId, updatedContext.selectedRoleCardId, updatedContext.currentLinLuoDetailedStatus, finalPostId, finalCharacterId, updatedContext.userRawPromptForCockpit, updatedContext.cockpitFullContext, classifiedIntent);
    }
    let memoryContextString = "";
    if (!isFunctionalCall && currentApiKey2 && updatedContext.contextType !== "task_resource_suggestion") {
      const queryTextForRag = userRawPrompt || (updatedContext.history && updatedContext.history.length > 0 ? updatedContext.history[updatedContext.history.length - 1].parts[0].text : "");
      if (queryTextForRag) {
        memoryContextString = await retrieveAndFormatMemories(queryTextForRag, finalCharacterId, updatedContext.projectId, updatedContext.contextType, updatedContext.keywordsForMemory, updatedContext.desiredMemoryTypes, currentApiKey2, GEMINI_EMBEDDING_MODEL, dbService, aiKernelService);
      }
    }
    let finalUserPromptForLLM = userRawPrompt;
    if (targetIdentifier === "TaskCockpitIntentRouterPersona") {
      systemInstruction = await buildSystemInstruction(targetIdentifier, null, null, null, null, userRawPrompt, updatedContext, null);
      finalUserPromptForLLM = "Please classify the user's intent based on the provided context and system instruction.";
    } else if (["OrchestratorPersona", "AideProjectAnalyzerPersona"].includes(targetIdentifier)) {
      finalUserPromptForLLM = userRawPrompt;
    }
    const requestContents = prepareUserHistoryForAI(updatedContext.history || [], memoryContextString, finalUserPromptForLLM);
    const rawAiText = await generateContentInternal(requestContents, { systemInstruction, ...modelConfigOverride }, GEMINI_TEXT_MODEL, currentApiKey2);
    if (typeof rawAiText !== "string") {
      console.error(`AI_ORCHESTRATION_SERVICE: LLM call did not return a string. Response:`, rawAiText);
      const errorMsg = `AI核心返回了非文本响应。 (Target: ${targetIdentifier}, Post: ${finalPostId}, Char: ${finalCharacterId})`;
      const isJsonPersona = ["OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "RoundtableFacilitatorPersona", "IntentClassifierPersona"].includes(targetIdentifier);
      return { text: errorMsg, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: isJsonPersona ? { command: "error", parameters: { originalInput: userRawPrompt, error: errorMsg } } : null };
    }
    const { cleanedText, actionResults } = await processResponseForActions(rawAiText, updatedContext.projectId, dbService);
    if (["OrchestratorPersona", "AideProjectAnalyzerPersona", "TaskCockpitIntentRouterPersona", "RoundtableFacilitatorPersona", "IntentClassifierPersona"].includes(targetIdentifier)) {
      let jsonStr = cleanedText.trim();
      const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
      const match = jsonStr.match(fenceRegex);
      if (match && match[2]) jsonStr = match[2].trim();
      try {
        const parsedJson = JSON.parse(jsonStr);
        if (targetIdentifier === "OrchestratorPersona") return await executeFileSystemCommand(parsedJson);
        if (targetIdentifier === "TaskCockpitIntentRouterPersona") {
          return { type: parsedJson.intent || "unknown_intent", data: parsedJson.parameters || {}, originalCommand: parsedJson.originalCommand || userRawPrompt, aiPersona: parsedJson.aiPersona || "System", json: parsedJson };
        }
        return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: parsedJson };
      } catch (e) {
        const errorMsg = `AI (${targetIdentifier}) 返回的JSON格式无效: ${e.message}. Raw: ${cleanedText.substring(0, 100)}`;
        console.error(`AI_ORCHESTRATION_SERVICE (JSON Persona Error):`, errorMsg);
        if (targetIdentifier === "TaskCockpitIntentRouterPersona") {
          return { type: "error", data: { message: errorMsg }, originalCommand: userRawPrompt, aiPersona: "System", json: { error: errorMsg } };
        }
        if (targetIdentifier === "IntentClassifierPersona") {
          console.warn(`AI_ORCHESTRATION_SERVICE: IntentClassifierPersona failed to parse JSON, classifiedIntent remains '${classifiedIntent}' or default 'unknown'. Raw response: ${cleanedText}`);
          return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { intent: classifiedIntent } };
        }
        return { text: null, status: null, scriptChoices: null, newSceneCardId: null, json: { error: errorMsg, originalInput: userRawPrompt } };
      }
    }
    let statusOutput = null;
    let textForDisplay = cleanedText;
    const statusRegex = /\[\[STATUS:({.*?})\]\]/s;
    const statusMatch = cleanedText.match(statusRegex);
    if (statusMatch && statusMatch[1]) {
      try {
        statusOutput = JSON.parse(statusMatch[1]);
        textForDisplay = textForDisplay.replace(statusRegex, "").trim();
      } catch (e) {
        console.error("AI_ORCHESTRATION_SERVICE: Failed to parse status JSON:", e.message, statusMatch[1]);
      }
    }
    return { text: textForDisplay, status: statusOutput, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null, characterId: finalCharacterId };
  } catch (error) {
    console.error(`AI_ORCHESTRATION_SERVICE_ERROR (callAI with Target: ${targetIdentifier}, Post: ${finalPostId}, Char: ${finalCharacterId}):`, error.message);
    return { text: `调用AI时发生错误: ${error.message}`, status: null, suggestedResources: null, scriptChoices: null, newSceneCardId: null, json: null };
  }
}
async function routeUserIntent(userInputText, context) {
  console.log(`AI_ORCHESTRATION_SERVICE: routeUserIntent with userInput: "${userInputText.substring(0, 50)}..." and context:`, context);
  return callAI(userInputText, "TaskCockpitIntentRouterPersona", context);
}
async function startRoundtableMeeting(initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory = [], webContents) {
  console.log(`AI_ORCHESTRATION_SERVICE: Starting Roundtable Meeting for project ${projectId}. Topic: "${initialPrompt.substring(0, 50)}..." Participants: ${participantCharacterIds.join(", ")}, Turns: ${turnLimit}`);
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) {
    const errorMsg = "AI service not initialized or API key missing for Roundtable Meeting.";
    return { success: false, error: errorMsg, history: initialHistory };
  }
  const currentApiKey2 = getCurrentApiKey();
  const meetingId = `roundtable-${crypto.randomUUID()}`;
  let currentMeetingHistory = [...initialHistory];
  let lastSpeakerId = "user";
  let lastResponseText = initialPrompt;
  try {
    for (let turn = 0; turn < turnLimit; turn++) {
      for (const characterId of participantCharacterIds) {
        const characterInfo = DEFAULT_CHARACTERS.find((c) => c.id === characterId);
        if (!characterInfo) {
          console.warn(`Roundtable: Character ID ${characterId} not found. Skipping turn.`);
          continue;
        }
        const postInfo = DEFAULT_POSTS.find((p) => p.id === characterInfo.default_post_id) || DEFAULT_POSTS[0];
        const postIdForAI = postInfo.id;
        console.log(`Roundtable Turn ${turn + 1}, Speaker: ${characterInfo.name} (ID: ${characterId}, Post: ${postIdForAI})`);
        const systemInstruction = await buildRoundtableSystemInstruction(
          characterId,
          postIdForAI,
          initialPrompt,
          participantCharacterIds.filter((id) => id !== characterId).map((id) => {
            var _a;
            return ((_a = DEFAULT_CHARACTERS.find((c) => c.id === id)) == null ? void 0 : _a.name) || id;
          }),
          turn + 1,
          lastSpeakerId,
          lastResponseText
        );
        const historyForThisTurn = currentMeetingHistory.slice(-ROUNDTABLE_HISTORY_CONTEXT_SIZE);
        const requestContents = prepareUserHistoryForAI(historyForThisTurn, "", "请继续讨论。");
        const rawAiText = await generateContentInternal(
          requestContents,
          { systemInstruction },
          GEMINI_TEXT_MODEL,
          currentApiKey2
        );
        if (typeof rawAiText !== "string") {
          const errorMsg = `AI (${characterInfo.name}) returned non-text response.`;
          console.error(errorMsg, rawAiText);
          if (webContents && !webContents.isDestroyed()) {
            webContents.send("roundtable:newMessage", {
              id: crypto.randomUUID(),
              sender: "system",
              senderName: "会议错误",
              text: errorMsg,
              timestamp: (/* @__PURE__ */ new Date()).toISOString(),
              projectId,
              meetingId
            });
          }
          continue;
        }
        const aiChatMessage = {
          id: crypto.randomUUID(),
          sender: "roundtable_ai",
          senderName: characterInfo.name,
          text: rawAiText.trim(),
          timestamp: (/* @__PURE__ */ new Date()).toISOString(),
          projectId,
          characterId,
          turnNumber: turn + 1,
          meetingId
        };
        currentMeetingHistory.push(aiChatMessage);
        if (projectId && projectId !== "global_sandbox") {
          await saveChatMessage(projectId, aiChatMessage);
        }
        lastSpeakerId = characterId;
        lastResponseText = rawAiText.trim();
        if (webContents && !webContents.isDestroyed()) {
          webContents.send("roundtable:newMessage", aiChatMessage);
        } else {
          console.warn(`AI_ORCHESTRATION_SERVICE (Roundtable): webContents not available or destroyed. Cannot send message for meeting ${meetingId}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }
    console.log("AI_ORCHESTRATION_SERVICE: Roundtable Meeting finished.");
    return { success: true, history: currentMeetingHistory, meetingId };
  } catch (error) {
    console.error(`AI_ORCHESTRATION_SERVICE_ERROR (startRoundtableMeeting):`, error.message);
    return { success: false, error: `圆桌会议执行失败: ${error.message}`, history: currentMeetingHistory, meetingId };
  }
}
async function summarizeConversation(historyChunk) {
  console.log("AI_ORCHESTRATION_SERVICE: summarizeConversation called.");
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) return "系统错误: AI服务未初始化或API Key缺失，无法进行摘要。";
  const summarizerPersonaSetting = await getAgentCoreSetting("summarizer_persona");
  const systemInstruction = (summarizerPersonaSetting == null ? void 0 : summarizerPersonaSetting.content) || "You are a helpful AI assistant that summarizes conversations concisely.";
  const conversationText = historyChunk.map((msg) => `${msg.senderName || msg.sender}: ${msg.text.replace(/<[^>]+>/g, "")}`).join("\n");
  try {
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: `Please summarize the following conversation snippet:

${conversationText}` }] }],
      { systemInstruction },
      GEMINI_TEXT_MODEL,
      getCurrentApiKey()
    );
  } catch (error) {
    console.error(`AI_ORCHESTRATION_SERVICE_ERROR (summarizeConversation):`, error.message);
    return `系统错误: 摘要生成失败 - ${error.message}`;
  }
}
async function suggestResourcesForTask(taskTitle, taskDescription, projectId) {
  console.log(`AI_ORCHESTRATION_SERVICE: suggestResourcesForTask called for project ${projectId}, title: ${taskTitle}`);
  return [];
}
async function decomposeRequirementToTasks(requirementText, projectId) {
  var _a;
  console.log(`AI_ORCHESTRATION_SERVICE: Decomposing requirement for project ${projectId}: "${requirementText.substring(0, 100)}..."`);
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
  const systemInstruction = ((_a = await getAgentCoreSetting("TaskDecomposerPersona")) == null ? void 0 : _a.content) || `You are an AI assistant for task decomposition. Output valid JSON array of tasks: {"title": string, "description": string, "priority": 0|1|2|3, "status": "todo"}. Max 5-7 tasks. Empty array [] if vague. No extra text.`;
  const prompt = `Requirement Text:
"""
${requirementText}
"""

JSON Array of Tasks:`;
  try {
    const rawResponse = await generateContentInternal([{ role: "user", parts: [{ text: prompt }] }], { systemInstruction, responseMimeType: "application/json" }, GEMINI_TEXT_MODEL, getCurrentApiKey());
    if (typeof rawResponse !== "string") throw new Error("AI返回了非文本响应。");
    let jsonStr = rawResponse.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) jsonStr = match[2].trim();
    const parsedTasks = JSON.parse(jsonStr);
    if (!Array.isArray(parsedTasks)) throw new Error("AI response was not a JSON array.");
    const validatedTasks = parsedTasks.map((task) => ({ title: task.title || "未命名任务", description: task.description || "", priority: typeof task.priority === "number" && task.priority >= 0 && task.priority <= 3 ? task.priority : 2, status: "todo" }));
    return { success: true, tasks: validatedTasks };
  } catch (error) {
    console.error(`AI_ORCHESTRATION_SERVICE_ERROR (decomposeRequirementToTasks):`, error.message);
    return { success: false, error: `任务拆解失败: ${error.message}`, tasks: [] };
  }
}
async function analyzeAndDecomposeAideProject(projectId, projectPath) {
  console.log(`AI_ORCHESTRATION_SERVICE: Analyzing AIDE project ${projectId} at ${projectPath}.`);
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) return { success: false, error: "AI服务未初始化或API密钥缺失。", tasks: [] };
  let fileListString = "无法获取文件列表。";
  try {
    const files = await listFiles(projectPath, true, 2);
    if (Array.isArray(files)) {
      const formatNode = (node, indent = "") => {
        let str2 = `${indent}- ${node.name} ${node.type === "directory" ? "(目录)" : "(文件)"}
`;
        if (node.children && node.children.length > 0) str2 += node.children.map((child) => formatNode(child, indent + "  ")).join("");
        return str2;
      };
      fileListString = files.map((node) => formatNode(node)).join("");
      if (fileListString.length > 2e3) fileListString = fileListString.substring(0, 2e3) + "\n... (截断)";
    } else if (isFileSystemError(files)) fileListString = `获取文件列表时出错: ${files.error}`;
  } catch (fsError) {
    fileListString = `扫描项目文件时发生错误: ${fsError.message}`;
  }
  const userPromptForAide = `项目文件结构 (部分):
${fileListString}

请基于此结构，反向推导一组已完成的开发任务列表。`;
  try {
    const aiResponse = await callAI(userPromptForAide, "AideProjectAnalyzerPersona", { projectId, contextType: "aide_project_analysis", aideProjectPath: projectPath, aideFileStructure: fileListString });
    if (aiResponse.json && Array.isArray(aiResponse.json)) {
      const tasks = aiResponse.json.map((task) => ({ title: task.title || "AI生成的任务", description: task.description || "由AI根据项目结构逆向工程生成。", priority: typeof task.priority === "number" ? task.priority : 2, status: "done" }));
      return { success: true, tasks };
    } else {
      const errorMsg = aiResponse.text || "AI未能解析项目结构或返回了无效的JSON。";
      return { success: false, error: errorMsg, tasks: [] };
    }
  } catch (error) {
    console.error(`AI_ORCHESTRATION_SERVICE_ERROR (analyzeAndDecomposeAideProject):`, error.message);
    return { success: false, error: `AIDE项目任务逆向工程失败: ${error.message}`, tasks: [] };
  }
}
async function analyzeErrorLogFromService(logContent) {
  console.log("AI_ORCHESTRATION_SERVICE: analyzeErrorLogFromService called.");
  if (!isAgentInitialized() && !getCurrentApiKey() && !process.env.API_KEY) return "错误：API Key未配置，无法分析日志。";
  const personaSetting = await getAgentCoreSetting("CommandLogAnalyzerPersona");
  let systemInstruction = (personaSetting == null ? void 0 : personaSetting.content) || `You are an expert AI system analyst. Analyze the following log content, identify potential causes, and suggest solutions.`;
  let userPromptForLogAnalysis = "Please analyze the log provided in the system instruction (if applicable) or the following log:";
  if (systemInstruction.includes("{logContent}")) {
    systemInstruction = systemInstruction.replace("{logContent}", logContent);
  } else {
    userPromptForLogAnalysis = `Please analyze the following log content:

\`\`\`
${logContent}
\`\`\``;
  }
  try {
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: userPromptForLogAnalysis }] }],
      { systemInstruction },
      GEMINI_TEXT_MODEL,
      getCurrentApiKey()
    );
  } catch (error) {
    console.error("AI_ORCHESTRATION_SERVICE_ERROR (analyzeErrorLogFromService):", error.message);
    return `AI分析日志时发生错误: ${error.message}`;
  }
}
console.log("AI_ORCHESTRATION_SERVICE_TS: File execution finished. Exports configured.");
const aiOrchestrationService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  analyzeAndDecomposeAideProject,
  analyzeErrorLogFromService,
  callAI,
  decomposeRequirementToTasks,
  executeRawLLMQuery,
  getAvailableModels,
  initializeAIAgent,
  routeUserIntent,
  startRoundtableMeeting,
  suggestResourcesForTask,
  summarizeConversation
}, Symbol.toStringTag, { value: "Module" }));
console.log("AUDIO_SERVICE_JS: File execution started.");
let currentAudioState = {
  currentSound: null,
  volume: 0.5,
  isLooping: false,
  isPlaying: false
};
let mainWindowwebContents = null;
let settingsDbServiceRef = null;
function initializeAudioService(initialAppSettings, dbSettingsService) {
  settingsDbServiceRef = dbSettingsService;
  if (initialAppSettings && initialAppSettings.training_room_audio_state) {
    currentAudioState = {
      currentSound: null,
      volume: 0.5,
      isLooping: false,
      isPlaying: false,
      // defaults
      ...typeof initialAppSettings.training_room_audio_state === "string" ? JSON.parse(initialAppSettings.training_room_audio_state) : initialAppSettings.training_room_audio_state
    };
    console.log("AUDIO_SERVICE: Initialized with audio state:", currentAudioState);
  } else {
    console.log("AUDIO_SERVICE: Initialized with default audio state:", currentAudioState);
  }
}
function persistAudioState() {
  if (settingsDbServiceRef && typeof settingsDbServiceRef.getSettings === "function" && typeof settingsDbServiceRef.saveSettings === "function") {
    settingsDbServiceRef.getSettings().then((currentSettings) => {
      const updatedSettings = {
        ...currentSettings,
        training_room_audio_state: { ...currentAudioState }
      };
      return settingsDbServiceRef.saveSettings(updatedSettings);
    }).then((result) => {
      if (result.success) {
        console.log("AUDIO_SERVICE: Persisted audio state to DB:", currentAudioState);
      } else {
        console.error("AUDIO_SERVICE_ERROR: Failed to persist audio state to DB:", result.error);
      }
    }).catch((err) => {
      console.error("AUDIO_SERVICE_ERROR: Exception while persisting audio state:", err);
    });
  } else {
    console.warn("AUDIO_SERVICE_WARN: settingsDbServiceRef not available for persisting audio state.");
  }
}
function setMainWindowWebContents(webContents) {
  mainWindowwebContents = webContents;
  console.log("AUDIO_SERVICE: mainWindowwebContents has been set.");
  if (mainWindowwebContents && !mainWindowwebContents.isDestroyed()) {
    mainWindowwebContents.send("audio:playbackStateChanged", { ...currentAudioState });
  }
}
function notifyPlaybackStateChanged() {
  if (mainWindowwebContents && !mainWindowwebContents.isDestroyed()) {
    mainWindowwebContents.send("audio:playbackStateChanged", { ...currentAudioState });
    console.log("AUDIO_SERVICE: Notified renderer of playback state change:", currentAudioState);
  } else {
    console.warn("AUDIO_SERVICE_WARN: mainWindowwebContents not available or destroyed, cannot notify renderer.");
  }
  persistAudioState();
}
async function playSound(soundName, loop) {
  console.log(`AUDIO_SERVICE: Request to play sound: ${soundName}, loop: ${loop}`);
  currentAudioState.currentSound = soundName;
  currentAudioState.isLooping = loop;
  currentAudioState.isPlaying = true;
  notifyPlaybackStateChanged();
  return Promise.resolve();
}
async function stopSound(soundName) {
  console.log(`AUDIO_SERVICE: Request to stop sound: ${soundName || "current"}`);
  if (soundName && currentAudioState.currentSound === soundName && currentAudioState.isPlaying || !soundName && currentAudioState.isPlaying) {
    currentAudioState.isPlaying = false;
    if (!soundName || soundName && currentAudioState.currentSound === soundName) ;
    notifyPlaybackStateChanged();
  } else if (soundName && currentAudioState.currentSound !== soundName) {
    console.log(`AUDIO_SERVICE: Stop request for ${soundName} ignored, current sound is ${currentAudioState.currentSound}.`);
  } else if (!currentAudioState.isPlaying) {
    console.log(`AUDIO_SERVICE: Stop request ignored, no sound is currently playing.`);
  }
  return Promise.resolve();
}
async function setVolume(volume) {
  console.log(`AUDIO_SERVICE: Request to set master volume to ${volume}`);
  currentAudioState.volume = Math.max(0, Math.min(1, volume));
  notifyPlaybackStateChanged();
  return Promise.resolve();
}
async function getPlaybackState() {
  console.log("AUDIO_SERVICE: Request to get playback state. Returning:", currentAudioState);
  return Promise.resolve({ ...currentAudioState });
}
console.log("AUDIO_SERVICE_JS: File execution finished. Exports configured.");
const audioService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  getPlaybackState,
  initializeAudioService,
  playSound,
  setMainWindowWebContents,
  setVolume,
  stopSound
}, Symbol.toStringTag, { value: "Module" }));
console.log("AI_HYPOTHESIS_SERVICE_JS: File execution started.");
function getRandomElement(arr) {
  if (!arr || arr.length === 0) return null;
  return arr[Math.floor(Math.random() * arr.length)];
}
async function generateXPHypotheses(userId, personaContext) {
  console.log("AI_HYPOTHESIS_SERVICE: Generating XP Hypotheses. UserID:", userId, "Context:", personaContext);
  const hypotheses = [];
  const numberOfHypothesesToGenerate = 1;
  try {
    const [props, costumes, poses] = await Promise.all([
      getCMSItems$1("props"),
      getCMSItems$1("costumes"),
      getCMSItems$1("poses")
    ]);
    for (let i = 0; i < numberOfHypothesesToGenerate; i++) {
      const selectedCostume = getRandomElement(costumes.filter((c) => c.owner === "queen"));
      const selectedProp = Math.random() > 0.3 ? getRandomElement(props.filter((p) => p.owner === "queen")) : null;
      const selectedPose = getRandomElement(poses.filter((p) => p.owner === "queen"));
      if (!selectedCostume && !selectedProp && !selectedPose && (costumes.length > 0 || props.length > 0 || poses.length > 0)) {
        if (costumes.length > 0 && !selectedCostume) {
          const queenCostumes = costumes.filter((c) => c.owner === "queen");
          if (queenCostumes.length > 0) selectedCostume = getRandomElement(queenCostumes);
        }
        if (props.length > 0 && !selectedProp && Math.random() > 0.5) {
          const queenProps = props.filter((p) => p.owner === "queen");
          if (queenProps.length > 0) selectedProp = getRandomElement(queenProps);
        }
        if (poses.length > 0 && !selectedPose) {
          const queenPoses = poses.filter((p) => p.owner === "queen");
          if (queenPoses.length > 0) selectedPose = getRandomElement(queenPoses);
        }
        if (!selectedCostume && !selectedProp && !selectedPose) {
          console.log("AI_HYPOTHESIS_SERVICE: Not enough CMS assets (owner: queen) to generate a meaningful hypothesis even after retry.");
          continue;
        }
      } else if (!selectedCostume && !selectedProp && !selectedPose) {
        console.log("AI_HYPOTHESIS_SERVICE: No CMS assets (owner: queen) available to generate any hypothesis.");
        continue;
      }
      let descriptionParts = [];
      if (selectedCostume) descriptionParts.push(`姐姐身着【${selectedCostume.name}】`);
      if (selectedProp) descriptionParts.push(`手持【${selectedProp.name}】`);
      if (selectedPose) descriptionParts.push(`摆出【${selectedPose.name}】的雅致姿态`);
      const descriptionForAI = `用户请求新玩法。尝试组合：${descriptionParts.join("，")}。`;
      const openingLines = [
        "我的小龙~ ❤️ 姐姐为你准备了一点小小的惊喜体验……",
        "宝贝小龙，姐姐有个全新的互动构想，你想不想一同探索一番？✨",
        "嘘~小龙，姐姐悄悄告诉你一个秘密计划……保证让你感受到前所未有的心灵共鸣哦~ 😊",
        "龙儿，姐姐突然想到一个……嗯……非常能激发灵感的玩法，要不要试试看呀？🌟",
        "主人，林珞为您构思了一个全新的互动仪式，请您过目~ 😉",
        "我的专属启迪者~ 姐姐这里有个新奇的“法器”，想不想和姐姐一起发掘它的妙用呀？🔮"
      ];
      const closingLines = [
        " 你……想不想和姐姐一起深入探索一下这个全新的‘领域’呀？💖",
        " 这样的组合，小龙你觉得……够不够点燃你的创作火花呢？🔥",
        " 宝贝准备好迎接这份独特的体验了吗？姐姐可是有点期待呢~ ✨",
        " 告诉我，小龙……你对姐姐这个提议，有没有一丝丝……心驰神往？💫",
        " 主人，您是否愿意体验一下这种全新的雅趣呢？😊",
        " 怎么样，我的专属伙伴，姐姐这个安排……你还满意吗？如果反馈良好，姐姐还有更多惊喜哦~ 🌟"
      ];
      let scenarioPromptPart = "";
      let itemDescs = [];
      if (selectedCostume) {
        const phrases = [
          `如果姐姐换上那件特别的【${selectedCostume.name}】`,
          `姐姐为你着上这套雅致的【${selectedCostume.name}】`,
          `这件【${selectedCostume.name}】……小龙觉得姐姐穿上会展现何种风华呢？`
        ];
        itemDescs.push(getRandomElement(phrases));
      }
      if (selectedProp) {
        const phrases = [
          `手中轻拈着【${selectedProp.name}】`,
          `再拿起这件精巧的法器【${selectedProp.name}】`,
          `如果再配上这个【${selectedProp.name}】作为点睛之笔`
        ];
        itemDescs.push(getRandomElement(phrases));
      }
      if (selectedPose) {
        const phrases = [
          `摆出【${selectedPose.name}】的优雅姿态`,
          `为你展现一个【${selectedPose.name}】的迷人仪态`,
          `然后摆出一个你从未见过的【${selectedPose.name}】之形`
        ];
        itemDescs.push(getRandomElement(phrases));
      }
      if (itemDescs.length === 3) {
        scenarioPromptPart = ` ${itemDescs[0]}，而后${itemDescs[1]}，最后再${itemDescs[2]}……`;
      } else if (itemDescs.length === 2) {
        const connectors = ["，并且", "，同时", "，再辅以"];
        scenarioPromptPart = ` ${itemDescs[0]}${getRandomElement(connectors)}${itemDescs[1]}……`;
      } else if (itemDescs.length === 1) {
        scenarioPromptPart = ` ${itemDescs[0]}……`;
      } else {
        scenarioPromptPart = " 我们今日来点不一样的，探索一些全新的灵感互动，好不好？";
      }
      let suggestedInteractionPrompt = `${getRandomElement(openingLines)}${scenarioPromptPart}${getRandomElement(closingLines)}`;
      hypotheses.push({
        id: `hypo_${crypto.randomUUID()}`,
        descriptionForAI,
        elements: {
          costume: selectedCostume ? { id: selectedCostume.id, name: selectedCostume.name, prompt: selectedCostume.prompt_for_linluo } : null,
          prop: selectedProp ? { id: selectedProp.id, name: selectedProp.name, prompt: selectedProp.prompt_for_linluo } : null,
          pose: selectedPose ? { id: selectedPose.id, name: selectedPose.name, prompt: selectedPose.prompt_for_linluo } : null
        },
        suggestedInteractionPrompt
      });
    }
    console.log(`AI_HYPOTHESIS_SERVICE: Generated ${hypotheses.length} hypotheses.`);
    return hypotheses;
  } catch (error) {
    console.error("AI_HYPOTHESIS_SERVICE: Error generating XP Hypotheses:", error);
    return [];
  }
}
console.log("AI_HYPOTHESIS_SERVICE_JS: File execution finished. Exports configured.");
const aiHypothesisService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  generateXPHypotheses
}, Symbol.toStringTag, { value: "Module" }));
console.log("AI_CODE_ASSIST_SERVICE_JS: File execution started.");
const constructBasePromptContext = async (context) => {
  let promptContextText = "";
  if (context.filePath) {
    promptContextText += `

[[当前文件上下文: ${context.filePath}]]`;
  }
  if (context.language) {
    promptContextText += `
[[编程语言: ${context.language}]]`;
  }
  if (context.projectId && typeof findRelevantMemories === "function" && typeof embedContentInternal === "function" && context.queryForMemory) {
    try {
      const appSettings = await getSettings();
      if (appSettings && appSettings.apiKey && appSettings.embeddingModel) {
        const queryEmbedding = await embedContentInternal(
          context.queryForMemory,
          "RETRIEVAL_QUERY",
          void 0,
          appSettings.apiKey
          // embeddingModel is handled by kernelService
        );
        if (typeof queryEmbedding !== "string" && queryEmbedding !== null) {
          const relevantMemories = await findRelevantMemories(
            queryEmbedding,
            {
              personaTarget: "XiaoLan",
              projectContextId: context.projectId,
              contextType: "code_assistance",
              currentTopicKeywords: context.keywordsForMemory || [],
              desiredMemoryTypes: ["technical_snippet", "coding_preference", "api_usage_example", "architecture_decision", "debug_log_solution"]
            },
            3
          );
          let memoryContext = "";
          if (relevantMemories && relevantMemories.direct && relevantMemories.direct.length > 0) {
            memoryContext += "\n\n[[相关核心记忆碎片 (供你参考)]]:\n";
            relevantMemories.direct.forEach((mem) => {
              memoryContext += `- ${mem.memory_content} (重要性: ${mem.importance}, 类型: ${mem.memory_type || "未知"})
`;
            });
          }
          promptContextText += memoryContext;
        }
      } else {
        console.warn("AI_CODE_ASSIST_SERVICE: API key or embedding model not configured. Skipping memory retrieval for RAG context.");
      }
    } catch (memError) {
      console.error("AI_CODE_ASSIST_SERVICE: Error retrieving memories for context:", memError);
    }
  }
  return promptContextText;
};
async function generateCode(context) {
  console.log("AI_CODE_ASSIST_SERVICE: generateCode called with context:", context);
  const { currentSelection, surroundingCode, language, userInstruction, filePath, projectId } = context;
  const basePromptContext = await constructBasePromptContext({
    filePath,
    language,
    projectId,
    queryForMemory: userInstruction + (currentSelection || "") + (surroundingCode || ""),
    keywordsForMemory: (userInstruction.match(/\b\w+\b/g) || []).slice(0, 5)
  });
  let prompt = `你是一位专业、精通多种编程语言的AI代码助手“小岚”。请根据用户的指令和提供的上下文生成或修改代码。
你的目标是提供准确、高效、可读性强的代码。

[[用户指令]]:
${userInstruction}
`;
  if (currentSelection) {
    prompt += `
[[当前选中的代码片段 (待修改或作为参考)]]:
\`\`\`${language}
${currentSelection}
\`\`\`
`;
  }
  if (surroundingCode) {
    prompt += `
[[选中代码周围的上下文代码 (供参考)]]:
\`\`\`${language}
${surroundingCode}
\`\`\`
`;
  }
  prompt += basePromptContext;
  prompt += "\n请直接输出生成的代码块，如果需要多个文件或片段，请使用清晰的注释（如 // FILE: path/to/file.ext）来区分。避免在代码之外添加不必要的解释，除非用户明确要求。";
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) return "错误：API Key未配置。";
    const response = await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.3, topK: 40, topP: 0.95 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
    const fenceRegex = /^```(?:\w*\n)?([\s\S]*?)\n?```$/;
    const match = response.match(fenceRegex);
    return match && match[1] ? match[1].trim() : response.trim();
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (generateCode) Error:", error);
    return `代码生成失败: ${error.message}`;
  }
}
async function generateModifiedCode(context) {
  var _a;
  console.log("AI_CODE_ASSIST_SERVICE: generateModifiedCode called for filePath:", context.filePath);
  const { filePath, userInstruction, originalCode } = context;
  const language = ((_a = filePath.split(".").pop()) == null ? void 0 : _a.toLowerCase()) || "plaintext";
  const prompt = `
你是一位精通代码重构和修改的AI代码助手“小岚”。
你的任务是根据用户的指令，对提供的原始代码进行修改，并返回修改后的【完整文件内容】。

[[用户指令]]:
${userInstruction}

[[文件路径]]:
${filePath}

[[编程语言]]:
${language}

[[原始代码 (完整内容)]]:
\`\`\`${language}
${originalCode}
\`\`\`

请严格按照用户指令修改上述原始代码。
你的输出【必须且只能是】修改后的【完整文件代码】，不要包含任何额外的解释、聊天对话、或Markdown标记（例如 \`\`\`${language} ... \`\`\` 这样的代码块标记也不要）。
确保返回的代码可以直接写入文件替换原有内容。
如果用户指令不清晰或无法安全执行，请返回原始代码并附带一条简短的注释说明原因，例如：
// AI_COMMENT: 用户指令不清晰，无法执行修改。
// [原始代码...]
`;
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) {
      return { error: "错误：API Key未配置。" };
    }
    const responseText = await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.2, topK: 30, topP: 0.9 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
    return { newCode: responseText.trim() };
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (generateModifiedCode) Error:", error);
    return { error: `代码修改失败: ${error.message}` };
  }
}
async function explainCode(context) {
  console.log("AI_CODE_ASSIST_SERVICE: explainCode called with context:", context);
  const { codeToExplain, language, filePath, projectId } = context;
  const basePromptContext = await constructBasePromptContext({
    filePath,
    language,
    projectId,
    queryForMemory: codeToExplain,
    keywordsForMemory: (codeToExplain.match(/\b\w+\b/g) || []).slice(0, 10)
  });
  const prompt = `你是一位AI代码助手“小岚”。请用简洁明了的语言解释以下 ${language} 代码片段的功能和主要逻辑。
如果可能，请提供一个简单的使用示例或说明其在典型场景中的作用。

[[待解释的代码片段]]:
\`\`\`${language}
${codeToExplain}
\`\`\`
${basePromptContext}

请专注于代码本身，提供清晰、准确的解释。
`;
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) return "错误：API Key未配置。";
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.5 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (explainCode) Error:", error);
    return `代码解释失败: ${error.message}`;
  }
}
async function generateDocForCode(context) {
  console.log("AI_CODE_ASSIST_SERVICE: generateDocForCode called with context:", context);
  const { codeToDoc, language, filePath, projectId } = context;
  const basePromptContext = await constructBasePromptContext({
    filePath,
    language,
    projectId,
    queryForMemory: codeToDoc,
    keywordsForMemory: (codeToDoc.match(/\b\w+\b/g) || []).filter((kw) => kw.length > 2).slice(0, 5)
  });
  const prompt = `你是一位AI代码助手“小岚”。请为以下 ${language} 代码片段生成标准格式的文档注释 (例如 JSDoc, Python Docstrings, JavaDoc等，根据语言选择合适的风格)。
文档应清晰描述其功能、参数（如有）、返回值（如有）和任何重要的注意事项。

[[待生成文档的代码片段]]:
\`\`\`${language}
${codeToDoc}
\`\`\`
${basePromptContext}

请仅输出生成的文档注释块，使其可以直接插入到代码中。
`;
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) return "错误：API Key未配置。";
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.2 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (generateDocForCode) Error:", error);
    return `文档生成失败: ${error.message}`;
  }
}
async function reviewCode(context) {
  console.log("AI_CODE_ASSIST_SERVICE: reviewCode called with context:", context);
  const { codeToReview, language, filePath, projectId } = context;
  const basePromptContext = await constructBasePromptContext({
    filePath,
    language,
    projectId,
    queryForMemory: codeToReview
  });
  const prompt = `你是一位经验丰富的AI代码审查员“小岚”。请仔细审查以下 ${language} 代码片段，并提供反馈。
你的反馈应包括：
1.  潜在的Bug或逻辑错误。
2.  代码风格和可读性方面的改进建议。
3.  性能优化建议（如果适用）。
4.  遵循的最佳实践或安全注意事项。

[[待审查的代码片段]]:
\`\`\`${language}
${codeToReview}
\`\`\`
${basePromptContext}

请提供具体、可操作的建议。如果代码质量很好，也请说明。
`;
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) return "错误：API Key未配置。";
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.6 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (reviewCode) Error:", error);
    return `代码审查失败: ${error.message}`;
  }
}
async function analyzeErrorLog(context) {
  console.log("AI_CODE_ASSIST_SERVICE: analyzeErrorLog called with context:", context);
  const { errorLog, language, filePath, projectId, surroundingCode } = context;
  const basePromptContext = await constructBasePromptContext({
    filePath,
    language,
    projectId,
    queryForMemory: errorLog + (surroundingCode || ""),
    keywordsForMemory: errorLog.match(/\b[A-Za-z_][A-Za-z0-9_]*Error\b|\b\w+Exception\b/g) || []
  });
  let prompt = `你是一位AI调试助手“小岚”。我遇到了一个错误，这是相关的日志：

[[错误日志]]:
\`\`\`
${errorLog}
\`\`\`
`;
  if (surroundingCode) {
    prompt += `
[[可能相关的代码上下文 (${language || "未知语言"})]]:
\`\`\`${language || ""}
${surroundingCode}
\`\`\`
`;
  }
  prompt += basePromptContext;
  prompt += `
请帮助我分析这个错误日志：
1.  指出错误的根本原因可能是什么。
2.  提供一些可能的解决方案或调试步骤。
3.  如果日志信息不足，请指出还需要哪些额外信息。
`;
  try {
    const appSettings = await getSettings();
    if (!appSettings.apiKey) return "错误：API Key未配置。";
    return await generateContentInternal(
      [{ role: "user", parts: [{ text: prompt }] }],
      { temperature: 0.7 },
      GEMINI_TEXT_MODEL,
      appSettings.apiKey
    );
  } catch (error) {
    console.error("AI_CODE_ASSIST_SERVICE (analyzeErrorLog) Error:", error);
    return `错误日志分析失败: ${error.message}`;
  }
}
console.log("AI_CODE_ASSIST_SERVICE_JS: File execution finished. Exports configured.");
const aiCodeAssistService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  analyzeErrorLog,
  explainCode,
  generateCode,
  generateDocForCode,
  generateModifiedCode,
  reviewCode
}, Symbol.toStringTag, { value: "Module" }));
console.log("COMMAND_EXECUTION_SERVICE_JS: File execution started.");
const activeProcesses = /* @__PURE__ */ new Map();
function executeCommand(internalPid, commandString, argsArray, cwd, onStdOut, onStdErr, onExit) {
  console.log(`COMMAND_EXECUTION_SERVICE: Executing (PID_INTERNAL: ${internalPid}): "${commandString} ${argsArray.join(" ")}" in "${cwd}"`);
  try {
    const child = spawn(commandString, argsArray, {
      cwd,
      shell: process.platform === "win32",
      // Use shell on Windows for .cmd, .bat, etc.
      stdio: ["ignore", "pipe", "pipe"]
      // ignore stdin, pipe stdout/stderr
    });
    const systemPid = child.pid;
    activeProcesses.set(internalPid, { child, commandString, argsArray, systemPid });
    console.log(`COMMAND_EXECUTION_SERVICE: Spawned (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid})`);
    child.stdout.setEncoding("utf8");
    child.stdout.on("data", (data) => {
      const normalizedData = data.toString().replace(/\r\n|\r/g, EOL);
      onStdOut(internalPid, systemPid, normalizedData);
    });
    child.stderr.setEncoding("utf8");
    child.stderr.on("data", (data) => {
      const normalizedData = data.toString().replace(/\r\n|\r/g, EOL);
      onStdErr(internalPid, systemPid, normalizedData);
    });
    child.on("exit", (code, signal) => {
      console.log(`COMMAND_EXECUTION_SERVICE: Process exited (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid}), code: ${code}, signal: ${signal}`);
      onExit(internalPid, systemPid, code, signal);
      activeProcesses.delete(internalPid);
    });
    child.on("error", (err) => {
      console.error(`COMMAND_EXECUTION_SERVICE: Failed to start process (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid}):`, err);
      onExit(internalPid, systemPid, 1, null, err.message);
      activeProcesses.delete(internalPid);
    });
  } catch (error) {
    console.error(`COMMAND_EXECUTION_SERVICE: Error spawning process (PID_INTERNAL: ${internalPid}):`, error);
    onExit(internalPid, void 0, 1, null, error.message);
    if (activeProcesses.has(internalPid)) {
      activeProcesses.delete(internalPid);
    }
  }
}
function killCommand(internalPid) {
  const processInfo = activeProcesses.get(internalPid);
  if (processInfo && processInfo.child && !processInfo.child.killed) {
    console.log(`COMMAND_EXECUTION_SERVICE: Attempting to kill process (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${processInfo.systemPid})`);
    const killed = processInfo.child.kill();
    if (killed) {
      console.log(`COMMAND_EXECUTION_SERVICE: Kill signal sent to (PID_INTERNAL: ${internalPid}). Process should exit soon.`);
    } else {
      console.warn(`COMMAND_EXECUTION_SERVICE: Failed to send kill signal to (PID_INTERNAL: ${internalPid}). It might have already exited.`);
    }
    return killed;
  } else {
    console.warn(`COMMAND_EXECUTION_SERVICE: Process (PID_INTERNAL: ${internalPid}) not found or already killed.`);
    return false;
  }
}
console.log("COMMAND_EXECUTION_SERVICE_JS: File execution finished. Exports configured.");
const commandExecutionService = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  executeCommand,
  killCommand
}, Symbol.toStringTag, { value: "Module" }));
console.log("ELECTRON MAIN PROCESS SCRIPT STARTED");
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
console.log(`ELECTRON MAIN: __filename: ${__filename}`);
console.log(`ELECTRON MAIN: __dirname set to: ${__dirname}`);
console.log("ELECTRON MAIN: Core Electron and Node modules imported.");
const fsPromises = fs.promises;
console.log("ELECTRON MAIN: Constants imported. DEFAULT_SETTINGS.chatModel:", DEFAULT_SETTINGS == null ? void 0 : DEFAULT_SETTINGS.chatModel);
process.env.DIST = path.join(__dirname, "../dist");
process.env.VITE_PUBLIC = app.isPackaged ? process.env.DIST : path.join(process.env.DIST, "../public");
const ragService = ragServiceImport;
console.log("ELECTRON MAIN: Services imported via ES module import.");
if (!dbService || !aiOrchestrationService || !audioService || !ragService || !aiHypothesisService || !aiCodeAssistService || !universalAssetService || !fileSystemService || !commandExecutionService || !organizationService) {
  console.error("ELECTRON MAIN CRITICAL ERROR: One or more services are not loaded after import. dbService:", !!dbService, "aiOrchestrationService:", !!aiOrchestrationService, "audioService:", !!audioService, "ragService:", !!ragService, "aiHypothesisService:", !!aiHypothesisService, "aiCodeAssistService:", !!aiCodeAssistService, "universalAssetService:", !!universalAssetService, "fileSystemService:", !!fileSystemService, "commandExecutionService:", !!commandExecutionService, "organizationService:", !!organizationService);
  dialog.showErrorBox("Service Undefined", "A critical service is undefined after loading attempt. Application will exit.");
  app.quit();
  throw new Error("Critical service undefined.");
}
console.log("ELECTRON MAIN: All required services (DB, AIOrchestration, Audio, RAG, AIHypothesis, AICodeAssist, UniversalAsset, FileSystem, CommandExecution, Organization) confirmed to be loaded.");
let mainWindow = null;
async function initializeServices() {
  console.log("ELECTRON MAIN: Initializing services (async)...");
  const userDataPath = app.getPath("userData");
  try {
    console.log("ELECTRON MAIN: Initializing organizationService (loads posts/characters)...");
    await initializeOrganizationService();
    console.log("ELECTRON MAIN: Organization service initialized.");
    console.log("ELECTRON MAIN: Initializing dbService (aggregator, which calls core)...");
    await initializeDatabaseService(userDataPath, DEFAULT_SETTINGS);
    console.log("ELECTRON MAIN: Database service initialized by main process.");
    const initialSettings = await getSettings();
    console.log("ELECTRON MAIN: Initializing ragService...");
    ragService.initializeRagService(dbService, userDataPath);
    console.log("ELECTRON MAIN: RAG service initialized by main process.");
    console.log("ELECTRON MAIN: Initializing universalAssetService...");
    initializeUniversalAssetService(userDataPath);
    console.log("ELECTRON MAIN: Universal Asset service initialized by main process.");
    console.log("ELECTRON MAIN: aiHypothesisService is available (no explicit init needed from main.ts).");
    console.log("ELECTRON MAIN: aiCodeAssistService is available (no explicit init needed from main.ts).");
    console.log("ELECTRON MAIN: fileSystemService is available (no explicit init needed from main.ts).");
    console.log("ELECTRON MAIN: commandExecutionService is available (no explicit init needed from main.ts).");
    if (initialSettings && initialSettings.apiKey && aiOrchestrationService && typeof initializeAIAgent === "function") {
      console.log("ELECTRON MAIN: Initializing AI Orchestration Service (which includes kernel) with API key from initial settings.");
      initializeAIAgent(initialSettings.apiKey);
    } else {
      console.log("[ELECTRON MAIN] AI Orchestration Service initialization SKIPPED (No API key or service issue).");
    }
    console.log("ELECTRON MAIN: Initializing AudioService...");
    initializeAudioService(initialSettings, dbService);
    console.log("ELECTRON MAIN: Audio service initialized.");
    console.log("ELECTRON MAIN: All services initialized successfully.");
  } catch (initError) {
    console.error("ELECTRON MAIN CRITICAL ERROR: Failed to initialize services.", initError);
    dialog.showErrorBox("Service Initialization Error", `Failed to initialize services: ${initError.message}. Application will exit.`);
    app.quit();
    throw initError;
  }
}
function createWindow() {
  console.log("ELECTRON MAIN: createWindow() called.");
  let preloadScriptPath;
  if (app.isPackaged) {
    preloadScriptPath = path.join(__dirname, "preload.mjs");
  } else {
    preloadScriptPath = path.join(__dirname, "preload.mjs");
  }
  console.log(`ELECTRON MAIN: Attempting to use preload script at: ${preloadScriptPath}`);
  if (!fs.existsSync(preloadScriptPath)) {
    const errorMsg = `Preload script not found at ${preloadScriptPath}. Vite might not have built it correctly or main.ts is looking for the wrong file extension.`;
    console.error(`ELECTRON MAIN CRITICAL ERROR: ${errorMsg}`);
    dialog.showErrorBox("Preload Script Error", `${errorMsg}
Application will exit.`);
    app.quit();
    return;
  }
  mainWindow = new BrowserWindow({
    width: 1600,
    height: 950,
    webPreferences: {
      preload: preloadScriptPath,
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: false,
      devTools: !app.isPackaged
    },
    icon: path.join(process.env.VITE_PUBLIC, "vite.svg")
  });
  console.log(`ELECTRON MAIN: BrowserWindow webPreferences configured. Preload: ${preloadScriptPath}`);
  if (mainWindow && audioService) {
    setMainWindowWebContents(mainWindow.webContents);
    console.log("ELECTRON MAIN: Passed mainWindow.webContents to audioService.");
  }
  const devServerUrl = process.env.VITE_DEV_SERVER_URL;
  if (devServerUrl) {
    console.log(`ELECTRON MAIN: Loading Dev Server URL: ${devServerUrl}`);
    mainWindow.loadURL(devServerUrl);
    if (!app.isPackaged) mainWindow.webContents.openDevTools();
  } else {
    const indexPath = path.join(process.env.DIST, "index.html");
    console.log(`ELECTRON MAIN: Loading production file from: ${indexPath}`);
    if (!fs.existsSync(indexPath)) {
      console.error(`ELECTRON MAIN CRITICAL ERROR: Production index.html not found at ${indexPath}`);
      dialog.showErrorBox("File Not Found", `Production index.html not found at ${indexPath}. Application will exit.`);
      app.quit();
      return;
    }
    mainWindow.loadFile(indexPath);
  }
  mainWindow.on("closed", () => {
    mainWindow = null;
  });
  console.log("ELECTRON MAIN: Main window created and content loading initiated.");
}
const AVATAR_SYSTEM_PREFIX = "system_packaged/";
const SYSTEM_RESOURCES_PATH_SEGMENT = "system";
app.whenReady().then(async () => {
  console.log("ELECTRON MAIN: App is ready.");
  await initializeServices();
  createWindow();
  protocol.registerFileProtocol("at-asset", (request, callback) => {
    try {
      const urlPath = request.url.slice("at-asset://".length).split("?")[0];
      const decodedPath = decodeURI(urlPath);
      const absolutePath = path.join(app.getPath("userData"), "at_cms_assets", decodedPath);
      if (fs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`AT-ASSET Protocol Error: File not found at ${absolutePath} (requested: ${request.url})`);
        callback({ error: -6 });
      }
    } catch (e) {
      console.error(`AT-ASSET Protocol Error: Error resolving path for ${request.url}`, e);
      callback({ error: -2 });
    }
  });
  console.log("ELECTRON MAIN: 'at-asset' custom protocol registered.");
  protocol.registerFileProtocol("tgc-asset", (request, callback) => {
    try {
      const urlPathParts = request.url.slice("tgc-asset://".length).split("?")[0];
      const decodedPath = decodeURI(urlPathParts);
      const absolutePath = path.join(app.getPath("userData"), "asset_packs", decodedPath);
      if (fs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`TGC-ASSET Protocol Error: File not found at ${absolutePath} (requested: ${request.url})`);
        callback({ error: -6 });
      }
    } catch (e) {
      console.error(`TGC-ASSET Protocol Error: Exception for ${request.url}`, e);
      callback({ error: -2 });
    }
  });
  console.log("ELECTRON MAIN: 'tgc-asset' custom protocol registered.");
  protocol.registerFileProtocol("app-avatar", (request, callback) => {
    try {
      let requestedPath = request.url.slice("app-avatar://".length).split("?")[0];
      let absolutePath;
      if (requestedPath.startsWith(AVATAR_SYSTEM_PREFIX)) {
        const relativePathInPackage = requestedPath.substring(AVATAR_SYSTEM_PREFIX.length);
        absolutePath = path.join(process.env.DIST, SYSTEM_RESOURCES_PATH_SEGMENT, relativePathInPackage);
        console.log(`APP-AVATAR: Resolving system_packaged avatar. Request URL: "${request.url}", RelativePathInPackage: "${relativePathInPackage}", Resolved AbsolutePath: "${absolutePath}"`);
      } else {
        absolutePath = path.join(app.getPath("userData"), decodeURI(requestedPath));
        console.log(`APP-AVATAR: Resolving user avatar. Request URL: "${request.url}", DecodedPath: "${decodeURI(requestedPath)}", Resolved AbsolutePath: "${absolutePath}"`);
      }
      if (fs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`APP-AVATAR Protocol Error: File NOT FOUND at resolved absolute path: "${absolutePath}" (Original request: "${request.url}")`);
        callback({ error: -6 });
      }
    } catch (e) {
      console.error(`APP-AVATAR Protocol Error: Exception for "${request.url}"`, e);
      callback({ error: -2 });
    }
  });
  console.log("ELECTRON MAIN: 'app-avatar' custom protocol registered with system_packaged handling.");
  app.on("activate", function() {
    if (BrowserWindow.getAllWindows().length === 0) {
      console.log("ELECTRON MAIN: App activated and no windows open, creating new window.");
      createWindow();
    }
  });
});
app.on("will-quit", () => {
  console.log("ELECTRON MAIN: App 'will-quit' event triggered. Cleaning up services.");
  if (dbService) {
    closeDatabaseConnection();
  }
  console.log("ELECTRON MAIN: Services cleanup requested during 'will-quit'.");
});
app.on("window-all-closed", function() {
  console.log("ELECTRON MAIN: All windows closed.");
  if (os.platform() !== "darwin") {
    app.quit();
  }
});
ipcMain.handle("db:getSettings", async () => {
  console.log("IPC_MAIN: Handling db:getSettings request.");
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getSettings - dbService not loaded.");
    return { ...DEFAULT_SETTINGS, error: "Database service not available." };
  }
  try {
    const settings = await getSettings();
    if (settings && settings.apiKey && aiOrchestrationService && typeof initializeAIAgent === "function") {
      initializeAIAgent(settings.apiKey);
      if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
        mainWindow.webContents.send("ai-service-ready");
        console.log("IPC_MAIN (db:getSettings): Sent 'ai-service-ready' due to API key presence.");
      }
    }
    if (audioService && typeof initializeAudioService === "function") {
      initializeAudioService(settings, dbService);
    }
    console.log("IPC_MAIN: db:getSettings returning settings.");
    return settings;
  } catch (e) {
    console.error("IPC_MAIN_ERROR: db:getSettings failed:", e);
    return { ...DEFAULT_SETTINGS, error: e.message };
  }
});
ipcMain.handle("db:saveSettings", async (event, settings) => {
  console.log("IPC_MAIN: Handling db:saveSettings request.");
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:saveSettings - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  try {
    const result = await saveSettings(settings);
    if (result.success && settings.apiKey && aiOrchestrationService && typeof initializeAIAgent === "function") {
      initializeAIAgent(settings.apiKey);
      if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
        mainWindow.webContents.send("ai-service-ready");
        console.log("IPC_MAIN (db:saveSettings): Sent 'ai-service-ready' after saving API key.");
      }
    }
    if (audioService && typeof initializeAudioService === "function") {
      initializeAudioService(settings, dbService);
    }
    console.log("IPC_MAIN: db:saveSettings result:", result.success);
    return result;
  } catch (e) {
    console.error("IPC_MAIN_ERROR: db:saveSettings failed:", e);
    return { success: false, error: e.message };
  }
});
ipcMain.handle("db:getAllProjects", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllProjects - dbService not loaded.");
    return [];
  }
  return await getAllProjects();
});
ipcMain.handle("db:addProject", async (event, projectData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addProject - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  try {
    const newProject = await addProject(projectData);
    return { success: true, project: newProject };
  } catch (error) {
    console.error("IPC_MAIN_ERROR: db:addProject failed:", error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle("db:getProjectById", async (event, projectId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getProjectById - dbService not loaded.");
    return null;
  }
  return await getProjectById(projectId);
});
ipcMain.handle("db:updateProject", async (event, projectData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateProject - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await updateProject(projectData);
});
ipcMain.handle("db:deleteProject", async (event, projectId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteProject - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteProject(projectId);
});
ipcMain.handle("db:duplicateProject", async (event, projectId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:duplicateProject - dbService not loaded.");
    return null;
  }
  return await duplicateProject(projectId);
});
ipcMain.handle("db:addNoteToProject", async (event, projectId, pouchType, noteData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addNoteToProject - dbService not loaded.");
    return null;
  }
  return await addNoteToPouch(projectId, pouchType, noteData);
});
ipcMain.handle("db:updateNoteInProject", async (event, pouchType, noteData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateNoteInProject - dbService not loaded.");
    return null;
  }
  return await updateNoteInPouch(pouchType, noteData);
});
ipcMain.handle("db:deleteNoteFromProject", async (event, pouchType, noteId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteNoteFromProject - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteNoteFromPouch(pouchType, noteId);
});
ipcMain.handle("db:updateProjectMindMap", async (event, projectId, nodes, connections) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateProjectMindMap - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await updateProjectMindMap(projectId, nodes, connections);
});
ipcMain.handle("db:getAllGlobalKnowledgeTomes", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllGlobalKnowledgeTomes - dbService not loaded.");
    return [];
  }
  return await getAllGlobalKnowledgeTomes();
});
ipcMain.handle("db:addGlobalKnowledgeTome", async (event, tomeData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addGlobalKnowledgeTome - dbService not loaded.");
    return null;
  }
  return await addGlobalKnowledgeTome(tomeData);
});
ipcMain.handle("db:updateGlobalKnowledgeTome", async (event, tomeData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateGlobalKnowledgeTome - dbService not loaded.");
    return null;
  }
  return await updateGlobalKnowledgeTome(tomeData);
});
ipcMain.handle("db:deleteGlobalKnowledgeTome", async (event, tomeId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteGlobalKnowledgeTome - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteGlobalKnowledgeTome(tomeId);
});
ipcMain.handle("db:addProjectKnowledgeTome", async (event, projectId, tomeData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addProjectKnowledgeTome - dbService not loaded.");
    return null;
  }
  return await addProjectKnowledgeTome(projectId, tomeData);
});
ipcMain.handle("db:updateProjectKnowledgeTome", async (event, projectId, tomeData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateProjectKnowledgeTome - dbService not loaded.");
    return null;
  }
  return await updateProjectKnowledgeTome(projectId, tomeData);
});
ipcMain.handle("db:deleteProjectKnowledgeTome", async (event, projectId, tomeId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteProjectKnowledgeTome - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteProjectKnowledgeTome(projectId, tomeId);
});
ipcMain.handle("db:addProjectKnowledgeCategory", async (event, projectId, categoryName) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addProjectKnowledgeCategory - dbService not loaded.");
    return void 0;
  }
  return await addProjectKnowledgeCategory(projectId, categoryName);
});
ipcMain.handle("db:removeProjectKnowledgeCategory", async (event, projectId, categoryName) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:removeProjectKnowledgeCategory - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await removeProjectKnowledgeCategory(projectId, categoryName);
});
ipcMain.handle("db:getAllGlobalQuickCommands", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllGlobalQuickCommands - dbService not loaded.");
    return [];
  }
  return await getAllGlobalQuickCommands();
});
ipcMain.handle("db:addGlobalQuickCommand", async (event, commandData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addGlobalQuickCommand - dbService not loaded.");
    return null;
  }
  return await addGlobalQuickCommand(commandData);
});
ipcMain.handle("db:updateGlobalQuickCommand", async (event, commandData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateGlobalQuickCommand - dbService not loaded.");
    return null;
  }
  return await updateGlobalQuickCommand(commandData);
});
ipcMain.handle("db:deleteGlobalQuickCommand", async (event, commandId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteGlobalQuickCommand - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteGlobalQuickCommand(commandId);
});
ipcMain.handle("db:saveChatMessage", async (event, projectId, message) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:saveChatMessage - dbService not loaded.");
    return null;
  }
  return await saveChatMessage(projectId, message);
});
ipcMain.handle("db:updateChatMessage", async (event, message) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateChatMessage - dbService not loaded.");
    return null;
  }
  return await updateChatMessage(message);
});
ipcMain.handle("db:getInitialChatMessages", async (event, projectId, limit) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getInitialChatMessages - dbService not loaded.");
    return [];
  }
  return await getInitialChatMessages(projectId, limit);
});
ipcMain.handle("db:getOlderChatMessages", async (event, projectId, beforeTimestamp, limit) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getOlderChatMessages - dbService not loaded.");
    return [];
  }
  return await getOlderChatMessages(projectId, beforeTimestamp, limit);
});
ipcMain.handle("db:summarizeAndReplaceMessages", async (event, projectId, messageIds, summaryMessage) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:summarizeAndReplaceMessages - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await summarizeAndReplaceMessages(projectId, messageIds, summaryMessage);
});
ipcMain.handle("db:getAllDevelopmentTasks", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllDevelopmentTasks - dbService not loaded.");
    return [];
  }
  return await getAllDevelopmentTasks();
});
ipcMain.handle("db:addDevelopmentTask", async (event, projectId, title) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addDevelopmentTask - dbService not loaded.");
    return null;
  }
  return await addDevelopmentTask(projectId, title);
});
ipcMain.handle("db:createDevelopmentTaskFromChat", async (event, payload) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:createDevelopmentTaskFromChat - dbService not loaded.");
    return null;
  }
  const { projectId, title, description } = payload;
  return await createDevelopmentTaskFromChat(projectId, title, description);
});
ipcMain.handle("db:deleteDevelopmentTask", async (event, taskId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteDevelopmentTask - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteDevelopmentTask(taskId);
});
ipcMain.handle("db:updateDevelopmentTaskContextFiles", async (event, taskId, contextFiles) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateDevelopmentTaskContextFiles - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await updateDevelopmentTaskContextFiles(taskId, contextFiles);
});
ipcMain.handle("db:updateDevelopmentTaskGeneratedCode", async (event, taskId, generatedCode) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateDevelopmentTaskGeneratedCode - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await updateDevelopmentTaskGeneratedCode(taskId, generatedCode);
});
ipcMain.handle("db:getAgentCoreSetting", async (event, settingId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAgentCoreSetting - dbService not loaded.");
    return null;
  }
  return await getAgentCoreSetting(settingId);
});
ipcMain.handle("db:getAllAgentCoreSettings", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllAgentCoreSettings - dbService not loaded.");
    return [];
  }
  return await getAllAgentCoreSettings();
});
ipcMain.handle("db:saveAgentCoreSetting", async (event, settingId, content) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:saveAgentCoreSetting - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await saveAgentCoreSetting(settingId, content);
});
ipcMain.handle("db:findRelevantMemories", async (event, queryEmbedding, contextInfo, limit) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:findRelevantMemories - dbService not loaded.");
    return { direct: [], associated: [] };
  }
  return await findRelevantMemories(queryEmbedding, contextInfo, limit);
});
ipcMain.handle("db:getCoreMemories", async (event, personaTarget, projectContextId, limit, importanceThreshold, keywords) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getCoreMemories - dbService not loaded.");
    return [];
  }
  return await getCoreMemories(personaTarget, projectContextId, limit, importanceThreshold, keywords);
});
ipcMain.handle("db:getAllCoreMemories", async (event, filters, sort, pagination) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllCoreMemories - dbService not loaded.");
    return [];
  }
  return await getAllCoreMemories(filters, sort, pagination);
});
ipcMain.handle("db:addCoreMemory", async (event, memoryData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addCoreMemory - dbService not loaded.");
    return null;
  }
  return await addCoreMemory(memoryData);
});
ipcMain.handle("db:addCoreMemoryFromChat", async (event, messageText, personaTarget, projectId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addCoreMemoryFromChat - dbService not loaded.");
    return null;
  }
  return await addCoreMemoryFromChat(messageText, personaTarget, projectId);
});
ipcMain.handle("db:updateCoreMemory", async (event, memoryData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateCoreMemory - dbService not loaded.");
    return null;
  }
  return await updateCoreMemory(memoryData);
});
ipcMain.handle("db:deleteCoreMemory", async (event, memoryId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:deleteCoreMemory - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteCoreMemory(memoryId);
});
ipcMain.handle("db:getCoreMemoryById", async (event, memoryId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getCoreMemoryById - dbService not loaded.");
    return null;
  }
  return await getCoreMemoryById(memoryId);
});
ipcMain.handle("settings:getAbsoluteTerritoryPassword", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: settings:getAbsoluteTerritoryPassword - dbService not loaded.");
    return null;
  }
  return await getAbsoluteTerritoryPassword();
});
ipcMain.handle("settings:setAbsoluteTerritoryPassword", async (event, password) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: settings:setAbsoluteTerritoryPassword - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await setAbsoluteTerritoryPassword(password);
});
ipcMain.handle("settings:verifyAbsoluteTerritoryPassword", async (event, password) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: settings:verifyAbsoluteTerritoryPassword - dbService not loaded.");
    return { isValid: false, isFirstTime: true };
  }
  return await verifyAbsoluteTerritoryPassword(password);
});
ipcMain.handle("db:getAbsoluteTerritoryMessages", async (event, limit, beforeTimestamp) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAbsoluteTerritoryMessages - dbService not loaded.");
    return [];
  }
  return await getAbsoluteTerritoryMessages(limit, beforeTimestamp);
});
ipcMain.handle("db:addAbsoluteTerritoryMessage", async (event, message) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addAbsoluteTerritoryMessage - dbService not loaded.");
    return null;
  }
  return await addAbsoluteTerritoryMessage(message);
});
ipcMain.handle("db:clearAbsoluteTerritoryHistory", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:clearAbsoluteTerritoryHistory - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await clearAbsoluteTerritoryHistory();
});
ipcMain.handle("cms:getCMSItems", async (event, type2) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:getCMSItems - dbService not loaded.");
    return [];
  }
  return await getCMSItems(type2);
});
ipcMain.handle("cms:addCMSItem", async (event, type2, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:addCMSItem - dbService not loaded.");
    return null;
  }
  return await addCMSItem(type2, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson);
});
ipcMain.handle("cms:updateCMSItem", async (event, type2, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:updateCMSItem - dbService not loaded.");
    return null;
  }
  return await updateCMSItem(type2, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson);
});
ipcMain.handle("cms:deleteCMSItem", async (event, type2, itemId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:deleteCMSItem - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteCMSItem(type2, itemId);
});
ipcMain.handle("cms:triggerHuntingTime", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:triggerHuntingTime - dbService not loaded.");
    return null;
  }
  return await triggerHuntingTime();
});
ipcMain.handle("cms:getRolePlayingCards", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:getRolePlayingCards - dbService not loaded.");
    return [];
  }
  return await getRolePlayingCards();
});
ipcMain.handle("cms:getRolePlayingCardById", async (event, cardId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:getRolePlayingCardById - dbService not loaded.");
    return null;
  }
  return await getRolePlayingCardById(cardId);
});
ipcMain.handle("cms:addRolePlayingCard", async (event, cardData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:addRolePlayingCard - dbService not loaded.");
    return null;
  }
  return await addRolePlayingCard(cardData);
});
ipcMain.handle("cms:updateRolePlayingCard", async (event, cardData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:updateRolePlayingCard - dbService not loaded.");
    return null;
  }
  return await updateRolePlayingCard(cardData);
});
ipcMain.handle("cms:deleteRolePlayingCard", async (event, cardId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: cms:deleteRolePlayingCard - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteRolePlayingCard(cardId);
});
ipcMain.handle("db:addLearningLog", async (event, logData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:addLearningLog - dbService not loaded.");
    return null;
  }
  return await addLearningLog(logData);
});
ipcMain.handle("db:getLearningLogs", async (event, filters, limit, offset) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getLearningLogs - dbService not loaded.");
    return [];
  }
  return await getLearningLogs(filters, limit, offset);
});
ipcMain.handle("db:getBodyDevelopment", async (event, zone_id) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getBodyDevelopment - dbService not loaded.");
    return null;
  }
  return await getBodyDevelopment(zone_id);
});
ipcMain.handle("db:getAllBodyDevelopment", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllBodyDevelopment - dbService not loaded.");
    return [];
  }
  return await getAllBodyDevelopment();
});
ipcMain.handle("db:updateBodyDevelopment", async (event, zone_id, pointsToAdd) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:updateBodyDevelopment - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await updateBodyDevelopment(zone_id, pointsToAdd);
});
ipcMain.handle("db:getAllAchievements", async () => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAllAchievements - dbService not loaded.");
    return [];
  }
  return await getAllAchievements();
});
ipcMain.handle("db:getUserAchievements", async (event, userId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getUserAchievements - dbService not loaded.");
    return [];
  }
  return await getUserAchievements(userId);
});
ipcMain.handle("db:unlockAchievement", async (event, userId, achievementId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:unlockAchievement - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await unlockAchievement(userId, achievementId);
});
ipcMain.handle("db:getAchievementById", async (event, achievementId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getAchievementById - dbService not loaded.");
    return null;
  }
  return await getAchievementById(achievementId);
});
ipcMain.handle("tasks:getTaskById", async (event, taskId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:getTaskById - dbService not loaded.");
    return null;
  }
  return await getTaskById(taskId);
});
ipcMain.handle("tasks:getTasksByProjectId", async (event, projectId, filters, sortOptions) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:getTasksByProjectId - dbService not loaded.");
    return [];
  }
  return await getTasksByProjectId(projectId, filters, sortOptions);
});
ipcMain.handle("tasks:getTasksByStatus", async (event, projectId, statusArray) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:getTasksByStatus - dbService not loaded.");
    return [];
  }
  return await getTasksByStatus(projectId, statusArray);
});
ipcMain.handle("tasks:addTask", async (event, taskData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:addTask - dbService not loaded.");
    return null;
  }
  return await addTask(taskData);
});
ipcMain.handle("tasks:updateTask", async (event, taskId, updates) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:updateTask - dbService not loaded.");
    return null;
  }
  return await updateTask(taskId, updates);
});
ipcMain.handle("tasks:deleteTask", async (event, taskId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:deleteTask - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await deleteTask(taskId);
});
ipcMain.handle("tasks:addResourceLink", async (event, taskId, resourceData) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:addResourceLink - dbService not loaded.");
    return null;
  }
  return await addResourceLinkToTask(taskId, resourceData);
});
ipcMain.handle("tasks:getResourceLinks", async (event, taskId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:getResourceLinks - dbService not loaded.");
    return [];
  }
  return await getResourceLinksForTask(taskId);
});
ipcMain.handle("tasks:removeResourceLink", async (event, linkId) => {
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: tasks:removeResourceLink - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  return await removeResourceLinkFromTask(linkId);
});
ipcMain.handle("tasks:suggestResources", async (event, taskId, taskTitle, taskDescription, projectId) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: tasks:suggestResources - aiOrchestrationService not loaded.");
    return [];
  }
  return await suggestResourcesForTask(taskTitle, taskDescription, projectId);
});
ipcMain.handle("aiCodeAssist:generateCode", async (event, context) => {
  if (!aiCodeAssistService) {
    console.error("IPC_MAIN_ERROR: aiCodeAssist:generateCode - aiCodeAssistService not loaded.");
    return "AI Code Assist service not available.";
  }
  return await generateCode(context);
});
ipcMain.handle("aiCodeAssist:explainCode", async (event, context) => {
  if (!aiCodeAssistService) {
    console.error("IPC_MAIN_ERROR: aiCodeAssist:explainCode - aiCodeAssistService not loaded.");
    return "AI Code Assist service not available.";
  }
  return await explainCode(context);
});
ipcMain.handle("aiCodeAssist:generateDocForCode", async (event, context) => {
  if (!aiCodeAssistService) {
    console.error("IPC_MAIN_ERROR: aiCodeAssist:generateDocForCode - aiCodeAssistService not loaded.");
    return "AI Code Assist service not available.";
  }
  return await generateDocForCode(context);
});
ipcMain.handle("aiCodeAssist:reviewCode", async (event, context) => {
  if (!aiCodeAssistService) {
    console.error("IPC_MAIN_ERROR: aiCodeAssist:reviewCode - aiCodeAssistService not loaded.");
    return "AI Code Assist service not available.";
  }
  return await reviewCode(context);
});
ipcMain.handle("aiCodeAssist:analyzeErrorLog", async (event, context) => {
  if (!aiCodeAssistService) {
    console.error("IPC_MAIN_ERROR: aiCodeAssist:analyzeErrorLog - aiCodeAssistService not loaded.");
    return "AI Code Assist service not available.";
  }
  return await analyzeErrorLog(context);
});
ipcMain.handle("ai:assist:modifyCode", async (event, context) => {
  if (!aiCodeAssistService || typeof generateModifiedCode !== "function") {
    console.error("IPC_MAIN_ERROR: ai:assist:modifyCode - aiCodeAssistService or generateModifiedCode function not loaded.");
    return { error: "AI Code Modification service not available." };
  }
  try {
    return await generateModifiedCode(context);
  } catch (error) {
    console.error("IPC_MAIN_ERROR: Error in ai:assist:modifyCode handler:", error);
    return { error: error.message || "Unknown error during code modification." };
  }
});
ipcMain.handle("fs:openDirectoryDialog", async () => {
  const result = await dialog.showOpenDialog(mainWindow, { properties: ["openDirectory"] });
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});
ipcMain.handle("fs:readDirectory", async (event, dirPath) => {
  try {
    const files = await fsPromises.readdir(dirPath, { withFileTypes: true });
    const fileNodes = await Promise.all(files.map(async (file) => {
      const filePath = path.join(dirPath, file.name);
      if (file.isDirectory()) {
        return { name: file.name, path: filePath, type: "directory", children: [] };
      } else {
        return { name: file.name, path: filePath, type: "file" };
      }
    }));
    return fileNodes;
  } catch (error) {
    return { error: error.message };
  }
});
ipcMain.handle("fs:readFileContent", async (event, filePath) => {
  try {
    return await fsPromises.readFile(filePath, "utf-8");
  } catch (error) {
    return { error: error.message };
  }
});
ipcMain.handle("fs:listFiles", async (event, directoryPath, recursive, depth) => {
  if (!fileSystemService) {
    console.error("IPC_MAIN_ERROR: fs:listFiles - fileSystemService not loaded.");
    return { error: "File system service not available." };
  }
  return await listFiles(directoryPath, recursive, depth);
});
ipcMain.handle("fs:readFile", async (event, filePath) => {
  if (!fileSystemService) {
    console.error("IPC_MAIN_ERROR: fs:readFile - fileSystemService not loaded.");
    return { error: "File system service not available." };
  }
  return await readFile(filePath);
});
ipcMain.handle("fs:writeFile", async (event, filePath, content) => {
  if (!fileSystemService) {
    console.error("IPC_MAIN_ERROR: fs:writeFile - fileSystemService not loaded.");
    return { success: false, error: "File system service not available." };
  }
  return await writeFile(filePath, content);
});
ipcMain.handle("fs:archiveFileVersion", async (event, filePath, originalCode, userInstruction) => {
  if (!fileSystemService || typeof archiveFileVersion !== "function") {
    console.error("IPC_MAIN_ERROR: fs:archiveFileVersion - fileSystemService or archiveFileVersion function not loaded.");
    return { success: false, error: "File versioning service not available." };
  }
  try {
    return await archiveFileVersion(filePath, originalCode, userInstruction);
  } catch (error) {
    console.error("IPC_MAIN_ERROR: Error in fs:archiveFileVersion handler:", error);
    return { success: false, error: error.message || "Unknown error during file versioning." };
  }
});
ipcMain.handle("fs:isDirectoryEmpty", async (event, directoryPath) => {
  if (!fileSystemService || typeof isDirectoryEmpty !== "function") {
    console.error("IPC_MAIN_ERROR: fs:isDirectoryEmpty - fileSystemService or isDirectoryEmpty function not loaded.");
    return { error: "File system service (isDirectoryEmpty) not available." };
  }
  return await isDirectoryEmpty(directoryPath);
});
ipcMain.handle("fs:copyDirectoryContents", async (event, sourceDir, targetDir) => {
  if (!fileSystemService || typeof copyDirectoryContents !== "function") {
    console.error("IPC_MAIN_ERROR: fs:copyDirectoryContents - fileSystemService or copyDirectoryContents function not loaded.");
    return { success: false, error: "File system service (copyDirectoryContents) not available." };
  }
  return await copyDirectoryContents(sourceDir, targetDir);
});
ipcMain.handle("file:read-package-json-scripts", async (event, projectPath) => {
  if (!projectPath || typeof projectPath !== "string") {
    return { error: "Invalid project path provided." };
  }
  const packageJsonPath = path.join(projectPath, "package.json");
  try {
    const content = await fsPromises.readFile(packageJsonPath, "utf-8");
    const packageJson = JSON.parse(content);
    return packageJson.scripts || {};
  } catch (error) {
    if (error.code === "ENOENT") {
      return { error: `package.json not found at ${packageJsonPath}` };
    }
    return { error: `Failed to read or parse package.json: ${error.message}` };
  }
});
ipcMain.handle("fs:saveFileContent", async (event, filePath, content) => {
  try {
    await fsPromises.writeFile(filePath, content, "utf-8");
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
ipcMain.handle("fs:openFileDialog", async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, { properties: ["openFile"], ...options || {} });
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});
ipcMain.handle("fs:openMultipleFilesDialog", async (event, extensions) => {
  const result = await dialog.showOpenDialog(mainWindow, { properties: ["openFile", "multiSelections"], filters: extensions.length > 0 ? [{ name: "Files", extensions }] : void 0 });
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths;
  }
  return null;
});
ipcMain.handle("fs:copyFileToUserData", async (event, sourcePath, targetSubdir, targetFilename) => {
  const userDataPath = app.getPath("userData");
  const targetDir = path.join(userDataPath, targetSubdir);
  try {
    await fsPromises.mkdir(targetDir, { recursive: true });
    const finalFilename = targetFilename || path.basename(sourcePath);
    const destinationPath = path.join(targetDir, finalFilename);
    await fsPromises.copyFile(sourcePath, destinationPath);
    const relativePath = path.join(targetSubdir, finalFilename).replace(/\\/g, "/");
    return relativePath;
  } catch (error) {
    return { error: error.message };
  }
});
ipcMain.handle("fs:exportChatHistory", async (event, messages, format, defaultFileName) => {
  const { filePath } = await dialog.showSaveDialog(mainWindow, { title: "保存聊天记录", defaultPath: `${defaultFileName}_${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.${format}`, filters: [format === "md" ? { name: "Markdown", extensions: ["md"] } : { name: "HTML", extensions: ["html"] }] });
  if (filePath) {
    try {
      let content = "";
      if (format === "md") {
        content = messages.map((msg) => `**${msg.senderName}** (${new Date(msg.timestamp).toLocaleString()}):

${msg.text.replace(/<br\s*\/?>/gi, "\n").replace(/<[^>]+>/g, "")}

---
`).join("");
      } else {
        content = `<html>...<body><h1>聊天记录</h1>${messages.map((msg) => `<div class="message ${msg.sender}"><p><span class="sender">${msg.senderName}</span> <span class="timestamp">(${new Date(msg.timestamp).toLocaleString()})</span>:</p><div>${msg.text}</div></div>`).join("")}</body></html>`;
      }
      await fsPromises.writeFile(filePath, content, "utf-8");
      return { success: true, path: filePath };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  return { success: false, error: "用户取消保存。" };
});
ipcMain.handle("rag:runProjectIndexing", async (event, projectId, projectPath, options) => {
  if (!ragService) {
    console.error("IPC_MAIN_ERROR: rag:runProjectIndexing - ragService not loaded.");
    return { success: false, error: "RAG service not available." };
  }
  return await ragService.runProjectIndexing(projectId, projectPath, options);
});
ipcMain.handle("rag:retrieveRelevantChunks", async (event, queryText, projectId, apiKey, embeddingModelName, topK) => {
  if (!ragService) {
    console.error("IPC_MAIN_ERROR: rag:retrieveRelevantChunks - ragService not loaded.");
    return { success: false, error: "RAG service not available." };
  }
  return await ragService.retrieveRelevantChunks(queryText, projectId, apiKey, embeddingModelName, topK);
});
ipcMain.handle("rag:indexFileContent", async (event, projectId, filePath, fileContent, options) => {
  if (!ragService || typeof ragService.indexFileContent !== "function") {
    console.error("IPC_MAIN_ERROR: rag:indexFileContent - ragService or indexFileContent function not loaded.");
    return { success: false, error: "RAG File Indexing service not available." };
  }
  return await ragService.indexFileContent(projectId, filePath, fileContent, options);
});
ipcMain.handle("ai:invoke-sandbox-request", async (event, args) => {
  var _a, _b, _c;
  console.log("IPC_MAIN (ai:invoke-sandbox-request): Received request. Args (brief):", { promptLength: (_a = args == null ? void 0 : args.prompt) == null ? void 0 : _a.length, historyLength: (_b = args == null ? void 0 : args.history) == null ? void 0 : _b.length, persona: args == null ? void 0 : args.persona, projectId: args == null ? void 0 : args.projectId, contextType: (_c = args == null ? void 0 : args.otherSandboxContext) == null ? void 0 : _c.contextType });
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR (ai:invoke-sandbox-request): aiOrchestrationService not available.");
    return { text: "AI Orchestration service not available.", status: null };
  }
  const context = {
    ...args.otherSandboxContext || {},
    projectId: args.projectId,
    contextType: "workspace",
    history: args.history
  };
  console.log("IPC_MAIN (ai:invoke-sandbox-request): Constructed context for callAI:", JSON.stringify(context, (key, value) => key === "history" ? `[${value == null ? void 0 : value.length} messages]` : value, 2));
  try {
    const result = await callAI(args.prompt, args.persona || "LinLuo", context);
    console.log("IPC_MAIN (ai:invoke-sandbox-request): Result from callAI:", JSON.stringify(result, null, 2));
    return result;
  } catch (e) {
    console.error("IPC_MAIN_ERROR (ai:invoke-sandbox-request): Error during callAI:", e.message, e.stack);
    return { text: `Sandbox AI call failed: ${e.message}`, status: null };
  }
});
ipcMain.handle("ai:invoke-territory-request", async (event, args) => {
  console.log("IPC_MAIN: Handling ai:invoke-territory-request", args);
  if (!aiOrchestrationService) {
    return { text: "AI Orchestration service not available.", status: null };
  }
  const context = {
    ...args.otherTerritoryContext,
    contextType: "training_room",
    history: args.history
  };
  const result = await callAI(args.prompt, args.persona || "LinLuo", context);
  return result;
});
ipcMain.handle("ai:discussWithAI", async (event, contents, config, modelName, apiKey) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:discussWithAI - aiOrchestrationService not loaded.");
    return "AI Orchestration service not available.";
  }
  return await executeRawLLMQuery(contents, config, modelName, apiKey);
});
ipcMain.handle("ai:getAvailableModels", async () => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:getAvailableModels - aiOrchestrationService not loaded.");
    return [];
  }
  return await getAvailableModels();
});
ipcMain.handle("ai:callAI", async (event, prompt, persona, context) => {
  console.warn("IPC_MAIN: Generic 'ai:callAI' was called. Ensure this is intentional and not from refactored chat components.");
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:callAI - aiOrchestrationService not loaded.");
    return { text: "AI Orchestration service not available.", status: null };
  }
  return await callAI(prompt, persona, context);
});
ipcMain.handle("ai:routeUserIntent", async (event, userInputText, context) => {
  console.log(`IPC_MAIN: Handling ai:routeUserIntent. Input: "${userInputText.substring(0, 50)}..."`);
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:routeUserIntent - aiOrchestrationService not loaded.");
    return { type: "error", data: { message: "AI 路由服务不可用。" } };
  }
  try {
    return await routeUserIntent(userInputText, context);
  } catch (error) {
    console.error("IPC_MAIN_ERROR: Error in ai:routeUserIntent handler:", error);
    return { type: "error", data: { message: `处理意图时发生错误: ${error.message}` } };
  }
});
ipcMain.handle("ai:summarizeConversation", async (event, historyChunk) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:summarizeConversation - aiOrchestrationService not loaded.");
    return "AI Orchestration service not available.";
  }
  return await summarizeConversation(historyChunk);
});
ipcMain.handle("ai:decomposeRequirementToTasks", async (event, requirementText, projectId) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:decomposeRequirementToTasks - aiOrchestrationService not loaded.");
    return { success: false, error: "AI Orchestration service not available.", tasks: [] };
  }
  return await decomposeRequirementToTasks(requirementText, projectId);
});
ipcMain.handle("ai:analyzeAndDecomposeAideProject", async (event, projectId, projectPath) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:analyzeAndDecomposeAideProject - aiOrchestrationService not loaded.");
    return { success: false, error: "AI Orchestration service not available.", tasks: [] };
  }
  try {
    return await analyzeAndDecomposeAideProject(projectId, projectPath);
  } catch (e) {
    console.error(`IPC_MAIN_ERROR: Error in ai:analyzeAndDecomposeAideProject handler:`, e);
    return { success: false, error: e.message || "Unknown error during AIDE project analysis.", tasks: [] };
  }
});
ipcMain.handle("bridgeAi:processIntent", async (event, userInputText) => {
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: bridgeAi:processIntent - Main aiOrchestrationService not loaded.");
    return "AI编排服务不可用。";
  }
  try {
    const response = await callAI(userInputText, "OrchestratorPersona", { contextType: "file_orchestration" });
    if (response && response.json && response.json.command) {
      return response.text || (response.json.error ? `AI指令解析错误: ${response.json.error}` : "操作已尝试执行，但无明确文本反馈。");
    } else if (response && typeof response.text === "string") {
      return response.text;
    }
    return "处理完成，但AI未返回明确文本结果或有效JSON指令。";
  } catch (error) {
    console.error(`IPC_MAIN_ERROR: bridgeAi:processIntent failed:`, error);
    return `处理舰桥指令时发生错误: ${error.message}`;
  }
});
ipcMain.handle("ai:startRoundtableMeeting", async (event, initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory) => {
  if (!aiOrchestrationService || typeof startRoundtableMeeting !== "function") {
    console.error("IPC_MAIN_ERROR: ai:startRoundtableMeeting - aiOrchestrationService or startRoundtableMeeting not loaded.");
    return { success: false, error: "圆桌会议服务不可用。" };
  }
  try {
    const result = await startRoundtableMeeting(initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory, mainWindow == null ? void 0 : mainWindow.webContents);
    return result;
  } catch (error) {
    console.error("IPC_MAIN_ERROR: Error in ai:startRoundtableMeeting handler:", error);
    return { success: false, error: error.message || "启动圆桌会议时发生未知错误。" };
  }
});
ipcMain.handle("audio:playSound", async (event, soundName, loop) => {
  if (!audioService) {
    console.error("IPC_MAIN_ERROR: audio:playSound - audioService not loaded.");
    return;
  }
  return await playSound(soundName, loop);
});
ipcMain.handle("audio:stopSound", async (event, soundName) => {
  if (!audioService) {
    console.error("IPC_MAIN_ERROR: audio:stopSound - audioService not loaded.");
    return;
  }
  return await stopSound(soundName);
});
ipcMain.handle("audio:setVolume", async (event, volume) => {
  if (!audioService) {
    console.error("IPC_MAIN_ERROR: audio:setVolume - audioService not loaded.");
    return;
  }
  return await setVolume(volume);
});
ipcMain.handle("audio:getPlaybackState", async () => {
  if (!audioService) {
    console.error("IPC_MAIN_ERROR: audio:getPlaybackState - audioService not loaded.");
    return { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false };
  }
  return await getPlaybackState();
});
ipcMain.handle("electronUtils:getPlatform", async () => {
  return os.platform();
});
ipcMain.handle("utils:combinePaths", (event, basePath, relativePath) => {
  return path.join(basePath, relativePath);
});
ipcMain.handle("assets:getLoadedAssets", async () => {
  if (!universalAssetService) {
    console.error("IPC_MAIN_ERROR: assets:getLoadedAssets - universalAssetService not loaded.");
    return { props: [], costumes: [], poses: [], scene_cards: [], achievements: [], scripts: [], role_cards: [] };
  }
  try {
    const assetPackAssets = getAllLoadedAssets();
    const cmsProps = await cmsDbService.getCMSItems("props");
    const cmsCostumes = await cmsDbService.getCMSItems("costumes");
    const cmsPoses = await cmsDbService.getCMSItems("poses");
    const mergedAssets = {
      ...assetPackAssets,
      props: [...cmsProps || [], ...assetPackAssets.props || []],
      costumes: [...cmsCostumes || [], ...assetPackAssets.costumes || []],
      poses: [...cmsPoses || [], ...assetPackAssets.poses || []]
    };
    return mergedAssets;
  } catch (error) {
    console.error("IPC_MAIN_ERROR: assets:getLoadedAssets - Error merging assets:", error.message);
    return getAllLoadedAssets();
  }
});
ipcMain.handle("assets:refreshAssetPacks", async () => {
  if (!universalAssetService) {
    console.error("IPC_MAIN_ERROR: assets:refreshAssetPacks - universalAssetService not loaded.");
    return { success: false, error: "Asset service not available." };
  }
  try {
    await refreshAssetPacks();
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});
ipcMain.handle("assets:createAssetPack", async (event, fileName, assetPackData) => {
  try {
    const yaml = require("js-yaml");
    const fs2 = require("fs").promises;
    const path2 = require("path");
    const assetPacksPath = path2.join(app.getPath("userData"), "asset_packs");
    await fs2.mkdir(assetPacksPath, { recursive: true });
    const filePath = path2.join(assetPacksPath, fileName);
    const yamlContent = yaml.dump(assetPackData, {
      indent: 2,
      lineWidth: -1,
      noRefs: true,
      quotingType: '"',
      forceQuotes: false
    });
    await fs2.writeFile(filePath, yamlContent, "utf8");
    console.log(`Asset pack created: ${filePath}`);
    return { success: true, filePath };
  } catch (error) {
    console.error("Error creating asset pack:", error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle("assets:deleteAssetPack", async (event, fileName) => {
  try {
    const fs2 = require("fs").promises;
    const path2 = require("path");
    const assetPacksPath = path2.join(app.getPath("userData"), "asset_packs");
    const filePath = path2.join(assetPacksPath, fileName);
    await fs2.unlink(filePath);
    console.log(`Asset pack deleted: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error("Error deleting asset pack:", error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle("command:execute", async (event, { commandString, args, cwd }) => {
  if (!commandExecutionService || !mainWindow) {
    console.error("IPC_MAIN_ERROR: command:execute - commandExecutionService or mainWindow not loaded.");
    return { error: "Command execution service or main window not available." };
  }
  const internalPid = crypto$1.randomUUID();
  executeCommand(
    internalPid,
    commandString,
    args,
    cwd,
    (pid, systemPid, data) => {
      if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) mainWindow.webContents.send("command:event", { internalPid: pid, systemPid, type: "stdout", data });
    },
    (pid, systemPid, data) => {
      if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) mainWindow.webContents.send("command:event", { internalPid: pid, systemPid, type: "stderr", data });
    },
    (pid, systemPid, code, signal, errorMsg) => {
      if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
        if (errorMsg) mainWindow.webContents.send("command:event", { internalPid: pid, systemPid, type: "error_event", error: errorMsg, code: code ?? 1 });
        else mainWindow.webContents.send("command:event", { internalPid: pid, systemPid, type: "exit", code, signal });
      }
    }
  );
  return { internalPid };
});
ipcMain.handle("command:kill", async (event, internalPid) => {
  if (!commandExecutionService) {
    console.error("IPC_MAIN_ERROR: command:kill - commandExecutionService not loaded.");
    return { success: false, error: "Command execution service not available." };
  }
  return { success: killCommand(internalPid) };
});
ipcMain.handle("command:analyze-log", async (event, logContent) => {
  if (!aiOrchestrationService || typeof analyzeErrorLogFromService !== "function") {
    console.error("IPC_MAIN_ERROR: command:analyze-log - aiOrchestrationService or analyzeErrorLogFromService not available.");
    return "AI日志分析服务不可用。";
  }
  try {
    return await analyzeErrorLogFromService(logContent);
  } catch (e) {
    console.error("IPC_MAIN_ERROR: Error in command:analyze-log handler:", e);
    return `AI分析错误日志时发生意外: ${e.message}`;
  }
});
ipcMain.handle("org:getPosts", async () => {
  if (!organizationService) {
    console.error("IPC_MAIN_ERROR: org:getPosts - organizationService not loaded.");
    return [];
  }
  return getPosts$1();
});
ipcMain.handle("org:getCharacters", async () => {
  if (!organizationService) {
    console.error("IPC_MAIN_ERROR: org:getCharacters - organizationService not loaded.");
    return [];
  }
  return await getCharacters$1();
});
ipcMain.handle("org:getAssignments", async () => {
  if (!organizationService) {
    console.error("IPC_MAIN_ERROR: org:getAssignments - organizationService not loaded.");
    return [];
  }
  return getAssignments$1();
});
ipcMain.handle("org:setAssignment", async (event, postId, characterId) => {
  if (!organizationService) {
    console.error("IPC_MAIN_ERROR: org:setAssignment - organizationService not loaded.");
    return { success: false, error: "Organization service not available." };
  }
  return setAssignment$1(postId, characterId);
});
ipcMain.handle("org:getAssignmentByPostId", async (event, postId) => {
  if (!organizationService) {
    console.error("IPC_MAIN_ERROR: org:getAssignmentByPostId - organizationService not loaded.");
    return null;
  }
  return getAssignmentByPostId$1(postId);
});
console.log("ELECTRON MAIN: All IPC Handlers registered.");
//# sourceMappingURL=main.js.map
