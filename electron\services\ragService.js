// electron/services/ragService.js
console.log('RAG_SERVICE_JS: File execution started (ESM).'); // First line of the file

import * as aiKernelService from './aiKernelService'; 
import fsPromises from 'node:fs/promises';
import path from 'node:path';
import crypto from 'node:crypto';

const DEFAULT_CHUNK_SIZE = 1000; 
const DEFAULT_OVERLAP_SIZE = 200; 
const NON_INDEXABLE_EXTENSIONS = new Set(['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp', '.exe', '.dll', '.so', '.dylib', '.zip', '.tar', '.gz', '.rar', '.7z', '.mp3', '.mp4', '.avi', '.mov', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.iso', '.dmg', '.pkg']);
const NON_INDEXABLE_DIRS = new Set(['node_modules', '.git', '.vscode', '.idea', '__pycache__']);

let dbAggregatorServiceInstance; 
let userDataPathInstance; 

export function initializeRagService(dbService, userDataPath) {
    dbAggregatorServiceInstance = dbService; 
    userDataPathInstance = userDataPath;
    console.log("RAG Service initialized with aggregated DB Service.");
}

function chunkText(text, options = {}) {
    const chunkSize = options.chunkSize || DEFAULT_CHUNK_SIZE;
    const overlapSize = options.overlapSize || DEFAULT_OVERLAP_SIZE;
    const chunks = [];
    let i = 0;
    while (i < text.length) {
        const end = Math.min(i + chunkSize, text.length);
        chunks.push(text.substring(i, end));
        i += chunkSize - overlapSize;
        if (i + overlapSize >= text.length && end === text.length) break;
    }
    return chunks.filter(chunk => chunk.trim() !== "");
}

async function getEmbeddingForChunk(textChunk, apiKey, embeddingModelName, taskType, title) {
    const result = await aiKernelService.embedContentInternal(textChunk, taskType, title, apiKey); 
    if (typeof result === 'string') { 
        console.error("RAG Service (getEmbeddingForChunk) received error string from aiKernelService:", result);
        throw new Error(result); 
    }
    if (result === null) {
        console.warn("RAG Service (getEmbeddingForChunk): Received null vector from aiKernelService. Text chunk (first 100 chars):", textChunk ? textChunk.substring(0,100) + "..." : "EMPTY_CHUNK_TEXT");
    }
    return result; 
}

async function* streamFiles(dirPath) {
    try {
        const dirents = await fsPromises.readdir(dirPath, { withFileTypes: true });
        for (const dirent of dirents) {
            const fullPath = path.join(dirPath, dirent.name);
            if (dirent.isDirectory()) {
                if (!NON_INDEXABLE_DIRS.has(dirent.name) && !dirent.name.startsWith('.')) {
                    yield* streamFiles(fullPath);
                }
            } else {
                const ext = path.extname(dirent.name).toLowerCase();
                if (!NON_INDEXABLE_EXTENSIONS.has(ext)) {
                    yield fullPath;
                }
            }
        }
    } catch (error) {
        console.warn('RAG_SERVICE: Could not read directory ' + dirPath + ': ' + error.message + '. Skipping.');
    }
}

export async function indexFileContent(projectId, filePath, fileContent, options) {
    const { apiKey, embeddingModelName } = options;
    console.log(`RAG_SERVICE: Starting single file content indexing for project ${projectId}, file: ${filePath}`);
    if (!dbAggregatorServiceInstance) return { success: false, error: "Database aggregator service not initialized." };
    if (!apiKey || !apiKey.trim()) return { success: false, error: "API Key is missing for file content indexing." };
    if (!embeddingModelName) return { success: false, error: "Embedding model name not provided." };
    if (!fileContent || typeof fileContent !== 'string' || !fileContent.trim()) return { success: false, error: "File content is empty or invalid." };

    try {
        // Clear old index entries for this specific file first
        const clearStmt = dbAggregatorServiceInstance.db.prepare('DELETE FROM knowledge_index WHERE source_project_id = ? AND source_file_path = ?');
        const clearResult = clearStmt.run(projectId, filePath);
        console.log(`RAG_SERVICE: Cleared ${clearResult.changes} old index entries for file: ${filePath}`);

        const textChunks = chunkText(fileContent);
        if (textChunks.length === 0) {
            return { success: true, message: "No indexable chunks generated from file content.", chunksCreated: 0 };
        }
        
        let chunksCreated = 0;
        for (let i = 0; i < textChunks.length; i++) {
            const chunk = textChunks[i];
            const embeddingVector = await getEmbeddingForChunk(chunk, apiKey, embeddingModelName, 'RETRIEVAL_DOCUMENT', path.basename(filePath));

            if (embeddingVector && Array.isArray(embeddingVector)) { 
                const fileExtension = path.extname(filePath).toLowerCase().substring(1);
                const metadata = {
                    file_type: fileExtension || 'unknown',
                    chunk_index: i,
                    original_file_name: path.basename(filePath)
                };
                await dbAggregatorServiceInstance.addKnowledgeIndexEntry({
                    id: crypto.randomUUID(),
                    source_project_id: projectId,
                    source_file_path: filePath,
                    chunk_text: chunk,
                    chunk_vector: JSON.stringify(embeddingVector), 
                    metadata: JSON.stringify(metadata), 
                    indexed_at: new Date().toISOString(),
                });
                chunksCreated++;
            } else if (embeddingVector === null) {
                console.warn(`RAG_SERVICE (indexFileContent): Skipping chunk in ${filePath} because embedding was null.`);
            } else {
                console.warn(`RAG_SERVICE (indexFileContent): Skipping chunk in ${filePath} due to invalid embedding vector type:`, typeof embeddingVector);
            }
        }
        const successMessage = `File content indexing complete for ${filePath}. Created ${chunksCreated} index entries.`;
        console.log('RAG_SERVICE: ' + successMessage);
        return { success: true, message: successMessage, chunksCreated };
    } catch (error) {
        console.error(`RAG_SERVICE: Error during file content indexing for ${filePath}:`, error);
        return { success: false, error: error.message || "An unexpected error occurred during file content indexing." };
    }
}


export async function runProjectIndexing(projectId, projectPath, options) {
    const { apiKey, embeddingModelName } = options; 
    console.log('RAG_SERVICE: Starting indexing for project ' + projectId + ' at path ' + projectPath + ' using model ' + embeddingModelName);
    if (!dbAggregatorServiceInstance) { 
        return { success: false, error: "Database aggregator service not initialized in RAG service." };
    }
    if (!apiKey || apiKey.trim() === "") {
        return { success: false, error: "API Key is missing for project indexing." };
    }
    if (!embeddingModelName) { 
        return { success: false, error: "Embedding model name not provided for project indexing."};
    }
     try {
        await fsPromises.access(projectPath, fsPromises.constants.R_OK);
    } catch (err) {
        return { success: false, error: 'Project path "' + projectPath + '" does not exist or is not accessible.' };
    }

    try {
        await dbAggregatorServiceInstance.clearKnowledgeIndexForProject(projectId);
        console.log('RAG_SERVICE: Cleared old index for project ' + projectId + '.');

        let filesProcessed = 0;
        let chunksCreated = 0;

        for await (const filePath of streamFiles(projectPath)) {
            try {
                console.log('RAG_SERVICE: Processing file: ' + filePath);
                const content = await fsPromises.readFile(filePath, 'utf-8');
                if (!content.trim()) {
                    console.log('RAG_SERVICE: Skipping empty file: ' + filePath);
                    continue;
                }

                const textChunks = chunkText(content);
                if (textChunks.length === 0) {
                    console.log('RAG_SERVICE: No chunks generated for file: ' + filePath);
                    continue;
                }
                
                filesProcessed++;

                for (let i = 0; i < textChunks.length; i++) {
                    const chunk = textChunks[i];
                    const embeddingVector = await getEmbeddingForChunk(chunk, apiKey, embeddingModelName, 'RETRIEVAL_DOCUMENT', path.basename(filePath));

                    if (embeddingVector && Array.isArray(embeddingVector)) { 
                        const fileExtension = path.extname(filePath).toLowerCase().substring(1);
                        const metadata = {
                            file_type: fileExtension || 'unknown',
                            chunk_index: i,
                            original_file_name: path.basename(filePath)
                        };

                        await dbAggregatorServiceInstance.addKnowledgeIndexEntry({
                            id: crypto.randomUUID(),
                            source_project_id: projectId,
                            source_file_path: filePath,
                            chunk_text: chunk,
                            chunk_vector: JSON.stringify(embeddingVector), 
                            metadata: JSON.stringify(metadata), 
                            indexed_at: new Date().toISOString(),
                        });
                        chunksCreated++;
                    } else if (embeddingVector === null) {
                        console.warn('RAG_SERVICE: Skipping chunk in ' + filePath + ' because embedding was null (likely empty text).');
                    } else {
                        console.warn('RAG_SERVICE: Skipping chunk in ' + filePath + ' due to invalid embedding vector type:', typeof embeddingVector);
                    }
                }
            } catch (fileError) { 
                console.error('RAG_SERVICE: Error processing file ' + filePath + ':', fileError.message);
                if (fileError.message.toLowerCase().includes("api key") || fileError.message.toLowerCase().includes("permission") || fileError.message.toLowerCase().includes("content_invalid")) {
                    throw fileError; 
                }
            }
        }

        const successMessage = 'Project indexing complete for ' + projectId + '. Processed ' + filesProcessed + ' files, created ' + chunksCreated + ' index entries.';
        console.log('RAG_SERVICE: ' + successMessage);
        return { success: true, message: successMessage };

    } catch (error) {
        console.error('RAG_SERVICE: Error during project indexing for ' + projectId + ':', error);
        return { success: false, error: error.message || "An unexpected error occurred during indexing." };
    }
}

function cosineSimilarity(vecA, vecB) {
    if (!vecA || !vecB || vecA.length !== vecB.length || vecA.length === 0) {
        console.warn("RAG_SERVICE: Cosine similarity: Invalid input vectors.", {vecALength: vecA?.length, vecBLength: vecB?.length});
        return 0; 
    }
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }
    if (normA === 0 || normB === 0) {
        return 0; 
    }
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

export async function retrieveRelevantChunks(queryText, projectId, apiKey, embeddingModelName, topK = 5) {
    console.log('RAG_SERVICE: Retrieving chunks for query "' + queryText + '" in project ' + projectId + ' using model ' + embeddingModelName);
    if (!dbAggregatorServiceInstance) { 
        return { success: false, error: "Database aggregator service not initialized in RAG service." };
    }
    if (!apiKey || apiKey.trim() === "") {
        return { success: false, error: "API Key is missing for chunk retrieval." };
    }
    if (!embeddingModelName) {
        return { success: false, error: "Embedding model name not provided for chunk retrieval."};
    }

    try {
        const queryVectorResult = await getEmbeddingForChunk(queryText, apiKey, embeddingModelName, 'RETRIEVAL_QUERY', undefined);
        
        if (queryVectorResult === null) {
            console.warn("RAG_SERVICE: Query text resulted in null vector, likely empty query. Returning no results.");
            return { success: true, results: [], message: "Query text was empty or invalid." };
        }
        const queryVector = queryVectorResult; 

        const allChunksRaw = await dbAggregatorServiceInstance.getKnowledgeIndexEntriesForProject(projectId);
        if (!allChunksRaw || allChunksRaw.length === 0) {
            return { success: true, results: [], message: "No knowledge index entries found for this project." };
        }

        const scoredChunks = [];
        for (const rawChunk of allChunksRaw) {
            try {
                const chunkVector = rawChunk.chunk_vector ? JSON.parse(rawChunk.chunk_vector) : null;
                const metadata = rawChunk.metadata ? JSON.parse(rawChunk.metadata) : {};
                
                if (!chunkVector || !Array.isArray(chunkVector)) {
                    console.warn('RAG_SERVICE: Skipping chunk ' + rawChunk.id + ' due to missing or invalid vector.');
                    continue;
                }

                const similarityScore = cosineSimilarity(queryVector, chunkVector);
                
                scoredChunks.push({
                    id: rawChunk.id,
                    source_project_id: rawChunk.source_project_id,
                    source_file_path: rawChunk.source_file_path,
                    chunk_text: rawChunk.chunk_text,
                    indexed_at: rawChunk.indexed_at,
                    chunk_vector: chunkVector, 
                    metadata: metadata,
                    similarityScore,
                });
            } catch (parseError) {
                console.error('RAG_SERVICE: Error parsing data for chunk ' + rawChunk.id + ' (file: ' + rawChunk.source_file_path + '):', parseError);
            }
        }
        
        scoredChunks.sort((a, b) => b.similarityScore - a.similarityScore);
        const topResults = scoredChunks.slice(0, topK);

        console.log('RAG_SERVICE: Retrieved ' + topResults.length + ' relevant chunks.');
        return { success: true, results: topResults };

    } catch (error) {
        console.error('RAG_SERVICE: Error retrieving relevant chunks for project ' + projectId + ':', error);
        return { success: false, error: error.message || "An unexpected error occurred during retrieval." };
    }
}

console.log('RAG_SERVICE_JS: File execution finished (ESM). Exports configured.');