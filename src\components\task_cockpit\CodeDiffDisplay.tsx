// src/components/task_cockpit/CodeDiffDisplay.tsx
import React, { useState, useRef, useEffect } from 'react';
import { DiffEditor, loader as monacoLoader, Monaco } from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import type { StreamCodeDiff } from '@/pages/TaskCockpitPage';
import { Icon } from '@/components/common/Icon';

monacoLoader.init().then(monacoInstance => {
  console.log("Monaco Diff Editor (CodeDiffDisplay) initialized via loader.");
}).catch(error => console.error('Failed to initialize Monaco Diff Editor (CodeDiffDisplay):', error));

interface CodeDiffDisplayProps {
  diffItem: StreamCodeDiff;
  onApplyChanges: (filePath: string, newCode: string, userInstruction?: string) => Promise<void>;
}

export const CodeDiffDisplay: React.FC<CodeDiffDisplayProps> = ({ diffItem, onApplyChanges }) => {
  const [isApplying, setIsApplying] = useState(false);
  const [isRejected, setIsRejected] = useState(false);
  const [isApplied, setIsApplied] = useState(false);

  const monacoRef = useRef<Monaco | null>(null);
  const monacoThemesDefined = useRef(false);

  const defineMonacoThemes = (monaco: Monaco) => {
    if (monacoThemesDefined.current) return;
    monaco.editor.defineTheme('tiangong-dark-diff', {
      base: 'vs-dark', inherit: true, rules: [],
      colors: { 'editor.background': '#1F2937', 'diffEditor.insertedTextBackground': '#05966930', 'diffEditor.removedTextBackground': '#DC262630' } // gray-800, green-600/20, red-600/20
    });
    monaco.editor.defineTheme('tiangong-light-diff', {
      base: 'vs', inherit: true, rules: [],
      colors: { 'editor.background': '#F9FAFB', 'diffEditor.insertedTextBackground': '#D1FAE5', 'diffEditor.removedTextBackground': '#FEE2E2' } // gray-50, green-100, red-100
    });
    monacoThemesDefined.current = true;
  };

  const handleApply = async () => {
    if (!diffItem.filePath || isApplying || isRejected || isApplied) return;
    setIsApplying(true);
    try {
      await onApplyChanges(diffItem.filePath, diffItem.modifiedCode, diffItem.userInstruction);
      setIsApplied(true);
    } catch (e) {
      console.error("Error applying code changes:", e);
      // Optionally show an error message to the user
    } finally {
      setIsApplying(false);
    }
  };

  const handleReject = () => {
    setIsRejected(true);
    // Potentially notify backend or log this rejection
  };

  const copyModifiedCode = () => {
    navigator.clipboard.writeText(diffItem.modifiedCode).then(() => {
      // Optional: show "Copied!" message
    }).catch(err => console.error("Failed to copy modified code:", err));
  };
  
  const effectiveTheme = document.documentElement.getAttribute('data-theme') === 'theme-light' ? 'tiangong-light-diff' : 'tiangong-dark-diff';


  return (
    <div className="my-2 p-3 bg-tg-bg-tertiary rounded-lg shadow-md border border-tg-border-interactive max-w-full">
      <div className="flex justify-between items-center mb-2">
        <h4 className="text-sm font-semibold text-tg-accent-secondary flex items-center">
          <Icon name="CodeBracket" className="w-4 h-4 mr-1.5"/>
          AI 代码修改建议 {diffItem.filePath ? `for ${diffItem.filePath.split('/').pop()}` : ''}
        </h4>
        <button onClick={copyModifiedCode} className="p-1 text-xs text-tg-text-placeholder hover:text-tg-accent-primary" title="复制修改后的代码">
          <Icon name="ClipboardDocument" className="w-3.5 h-3.5"/>
        </button>
      </div>
      {diffItem.userInstruction && <p className="text-xs italic text-tg-text-secondary mb-2">基于指令: "{diffItem.userInstruction}"</p>}
      
      <div className="h-64 md:h-80 w-full border border-tg-border-primary rounded overflow-hidden">
        <DiffEditor
          height="100%"
          original={diffItem.originalCode}
          modified={diffItem.modifiedCode}
          language={diffItem.language || 'plaintext'}
          theme={effectiveTheme}
          onMount={(editor, monaco) => { monacoRef.current = monaco; defineMonacoThemes(monaco); }}
          options={{
            readOnly: true,
            renderSideBySide: true,
            minimap: { enabled: false },
            scrollbar: { verticalScrollbarSize: 8, horizontalScrollbarSize: 8 },
          }}
        />
      </div>

      <div className="mt-3 flex space-x-2">
        <button
          onClick={handleApply}
          disabled={isApplying || isRejected || isApplied || !diffItem.filePath}
          className="flex-1 px-3 py-1.5 text-xs bg-green-600 hover:bg-green-700 text-white rounded flex items-center justify-center disabled:opacity-60"
        >
          {isApplying ? <Icon name="Loader2" className="w-4 h-4 mr-1 animate-spin"/> : <Icon name="CheckCircle2" className="w-4 h-4 mr-1"/>}
          {isApplied ? "已应用" : (isApplying ? "应用中..." : "应用修改")}
        </button>
        <button
          onClick={handleReject}
          disabled={isApplying || isRejected || isApplied}
          className="flex-1 px-3 py-1.5 text-xs bg-red-600 hover:bg-red-700 text-white rounded flex items-center justify-center disabled:opacity-60"
        >
          <Icon name="X" className="w-4 h-4 mr-1"/>
          {isRejected ? "已拒绝" : "拒绝修改"}
        </button>
      </div>
    </div>
  );
};