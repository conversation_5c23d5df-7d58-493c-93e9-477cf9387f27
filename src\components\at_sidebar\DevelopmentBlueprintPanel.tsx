
// src/components/at_sidebar/DevelopmentBlueprintPanel.tsx
import React from 'react';
import type { LinLuoBodyDevelopment, BodyZoneKey, CMSItemBase, UnlockRequirement } from '@/types';
import { ALL_BODY_ZONES } from '@/types'; 
import { Icon } from '@/components/common/Icon';
import { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';

interface DevelopmentBlueprintPanelProps {
  bodyDevelopment: LinLuoBodyDevelopment[];
  allCMSItems: CMSItemBase[]; 
  isLoadingBodyDevelopment: boolean;
  isOpen: boolean;
  onToggle: () => void;
}

const DevelopmentMeter: React.FC<{zoneKey: BodyZoneKey, currentPoints: number, allCMSItems: CMSItemBase[], bodyDevelopmentData: LinLuoBodyDevelopment[]}> = ({ zoneKey, currentPoints, allCMSItems, bodyDevelopmentData }) => {
    let targetPoints = 250; 
    let nextUnlockName = "阶段圆满";

    const relevantUnlocks = allCMSItems
        .filter(item => item.unlock_requirements_json && item.unlock_requirements_json !== '[]')
        .flatMap(item => {
            try {
                const reqs: UnlockRequirement[] = JSON.parse(item.unlock_requirements_json!);
                return reqs.filter(req => req.zone === zoneKey).map(req => ({ ...req, itemName: item.name, itemId: item.id }));
            } catch { return []; }
        })
        .filter(req => {
             const itemForCheck = allCMSItems.find(i => i.id === req.itemId);
             if (!itemForCheck) return true; 
             
             if (!itemForCheck.unlock_requirements_json || itemForCheck.unlock_requirements_json === '[]') return false; 
             try {
                const fullItemReqs: UnlockRequirement[] = JSON.parse(itemForCheck.unlock_requirements_json);
                const isItemUnlocked = fullItemReqs.every(fullReq => {
                    const zoneDev = bodyDevelopmentData.find(dev => dev.zone_id === fullReq.zone);
                    return zoneDev && zoneDev.development_points >= fullReq.points_required;
                });
                return !isItemUnlocked; 
             } catch { return true; } 
        })
        .sort((a, b) => a.points_required - b.points_required);

    if (relevantUnlocks.length > 0) {
        targetPoints = relevantUnlocks[0].points_required;
        nextUnlockName = `解锁: ${relevantUnlocks[0].itemName}`;
    }
    
    const percentage = targetPoints > 0 ? Math.min(100, (currentPoints / targetPoints) * 100) : (currentPoints > 0 ? 100 : 0);
    const zoneDisplayName = BODY_ZONE_DISPLAY_NAMES[zoneKey] || zoneKey;

    return (
      <div className="text-xs mb-0.5" title={`${zoneDisplayName}开发度: ${currentPoints} / ${targetPoints} (目标: ${nextUnlockName})`}>
        <div className="flex justify-between items-center mb-0.5">
          <span className="text-gray-300 truncate w-1/3">{zoneDisplayName}</span>
          <span className="text-gray-400 text-right">{`${currentPoints} / ${targetPoints}`}</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-1.5 shadow-inner">
          <div 
            className={`h-1.5 rounded-full transition-all duration-300 ease-out bg-purple-500`}
            style={{ width: `${percentage}%` }}
            role="progressbar"
            aria-valuenow={percentage}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`${zoneDisplayName} 开发进度`}
          ></div>
        </div>
      </div>
    );
};

export const DevelopmentBlueprintPanel: React.FC<DevelopmentBlueprintPanelProps> = ({ 
    bodyDevelopment, allCMSItems, isLoadingBodyDevelopment, isOpen, onToggle 
}) => {
  if (isLoadingBodyDevelopment) {
    return (
        <div className="mb-2 p-2.5 bg-gray-900/70 rounded-lg border border-purple-700/50">
            <button 
                onClick={onToggle}
                className="w-full flex justify-between items-center text-xs font-semibold text-purple-400 mb-1 hover:text-purple-300"
            >
                <span className="flex items-center"><Icon name="GitFork" className="w-4 h-4 mr-1.5 text-purple-400"/>姐姐的养成蓝图</span>
                <Icon name={isOpen ? "ChevronUp" : "ChevronDown"} className="w-4 h-4"/>
            </button>
            {isOpen && <p className="text-xs text-gray-400 text-center py-1">蓝图数据加载中...</p>}
        </div>
    );
  }

  return (
    <div className="mb-2 p-2.5 bg-gray-900/70 rounded-lg border border-purple-700/50">
        <button 
            onClick={onToggle}
            className="w-full flex justify-between items-center text-xs font-semibold text-purple-400 mb-1 hover:text-purple-300"
            aria-expanded={isOpen}
            aria-controls="dev-blueprint-content"
        >
            <span className="flex items-center"><Icon name="GitFork" className="w-4 h-4 mr-1.5 text-purple-400"/>姐姐的养成蓝图</span>
            <Icon name={isOpen ? "ChevronUp" : "ChevronDown"} className="w-4 h-4"/>
        </button>
        {isOpen && (
            <div id="dev-blueprint-content" className="space-y-1 text-xs pt-1 border-t border-purple-800/50 max-h-48 overflow-y-auto custom-scrollbar pr-1">
                {ALL_BODY_ZONES.map(zoneKey => {
                    const zoneData = bodyDevelopment.find(z => z.zone_id === zoneKey);
                    const currentPoints = zoneData?.development_points || 0;
                    return <DevelopmentMeter key={zoneKey} zoneKey={zoneKey} currentPoints={currentPoints} allCMSItems={allCMSItems} bodyDevelopmentData={bodyDevelopment} />;
                })}
            </div>
        )}
    </div>
  );
};
