// src/components/task_cockpit/MainInteractionStream.tsx
import React, { useState, useRef, useEffect, useCallback } from 'react';
import type { AppChatMessage, AppSettings, WisdomPouchType } from '@/types'; 
import type { StreamItem, StreamLogEntry, StreamCodeDiff, StreamTaskUpdate } from '@/pages/TaskCockpitPage'; 
import { Icon } from '@/components/common/Icon'; 
import { ChatDiscussionMessageItem } from '@/components/chat/ChatDiscussionMessageItem'; 
import { LogOutputDisplay } from './LogOutputDisplay'; 
import { CodeDiffDisplay } from './CodeDiffDisplay';   
import { TaskUpdateDisplay } from './TaskUpdateDisplay'; 


interface MainInteractionStreamProps {
  streamItems: StreamItem[];
  onSendCommand: (commandText: string) => Promise<void>;
  isAiProcessing: boolean;
  settings: AppSettings;
  currentUserName?: string;
  onApplyDiffChanges: (filePath: string, newCode: string, userInstruction?: string) => Promise<void>;
  onOpenWisdomPouch?: () => void; 
}

export const MainInteractionStream: React.FC<MainInteractionStreamProps> = ({
  streamItems,
  onSendCommand,
  isAiProcessing,
  settings,
  currentUserName = "用户",
  onApplyDiffChanges,
  onOpenWisdomPouch, 
}) => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isWisdomPouchMenuOpen, setIsWisdomPouchMenuOpen] = useState(false);
  const wisdomPouchButtonRef = useRef<HTMLButtonElement>(null);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(scrollToBottom, [streamItems]);

  const handleSend = () => {
    if (inputValue.trim() && !isAiProcessing) {
      onSendCommand(inputValue.trim());
      setInputValue('');
    }
  };
  
  useEffect(() => {
    if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'; 
        const newHeight = Math.min(textareaRef.current.scrollHeight, parseFloat(getComputedStyle(textareaRef.current).maxHeight || '9rem'));
        textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [inputValue]);

  useEffect(() => {
    const handleClickOutsideWisdomPouch = (event: MouseEvent) => {
      if (isWisdomPouchMenuOpen && wisdomPouchButtonRef.current && !wisdomPouchButtonRef.current.contains(event.target as Node)) {
        const menuElement = document.querySelector('[data-wisdom-pouch-menu="true"]'); // Ensure menu has this attribute
        if (menuElement && !menuElement.contains(event.target as Node)) {
          setIsWisdomPouchMenuOpen(false);
        }
      }
    };
    document.addEventListener('mousedown', handleClickOutsideWisdomPouch);
    return () => {
        document.removeEventListener('mousedown', handleClickOutsideWisdomPouch);
    };
  }, [isWisdomPouchMenuOpen]);


  return (
    <div className="flex flex-col h-full bg-tg-bg-primary rounded-lg shadow-inner border border-tg-border-primary overflow-hidden">
      <div className="flex-grow p-3 overflow-y-auto custom-scrollbar space-y-3">
        {streamItems.map((item) => {
          if (item.type === 'log_chunk') {
            const logItem = item as StreamLogEntry; 
            return <LogOutputDisplay key={item.id} logEntry={logItem} />;
          } else if (item.type === 'code_diff') {
            const diffItem = item as StreamCodeDiff; 
            return <CodeDiffDisplay key={item.id} diffItem={diffItem} onApplyChanges={onApplyDiffChanges} />;
          } else if (item.type === 'task_update') {
            const updateItem = item as StreamTaskUpdate; 
            return <TaskUpdateDisplay key={item.id} updateItem={updateItem} />;
          } else { 
            const chatItem = item as AppChatMessage; 
            return (
              <ChatDiscussionMessageItem
                key={item.id}
                message={chatItem}
                isOwnMessage={chatItem.sender === 'user'}
                onUpdateMessage={() => {}} 
                onToggleStar={() => {}}
                onTogglePin={() => {}}
                onCopy={() => navigator.clipboard.writeText(chatItem.text)}
                isRichEditingActive={false}
                onRequestRichEdit={() => {}}
                settings={settings}
                currentUserName={currentUserName}
                isWorkspaceContext={true} 
              />
            );
          }
        })}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-3 border-t border-tg-border-primary bg-tg-bg-secondary flex-shrink-0">
        <div className="flex items-end space-x-2">
          {onOpenWisdomPouch && (
            <div className="relative flex-shrink-0">
              <button
                ref={wisdomPouchButtonRef}
                onClick={() => setIsWisdomPouchMenuOpen(prev => !prev)}
                className="p-3 rounded-lg transition-colors text-tg-text-secondary hover:bg-tg-bg-hover"
                title="智慧锦囊"
                disabled={isAiProcessing}
              >
                <Icon name="Package" className="w-5 h-5"/>
              </button>
              {isWisdomPouchMenuOpen && (
                <div 
                  data-wisdom-pouch-menu="true" // Added for click outside detection
                  className="absolute bottom-full left-0 mb-1 w-40 bg-tg-bg-tertiary border border-tg-border-interactive rounded-md shadow-lg py-1 z-30"
                >
                  <button
                    onClick={() => { onOpenWisdomPouch(); setIsWisdomPouchMenuOpen(false); }}
                    className="w-full text-left px-3 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary flex items-center"
                  >
                    <Icon name="cog" className="w-4 h-4 mr-1.5"/> 管理智慧锦囊
                  </button>
                  {/* Other quick actions can be added here */}
                </div>
              )}
            </div>
          )}
          <textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="输入您的指令或与AI对话..."
            className="flex-grow p-2.5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-lg resize-none outline-none focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/50 text-sm leading-relaxed min-h-[calc(1.5rem*3+1.25rem)] max-h-48" 
            style={{lineHeight: '1.5rem'}} 
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && !isAiProcessing) {
                e.preventDefault();
                handleSend();
              }
            }}
            disabled={isAiProcessing}
            aria-label="指令输入框"
          />
          <button
            onClick={handleSend}
            disabled={isAiProcessing || !inputValue.trim()}
            className="p-3 rounded-lg transition-colors text-white flex-shrink-0 bg-tg-accent-primary hover:bg-tg-accent-primary-hover disabled:bg-tg-bg-tertiary disabled:text-tg-text-placeholder disabled:cursor-not-allowed"
            title="发送指令"
          >
            {isAiProcessing ? <Icon name="arrow-path" className="w-5 h-5 animate-spin"/> : <Icon name="paper-airplane" className="w-5 h-5" />}
          </button>
        </div>
      </div>
    </div>
  );
};
