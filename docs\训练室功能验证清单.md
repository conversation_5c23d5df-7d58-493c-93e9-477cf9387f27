# 训练室功能验证清单

## 🎯 立即验证步骤

### 第一步：启动验证
- [ ] 重启应用，确认无启动错误
- [ ] 查看开发者控制台，确认无红色错误信息
- [ ] 进入绝对领域训练室页面

### 第二步：基础功能验证
- [ ] 左侧状态面板正常显示
- [ ] 右侧军械库面板正常显示
- [ ] 道具、服装、姿势标签可以切换

### 第三步：道具点击验证
- [ ] 点击任意道具按钮
- [ ] 查看控制台是否出现调试信息：
  ```
  🔘 道具按钮点击: { name: "道具名", ... }
  🎮 道具点击事件触发: { item: "道具名", ... }
  📝 生成的提示词: "..."
  💬 创建的消息: { ... }
  ```
- [ ] 观察聊天区域是否出现用户消息
- [ ] 等待 AI 响应

### 第四步：AI 响应验证
- [ ] AI 是否正常响应道具使用
- [ ] 响应内容是否符合道具描述
- [ ] 状态监控面板数值是否有变化
- [ ] 是否出现身体发展提升消息

## 🔍 问题排查指南

### 如果道具点击无反应：
1. **检查控制台日志**
   - 是否有 "🔘 道具按钮点击" 消息？
   - 是否有错误信息？

2. **检查道具解锁状态**
   - 道具按钮是否为灰色（未解锁）？
   - 鼠标悬停是否显示解锁要求？

3. **检查数据加载**
   - 运行诊断工具检查资产数据
   - 确认道具数据已正确加载

### 如果 AI 无响应：
1. **检查 API 配置**
   - 设置页面中 API Key 是否已配置
   - AI 服务状态是否显示"就绪"

2. **检查网络连接**
   - 控制台是否有网络错误
   - API 调用是否成功

### 如果状态不更新：
1. **检查 AI 响应格式**
   - AI 响应是否包含 `[[STATUS:{}]]` 标记
   - JSON 格式是否正确

2. **检查状态解析**
   - 控制台是否有状态解析错误
   - 状态监控面板是否展开

## 🎮 完整功能测试

### 道具使用测试
1. **基础道具**
   - [ ] 点击"如意墨毫笔"
   - [ ] 观察 AI 响应和状态变化
   - [ ] 检查是否有身体发展提升

2. **角色专属道具**
   - [ ] 切换到雾岛玲奈角色卡
   - [ ] 使用"高级产品展示箱"
   - [ ] 观察人格化响应

3. **林珞自然骚魂版道具**
   - [ ] 切换到林珞自然骚魂版
   - [ ] 使用"灵感激发器"
   - [ ] 观察特殊效果

### 服装和姿势测试
1. **服装切换**
   - [ ] 切换到服装标签
   - [ ] 点击任意服装
   - [ ] 观察 AI 反应

2. **姿势变化**
   - [ ] 切换到姿势标签
   - [ ] 选择不同姿势
   - [ ] 确认描述符合姿势

### 角色卡切换测试
1. **角色卡功能**
   - [ ] 在右侧面板选择不同角色卡
   - [ ] 观察系统消息："角色卡已切换为：【...】"
   - [ ] 确认 AI 人格发生变化

2. **状态重置**
   - [ ] 切换角色卡后状态是否更新
   - [ ] 专属道具是否正确显示

## 📊 预期结果

### 正常工作的表现：
- ✅ 道具点击有调试日志输出
- ✅ 用户消息立即出现在聊天区
- ✅ AI 在几秒内给出响应
- ✅ 状态数值实时更新
- ✅ 身体发展度正确提升
- ✅ 角色卡切换改变 AI 人格

### 异常情况：
- ❌ 点击道具无任何反应
- ❌ 控制台出现红色错误
- ❌ AI 响应超时或无响应
- ❌ 状态数值不变化
- ❌ 角色卡切换无效果

## 🛠️ 高级调试

### 使用诊断工具
1. **运行自动诊断**
   - 在训练室点击"🔍 运行功能诊断"按钮
   - 查看详细的系统状态报告
   - 根据报告解决问题

2. **手动检查**
   - 打开开发者工具 (F12)
   - 查看 Console 标签页
   - 观察 Network 标签页的 API 调用

### 数据验证
1. **检查资产数据**
   - 进入设置 → 军械库 → 资产管理中心
   - 确认道具、服装、姿势数量正确
   - 验证角色卡是否已导入

2. **检查身体发展数据**
   - 在状态监控面板查看当前数值
   - 在身体发展蓝图面板查看各部位开发度

## 📝 测试记录

### 测试结果记录：
```
测试时间：____
测试项目：____
预期结果：____
实际结果：____
问题描述：____
解决状态：____
```

### 常见问题及解决方案：
1. **道具按钮灰色** → 检查解锁要求，提升对应身体部位开发度
2. **AI 无响应** → 检查 API Key 配置和网络连接
3. **状态不更新** → 确认 AI 响应包含正确的状态标记
4. **角色卡无效** → 重新导入资产包或检查数据库

完成测试后，请报告具体的测试结果和遇到的任何问题！
