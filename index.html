<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>天工阁·创世框架</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    
    <script src="https://cdn.tailwindcss.com?plugins=typography,forms"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'tg-bg-primary': 'var(--color-bg-primary)',
              'tg-bg-secondary': 'var(--color-bg-secondary)',
              'tg-bg-tertiary': 'var(--color-bg-tertiary)',
              'tg-bg-hover': 'var(--color-bg-hover)',
              'tg-border-primary': 'var(--color-border-primary)',
              'tg-border-interactive': 'var(--color-border-interactive)',
              'tg-text-primary': 'var(--color-text-primary)',
              'tg-text-secondary': 'var(--color-text-secondary)',
              'tg-text-placeholder': 'var(--color-text-placeholder)',
              'tg-accent-primary': 'var(--color-accent-primary)',
              'tg-accent-primary-hover': 'var(--color-accent-primary-hover)',
              'tg-accent-secondary': 'var(--color-accent-secondary)',
              'tg-danger': 'var(--color-danger)',
              'tg-danger-hover': 'var(--color-danger-hover)',
              'tg-success': 'var(--color-success)',
              'tg-warning': 'var(--color-warning)',
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
             boxShadow: {
              'sm': 'var(--shadow-sm)',
              'md': 'var(--shadow-md)',
              'lg': 'var(--shadow-lg)',
              'xl': 'var(--shadow-xl)',
              '2xl': 'var(--shadow-2xl)',
              'inner': 'var(--shadow-inner)',
              'glow': 'var(--shadow-glow)',
              'glow-purple': 'var(--shadow-glow-purple)',
              'glow-green': 'var(--shadow-glow-green)',
            },
            borderRadius: {
              'sm': 'var(--border-radius-sm)',
              'md': 'var(--border-radius-md)',
              'lg': 'var(--border-radius-lg)',
              'xl': 'var(--border-radius-xl)',
              '2xl': 'var(--border-radius-2xl)',
              '3xl': 'var(--border-radius-3xl)',
            },
            backgroundImage: {
              'gradient-primary': 'var(--gradient-primary)',
              'gradient-secondary': 'var(--gradient-secondary)',
              'gradient-danger': 'var(--gradient-danger)',
              'gradient-success': 'var(--gradient-success)',
              'gradient-warning': 'var(--gradient-warning)',
              'gradient-dark': 'var(--gradient-dark)',
              'gradient-card': 'var(--gradient-card)',
            },
            backdropBlur: {
              'xs': '2px',
              'sm': '4px',
              'md': '8px',
              'lg': '12px',
              'xl': '16px',
              '2xl': '24px',
              '3xl': '40px',
            },
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out',
              'slide-up': 'slideUp 0.3s ease-out',
              'slide-down': 'slideDown 0.3s ease-out',
              'scale-in': 'scaleIn 0.2s ease-out',
              'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
              'glow-pulse': 'glowPulse 2s ease-in-out infinite',
              'float': 'float 3s ease-in-out infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideUp: {
                '0%': { transform: 'translateY(10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
              slideDown: {
                '0%': { transform: 'translateY(-10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
              scaleIn: {
                '0%': { transform: 'scale(0.95)', opacity: '0' },
                '100%': { transform: 'scale(1)', opacity: '1' },
              },
              bounceSubtle: {
                '0%, 100%': { transform: 'translateY(0)' },
                '50%': { transform: 'translateY(-2px)' },
              },
              glowPulse: {
                '0%, 100%': { boxShadow: 'var(--shadow-glow)' },
                '50%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)' },
              },
              float: {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-6px)' },
              },
            },
            typography: (theme) => { 
              return {
                DEFAULT: {
                  css: {
                    color: theme('colors.tg-text-primary'),
                    a: {
                      color: theme('colors.tg-accent-primary'),
                      '&:hover': {
                        color: theme('colors.tg-accent-primary-hover'),
                      },
                    },
                    strong: {
                      color: theme('colors.tg-text-primary'), 
                      fontWeight: '600',
                    },
                     em: {
                      color: theme('colors.tg-text-primary'), 
                    }
                  },
                },
                'tg-text-primary': { 
                   css: {
                      '--tw-prose-body': theme('colors.tg-text-primary'),
                      '--tw-prose-headings': theme('colors.tg-text-primary'),
                      '--tw-prose-lead': theme('colors.tg-text-secondary'),
                      '--tw-prose-links': theme('colors.tg-accent-primary'),
                      '--tw-prose-bold': theme('colors.tg-text-primary'),
                      '--tw-prose-counters': theme('colors.tg-text-secondary'),
                      '--tw-prose-bullets': theme('colors.tg-text-secondary'),
                      '--tw-prose-hr': theme('colors.tg-border-primary'),
                      '--tw-prose-quotes': theme('colors.tg-text-primary'),
                      '--tw-prose-quote-borders': theme('colors.tg-border-interactive'),
                      '--tw-prose-captions': theme('colors.tg-text-placeholder'),
                      '--tw-prose-code': theme('colors.tg-accent-secondary'),
                      '--tw-prose-pre-code': theme('colors.tg-accent-secondary'),
                      '--tw-prose-pre-bg': theme('colors.tg-bg-tertiary'),
                      '--tw-prose-th-borders': theme('colors.tg-border-primary'),
                      '--tw-prose-td-borders': theme('colors.tg-border-primary'),
                      '--tw-prose-invert-body': theme('colors.white'),
                      '--tw-prose-invert-headings': theme('colors.white'),
                      '--tw-prose-invert-lead': theme('colors.blue-300'),
                      '--tw-prose-invert-links': theme('colors.yellow-300'),
                      '--tw-prose-invert-bold': theme('colors.white'),
                      '--tw-prose-invert-counters': theme('colors.blue-300'),
                      '--tw-prose-invert-bullets': theme('colors.blue-300'),
                      '--tw-prose-invert-hr': theme('colors.tg-border-primary / 50%'),
                      '--tw-prose-invert-quotes': theme('colors.white'),
                      '--tw-prose-invert-quote-borders': theme('colors.yellow-400'),
                      '--tw-prose-invert-captions': theme('colors.tg-text-placeholder'),
                      '--tw-prose-invert-code': theme('colors.white'),
                      '--tw-prose-invert-pre-code': theme('colors.white'),
                      '--tw-prose-invert-pre-bg': 'rgb(0 0 0 / 50%)',
                      '--tw-prose-invert-th-borders': theme('colors.tg-border-primary'),
                      '--tw-prose-invert-td-borders': theme('colors.tg-border-primary'),
                   }
                },
                'yellow': { css: { '--tw-prose-body': theme('colors.yellow.200'), '--tw-prose-bold': theme('colors.yellow.100'), '--tw-prose-links': theme('colors.yellow.300') } },
                'sky': { css: { '--tw-prose-body': theme('colors.sky.200'), '--tw-prose-bold': theme('colors.sky.100'), '--tw-prose-links': theme('colors.sky.300') } },
                'slate': { css: { '--tw-prose-body': theme('colors.slate.200'), '--tw-prose-bold': theme('colors.slate.100'), '--tw-prose-links': theme('colors.slate.300') } },
                'lime': { css: { '--tw-prose-body': theme('colors.lime.200'), '--tw-prose-bold': theme('colors.lime.100'), '--tw-prose-links': theme('colors.lime.300') } }
              };
            }
          }
        }
      }
    </script>
    <style>
      /* Default (Dark) Theme */
      :root {
        --font-sans: 'Inter', sans-serif; 
        
        --color-bg-primary: hsl(224, 20%, 10%);
        --color-bg-secondary: hsl(224, 18%, 15%);
        --color-bg-tertiary: hsl(224, 16%, 22%);
        --color-bg-hover: hsl(224, 16%, 25%);

        --color-border-primary: hsl(224, 15%, 28%);
        --color-border-interactive: hsl(205, 70%, 60%);

        --color-text-primary: hsl(210, 25%, 92%);
        --color-text-secondary: hsl(210, 18%, 70%);
        --color-text-placeholder: hsl(210, 15%, 55%);
        
        --color-accent-primary: hsl(205, 78%, 55%);
        --color-accent-primary-hover: hsl(205, 85%, 65%);
        --color-accent-secondary: hsl(170, 60%, 45%);

        --color-danger: hsl(0, 70%, 65%);
        --color-danger-hover: hsl(0, 75%, 70%);
        --color-success: hsl(145, 60%, 50%);
        --color-warning: hsl(40, 85%, 60%);

        /* 渐变色 */
        --gradient-primary: linear-gradient(135deg, hsl(205, 78%, 55%) 0%, hsl(170, 60%, 45%) 100%);
        --gradient-secondary: linear-gradient(135deg, hsl(276, 70%, 60%) 0%, hsl(205, 85%, 60%) 100%);
        --gradient-danger: linear-gradient(135deg, hsl(0, 70%, 65%) 0%, hsl(15, 75%, 60%) 100%);
        --gradient-success: linear-gradient(135deg, hsl(145, 60%, 50%) 0%, hsl(170, 55%, 45%) 100%);
        --gradient-warning: linear-gradient(135deg, hsl(40, 85%, 60%) 0%, hsl(25, 80%, 55%) 100%);
        --gradient-dark: linear-gradient(135deg, hsl(224, 20%, 10%) 0%, hsl(224, 18%, 15%) 100%);
        --gradient-card: linear-gradient(135deg, hsl(224, 18%, 15%) 0%, hsl(224, 16%, 22%) 100%);

        /* 特殊效果颜色 */
        --color-glass-bg: hsla(224, 18%, 15%, 0.8);
        --color-glass-border: hsla(210, 25%, 92%, 0.1);
        --color-backdrop-blur: hsla(224, 20%, 10%, 0.9);

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.15);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.35);
        --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
        --shadow-glow-purple: 0 0 20px rgba(147, 51, 234, 0.3);
        --shadow-glow-green: 0 0 20px rgba(34, 197, 94, 0.3);

        --border-radius-sm: 0.25rem;
        --border-radius-md: 0.375rem;
        --border-radius-lg: 0.5rem;
        --border-radius-xl: 0.75rem;
        --border-radius-2xl: 1rem;
        --border-radius-3xl: 1.5rem;

        --color-linluo-border: hsl(276, 70%, 60%); 
        --color-linluo-name: hsl(276, 80%, 75%);   
        --color-xiaolan-border: hsl(205, 85%, 60%); 
        --color-xiaolan-name: hsl(205, 90%, 75%); 
        --color-yujing-border: hsl(145, 55%, 50%); 
        --color-yujing-name: hsl(145, 65%, 70%);

        --color-shadow-rgb: 0, 0, 0; /* For dark theme box shadows */
      }

      /* Light Theme */
      html[data-theme="theme-light"] :root {
        --color-bg-primary: hsl(0, 0%, 100%); /* White */
        --color-bg-secondary: hsl(210, 20%, 98%); /* Very light gray */
        --color-bg-tertiary: hsl(210, 15%, 94%); /* Light gray */
        --color-bg-hover: hsl(210, 15%, 90%); /* Slightly darker gray for hover */

        --color-border-primary: hsl(210, 15%, 88%); /* Light border */
        --color-border-interactive: hsl(205, 70%, 55%); /* Accent for interactive borders */

        --color-text-primary: hsl(224, 20%, 10%); /* Dark text */
        --color-text-secondary: hsl(224, 15%, 30%); /* Medium dark text */
        --color-text-placeholder: hsl(210, 12%, 45%); /* Placeholder text */
        
        /* Accents can remain similar or be adjusted for light theme contrast */
        --color-accent-primary: hsl(205, 70%, 50%);
        --color-accent-primary-hover: hsl(205, 75%, 60%);
        --color-accent-secondary: hsl(170, 55%, 40%);

        --color-danger: hsl(0, 65%, 60%);
        --color-danger-hover: hsl(0, 70%, 65%);
        --color-success: hsl(145, 55%, 45%);
        --color-warning: hsl(40, 80%, 55%);
        
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.06), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.03);

        --color-linluo-border: hsl(276, 60%, 55%); 
        --color-linluo-name: hsl(276, 70%, 65%);   
        --color-xiaolan-border: hsl(205, 75%, 55%); 
        --color-xiaolan-name: hsl(205, 80%, 65%); 
        --color-yujing-border: hsl(145, 50%, 45%); 
        --color-yujing-name: hsl(145, 60%, 55%);

        --color-shadow-rgb: 0, 0, 0; /* Shadow color base, keep it black but alpha is lower */
      }


      body {
        margin: 0;
        font-family: var(--font-sans);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: var(--color-bg-primary);
        color: var(--color-text-primary);
        line-height: 1.6;
      }
      #root {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }

      input[type="text"],
      input[type="password"],
      input[type="email"],
      input[type="search"],
      textarea, 
      select {
        background-color: var(--color-bg-tertiary);
        color: var(--color-text-primary);
        border: 1px solid var(--color-border-primary);
        border-radius: var(--border-radius-md);
        padding: 0.5rem 0.75rem; 
        font-size: 0.875rem; 
      }
      input[type="text"]:focus,
      input[type="password"]:focus,
      input[type="email"]:focus,
      input[type="search"]:focus,
      textarea:focus,
      select:focus {
        outline: none;
        border-color: var(--color-accent-primary);
        box-shadow: 0 0 0 2px hsla(from var(--color-accent-primary) h s l / 0.3); 
      }
      
      select {
        /* Default appearance reset is handled by Tailwind Forms plugin if active */
        /* If forms plugin is NOT active, you might need to keep or adjust this for basic styling */
        /* appearance: none; */ 
        /* background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E"); */
        /* background-position: right 0.5rem center; */
        /* background-repeat: no-repeat; */
        /* background-size: 1.25em 1.25em; */
        /* padding-right: 2.5rem;  */
      }
      
      /* Styling for checkbox with forms plugin */
      /* input[type="checkbox"] { */
        /* border-radius: var(--border-radius-sm); */
        /* color: var(--color-accent-primary); */
      /* } */
      /* input[type="checkbox"]:focus { */
        /* ring: 2px; */
        /* ring-offset-color: var(--color-bg-secondary); */
        /* ring-color: var(--color-accent-primary); */
      /* } */


      .ProseMirror {
        padding: 0.5rem 0.75rem; 
        outline: none;
        color: var(--color-text-primary);
        line-height: 1.7;
        font-size: 0.9375rem; 
        flex-grow: 1; 
        overflow-y: auto; 
      }

      .ProseMirror p.is-editor-empty:first-child::before {
        content: attr(data-placeholder);
        float: left;
        color: var(--color-text-placeholder);
        pointer-events: none;
        height: 0;
      }
      .ProseMirror:focus {
        outline: none; 
      }

      .ProseMirror mark { 
        background-color: #FEF9C3; 
        color: #713F12; 
        padding: 0.05em 0.15em;
        border-radius: 0.1em;
      }

      ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }
      ::-webkit-scrollbar-track {
        background: var(--color-bg-tertiary);
        border-radius: var(--border-radius-sm);
      }
      ::-webkit-scrollbar-thumb {
        background: var(--color-border-primary);
        border-radius: var(--border-radius-sm);
      }
      ::-webkit-scrollbar-thumb:hover {
        background: var(--color-border-interactive);
      }

      /* 全局美化样式 */
      .glass-effect {
        background: var(--color-glass-bg);
        border: 1px solid var(--color-glass-border);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
      }

      .card-hover {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
      }

      .glow-on-hover {
        transition: all 0.3s ease;
      }

      .glow-on-hover:hover {
        box-shadow: var(--shadow-glow);
      }

      .button-gradient {
        background: var(--gradient-primary);
        transition: all 0.3s ease;
      }

      .button-gradient:hover {
        background: var(--gradient-secondary);
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
      }

      .text-gradient {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .border-gradient {
        border: 2px solid transparent;
        background: linear-gradient(var(--color-bg-secondary), var(--color-bg-secondary)) padding-box,
                    var(--gradient-primary) border-box;
      }

      /* 改进的输入框样式 */
      .input-enhanced {
        background: var(--color-bg-tertiary);
        border: 1px solid var(--color-border-primary);
        border-radius: var(--border-radius-lg);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
      }

      .input-enhanced:focus {
        border-color: var(--color-accent-primary);
        box-shadow: var(--shadow-glow);
        transform: translateY(-1px);
      }

      /* 改进的按钮样式 */
      .btn-primary {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: var(--border-radius-lg);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-md);
      }

      .btn-primary:hover {
        background: var(--gradient-secondary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
      }

      .btn-secondary {
        background: var(--color-bg-tertiary);
        color: var(--color-text-primary);
        border: 1px solid var(--color-border-primary);
        border-radius: var(--border-radius-lg);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .btn-secondary:hover {
        background: var(--color-bg-hover);
        border-color: var(--color-accent-primary);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      /* 改进的卡片样式 */
      .card-enhanced {
        background: var(--gradient-card);
        border: 1px solid var(--color-border-primary);
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-lg);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .card-enhanced:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--color-accent-primary);
      }

      /* 动画类 */
      .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }

      .animate-slide-up {
        animation: slideUp 0.3s ease-out;
      }

      .animate-scale-in {
        animation: scaleIn 0.2s ease-out;
      }

      /* 响应式间距 */
      .spacing-responsive {
        padding: 1rem;
      }

      @media (min-width: 768px) {
        .spacing-responsive {
          padding: 1.5rem;
        }
      }

      @media (min-width: 1024px) {
        .spacing-responsive {
          padding: 2rem;
        }
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "vite": "https://esm.sh/vite@^6.3.5",
    "@vitejs/plugin-react": "https://esm.sh/@vitejs/plugin-react@^4.5.2",
    "vite-plugin-electron/": "https://esm.sh/vite-plugin-electron@^0.29.0/",
    "vite-plugin-static-copy": "https://esm.sh/vite-plugin-static-copy@^3.0.2",
    "electron": "https://esm.sh/electron@^36.5.0",
    "better-sqlite3": "https://esm.sh/better-sqlite3@^11.10.0",
    "react": "https://esm.sh/react@^19.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "@google/genai": "https://esm.sh/@google/genai@^1.5.1",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@tiptap/core": "https://esm.sh/@tiptap/core@^2.14.0",
    "react-draggable": "https://esm.sh/react-draggable@^4.4.6",
    "@monaco-editor/react": "https://esm.sh/@monaco-editor/react@^4.7.0",
    "monaco-editor": "https://esm.sh/monaco-editor@^0.52.2",
    "react-syntax-highlighter": "https://esm.sh/react-syntax-highlighter@^15.6.1",
    "react-syntax-highlighter/": "https://esm.sh/react-syntax-highlighter@^15.6.1/",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@tiptap/react": "https://esm.sh/@tiptap/react@^2.14.0",
    "@tiptap/starter-kit": "https://esm.sh/@tiptap/starter-kit@^2.14.0",
    "@tiptap/extension-highlight": "https://esm.sh/@tiptap/extension-highlight@^2.14.0",
    "@tiptap/extension-text-style": "https://esm.sh/@tiptap/extension-text-style@^2.14.0",
    "@tiptap/extension-color": "https://esm.sh/@tiptap/extension-color@^2.14.0",
    "@tiptap/extension-placeholder": "https://esm.sh/@tiptap/extension-placeholder@^2.14.0",
    "react-beautiful-dnd": "https://esm.sh/react-beautiful-dnd@^13.1.1",
    "js-yaml": "https://esm.sh/js-yaml@^4.1.0",
    "lucide-react": "https://esm.sh/lucide-react@^0.517.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>