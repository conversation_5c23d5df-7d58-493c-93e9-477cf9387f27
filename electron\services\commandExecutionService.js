// electron/services/commandExecutionService.js
console.log('COMMAND_EXECUTION_SERVICE_JS: File execution started.');

import { spawn } from 'node:child_process';
import { EOL } from 'node:os';

const activeProcesses = new Map(); // Stores internalPid -> { childProcess, commandString, args }

/**
 * Executes a shell command.
 * @param {string} internalPid - A unique ID generated by the main process for this command instance.
 * @param {string} commandString - The command to execute (e.g., 'npm', 'node').
 * @param {string[]} argsArray - An array of arguments for the command (e.g., ['run', 'dev']).
 * @param {string} cwd - The current working directory for the command.
 * @param {(internalPid: string, systemPid: number | undefined, data: string) => void} onStdOut - Callback for stdout data.
 * @param {(internalPid: string, systemPid: number | undefined, data: string) => void} onStdErr - Callback for stderr data.
 * @param {(internalPid: string, systemPid: number | undefined, code: number | null, signal: string | null, error?: string) => void} onExit - Callback for process exit or error.
 */
export function executeCommand(internalPid, commandString, argsArray, cwd, onStdOut, onStdErr, onExit) {
  console.log(`COMMAND_EXECUTION_SERVICE: Executing (PID_INTERNAL: ${internalPid}): "${commandString} ${argsArray.join(' ')}" in "${cwd}"`);
  
  try {
    const child = spawn(commandString, argsArray, { 
      cwd, 
      shell: process.platform === 'win32', // Use shell on Windows for .cmd, .bat, etc.
      stdio: ['ignore', 'pipe', 'pipe'] // ignore stdin, pipe stdout/stderr
    });

    const systemPid = child.pid;
    activeProcesses.set(internalPid, { child, commandString, argsArray, systemPid });
    console.log(`COMMAND_EXECUTION_SERVICE: Spawned (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid})`);

    child.stdout.setEncoding('utf8');
    child.stdout.on('data', (data) => {
      // Normalize line endings
      const normalizedData = data.toString().replace(/\r\n|\r/g, EOL);
      onStdOut(internalPid, systemPid, normalizedData);
    });

    child.stderr.setEncoding('utf8');
    child.stderr.on('data', (data) => {
      const normalizedData = data.toString().replace(/\r\n|\r/g, EOL);
      onStdErr(internalPid, systemPid, normalizedData);
    });

    child.on('exit', (code, signal) => {
      console.log(`COMMAND_EXECUTION_SERVICE: Process exited (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid}), code: ${code}, signal: ${signal}`);
      onExit(internalPid, systemPid, code, signal);
      activeProcesses.delete(internalPid);
    });

    child.on('error', (err) => {
      console.error(`COMMAND_EXECUTION_SERVICE: Failed to start process (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${systemPid}):`, err);
      // Treat spawn error as an exit event with an error message
      onExit(internalPid, systemPid, 1, null, err.message); 
      activeProcesses.delete(internalPid);
    });

  } catch (error) {
    console.error(`COMMAND_EXECUTION_SERVICE: Error spawning process (PID_INTERNAL: ${internalPid}):`, error);
    // Catch synchronous errors from spawn itself (e.g., command not found if shell:false and not in PATH)
    onExit(internalPid, undefined, 1, null, error.message);
    if (activeProcesses.has(internalPid)) {
        activeProcesses.delete(internalPid);
    }
  }
}

/**
 * Kills an active command by its internal PID.
 * @param {string} internalPid - The internal PID of the command to kill.
 * @returns {boolean} True if a kill signal was sent, false otherwise.
 */
export function killCommand(internalPid) {
  const processInfo = activeProcesses.get(internalPid);
  if (processInfo && processInfo.child && !processInfo.child.killed) {
    console.log(`COMMAND_EXECUTION_SERVICE: Attempting to kill process (PID_INTERNAL: ${internalPid}, PID_SYSTEM: ${processInfo.systemPid})`);
    // For Windows, 'taskkill' might be more robust for child processes spawned with shell:true.
    // For other OS, child.kill() should work.
    // Using tree-kill package would be a more robust cross-platform solution for killing process trees.
    // For now, standard kill:
    const killed = processInfo.child.kill(); // Sends SIGTERM by default
    if (killed) {
        console.log(`COMMAND_EXECUTION_SERVICE: Kill signal sent to (PID_INTERNAL: ${internalPid}). Process should exit soon.`);
    } else {
        console.warn(`COMMAND_EXECUTION_SERVICE: Failed to send kill signal to (PID_INTERNAL: ${internalPid}). It might have already exited.`);
    }
    // The 'exit' event on the child process will handle removal from activeProcesses.
    return killed;
  } else {
    console.warn(`COMMAND_EXECUTION_SERVICE: Process (PID_INTERNAL: ${internalPid}) not found or already killed.`);
    return false;
  }
}

console.log('COMMAND_EXECUTION_SERVICE_JS: File execution finished. Exports configured.');
