// electron/services/settingsDbService.js
console.log('SETTINGS_DB_SERVICE_JS: File execution started.');

import { db, _DEFAULT_SETTINGS } from './databaseCore';

export function getSettings() {
  if (!db) { 
    console.error("SETTINGS_DB_ERROR: getSettings - db not available."); 
    const defaultSettings = _DEFAULT_SETTINGS || { 
        apiKey: "", 
        chatModel: "gemini-2.5-flash-preview-04-17", 
        embeddingModel: "text-embedding-004", 
        backupPath: "", 
        defaultCover: "", 
        absolute_territory_password: null,
        user_avatar_path: null,
        linluo_avatar_path: null,
        xiaolan_avatar_path: null,
        training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }
    };
    return { ...defaultSettings }; 
  }
  try {
    const settingsRow = db.prepare('SELECT apiKey, chatModel, embeddingModel, backupPath, defaultCover, absolute_territory_password, user_avatar_path, linluo_avatar_path, xiaolan_avatar_path, training_room_audio_state FROM app_settings WHERE id = 1').get();
    
    let audioState;
    const defaultAudioStateFromCore = _DEFAULT_SETTINGS?.training_room_audio_state || { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false };

    if (settingsRow?.training_room_audio_state && typeof settingsRow.training_room_audio_state === 'string') {
        try {
            audioState = JSON.parse(settingsRow.training_room_audio_state);
            // Ensure all keys are present, otherwise merge with default
            audioState = { ...defaultAudioStateFromCore, ...audioState };
        } catch (e) {
            console.warn("SETTINGS_DB_WARN: Failed to parse training_room_audio_state from DB, using default.", e.message);
            audioState = defaultAudioStateFromCore;
        }
    } else {
        audioState = defaultAudioStateFromCore;
    }

    return {
        apiKey: settingsRow?.apiKey ?? _DEFAULT_SETTINGS.apiKey,
        chatModel: settingsRow?.chatModel ?? _DEFAULT_SETTINGS.chatModel,
        embeddingModel: settingsRow?.embeddingModel ?? _DEFAULT_SETTINGS.embeddingModel,
        backupPath: settingsRow?.backupPath ?? _DEFAULT_SETTINGS.backupPath,
        defaultCover: settingsRow?.defaultCover ?? _DEFAULT_SETTINGS.defaultCover,
        absolute_territory_password: settingsRow?.absolute_territory_password, 
        user_avatar_path: settingsRow?.user_avatar_path ?? _DEFAULT_SETTINGS.user_avatar_path,
        linluo_avatar_path: settingsRow?.linluo_avatar_path ?? _DEFAULT_SETTINGS.linluo_avatar_path,
        xiaolan_avatar_path: settingsRow?.xiaolan_avatar_path ?? _DEFAULT_SETTINGS.xiaolan_avatar_path,
        training_room_audio_state: audioState
    };
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error getting settings:', error);
    const defaultSettingsFallback = _DEFAULT_SETTINGS || { 
        apiKey: "", 
        chatModel: "gemini-2.5-flash-preview-04-17", 
        embeddingModel: "text-embedding-004", 
        backupPath: "", 
        defaultCover: "", 
        absolute_territory_password: null,
        user_avatar_path: null,
        linluo_avatar_path: null,
        xiaolan_avatar_path: null,
        training_room_audio_state: { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }
    };
    return { ...defaultSettingsFallback }; 
  }
}

export function saveSettings(settings) {
 if (!db) { console.error("SETTINGS_DB_ERROR: saveSettings - db not available."); return { success: false, error: "Database not available." }; }
 const stmt = db.prepare(`
    INSERT INTO app_settings (id, apiKey, chatModel, embeddingModel, backupPath, defaultCover, absolute_territory_password, user_avatar_path, linluo_avatar_path, xiaolan_avatar_path, training_room_audio_state)
    VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON CONFLICT(id) DO UPDATE SET
      apiKey = excluded.apiKey,
      chatModel = excluded.chatModel,
      embeddingModel = excluded.embeddingModel,
      backupPath = excluded.backupPath,
      defaultCover = excluded.defaultCover,
      absolute_territory_password = excluded.absolute_territory_password,
      user_avatar_path = excluded.user_avatar_path,
      linluo_avatar_path = excluded.linluo_avatar_path,
      xiaolan_avatar_path = excluded.xiaolan_avatar_path,
      training_room_audio_state = excluded.training_room_audio_state;
  `);
  try {
    const defaultAudioStateFromCore = _DEFAULT_SETTINGS?.training_room_audio_state || { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false };
    const audioStateToSave = settings.training_room_audio_state 
        ? JSON.stringify({ ...defaultAudioStateFromCore, ...settings.training_room_audio_state }) 
        : JSON.stringify(defaultAudioStateFromCore);

    stmt.run(
        settings.apiKey, 
        settings.chatModel, 
        settings.embeddingModel, 
        settings.backupPath, 
        settings.defaultCover, 
        settings.absolute_territory_password,
        settings.user_avatar_path,
        settings.linluo_avatar_path,
        settings.xiaolan_avatar_path,
        audioStateToSave
    );
    console.log(`SETTINGS_DB: Saved settings.`);
    return { success: true };
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error saving settings:', error);
    return { success: false, error: error.message };
  }
}

// Agent Core Settings CRUD
export function getAgentCoreSetting(settingId) {
  if (!db) { console.error(`SETTINGS_DB_ERROR: getAgentCoreSetting(${settingId}) - db not available.`); return null; }
  try {
    const row = db.prepare('SELECT setting_id, content, last_updated_at FROM agent_core_settings WHERE setting_id = ?').get(settingId);
    console.log(`SETTINGS_DB: Retrieved agent core setting for ID: ${settingId}. Has content: ${!!row?.content}`);
    return row || null; 
  } catch (error) {
    console.error(`SETTINGS_DB_ERROR: Error getting agent core setting for ID ${settingId}:`, error);
    return null;
  }
}

export function getAllAgentCoreSettings() {
  if (!db) { console.error("SETTINGS_DB_ERROR: getAllAgentCoreSettings - db not available."); return []; }
  try {
    const rows = db.prepare('SELECT setting_id, content, last_updated_at FROM agent_core_settings').all();
    console.log(`SETTINGS_DB: Retrieved ${rows.length} agent core settings.`);
    return rows;
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error getting all agent core settings:', error);
    return [];
  }
}

export function saveAgentCoreSetting(settingId, content) {
  if (!db) { console.error(`SETTINGS_DB_ERROR: saveAgentCoreSetting(${settingId}) - db not available.`); return { success: false, error: "Database not available." }; }
  const stmt = db.prepare(`
    INSERT INTO agent_core_settings (setting_id, content, last_updated_at)
    VALUES (?, ?, ?)
    ON CONFLICT(setting_id) DO UPDATE SET
      content = excluded.content,
      last_updated_at = excluded.last_updated_at;
  `);
  try {
    stmt.run(settingId, content, new Date().toISOString());
    console.log(`SETTINGS_DB: Saved agent core setting for ID: ${settingId}.`);
    return { success: true };
  } catch (error) {
    console.error(`SETTINGS_DB_ERROR: Error saving agent core setting for ID ${settingId}:`, error);
    return { success: false, error: error.message };
  }
}

// Absolute Territory Password Management
export function getAbsoluteTerritoryPassword() {
  if (!db) { console.error("SETTINGS_DB_ERROR: getAbsoluteTerritoryPassword - db not available."); return null; }
  try {
    const row = db.prepare('SELECT absolute_territory_password FROM app_settings WHERE id = 1').get();
    return row?.absolute_territory_password; 
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error getting Absolute Territory password:', error);
    return null;
  }
}

export function setAbsoluteTerritoryPassword(password) {
  if (!db) { console.error("SETTINGS_DB_ERROR: setAbsoluteTerritoryPassword - db not available."); return { success: false, error: "Database not available." }; }
  try {
    const stmt = db.prepare('UPDATE app_settings SET absolute_territory_password = ? WHERE id = 1');
    const result = stmt.run(password); // password can be null to clear it
    if (result.changes > 0) {
        console.log(`SETTINGS_DB: Absolute Territory password ${password === null ? 'cleared' : 'updated'}.`);
        return { success: true };
    } else {
        console.warn('SETTINGS_DB_WARN: setAbsoluteTerritoryPassword - No rows updated. Settings row might be missing.');
        // Fallback to ensure settings row exists and then update
        const currentSettings = getSettings(); 
        const newSettings = { ...currentSettings, absolute_territory_password: password };
        return saveSettings(newSettings); 
    }
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error setting Absolute Territory password:', error);
    return { success: false, error: error.message };
  }
}

export function verifyAbsoluteTerritoryPassword(passwordToVerify) {
  if (!db) { 
    console.error("SETTINGS_DB_ERROR: verifyAbsoluteTerritoryPassword - db not available."); 
    return { isValid: false, isFirstTime: true }; // Assume first time if DB error occurs
  }
  try {
    const storedPassword = getAbsoluteTerritoryPassword();
    if (storedPassword === null || storedPassword === undefined || storedPassword === '') {
      // No password set, this is effectively the first time, or password was cleared.
      // If a password is provided now, it's being set.
      return { isValid: true, isFirstTime: true }; 
    }
    return { isValid: storedPassword === passwordToVerify, isFirstTime: false };
  } catch (error) {
    console.error('SETTINGS_DB_ERROR: Error verifying Absolute Territory password:', error);
    return { isValid: false, isFirstTime: true }; // Default to first time on error for safety
  }
}


console.log('SETTINGS_DB_SERVICE_JS: File execution finished. Exports configured.');