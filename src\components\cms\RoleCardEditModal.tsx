// src/components/cms/RoleCardEditModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import type { RolePlayingCard } from '@/types';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

interface RoleCardEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  existingCard: RolePlayingCard | null;
  onSaveSuccess: () => void;
}

export const RoleCardEditModal: React.FC<RoleCardEditModalProps> = ({ isOpen, onClose, existingCard, onSaveSuccess }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [iconBase64, setIconBase64] = useState<string | null | undefined>(undefined);
  const [iconPreview, setIconPreview] = useState<string | null>(null);
  const [initialStatusOverrideJson, setInitialStatusOverrideJson] = useState('{}');
  const [personaSnippetOverride, setPersonaSnippetOverride] = useState('');
  const [sortOrder, setSortOrder] = useState(0);
  const iconFileInputRef = useRef<HTMLInputElement>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      setName(existingCard?.name || '');
      setDescription(existingCard?.description || '');
      setInitialStatusOverrideJson(existingCard?.initial_status_override_json || '{}');
      setPersonaSnippetOverride(existingCard?.persona_snippet_override || '');
      setSortOrder(existingCard?.sort_order || 0);
      setIconPreview(existingCard?.icon_path ? `at-asset://${existingCard.icon_path}?t=${Date.now()}` : null);
      setIconBase64(undefined);
      setIsSaving(false);
      setError(null);
    }
  }, [existingCard, isOpen]);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setIconBase64(reader.result as string);
        setIconPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
    if (event.target) event.target.value = '';
  };

  const handleClearImage = () => {
    setIconBase64(null);
    setIconPreview(null);
    if (iconFileInputRef.current) iconFileInputRef.current.value = "";
  };

  const handleSave = async () => {
    if (!name.trim()) { setError("角色卡名称不能为空！"); return; }
    setIsSaving(true); setError(null);

    try {
      let statusJsonToSave = initialStatusOverrideJson.trim();
      if (statusJsonToSave) {
        try { JSON.parse(statusJsonToSave); }
        catch (e) { setError("初始状态覆盖JSON格式无效。"); setIsSaving(false); return; }
      } else {
        statusJsonToSave = '{}';
      }

      const cardDataPayload = {
        name: name.trim(),
        description: description.trim(),
        icon_base64: iconBase64, 
        icon_path: existingCard?.icon_path || null, // Needed for addRolePlayingCard type
        initial_status_override_json: statusJsonToSave,
        persona_snippet_override: personaSnippetOverride.trim(),
        sort_order: Number(sortOrder),
      };
      
      // If iconBase64 is explicitly set (even to null for clearing), it's handled by backend.
      // If iconBase64 is undefined, it means "don't change icon", so existingCard.icon_path is used.

      if (existingCard) {
        // For update, icon_base64 being undefined means "don't change", null means "clear".
        // The icon_path in the payload will be ignored if icon_base64 is set.
        const updatePayload = { 
          ...cardDataPayload, 
          id: existingCard.id,
        };
        delete updatePayload.icon_path; // icon_path is not part of update payload structure for `updateRolePlayingCard` if icon_base64 is present.
                                        // if icon_base64 is undefined, the original icon_path from DB is kept.
        await window.api.cms.updateRolePlayingCard(updatePayload);
      } else {
        // For add, we send icon_base64. The backend `addRolePlayingCard` saves it and sets icon_path.
        // The type for addRolePlayingCard requires icon_path, even if it's null initially when icon_base64 is used for creation.
        // The actual path is generated by the backend from icon_base64.
        const addPayload = {
            name: cardDataPayload.name,
            description: cardDataPayload.description,
            icon_path: null, // Will be set by backend if icon_base64 is provided
            initial_status_override_json: cardDataPayload.initial_status_override_json,
            persona_snippet_override: cardDataPayload.persona_snippet_override,
            sort_order: cardDataPayload.sort_order,
            icon_base64: cardDataPayload.icon_base64 // Pass base64 for creation
        };
        await window.api.cms.addRolePlayingCard(addPayload);
      }
      onSaveSuccess();
      onClose();
    } catch (err: any) {
      console.error("Failed to save role card:", err);
      setError(`保存失败: ${err.message || '未知错误'}`);
    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-[130] p-4 backdrop-blur-sm" onClick={onClose}>
      <div className="bg-tg-bg-secondary p-5 rounded-lg shadow-xl w-full max-w-xl border border-pink-500 max-h-[90vh] flex flex-col" onClick={e => e.stopPropagation()}>
        <h4 className="text-xl font-semibold mb-4 text-pink-400">{existingCard ? '编辑角色卡' : '新增角色卡'}</h4>
        {error && <p className="text-xs text-red-400 mb-2 bg-red-900/50 p-2 rounded">{error}</p>}
        <div className="space-y-3 overflow-y-auto pr-1 flex-grow text-sm">
          <div>
            <label className="text-xs text-gray-400 block mb-1">名称*</label>
            <input type="text" value={name} onChange={e => setName(e.target.value)} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-pink-700 rounded focus:border-pink-400 focus:ring-pink-400" />
          </div>
          <div>
            <label className="text-xs text-gray-400 block mb-1">描述</label>
            <textarea value={description} onChange={e => setDescription(e.target.value)} rows={2} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-pink-700 rounded focus:border-pink-400 focus:ring-pink-400 text-xs" />
          </div>
           <div>
              <label className="text-xs text-gray-400 block mb-1">图标 (可选)</label>
              <input type="file" accept="image/*" onChange={handleImageChange} ref={iconFileInputRef} className="hidden" />
              <button onClick={() => iconFileInputRef.current?.click()} className="w-full p-2 bg-tg-bg-tertiary text-gray-300 border border-dashed border-pink-700 rounded hover:border-pink-500 focus:outline-none focus:ring-2 focus:ring-pink-500 text-xs">
                  {iconPreview ? "更改图标" : "选择图标"}
              </button>
              {iconPreview && (
                  <div className="mt-2 relative w-20 h-20 mx-auto">
                      <img src={iconPreview} alt="Icon Preview" className="w-full h-full object-contain rounded border border-pink-500"/>
                      <button onClick={handleClearImage} className="absolute -top-1 -right-1 bg-red-600 text-white rounded-full p-0.5 text-xs hover:bg-red-700"><Icon name="X" className="w-3 h-3"/></button>
                  </div>
              )}
          </div>
          <div>
            <label className="text-xs text-gray-400 block mb-1">初始状态覆盖 (JSON - 可选)</label>
            <textarea value={initialStatusOverrideJson} onChange={e => setInitialStatusOverrideJson(e.target.value)} rows={3} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-pink-700 rounded focus:border-pink-400 focus:ring-pink-400 text-xs font-mono" placeholder='例如: {"arousal": 70, "mood": "渴望的"}'/>
          </div>
          <div>
            <label className="text-xs text-gray-400 block mb-1">人格指令片段*</label>
            <textarea value={personaSnippetOverride} onChange={e => setPersonaSnippetOverride(e.target.value)} rows={4} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-pink-700 rounded focus:border-pink-400 focus:ring-pink-400 text-xs" placeholder="例如: 你现在是一个害羞的学妹，对主人充满了好奇和服从..."/>
          </div>
           <div>
            <label className="text-xs text-gray-400 block mb-1">排序权重 (数字，小号在前)</label>
            <input type="number" value={sortOrder} onChange={e => setSortOrder(parseInt(e.target.value,10) || 0)} className="w-full p-2 bg-tg-bg-tertiary text-gray-100 border border-pink-700 rounded focus:border-pink-400 focus:ring-pink-400" />
          </div>
        </div>
        <div className="flex justify-end space-x-2 mt-4 pt-3 border-t border-pink-800">
          <button onClick={onClose} className="py-2 px-3 text-xs bg-gray-600 hover:bg-gray-700 text-gray-100 rounded">取消</button>
          <button onClick={handleSave} disabled={isSaving} className="py-2 px-3 text-xs bg-pink-600 hover:bg-pink-700 text-white rounded disabled:opacity-50">
            {isSaving ? '保存中...' : (existingCard ? '保存更改' : '新增角色卡')}
          </button>
        </div>
      </div>
    </div>
  );
};