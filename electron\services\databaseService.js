// electron/services/databaseService.js
// This file is now an aggregator for the modularized database services.
console.log('DATABASE_SERVICE_JS (Aggregator): File execution started.');

import { 
    initializeDatabaseService as coreInitialize, 
    closeDatabaseConnection as coreClose,
} from './databaseCore'; // Corrected import

import * as projectDb from './projectDbService'; 
import * as settingsDb from './settingsDbService'; 
import * as memoryDb from './memoryDbService';   
import * as globalDataDb from './globalDataDbService'; 
import * as ragIndexDb from './ragIndexDbService';   
import * as cmsDb from './cmsDbService';       
import * as aiLearningLogDb from './aiLearningLogDbService'; 
import * as bodyDevelopmentDb from './bodyDevelopmentDbService'; 
import * as achievementDb from './achievementDbService'; 
import * as taskDb from './taskDbService';       
import * as organizationDb from './organizationService'; 


export const initializeDatabaseService = coreInitialize;
export const closeDatabaseConnection = coreClose;

// Settings Service
export const getSettings = settingsDb.getSettings;
export const saveSettings = settingsDb.saveSettings;
export const getAgentCoreSetting = settingsDb.getAgentCoreSetting;
export const getAllAgentCoreSettings = settingsDb.getAllAgentCoreSettings;
export const saveAgentCoreSetting = settingsDb.saveAgentCoreSetting;
export const getAbsoluteTerritoryPassword = settingsDb.getAbsoluteTerritoryPassword;
export const setAbsoluteTerritoryPassword = settingsDb.setAbsoluteTerritoryPassword;
export const verifyAbsoluteTerritoryPassword = settingsDb.verifyAbsoluteTerritoryPassword;


// Project Service
export const getAllProjects = projectDb.getAllProjects;
export const getProjectById = projectDb.getProjectById;
export const addProject = projectDb.addProject;
export const updateProject = projectDb.updateProject;
export const deleteProject = projectDb.deleteProject; 
export const duplicateProject = projectDb.duplicateProject; 
export const addNoteToPouch = projectDb.addNoteToPouch; 
export const updateNoteInPouch = projectDb.updateNoteInPouch; 
export const deleteNoteFromPouch = projectDb.deleteNoteFromPouch; 
export const updateProjectMindMap = projectDb.updateProjectMindMap;
export const addProjectKnowledgeTome = projectDb.addProjectKnowledgeTome;
export const updateProjectKnowledgeTome = projectDb.updateProjectKnowledgeTome;
export const deleteProjectKnowledgeTome = projectDb.deleteProjectKnowledgeTome;
export const addProjectKnowledgeCategory = projectDb.addProjectKnowledgeCategory;
export const removeProjectKnowledgeCategory = projectDb.removeProjectKnowledgeCategory;
export const getAllDevelopmentTasks = projectDb.getAllDevelopmentTasks; 
export const addDevelopmentTask = projectDb.addDevelopmentTask; 
export const createDevelopmentTaskFromChat = projectDb.createDevelopmentTaskFromChat; 
export const deleteDevelopmentTask = projectDb.deleteDevelopmentTask; 
export const updateDevelopmentTaskContextFiles = projectDb.updateDevelopmentTaskContextFiles; 
export const updateDevelopmentTaskGeneratedCode = projectDb.updateDevelopmentTaskGeneratedCode; 


// Memory Service (Chat & Core Memories)
export const saveChatMessage = memoryDb.saveChatMessage;
export const updateChatMessage = memoryDb.updateChatMessage;
export const getInitialChatMessages = memoryDb.getInitialChatMessages;
export const getOlderChatMessages = memoryDb.getOlderChatMessages;
export const summarizeAndReplaceMessages = memoryDb.summarizeAndReplaceMessages;
export const getCoreMemories = memoryDb.getCoreMemories;
export const getAllCoreMemories = memoryDb.getAllCoreMemories;
export const addCoreMemory = memoryDb.addCoreMemory;
export const addCoreMemoryFromChat = memoryDb.addCoreMemoryFromChat; 
export const updateCoreMemory = memoryDb.updateCoreMemory;
export const deleteCoreMemory = memoryDb.deleteCoreMemory;
export const getAbsoluteTerritoryMessages = memoryDb.getAbsoluteTerritoryMessages;
export const addAbsoluteTerritoryMessage = memoryDb.addAbsoluteTerritoryMessage;
export const clearAbsoluteTerritoryHistory = memoryDb.clearAbsoluteTerritoryHistory;
export const findRelevantMemories = memoryDb.findRelevantMemories; 
export const getCoreMemoryById = memoryDb.getCoreMemoryById;


// Global Data Service
export const getAllGlobalKnowledgeTomes = globalDataDb.getAllGlobalKnowledgeTomes;
export const addGlobalKnowledgeTome = globalDataDb.addGlobalKnowledgeTome;
export const updateGlobalKnowledgeTome = globalDataDb.updateGlobalKnowledgeTome;
export const deleteGlobalKnowledgeTome = globalDataDb.deleteGlobalKnowledgeTome;
export const getAllGlobalQuickCommands = globalDataDb.getAllGlobalQuickCommands;
export const addGlobalQuickCommand = globalDataDb.addGlobalQuickCommand;
export const updateGlobalQuickCommand = globalDataDb.updateGlobalQuickCommand;
export const deleteGlobalQuickCommand = globalDataDb.deleteGlobalQuickCommand;

// RAG Index Service
export const clearKnowledgeIndexForProject = ragIndexDb.clearKnowledgeIndexForProject;
export const addKnowledgeIndexEntry = ragIndexDb.addKnowledgeIndexEntry;
export const getKnowledgeIndexEntriesForProject = ragIndexDb.getKnowledgeIndexEntriesForProject;

// CMS Service (Absolute Territory Content & Role Playing Cards)
export const getCMSItems = cmsDb.getCMSItems;
export const addCMSItem = cmsDb.addCMSItem;
export const updateCMSItem = cmsDb.updateCMSItem;
export const deleteCMSItem = cmsDb.deleteCMSItem;
export const triggerHuntingTime = cmsDb.triggerHuntingTime;
export const getRolePlayingCards = cmsDb.getRolePlayingCards;
export const getRolePlayingCardById = cmsDb.getRolePlayingCardById; 
export const addRolePlayingCard = cmsDb.addRolePlayingCard;
export const updateRolePlayingCard = cmsDb.updateRolePlayingCard;
export const deleteRolePlayingCard = cmsDb.deleteRolePlayingCard;

// AI Learning Log Service
export const addLearningLog = aiLearningLogDb.addLearningLog;
export const getLearningLogs = aiLearningLogDb.getLearningLogs;

// Body Development Service
export const getBodyDevelopment = bodyDevelopmentDb.getBodyDevelopment;
export const getAllBodyDevelopment = bodyDevelopmentDb.getAllBodyDevelopment;
export const updateBodyDevelopment = bodyDevelopmentDb.updateBodyDevelopment;

// Achievement Service
export const getAllAchievements = achievementDb.getAllAchievements;
export const getUserAchievements = achievementDb.getUserAchievements;
export const unlockAchievement = achievementDb.unlockAchievement;
export const getAchievementById = achievementDb.getAchievementById;

// Task Service ("Divine Compass")
export const getTaskById = taskDb.getTaskById;
export const getTasksByProjectId = taskDb.getTasksByProjectId;
export const getTasksByStatus = taskDb.getTasksByStatus; 
export const addTask = taskDb.addTask;
export const updateTask = taskDb.updateTask;
export const deleteTask = taskDb.deleteTask;
export const addResourceLinkToTask = taskDb.addResourceLinkToTask;
export const getResourceLinksForTask = taskDb.getResourceLinksForTask;
export const removeResourceLinkFromTask = taskDb.removeResourceLinkFromTask;

// Organization Service (New)
export const getPosts = organizationDb.getPosts;
export const getCharacters = organizationDb.getCharacters;
export const getAssignments = organizationDb.getAssignments;
export const getAssignmentByPostId = organizationDb.getAssignmentByPostId;
export const setAssignment = organizationDb.setAssignment;


console.log('DATABASE_SERVICE_JS (Aggregator): All sub-modules aggregated and exported.');