// src/components/common/Button.tsx
import React from 'react';
import { Icon, IconProps, IconSize } from '@/components/common/Icon'; // Updated Icon import

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info' | 'light' | 'dark' | 'gradient' | 'glass';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  leftIconName?: IconProps['name'];
  rightIconName?: IconProps['name'];
  iconSize?: IconSize; // Use standard icon sizes
  iconClassName?: string;
  isLoading?: boolean;
  loadingText?: string;
  glow?: boolean; // Add glow effect
  float?: boolean; // Add floating animation
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
}

export const Button: React.FC<ButtonProps> = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  leftIconName,
  rightIconName,
  iconSize,
  iconClassName,
  isLoading = false,
  loadingText = "处理中...",
  glow = false,
  float = false,
  rounded = 'lg',
  ...props
}) => {
  // Determine icon size based on button size if not explicitly provided
  const defaultIconSize: IconSize = iconSize || (
    size === 'xs' ? 'xs' :
    size === 'sm' ? 'sm' :
    size === 'lg' ? 'lg' :
    size === 'xl' ? 'xl' : 'md'
  );
  const baseStyle = "inline-flex items-center justify-center font-medium shadow-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-tg-bg-secondary disabled:opacity-60 disabled:cursor-not-allowed transform hover:-translate-y-0.5";
  
  const variantStyles = {
    primary: "bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover focus:ring-tg-accent-primary hover:shadow-lg",
    secondary: "bg-tg-bg-secondary text-tg-text-primary hover:bg-tg-bg-hover border border-tg-border-primary focus:ring-tg-border-interactive hover:shadow-md hover:border-tg-accent-primary",
    danger: "bg-tg-danger text-white hover:bg-tg-danger-hover focus:ring-tg-danger hover:shadow-lg",
    success: "bg-tg-success text-white hover:brightness-110 focus:ring-tg-success hover:shadow-lg",
    warning: "bg-tg-warning text-tg-bg-primary hover:brightness-110 focus:ring-tg-warning hover:shadow-lg",
    info: "bg-sky-500 text-white hover:bg-sky-600 focus:ring-sky-500 hover:shadow-lg",
    light: "bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300 focus:ring-gray-400 hover:shadow-md",
    dark: "bg-gray-700 text-gray-100 hover:bg-gray-600 border border-gray-600 focus:ring-gray-500 hover:shadow-lg",
    gradient: "bg-gradient-primary text-white hover:bg-gradient-secondary focus:ring-tg-accent-primary hover:shadow-xl",
    glass: "glass-effect text-tg-text-primary hover:bg-opacity-80 focus:ring-tg-accent-primary hover:shadow-xl backdrop-blur-md",
  };

  const sizeStyles = {
    xs: "px-2 py-1 text-xs",
    sm: "px-2.5 py-1.5 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-5 py-2.5 text-base",
    xl: "px-8 py-4 text-lg",
  };

  const roundedStyles = {
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
    '2xl': "rounded-2xl",
    '3xl': "rounded-3xl",
    full: "rounded-full"
  };
  
  const currentVariantStyle = variantStyles[variant] || variantStyles.primary;
  const currentSizeStyle = sizeStyles[size] || sizeStyles.md;
  const currentRoundedStyle = roundedStyles[rounded] || roundedStyles.lg;

  const glowClass = glow ? "glow-on-hover" : "";
  const floatClass = float ? "animate-float" : "";

  return (
    <button
      className={`${baseStyle} ${currentVariantStyle} ${currentSizeStyle} ${currentRoundedStyle} ${glowClass} ${floatClass} ${className || ''}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading && <Icon name="Loader" size={defaultIconSize} className={`${iconClassName || ''} mr-2 animate-spin`} />}
      {!isLoading && leftIconName && <Icon name={leftIconName} size={defaultIconSize} className={`${iconClassName || ''} mr-2`} />}
      {isLoading ? loadingText : children}
      {!isLoading && rightIconName && <Icon name={rightIconName} size={defaultIconSize} className={`${iconClassName || ''} ml-2`} />}
    </button>
  );
};

export default Button;
