// src/components/common/IconTest.tsx
// 这是一个测试组件，用于验证图标修复效果
import React from 'react';
import { Icon, ICON_SIZES } from './Icon';

export const IconTest: React.FC = () => {
  // 测试修复的图标名称
  const fixedIcons = [
    { old: 'LoaderCircle', new: 'Loader', description: '加载图标' },
    { old: 'Home', new: 'House', description: '主殿图标' },
    { old: 'Cog', new: 'Settings', description: '设置图标' },
    { old: 'Ship', new: 'Anchor', description: '舰桥图标' },
    { old: 'Blush', new: 'Heart', description: '娇羞度图标' },
    { old: 'Droplets', new: 'Droplet', description: '水滴图标' },
    { old: 'Settings2', new: 'Settings', description: '设置图标2' },
    { old: 'SlidersHorizontal', new: 'Sliders', description: '滑块图标' },
  ];

  // 测试标准尺寸
  const sizeTests = Object.keys(ICON_SIZES) as Array<keyof typeof ICON_SIZES>;

  return (
    <div className="p-6 bg-tg-bg-primary text-tg-text-primary">
      <h2 className="text-2xl font-bold mb-6">图标修复测试</h2>
      
      {/* 修复的图标测试 */}
      <section className="mb-8">
        <h3 className="text-lg font-semibold mb-4">修复的图标映射</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {fixedIcons.map(({ old, new: newIcon, description }) => (
            <div key={old} className="p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary">
              <div className="flex items-center space-x-3 mb-2">
                <Icon name={newIcon as any} size="lg" />
                <div>
                  <div className="font-medium">{description}</div>
                  <div className="text-sm text-tg-text-secondary">
                    {old} → {newIcon}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 尺寸测试 */}
      <section className="mb-8">
        <h3 className="text-lg font-semibold mb-4">标准尺寸测试</h3>
        <div className="flex flex-wrap items-center gap-6">
          {sizeTests.map((size) => (
            <div key={size} className="flex flex-col items-center space-y-2">
              <Icon name="Star" size={size} className="text-tg-accent-primary" />
              <span className="text-sm text-tg-text-secondary">
                {size} ({ICON_SIZES[size]}px)
              </span>
            </div>
          ))}
        </div>
      </section>

      {/* 错误处理测试 */}
      <section className="mb-8">
        <h3 className="text-lg font-semibold mb-4">错误处理测试</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary">
            <h4 className="font-medium mb-2">不存在的图标（默认回退）</h4>
            <Icon name={'NonExistentIcon' as any} size="lg" />
          </div>
          <div className="p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary">
            <h4 className="font-medium mb-2">不存在的图标（警告回退）</h4>
            <Icon name={'AnotherBadIcon' as any} size="lg" fallback="alert" />
          </div>
          <div className="p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary">
            <h4 className="font-medium mb-2">自动映射测试</h4>
            <Icon name={'LoaderCircle' as any} size="lg" />
            <span className="text-sm text-tg-text-secondary ml-2">应该显示 Loader</span>
          </div>
          <div className="p-4 bg-tg-bg-secondary rounded-lg border border-tg-border-primary">
            <h4 className="font-medium mb-2">带错误提示</h4>
            <Icon name={'BadIcon' as any} size="lg" fallback="help" showTooltipOnError />
            <span className="text-sm text-tg-text-secondary ml-2">悬停查看提示</span>
          </div>
        </div>
      </section>

      {/* 动画测试 */}
      <section>
        <h3 className="text-lg font-semibold mb-4">动画测试</h3>
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <Icon name="Loader" size="lg" className="animate-spin text-tg-accent-primary" />
            <span>加载动画</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="Heart" size="lg" className="animate-pulse text-red-500" />
            <span>心跳动画</span>
          </div>
        </div>
      </section>
    </div>
  );
};
