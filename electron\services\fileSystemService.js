// electron/services/fileSystemService.js
console.log('FILE_SYSTEM_SERVICE_JS: File execution started.');

import fsPromises from 'node:fs/promises';
import path from 'node:path';
import { db, crypto } from './databaseCore'; // Import db and crypto

/**
 * Lists files and directories within a given directory.
 * @param {string} directoryPath - The absolute path to the directory.
 * @param {boolean} [recursive=false] - Whether to list files recursively.
 * @param {number} [depth=Infinity] - Maximum depth for recursion.
 * @returns {Promise<Array<{name: string, path: string, type: 'file' | 'directory', children?: any[]}> | {error: string}>}
 */
export async function listFiles(directoryPath, recursive = false, depth = Infinity, currentDepth = 0) {
  console.log(`FILE_SYSTEM_SERVICE: Listing files for directory: ${directoryPath}, recursive: ${recursive}, depth: ${depth}, currentDepth: ${currentDepth}`);
  if (currentDepth >= depth) {
    return [];
  }
  try {
    await fsPromises.access(directoryPath, fsPromises.constants.R_OK);
    const dirents = await fsPromises.readdir(directoryPath, { withFileTypes: true });
    const files = await Promise.all(
      dirents.map(async (dirent) => {
        const resPath = path.join(directoryPath, dirent.name);
        const fileNode = {
          name: dirent.name,
          path: resPath,
          type: dirent.isDirectory() ? 'directory' : 'file',
        };
        if (dirent.isDirectory() && recursive) {
          // @ts-ignore
          fileNode.children = await listFiles(resPath, true, depth, currentDepth + 1);
        }
        return fileNode;
      })
    );
    if (currentDepth === 0) console.log(`FILE_SYSTEM_SERVICE: Found ${files.length} items in ${directoryPath} (root level)`);
    return files;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error listing files in ${directoryPath}:`, error);
    if (error.code === 'ENOENT') {
      return { error: `Directory not found: ${directoryPath}` };
    } else if (error.code === 'EACCES') {
      return { error: `Permission denied for directory: ${directoryPath}` };
    }
    return { error: `Failed to list files: ${error.message}` };
  }
}

/**
 * Reads the content of a file.
 * @param {string} filePath - The absolute path to the file.
 * @returns {Promise<string | {error: string}>} The file content as a string or an error object.
 */
export async function readFile(filePath) {
  console.log(`FILE_SYSTEM_SERVICE: Reading file: ${filePath}`);
  try {
    await fsPromises.access(filePath, fsPromises.constants.R_OK);
    const content = await fsPromises.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error reading file ${filePath}:`, error);
    if (error.code === 'ENOENT') {
      return { error: `File not found: ${filePath}` };
    } else if (error.code === 'EACCES') {
      return { error: `Permission denied for file: ${filePath}` };
    }
    return { error: `Failed to read file: ${error.message}` };
  }
}

/**
 * Writes content to a file.
 * @param {string} filePath - The absolute path to the file.
 * @param {string} content - The content to write.
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function writeFile(filePath, content) {
  console.log(`FILE_SYSTEM_SERVICE: Writing to file: ${filePath}`);
  try {
    await fsPromises.writeFile(filePath, content, 'utf-8');
    console.log(`FILE_SYSTEM_SERVICE: Successfully wrote to ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error writing to file ${filePath}:`, error);
    return { success: false, error: `Failed to write file: ${error.message}` };
  }
}

/**
 * Archives a version of a file along with user instruction.
 * Stores it in a 'file_versions' table.
 * @param {string} filePath - The absolute path to the file being versioned.
 * @param {string} originalCode - The content of the file to archive.
 * @param {string} userInstruction - The user instruction that led to this version.
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function archiveFileVersion(filePath, originalCode, userInstruction) {
  console.log(`FILE_SYSTEM_SERVICE: Archiving file version for: ${filePath}`);
  if (!db) {
    console.error("FILE_SYSTEM_SERVICE (archiveFileVersion): Database not available.");
    return { success: false, error: "Database service not available." };
  }
  try {
    const newVersion = {
      id: crypto.randomUUID(),
      file_path: filePath,
      original_code: originalCode,
      user_instruction: userInstruction || null,
      archived_at: new Date().toISOString(),
    };
    const stmt = db.prepare('INSERT INTO file_versions (id, file_path, original_code, user_instruction, archived_at) VALUES (?, ?, ?, ?, ?)');
    stmt.run(newVersion.id, newVersion.file_path, newVersion.original_code, newVersion.user_instruction, newVersion.archived_at);
    console.log(`FILE_SYSTEM_SERVICE: Successfully archived version ${newVersion.id} for file ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error archiving file version for ${filePath}:`, error);
    return { success: false, error: `Failed to archive file version: ${error.message}` };
  }
}

/**
 * Checks if a directory is empty.
 * @param {string} directoryPath - The absolute path to the directory.
 * @returns {Promise<boolean | {error: string}>} True if empty, false otherwise, or an error object.
 */
export async function isDirectoryEmpty(directoryPath) {
  console.log(`FILE_SYSTEM_SERVICE: Checking if directory is empty: ${directoryPath}`);
  try {
    await fsPromises.access(directoryPath, fsPromises.constants.R_OK);
    const files = await fsPromises.readdir(directoryPath);
    return files.length === 0;
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error checking if directory ${directoryPath} is empty:`, error);
    if (error.code === 'ENOENT') {
      return { error: `Directory not found: ${directoryPath}` };
    }
    return { error: `Failed to check directory: ${error.message}` };
  }
}

/**
 * Recursively copies the contents of a source directory to a target directory.
 * @param {string} sourceDir - The absolute path to the source directory.
 * @param {string} targetDir - The absolute path to the target directory.
 * @returns {Promise<{success: boolean, error?: string}>}
 */
export async function copyDirectoryContents(sourceDir, targetDir) {
  console.log(`FILE_SYSTEM_SERVICE: Copying directory contents from ${sourceDir} to ${targetDir}`);
  try {
    await fsPromises.mkdir(targetDir, { recursive: true });
    const entries = await fsPromises.readdir(sourceDir, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(sourceDir, entry.name);
      const destPath = path.join(targetDir, entry.name);

      if (entry.isDirectory()) {
        const result = await copyDirectoryContents(srcPath, destPath); // Recursive call
        if (!result.success) {
          // Propagate error up if a subdirectory copy fails
          return result;
        }
      } else {
        await fsPromises.copyFile(srcPath, destPath);
      }
    }
    console.log(`FILE_SYSTEM_SERVICE: Successfully copied contents from ${sourceDir} to ${targetDir}`);
    return { success: true };
  } catch (error) {
    console.error(`FILE_SYSTEM_SERVICE: Error copying directory contents from ${sourceDir} to ${targetDir}:`, error);
    return { success: false, error: `Failed to copy directory: ${error.message}` };
  }
}
