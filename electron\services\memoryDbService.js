// electron/services/memoryDbService.js
console.log('MEMORY_DB_SERVICE_JS: File execution started.');

import { db, mapChatMessage, crypto } from './databaseCore';
import * as aiKernelService from './aiKernelService'; 
import * as settingsDb from './settingsDbService'; 

export function saveChatMessage(projectId, message) {
    if (!db) { console.error("MEMORY_DB_ERROR: saveChatMessage - db not available."); return null; }
    const stmt = db.prepare('INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
    try {
        stmt.run(message.id, projectId, message.sender, message.senderName, message.text, message.timestamp, message.isEditing ? 1:0, message.isStarred ? 1:0, message.isPinned ? 1:0, message.theme, message.replyToMessageId, message.triggeringUserMessageId);
        console.log(`MEMORY_DB: Saved chat message ${message.id} for project ${projectId}.`);
        return message;
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error saving chat message:', error);
        return null;
    }
}

export function updateChatMessage(message) {
    if (!db) { console.error("MEMORY_DB_ERROR: updateChatMessage - db not available."); return null; }
    const stmt = db.prepare('UPDATE chat_messages SET text = ?, timestamp = ?, isEditing = ?, isStarred = ?, isPinned = ?, theme = ?, replyToMessageId = ?, triggeringUserMessageId = ? WHERE id = ?');
    try {
        stmt.run(message.text, message.timestamp, message.isEditing ? 1:0, message.isStarred ? 1:0, message.isPinned ? 1:0, message.theme, message.replyToMessageId, message.triggeringUserMessageId, message.id);
        console.log(`MEMORY_DB: Updated chat message ${message.id}.`);
        return message;
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error updating chat message:', error);
        return null;
    }
}

export function getInitialChatMessages(projectId, limit) {
    if (!db) { console.error("MEMORY_DB_ERROR: getInitialChatMessages - db not available."); return []; }
    try {
        const rows = db.prepare('SELECT * FROM chat_messages WHERE projectId = ? ORDER BY timestamp DESC LIMIT ?').all(projectId, limit);
        return rows.map(mapChatMessage).reverse();
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error getting initial chat messages:', error);
        return [];
    }
}

export function getOlderChatMessages(projectId, beforeTimestamp, limit) {
    if (!db) { console.error("MEMORY_DB_ERROR: getOlderChatMessages - db not available."); return []; }
    try {
        const rows = db.prepare('SELECT * FROM chat_messages WHERE projectId = ? AND timestamp < ? ORDER BY timestamp DESC LIMIT ?').all(projectId, beforeTimestamp, limit);
        return rows.map(mapChatMessage).reverse();
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error getting older chat messages:', error);
        return [];
    }
}

export function summarizeAndReplaceMessages(projectId, messageIdsToReplace, summaryMessage) {
 if (!db) {
   console.error("MEMORY_DB_ERROR: summarizeAndReplaceMessages - Database not initialized.");
   return { success: false, error: "Database service not available." };
 }
 const transaction = db.transaction(() => {
   if (!messageIdsToReplace || messageIdsToReplace.length === 0) {
     console.warn("MEMORY_DB_WARN: summarizeAndReplaceMessages called with no message IDs to replace. Only inserting summary.");
   } else {
       const deleteStmt = db.prepare(`DELETE FROM chat_messages WHERE projectId = ? AND id IN (${messageIdsToReplace.map(() => '?').join(',')})`);
       const deleteResult = deleteStmt.run(projectId, ...messageIdsToReplace);
       console.log(`MEMORY_DB: Deleted ${deleteResult.changes} old messages for project ${projectId}. IDs: ${messageIdsToReplace.join(', ')}`);
   }
   
   const insertStmt = db.prepare('INSERT INTO chat_messages (id, projectId, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, replyToMessageId, triggeringUserMessageId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
   insertStmt.run(summaryMessage.id, projectId, summaryMessage.sender, summaryMessage.senderName, summaryMessage.text, summaryMessage.timestamp, 0, 0, 0, null, summaryMessage.replyToMessageId, summaryMessage.triggeringUserMessageId);
   console.log(`MEMORY_DB: Inserted summary message ${summaryMessage.id} for project ${projectId}.`);
   
   const fetchedRaw = db.prepare('SELECT * FROM chat_messages WHERE id = ?').get(summaryMessage.id);
   const fetchedSummaryMessage = fetchedRaw ? mapChatMessage(fetchedRaw) : summaryMessage;

   return { success: true, newSummaryMessage: fetchedSummaryMessage, replacedMessageIds: messageIdsToReplace };
 });

 try {
   return transaction();
 } catch (error) {
   console.error(`MEMORY_DB_ERROR: Error in summarizeAndReplaceMessages for project ${projectId}:`, error);
   return { success: false, error: error.message };
 }
}

// Core Memories
const MAX_KEYWORDS_FOR_DB_QUERY = 5; 
const MAX_ASSOCIATED_MEMORIES = 5;

function cosineSimilarity(vecA, vecB) {
    if (!vecA || !vecB || vecA.length !== vecB.length || vecA.length === 0) {
        return 0;
    }
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }
    if (normA === 0 || normB === 0) return 0;
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

export function findRelevantMemories(queryEmbedding, contextInfo, limit) {
    if (!db) { console.error("MEMORY_DB_ERROR: findRelevantMemories - db not available."); return { direct: [], associated: [] }; }
    if (!queryEmbedding || !Array.isArray(queryEmbedding) || queryEmbedding.length === 0) {
        console.warn("MEMORY_DB_WARN: findRelevantMemories called with invalid queryEmbedding.");
        return { direct: [], associated: [] };
    }
    const { personaTarget, contextType, currentTopicKeywords, desiredMemoryTypes, projectContextId } = contextInfo;

    try {
        let directSql = `
            SELECT id, memory_content, created_at, last_accessed_at, access_count, importance, 
                   persona_target, memory_type, keywords, embedding, project_context_id, source_message_id,
                   context_affinity_tags, user_feedback_score, status, value_score
            FROM core_memories 
            WHERE (persona_target = ?1 OR persona_target = 'all' OR persona_target = 'shared') AND status = 'active'
        `;
        const directParams = [personaTarget];
        let directParamIndex = 2;

        if (projectContextId) {
            directSql += ` AND (project_context_id = ?${directParamIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
            directParams.push(projectContextId);
        } else {
             directSql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
        }

        if (desiredMemoryTypes && desiredMemoryTypes.length > 0) {
            directSql += ` AND memory_type IN (${desiredMemoryTypes.map(() => `?${directParamIndex++}`).join(',')})`;
            directParams.push(...desiredMemoryTypes);
        }
        
        const candidateLimit = Math.max(limit * 5, 20); 
        directSql += ` ORDER BY value_score DESC, last_accessed_at DESC, created_at DESC LIMIT ?${directParamIndex++}`;
        directParams.push(candidateLimit);


        const candidateMemories = db.prepare(directSql).all(...directParams);
        console.log(`MEMORY_DB: Retrieved ${candidateMemories.length} candidate memories for direct relevance for persona '${personaTarget}'.`);
        
        const scoredDirectMemories = [];
        for (const mem of candidateMemories) {
            if (mem.embedding) {
                try {
                    const storedEmbedding = JSON.parse(mem.embedding);
                    if (!Array.isArray(storedEmbedding) || storedEmbedding.length !== queryEmbedding.length) {
                        console.warn(`MEMORY_DB_WARN: Embedding for memory ${mem.id} has mismatched length or is not an array.`);
                        continue;
                    }
                    const similarityScore = cosineSimilarity(queryEmbedding, storedEmbedding);
                    
                    let contextAffinityScore = 0;
                    const affinityTags = mem.context_affinity_tags ? mem.context_affinity_tags.toLowerCase().split(',').map(t => t.trim()) : [];
                    if (contextType && affinityTags.includes(contextType.toLowerCase())) {
                        contextAffinityScore += 0.5;
                    }
                    if (currentTopicKeywords && currentTopicKeywords.length > 0) {
                        const keywordMatchCount = currentTopicKeywords.filter(kw => affinityTags.includes(kw.toLowerCase())).length;
                        contextAffinityScore += keywordMatchCount * 0.1;
                    }

                    let timelinessScore = 0;
                    if (mem.last_accessed_at) {
                        const hoursSinceAccess = (new Date().getTime() - new Date(mem.last_accessed_at).getTime()) / (1000 * 60 * 60);
                        timelinessScore += Math.max(0, 1 - (hoursSinceAccess / (24 * 7))); // Higher score for recent access
                    } else if (mem.created_at) {
                        const hoursSinceCreation = (new Date().getTime() - new Date(mem.created_at).getTime()) / (1000 * 60 * 60);
                        timelinessScore += Math.max(0, 0.5 - (hoursSinceCreation / (24 * 30))); // Base score for creation, decays
                    }
                    timelinessScore += (mem.access_count || 0) * 0.05; // More accesses = slightly more timely/relevant

                    let importanceScoreValue = 0.5; // medium
                    if (mem.importance === 'high') importanceScoreValue = 1.0;
                    else if (mem.importance === 'low') importanceScoreValue = 0.2;

                    const feedbackScore = mem.user_feedback_score || 0.0; // Assume 0 if no feedback, can be -1 to 1

                    // Recalculate totalScore for sorting, value_score is for persistence
                    const currentTotalScore = 
                        (0.5 * similarityScore +       // Semantic similarity
                         0.2 * Math.min(1, contextAffinityScore) + // Contextual fit
                         0.1 * Math.min(1, timelinessScore) +      // Recency & frequency
                         0.1 * importanceScoreValue +  // Explicit importance
                         0.1 * feedbackScore) * (mem.value_score || 0.5) ; // Base value_score as a multiplier
                    
                    scoredDirectMemories.push({ ...mem, similarityScore, contextAffinityScore, timelinessScore, totalScore: currentTotalScore, retrieval_source: 'semantic' });

                } catch (e) {
                    console.error(`MEMORY_DB_ERROR: Failed to parse embedding or score memory ${mem.id}:`, e);
                }
            }
        }

        scoredDirectMemories.sort((a, b) => b.totalScore - a.totalScore); 
        const directResults = scoredDirectMemories.slice(0, limit);

        let associatedResults = [];
        const directResultIds = new Set(directResults.map(mem => mem.id));
        const tagsForExpansion = new Set();
        directResults.forEach(mem => {
            if (mem.keywords && typeof mem.keywords === 'string') {
                mem.keywords.split(',')
                    .map(tag => tag.trim().toLowerCase())
                    .filter(tag => tag) 
                    .forEach(tag => tagsForExpansion.add(tag));
            }
        });
        
        if (tagsForExpansion.size > 0) {
            const tagConditions = Array.from(tagsForExpansion).map(tag => `keywords LIKE '%${tag.replace(/'/g, "''")}%'`).join(' OR ');
            let associatedSql = `
                SELECT id, memory_content, created_at, last_accessed_at, access_count, importance, 
                       persona_target, memory_type, keywords, embedding, project_context_id, source_message_id,
                       context_affinity_tags, user_feedback_score, status, value_score
                FROM core_memories
                WHERE (persona_target = ?1 OR persona_target = 'all' OR persona_target = 'shared') AND status = 'active'
                  AND (${tagConditions})
            `;
            const associatedParams = [personaTarget];
            let associatedParamIndex = 2;

             if (projectContextId) {
                associatedSql += ` AND (project_context_id = ?${associatedParamIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
                associatedParams.push(projectContextId);
            } else {
                associatedSql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
            }

            if (directResultIds.size > 0) {
                associatedSql += ` AND id NOT IN (${Array.from(directResultIds).map(() => `?${associatedParamIndex++}`).join(',')})`;
                associatedParams.push(...Array.from(directResultIds));
            }

            associatedSql += ` ORDER BY value_score DESC, CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ASC, last_accessed_at DESC, created_at DESC LIMIT ?${associatedParamIndex++}`;
            associatedParams.push(MAX_ASSOCIATED_MEMORIES);

            const associatedMemoriesRaw = db.prepare(associatedSql).all(...associatedParams);
            associatedResults = associatedMemoriesRaw.map(mem => ({...mem, retrieval_source: 'tag_associated'}));
            console.log(`MEMORY_DB: Found ${associatedResults.length} associated memories based on tags from direct results.`);
        }

        if (directResults.length > 0) {
            const updateStmt = db.prepare('UPDATE core_memories SET access_count = access_count + 1, last_accessed_at = ? WHERE id = ?');
            const now = new Date().toISOString();
            directResults.forEach(mem => {
                try {
                    updateStmt.run(now, mem.id);
                } catch (updateError) {
                    console.error(`MEMORY_DB_ERROR: Failed to update access stats for memory ${mem.id}:`, updateError);
                }
            });
        }

        console.log(`MEMORY_DB: Final results - Direct: ${directResults.length}, Associated: ${associatedResults.length} for persona '${personaTarget}'.`);
        return { direct: directResults, associated: associatedResults };

    } catch (error) {
        console.error(`MEMORY_DB_ERROR: Error finding relevant memories:`, error);
        return { direct: [], associated: [] };
    }
}


export function getCoreMemories(personaTarget, projectContextId, limit = 5, importanceThreshold = null, keywords = []) {
  if (!db) { console.error("MEMORY_DB_ERROR: getCoreMemories - db not available."); return []; }
  console.warn("MEMORY_DB_WARN: getCoreMemories (keyword-based) called. Consider using findRelevantMemories with embeddings for semantic RAG.");
  
  try {
    let sql = `
      SELECT *
      FROM core_memories 
      WHERE (persona_target = ?1 OR persona_target = 'shared') AND status = 'active'
    `;
    const params = [personaTarget];
    let paramIndex = 2;

    if (projectContextId) {
      sql += ` AND (project_context_id = ?${paramIndex++} OR project_context_id = 'global' OR project_context_id IS NULL)`;
      params.push(projectContextId);
    } else {
      sql += ` AND (project_context_id = 'global' OR project_context_id IS NULL)`;
    }

    if (importanceThreshold) {
      sql += ` AND importance = ?${paramIndex++}`;
      params.push(importanceThreshold);
    }

    let effectiveKeywords = [];
    if (Array.isArray(keywords) && keywords.length > 0) {
        effectiveKeywords = keywords
            .filter(kw => typeof kw === 'string' && kw.trim() !== '')
            .slice(0, MAX_KEYWORDS_FOR_DB_QUERY);
    }

    if (effectiveKeywords.length > 0) {
      const keywordConditions = effectiveKeywords.map(kw => 
        `(keywords LIKE '%,${kw.replace(/'/g, "''")},%')`
      ).join(' OR ');
      sql += ` AND (${keywordConditions})`;
    }

    sql += ` ORDER BY value_score DESC, CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ASC, created_at DESC LIMIT ?${paramIndex++}`;
    params.push(limit);
    
    const stmt = db.prepare(sql);
    const memories = stmt.all(...params);
    console.log(`MEMORY_DB: Retrieved ${memories.length} core memories (getCoreMemories) for persona '${personaTarget}', project '${projectContextId || 'any/global'}'.`);
    return memories;
  } catch (error) {
    console.error('MEMORY_DB_ERROR: Error getting core memories (getCoreMemories):', error);
    return [];
  }
}

export function getAllCoreMemories(filters = {}, sort = { field: 'created_at', order: 'DESC' }, pagination = { limit: 50, offset: 0 }) {
    if (!db) { console.error("MEMORY_DB_ERROR: getAllCoreMemories - db not available."); return []; }
    try {
        let query = 'SELECT * FROM core_memories';
        const params = [];
        const conditions = [];

        if (filters.persona_target && filters.persona_target !== 'all') {
            conditions.push('persona_target = ?');
            params.push(filters.persona_target);
        }
        if (filters.memory_type_query && filters.memory_type_query.trim() !== '') {
            conditions.push('memory_type LIKE ?');
            params.push(`%${filters.memory_type_query.trim()}%`);
        }
        if (filters.importance && filters.importance !== 'all') {
            conditions.push('importance = ?');
            params.push(filters.importance);
        }
        if (filters.keywords_query && filters.keywords_query.trim() !== '') {
            const keywordForQuery = filters.keywords_query.trim().toLowerCase();
            conditions.push(`(keywords LIKE ? OR memory_content LIKE ?)`);
             // For filtering by a single keyword input that might be part of the comma-separated list
            params.push(`%,${keywordForQuery},%`, `%${keywordForQuery}%`);
        }
        if (filters.project_context_id && filters.project_context_id.trim() !== '') {
            if (filters.project_context_id.toLowerCase() === 'global') {
                conditions.push("(project_context_id = 'global' OR project_context_id IS NULL)");
            } else {
                conditions.push('project_context_id = ?');
                params.push(filters.project_context_id.trim());
            }
        }
         if (filters.status && filters.status !== 'all') {
            conditions.push('status = ?');
            params.push(filters.status);
        }
        if (filters.memory_content_query && filters.memory_content_query.trim() !== '') {
            conditions.push('memory_content LIKE ?');
            params.push(`%${filters.memory_content_query.trim()}%`);
        }


        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        const validSortFields = ['created_at', 'importance', 'persona_target', 'memory_type', 'access_count', 'last_accessed_at', 'value_score'];
        const sortField = validSortFields.includes(sort.field) ? sort.field : 'created_at';
        const sortOrder = sort.order === 'ASC' ? 'ASC' : 'DESC';
        
        if (sortField === 'importance') {
            query += ` ORDER BY CASE importance WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 ELSE 4 END ${sortOrder}, value_score DESC, created_at DESC`;
        } else {
            query += ` ORDER BY ${sortField} ${sortOrder}, value_score DESC`;
        }

        query += ` LIMIT ? OFFSET ?`;
        params.push(pagination.limit || 50, pagination.offset || 0);

        const rows = db.prepare(query).all(...params);
        console.log(`MEMORY_DB: Retrieved ${rows.length} core memories with filters/sort/pagination.`);
        return rows;
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error getting all core memories:', error);
        return [];
    }
}

export function getCoreMemoryById(memoryId) {
    if (!db) { console.error("MEMORY_DB_ERROR: getCoreMemoryById - db not available."); return null; }
    try {
        const row = db.prepare('SELECT * FROM core_memories WHERE id = ?').get(memoryId);
        return row || null;
    } catch (error) {
        console.error(`MEMORY_DB_ERROR: Error getting core memory by ID ${memoryId}:`, error);
        return null;
    }
}


export async function addCoreMemory(memoryData) {
    if (!db) { console.error("MEMORY_DB_ERROR: addCoreMemory - db not available."); return null; }
    
    let embeddingJson = null;
    if (memoryData.memory_content) {
      try {
        const appSettings = await settingsDb.getSettings();
        if (!appSettings || !appSettings.apiKey || !appSettings.embeddingModel) {
          console.warn("MEMORY_DB_WARN: API key or embedding model not configured. Skipping embedding for new memory.");
        } else {
          // Changed to use embedContentInternal from aiKernelService
          const embeddingVector = await aiKernelService.embedContentInternal(
            memoryData.memory_content, 
            'RETRIEVAL_DOCUMENT', 
            memoryData.memory_content.substring(0, 50), // title for embedding
            appSettings.apiKey // Pass apiKey
            // embeddingModel is handled by aiKernelService
          );
          if (embeddingVector && typeof embeddingVector !== 'string') { // Check if not string error
            embeddingJson = JSON.stringify(embeddingVector);
          } else {
            console.error("MEMORY_DB_ERROR: Failed to generate embedding for new memory:", embeddingVector || "Null vector");
          }
        }
      } catch (embedError) {
        console.error("MEMORY_DB_ERROR: Error during embedding for new memory:", embedError);
      }
    }
    
    const keywordsFormatted = memoryData.keywords && typeof memoryData.keywords === 'string' && memoryData.keywords.trim() 
        ? `,${memoryData.keywords.trim().split(',').map(k => k.trim().toLowerCase()).filter(k => k).join(',')},` 
        : null;
    
    const affinityTagsFormatted = memoryData.context_affinity_tags && typeof memoryData.context_affinity_tags === 'string' && memoryData.context_affinity_tags.trim()
        ? memoryData.context_affinity_tags.trim().split(',').map(t => { const tag = t.trim().toLowerCase(); return tag.startsWith('#') ? tag : `#${tag}`;}).filter(t => t.length > 1).join(',')
        : null;

    const newMemory = {
        id: crypto.randomUUID(),
        access_count: 0,
        keywords: keywordsFormatted,
        embedding: embeddingJson, 
        source_message_id: memoryData.source_message_id || null,
        context_affinity_tags: affinityTagsFormatted, 
        user_feedback_score: memoryData.user_feedback_score || 0.0,  
        status: memoryData.status || 'active',
        value_score: memoryData.value_score === undefined ? 0.5 : memoryData.value_score,  
        ...memoryData, 
        created_at: memoryData.created_at || new Date().toISOString(),
        last_accessed_at: memoryData.last_accessed_at || null,
        project_context_id: memoryData.project_context_id?.trim() || null,
    };

    const stmt = db.prepare(`
        INSERT INTO core_memories (id, memory_content, created_at, last_accessed_at, access_count, importance, persona_target, memory_type, keywords, embedding, project_context_id, source_message_id, context_affinity_tags, user_feedback_score, status, value_score)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    try {
        stmt.run(
            newMemory.id, newMemory.memory_content, newMemory.created_at, newMemory.last_accessed_at,
            newMemory.access_count, newMemory.importance, newMemory.persona_target, newMemory.memory_type,
            newMemory.keywords, newMemory.embedding, newMemory.project_context_id, newMemory.source_message_id,
            newMemory.context_affinity_tags, newMemory.user_feedback_score, newMemory.status, newMemory.value_score
        );
        console.log(`MEMORY_DB: Added core memory ${newMemory.id}. Keywords: ${newMemory.keywords}, Affinity Tags: ${newMemory.context_affinity_tags}`);
        return newMemory;
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error adding core memory:', error);
        return null;
    }
}

export async function addCoreMemoryFromChat(messageText, personaTarget, projectId = null) {
    if (!db) { console.error("MEMORY_DB_ERROR: addCoreMemoryFromChat - db not available."); return null; }
     const memoryData = {
        memory_content: messageText,
        persona_target: personaTarget,
        memory_type: 'chat_snippet', 
        importance: 'medium', 
        project_context_id: projectId,
    };
    return addCoreMemory(memoryData);
}


export async function updateCoreMemory(memoryData) {
    if (!db) { console.error("MEMORY_DB_ERROR: updateCoreMemory - db not available."); return null; }

    const existingMemory = await getCoreMemoryById(memoryData.id);
    if (!existingMemory) {
        console.warn(`MEMORY_DB_WARN: No core memory found with ID ${memoryData.id} to update.`);
        return null;
    }

    let embeddingJson = existingMemory.embedding;
    if (memoryData.memory_content && memoryData.memory_content !== existingMemory.memory_content) {
      console.log(`MEMORY_DB: Content changed for memory ${memoryData.id}. Re-embedding...`);
      try {
        const appSettings = await settingsDb.getSettings();
        if (!appSettings || !appSettings.apiKey || !appSettings.embeddingModel) {
          console.warn("MEMORY_DB_WARN: API key or embedding model not configured. Cannot re-embed memory.");
        } else {
          // Changed to use embedContentInternal from aiKernelService
          const embeddingVector = await aiKernelService.embedContentInternal(
            memoryData.memory_content, 
            'RETRIEVAL_DOCUMENT', 
            memoryData.memory_content.substring(0, 50), // title
            appSettings.apiKey // Pass apiKey
            // embeddingModel is handled by aiKernelService
          );
          if (embeddingVector && typeof embeddingVector !== 'string') { // Check if not string error
            embeddingJson = JSON.stringify(embeddingVector);
            console.log(`MEMORY_DB: Successfully re-embedded memory ${memoryData.id}.`);
          } else {
            console.error("MEMORY_DB_ERROR: Failed to re-generate embedding for updated memory:", embeddingVector || "Null vector");
          }
        }
      } catch (embedError) {
        console.error("MEMORY_DB_ERROR: Error during re-embedding for updated memory:", embedError);
      }
    }
    
    const keywordsFormatted = memoryData.keywords && typeof memoryData.keywords === 'string' && memoryData.keywords.trim() 
        ? `,${memoryData.keywords.trim().split(',').map(k => k.trim().toLowerCase()).filter(k => k).join(',')},` 
        : (existingMemory.keywords || null); // Preserve if not provided or empty
    
    const affinityTagsFormatted = memoryData.context_affinity_tags && typeof memoryData.context_affinity_tags === 'string' && memoryData.context_affinity_tags.trim()
        ? memoryData.context_affinity_tags.trim().split(',').map(t => { const tag = t.trim().toLowerCase(); return tag.startsWith('#') ? tag : `#${tag}`;}).filter(t => t.length > 1).join(',')
        : (existingMemory.context_affinity_tags || null);


    const updatedMemory = {
        ...existingMemory, 
        ...memoryData,    
        keywords: keywordsFormatted,
        embedding: embeddingJson,
        context_affinity_tags: affinityTagsFormatted,
        last_accessed_at: new Date().toISOString(), 
    };

    const stmt = db.prepare(`
        UPDATE core_memories 
        SET memory_content = ?, last_accessed_at = ?, access_count = ?, importance = ?, persona_target = ?, 
            memory_type = ?, keywords = ?, embedding = ?, project_context_id = ?, source_message_id = ?,
            context_affinity_tags = ?, user_feedback_score = ?, status = ?, value_score = ?
        WHERE id = ?
    `);
    try {
        stmt.run(
            updatedMemory.memory_content, updatedMemory.last_accessed_at, updatedMemory.access_count,
            updatedMemory.importance, updatedMemory.persona_target, updatedMemory.memory_type,
            updatedMemory.keywords, updatedMemory.embedding, updatedMemory.project_context_id,
            updatedMemory.source_message_id, updatedMemory.context_affinity_tags, updatedMemory.user_feedback_score,
            updatedMemory.status, updatedMemory.value_score,
            updatedMemory.id
        );
        console.log(`MEMORY_DB: Updated core memory ${updatedMemory.id}. Keywords: ${updatedMemory.keywords}, Affinity Tags: ${updatedMemory.context_affinity_tags}`);
        return db.prepare('SELECT * FROM core_memories WHERE id = ?').get(updatedMemory.id);
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error updating core memory:', error);
        return null;
    }
}

export function deleteCoreMemory(memoryId) {
    if (!db) { console.error("MEMORY_DB_ERROR: deleteCoreMemory - db not available."); return { success: false, error: "Database not available." }; }
    const stmt = db.prepare('DELETE FROM core_memories WHERE id = ?');
    try {
        const result = stmt.run(memoryId);
        if (result.changes === 0) {
            console.warn(`MEMORY_DB_WARN: No core memory found with ID ${memoryId} to delete.`);
            return { success: false, error: "Memory not found." };
        }
        console.log(`MEMORY_DB: Deleted core memory ${memoryId}.`);
        return { success: true };
    } catch (error) {
        console.error('MEMORY_DB_ERROR: Error deleting core memory:', error);
        return { success: false, error: error.message };
    }
}

// Absolute Territory Chat History
export function getAbsoluteTerritoryMessages(limit, beforeTimestamp) {
  if (!db) { console.error("MEMORY_DB_ERROR: getAbsoluteTerritoryMessages - db not available."); return []; }
  try {
    let query = 'SELECT id, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, itemIconPath, itemCgPath, avatarPathOverride FROM absolute_territory_chat_messages';
    const params = [];
    if (beforeTimestamp) {
      query += ' WHERE timestamp < ?';
      params.push(beforeTimestamp);
    }
    query += ' ORDER BY timestamp DESC LIMIT ?';
    params.push(limit);
    
    const rows = db.prepare(query).all(...params);
    return rows.map(mapChatMessage).reverse(); 
  } catch (error) {
    console.error('MEMORY_DB_ERROR: Error getting Absolute Territory messages:', error);
    return [];
  }
}

export function addAbsoluteTerritoryMessage(message) {
  if (!db) { console.error("MEMORY_DB_ERROR: addAbsoluteTerritoryMessage - db not available."); return null; }
  const stmt = db.prepare('INSERT INTO absolute_territory_chat_messages (id, sender, senderName, text, timestamp, isEditing, isStarred, isPinned, theme, itemIconPath, itemCgPath, avatarPathOverride) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
  try {
    const messageToSave = { 
        id: message.id || crypto.randomUUID(),
        sender: message.sender,
        senderName: message.senderName,
        text: message.text,
        timestamp: message.timestamp || new Date().toISOString(),
        isEditing: message.isEditing ? 1 : 0,
        isStarred: message.isStarred ? 1 : 0,
        isPinned: message.isPinned ? 1 : 0,
        theme: message.theme || null,
        itemIconPath: message.itemIconPath || null,
        itemCgPath: message.itemCgPath || null,
        avatarPathOverride: message.avatarPathOverride || null,
    };
    stmt.run(messageToSave.id, messageToSave.sender, messageToSave.senderName, messageToSave.text, messageToSave.timestamp, messageToSave.isEditing, messageToSave.isStarred, messageToSave.isPinned, messageToSave.theme, messageToSave.itemIconPath, messageToSave.itemCgPath, messageToSave.avatarPathOverride);
    console.log(`MEMORY_DB: Added Absolute Territory message ${messageToSave.id}.`);
    return messageToSave;
  } catch (error) {
    console.error('MEMORY_DB_ERROR: Error adding Absolute Territory message:', error);
    return null;
  }
}

export function clearAbsoluteTerritoryHistory() {
  if (!db) { console.error("MEMORY_DB_ERROR: clearAbsoluteTerritoryHistory - db not available."); return { success: false, error: "Database not available." }; }
  try {
    db.prepare('DELETE FROM absolute_territory_chat_messages').run();
    console.log('MEMORY_DB: Cleared Absolute Territory chat history.');
    return { success: true };
  } catch (error) {
    console.error('MEMORY_DB_ERROR: Error clearing Absolute Territory chat history:', error);
    return { success: false, error: error.message };
  }
}

console.log('MEMORY_DB_SERVICE_JS: File execution finished. Exports configured.');