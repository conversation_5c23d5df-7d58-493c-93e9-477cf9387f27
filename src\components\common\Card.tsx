// src/components/common/Card.tsx
import React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'glass' | 'gradient' | 'bordered';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  hover?: boolean; // Enable hover effects
  glow?: boolean; // Add glow effect
  float?: boolean; // Add floating animation
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  rounded = 'lg',
  hover = true,
  glow = false,
  float = false,
  padding = 'md',
  ...props
}) => {
  const baseClasses = "transition-all duration-300 transform";
  
  const variantClasses = {
    default: "bg-tg-bg-secondary border border-tg-border-primary",
    elevated: "bg-tg-bg-secondary border border-tg-border-primary shadow-lg",
    glass: "glass-effect backdrop-blur-md",
    gradient: "bg-gradient-card border border-tg-border-primary",
    bordered: "bg-tg-bg-secondary border-2 border-tg-accent-primary"
  };

  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md", 
    lg: "max-w-lg",
    xl: "max-w-xl"
  };

  const roundedClasses = {
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg", 
    xl: "rounded-xl",
    '2xl': "rounded-2xl",
    '3xl': "rounded-3xl"
  };

  const paddingClasses = {
    none: "",
    sm: "p-3",
    md: "p-4",
    lg: "p-6",
    xl: "p-8"
  };

  const hoverClass = hover ? "card-hover" : "";
  const glowClass = glow ? "glow-on-hover" : "";
  const floatClass = float ? "animate-float" : "";

  const currentVariantClass = variantClasses[variant] || variantClasses.default;
  const currentSizeClass = sizeClasses[size] || sizeClasses.md;
  const currentRoundedClass = roundedClasses[rounded] || roundedClasses.lg;
  const currentPaddingClass = paddingClasses[padding] || paddingClasses.md;

  return (
    <div
      className={`${baseClasses} ${currentVariantClass} ${currentSizeClass} ${currentRoundedClass} ${currentPaddingClass} ${hoverClass} ${glowClass} ${floatClass} ${className || ''}`}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;
