// electron/services/tgcLoaderService.ts
console.log('TGC_LOADER_SERVICE_TS: File execution started (JSON Mode).');

import fs from 'node:fs/promises';
import path from 'node:path';
import { app } from 'electron';
import type { Character } from '../../src/types'; 
import type { PropAsset, ThemeAsset } from '../../src/types'; 
import { parseTgcJson } from '../utils/jsonParser'; 

const SYSTEM_RESOURCES_PATH_SEGMENT = 'system'; 
const DEFAULTS_PATH_SEGMENT = 'defaults';
const CHARACTERS_DIR_SEGMENT = 'characters';
const PROPS_DIR_SEGMENT = 'props';
const THEMES_DIR_SEGMENT = 'themes';
const AVATAR_SYSTEM_PREFIX = 'system_packaged/'; 

function getDefaultsBasePath(): string {
  const baseAppPath = app.getAppPath(); 
  // In packaged app, app.getAppPath() points to app.asar, resources are often outside.
  // We've configured viteStaticCopy to put 'resources/system' into 'dist-electron/system'.
  const effectiveBasePath = baseAppPath.includes('app.asar')
    ? path.join(baseAppPath, '..') // Go up from app.asar to its containing directory
    : baseAppPath; // In dev, app.getAppPath() is project root

  return path.resolve(effectiveBasePath, 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT);
}

async function loadJsonFilesFromDirectory<T>(dirSegment: string, typeName: string, fileSuffix: string): Promise<T[]> {
  const dirPath = path.join(getDefaultsBasePath(), dirSegment);
  console.log(`TGC_LOADER_SERVICE: Attempting to load ${typeName} from ${dirPath}`);
  const loadedItems: T[] = [];

  try {
    await fs.access(dirPath); // Check if directory exists
    const files = await fs.readdir(dirPath);
    console.log(`TGC_LOADER_SERVICE: Found ${files.length} files/dirs in ${dirPath}. Filtering for '${fileSuffix}'...`);
    
    for (const file of files) {
      if (file.endsWith(fileSuffix)) {
        const filePath = path.join(dirPath, file);
        console.log(`TGC_LOADER_SERVICE: Reading ${typeName} file: ${filePath}`);
        try {
          const fileContent = await fs.readFile(filePath, 'utf-8');
          const itemData = parseTgcJson(fileContent) as T;
          
          // Basic validation: itemData should be an object and have an id and name (common for most assets)
          if (itemData && typeof itemData === 'object' && (itemData as any).id && (itemData as any).name) {
            loadedItems.push(itemData);
            console.log(`TGC_LOADER_SERVICE: Successfully loaded and parsed ${typeName}: ${(itemData as any).name} (ID: ${(itemData as any).id})`);
          } else {
            console.warn(`TGC_LOADER_SERVICE: Invalid or incomplete ${typeName} data structure in ${file}. Skipping.`);
          }
        } catch (err: any) {
          console.error(`TGC_LOADER_SERVICE: Failed to load or parse ${typeName} file ${filePath}. Reason: ${err.message}. Skipping.`);
        }
      }
    }
  } catch (err: any) {
    console.error(`TGC_LOADER_SERVICE: Failed to access or read ${typeName} directory ${dirPath}. Reason: ${err.message}. Returning empty array for ${typeName}.`);
    return []; // Return empty if directory itself is inaccessible
  }
  console.log(`TGC_LOADER_SERVICE: Successfully loaded ${loadedItems.length} default ${typeName}.`);
  return loadedItems;
}

export async function loadDefaultCharacters(): Promise<Character[]> {
  const characters = await loadJsonFilesFromDirectory<Character>(CHARACTERS_DIR_SEGMENT, 'characters', '.character.pack.json');
  return characters.map(char => {
    let finalAvatarPath = char.avatar_path;
    if (finalAvatarPath && typeof finalAvatarPath === 'string' &&
        !finalAvatarPath.startsWith('http') && 
        !finalAvatarPath.startsWith('app-avatar://') && 
        !finalAvatarPath.startsWith(AVATAR_SYSTEM_PREFIX)) {
      // Prepend system prefix if it's a relative path within the system avatars dir
      finalAvatarPath = `${AVATAR_SYSTEM_PREFIX}${finalAvatarPath.replace(/\\/g, '/')}`;
    } else if (finalAvatarPath && typeof finalAvatarPath === 'string') {
      // Ensure consistent path separators for existing app-avatar or system_packaged paths
      finalAvatarPath = finalAvatarPath.replace(/\\/g, '/');
    }
    return {
      ...char,
      avatar_path: finalAvatarPath || null,
      // persona_prompt is already a string after parseTgcJson processes the array from JSON
    };
  });
}

export async function loadDefaultProps(): Promise<PropAsset[]> {
  return loadJsonFilesFromDirectory<PropAsset>(PROPS_DIR_SEGMENT, 'props', '.prop.json');
}

export async function loadDefaultThemes(): Promise<ThemeAsset[]> {
  return loadJsonFilesFromDirectory<ThemeAsset>(THEMES_DIR_SEGMENT, 'themes', '.theme.json');
}

console.log('TGC_LOADER_SERVICE_TS: File execution finished (JSON Mode). Service functions defined.');