
// src/components/MainHallPage.tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// useSettings hook import removed
import type { Project, AppSettings } from '@/types';
import { ProjectCard } from '@/components/ProjectCard';
import { Icon } from '@/components/common/Icon';
import { SmartIcon, VisualHeading, VisualContainer } from '@/components/common/VisualUtils';
import { PROJECT_CARD_ID_NEW_PROJECT, PROJECT_CARD_ID_TRAINING_ROOM } from '@/features/projects/projectConstants';

interface MainHallPageProps {
  projects: Project[];
  addProject: (name: string) => Promise<Project | null>;
  deleteProject: (projectId: string) => Promise<void>;
  updateProject: (project: Project) => Promise<void>;
  duplicateProject: (projectId: string) => Promise<Project | null | undefined>;
  settings: AppSettings; // Settings now come as a prop
}

const PasswordModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (password: string) => Promise<{isValid: boolean, isFirstTimeSetting: boolean}>;
  onSuccessNavigation: () => void;
}> = ({ isOpen, onClose, onConfirm, onSuccessNavigation }) => {
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setPassword('');
      setError(null);
      setIsLoading(false);
      window.api.settings.getAbsoluteTerritoryPassword().then(pass => {
        setIsFirstTime(pass === null || pass === undefined || pass === '');
        setTimeout(() => inputRef.current?.focus(), 50);
      });
    }
  }, [isOpen]);

  const handleSubmit = async () => {
    if (!password.trim()) {
      setError("通行令不能为空。");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const verificationResult = await onConfirm(password);
      if (verificationResult.isValid) {
        onSuccessNavigation();
        onClose();
      } else {
        setError("通行令错误，请重试。");
        if (inputRef.current) {
          inputRef.current.classList.add('animate-shake');
          setTimeout(() => inputRef.current?.classList.remove('animate-shake'), 500);
        }
      }
    } catch (e: any) {
      setError(`验证通行令时出错: ${e.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4 backdrop-blur-sm" onClick={onClose}>
      <div className="bg-tg-bg-secondary p-8 rounded-lg shadow-xl w-full max-w-md border border-tg-border-primary" onClick={e => e.stopPropagation()}>
        <h3 className="text-2xl font-semibold mb-1 text-tg-text-primary">
          {isFirstTime ? "设置初始通行令" : "验证通行令"}
        </h3>
        <p className="text-sm text-tg-text-secondary mb-6">
          {isFirstTime ? "请为您的“训练室”设置一个通行令，它将用于保护您的私密空间。" : "请输入通行令以进入“训练室”。"}
        </p>
        <input
          ref={inputRef}
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          placeholder="输入通行令..."
          className="w-full p-3 mb-4 text-base bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-2 focus:ring-tg-accent-primary/30"
          onKeyPress={e => e.key === 'Enter' && !isLoading && handleSubmit()}
          disabled={isLoading}
        />
        {error && <p className="text-sm text-red-400 mb-4">{error}</p>}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="py-2 px-4 rounded-md transition-colors text-sm bg-tg-bg-tertiary text-tg-text-secondary border border-tg-border-primary hover:bg-tg-bg-hover"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="py-2 px-4 rounded-md transition-colors font-semibold text-sm bg-tg-accent-primary text-white hover:bg-tg-accent-primary-hover disabled:opacity-70"
            disabled={isLoading}
          >
            {isLoading ? "验证中..." : (isFirstTime ? "设置并进入" : "确认进入")}
          </button>
        </div>
      </div>
      <style>{`
        .animate-shake {
          animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
          transform: translate3d(0, 0, 0);
        }
        @keyframes shake {
          10%, 90% { transform: translate3d(-1px, 0, 0); }
          20%, 80% { transform: translate3d(2px, 0, 0); }
          30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
          40%, 60% { transform: translate3d(4px, 0, 0); }
        }
      `}</style>
    </div>
  );
};

interface ActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (value: string) => void;
  title: string;
  prompt: string;
  initialValue?: string;
  confirmText?: string;
}

const ActionModal: React.FC<ActionModalProps> = ({ isOpen, onClose, onConfirm, title, prompt, initialValue = '', confirmText = "确认" }) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const inputRef = React.useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setInputValue(initialValue);
      setTimeout(() => inputRef.current?.focus(), 50);
    }
  }, [isOpen, initialValue]);

  const handleSubmit = () => {
    console.log('!!!!!! [FRONTEND_BTN_CLICK] "Confirm Create Project" button (ActionModal handleSubmit) was CLICKED! Timestamp:', Date.now());
    if (inputValue.trim()) {
      onConfirm(inputValue.trim());
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[60] p-4 backdrop-blur-sm" onClick={onClose}>
      <div className="bg-tg-bg-secondary p-6 rounded-lg shadow-xl w-full max-w-md border border-tg-border-primary" onClick={e => e.stopPropagation()}>
        <h3 className="text-xl font-semibold mb-3 text-tg-text-primary">{title}</h3>
        <p className="text-sm text-tg-text-secondary mb-4">{prompt}</p>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          className="w-full p-2.5 mb-5 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary focus:ring-1 focus:ring-tg-accent-primary/30"
          onKeyPress={e => e.key === 'Enter' && handleSubmit()}
        />
        <div className="flex justify-end space-x-3">
          <button onClick={onClose} className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary">取消</button>
          <button onClick={handleSubmit} className="py-2 px-4 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover">{confirmText}</button>
        </div>
      </div>
    </div>
  );
};


export const MainHallPage: React.FC<MainHallPageProps> = ({ projects, addProject, deleteProject, updateProject, duplicateProject, settings }) => {
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const navigate = useNavigate();

  const [showRenameModal, setShowRenameModal] = useState(false);
  const [projectToRename, setProjectToRename] = useState<Project | null>(null);

  const handleCreateProject = async (nameFromModal: string) => {
    if (nameFromModal.trim()) {
      try {
        const newProj = await addProject(nameFromModal);
        if (newProj) {
            setNewProjectName('');
            setShowNewProjectModal(false);
            navigate(`/project/${newProj.id}`);
        } else {
            console.error("MainHallPage: addProject returned null, possibly due to an error in App.tsx or backend.");
            alert("创建项目失败，请检查日志或联系支持。");
        }
      } catch (error: any) {
        console.error("MainHallPage: Failed to create project via addProject prop:", error);
        alert(`创建项目时出错: ${error.message || '未知错误'}`);
      }
    }
  };

  const handleDuplicateProjectUI = async (projectId: string): Promise<Project | undefined> => {
    try {
      const duplicatedProj = await duplicateProject(projectId);
      if (!duplicatedProj) {
        alert("复制项目失败。可能是后台处理时发生错误，请检查应用主日志。");
        return undefined;
      }
      return duplicatedProj;
    } catch (error: any) {
      console.error("MainHallPage: Error during project duplication:", error);
      alert(`复制项目时发生错误: ${error.message || '未知错误'}`);
      return undefined;
    }
  };

  const handleVerifyPassword = async (password: string) => {
    const result = await window.api.settings.verifyAbsoluteTerritoryPassword(password);
    return { isValid: result.isValid, isFirstTimeSetting: result.isFirstTime };
  };

  const openRenameModal = (project: Project) => {
    setProjectToRename(project);
    setShowRenameModal(true);
  };

  const handleRenameProject = async (newName: string) => {
    if (projectToRename && newName.trim()) {
      await updateProject({ ...projectToRename, name: newName.trim() });
    }
  };

  const handleSetProjectCover = async (project: Project) => {
    const filePath = await window.api.fs.openFileDialog({ title: "选择项目封面图片", filters: [{ name: 'Images', extensions: ['png', 'jpg', 'jpeg', 'webp'] }] });
    if (filePath) {
      try {
        const uniqueFilename = `${Date.now()}_${filePath.split(/[\\/]/).pop()}`;
        const coverImageRelativePath = await window.api.fs.copyFileToUserData(filePath, `project_covers/${project.id}`, uniqueFilename);
        if (typeof coverImageRelativePath === 'string') {
          await updateProject({ ...project, coverImageUrl: coverImageRelativePath });
        } else {
          throw new Error(coverImageRelativePath.error || "复制封面图片失败");
        }
      } catch (error) {
        console.error("Failed to set project cover:", error);
      }
    }
  };

  const newProjectCardData: Project = {
    id: PROJECT_CARD_ID_NEW_PROJECT,
    name: '新建项目',
    createdAt: new Date().toISOString(),
    lastModifiedAt: new Date().toISOString(),
    discussionMessages: [],
    inspirationNotes: [],
    bugMemoNotes: [],
    quickCommandsNotes: [],
  };

  const trainingRoomCardData: Project = {
    id: PROJECT_CARD_ID_TRAINING_ROOM,
    name: '训练室',
    createdAt: new Date().toISOString(),
    lastModifiedAt: new Date().toISOString(),
    discussionMessages: [],
    inspirationNotes: [],
    bugMemoNotes: [],
    quickCommandsNotes: [],
  };

  const allDisplayItems = [newProjectCardData, trainingRoomCardData, ...projects];

  return (
    <div className="p-6 md:p-8 min-h-full bg-gradient-dark relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 bg-tg-accent-primary rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-32 right-32 w-24 h-24 bg-purple-500 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-blue-500 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="relative z-10">
        <div className="flex justify-between items-center mb-10">
          <div className="flex items-center">
            <SmartIcon name="FolderOpen" level="accent" context="navigation" className="mr-4" />
            <VisualHeading level="accent" size="3xl" gradient>项目陈列区</VisualHeading>
          </div>
        </div>

        {allDisplayItems.length === 0 && (
          <VisualContainer level="secondary" variant="panel" className="text-center py-16 mx-auto max-w-md">
            <SmartIcon name="FolderPlus" level="muted" context="decoration" className="mx-auto mb-4" animate="float" />
            <VisualHeading level="secondary" size="lg" className="mb-3">尚无项目</VisualHeading>
            <p className="text-tg-text-placeholder leading-relaxed">点击 "新建项目" 开始您的第一个创作吧！</p>
          </VisualContainer>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8">
        {allDisplayItems.map(item => (
          <ProjectCard
            key={item.id}
            project={item}
            onClickOverride={
              item.id === PROJECT_CARD_ID_NEW_PROJECT ? () => setShowNewProjectModal(true) :
              item.id === PROJECT_CARD_ID_TRAINING_ROOM ? () => setShowPasswordModal(true) :
              undefined
            }
            isSpecialCard={item.id === PROJECT_CARD_ID_NEW_PROJECT || item.id === PROJECT_CARD_ID_TRAINING_ROOM}
            onDelete={deleteProject}
            onRename={openRenameModal}
            onDuplicate={handleDuplicateProjectUI}
            onSetCover={handleSetProjectCover}
            settings={settings} // Pass settings to ProjectCard
          />
        ))}
      </div>

      <ActionModal
        isOpen={showNewProjectModal}
        onClose={() => setShowNewProjectModal(false)}
        onConfirm={handleCreateProject}
        title="创建新项目"
        prompt="请输入新项目的名称："
        confirmText="创建"
      />

      {projectToRename && (
        <ActionModal
          isOpen={showRenameModal}
          onClose={() => { setShowRenameModal(false); setProjectToRename(null); }}
          onConfirm={handleRenameProject}
          title="重命名项目"
          prompt={`为项目 "${projectToRename.name}" 输入新的名称：`}
          initialValue={projectToRename.name}
          confirmText="重命名"
        />
      )}

        <PasswordModal
          isOpen={showPasswordModal}
          onClose={() => setShowPasswordModal(false)}
          onConfirm={handleVerifyPassword}
          onSuccessNavigation={() => navigate('/absolute-territory')}
        />
      </div>
    </div>
  );
};
