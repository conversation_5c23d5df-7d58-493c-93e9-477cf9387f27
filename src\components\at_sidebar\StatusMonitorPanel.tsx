// src/components/at_sidebar/StatusMonitorPanel.tsx
import React from 'react';
import type { LinLuoDetailedStatus } from '@/types';
import { Icon } from '@/components/common/Icon'; 
import type { IconName } from 'lucide-react';

interface StatusMonitorPanelProps {
  linLuoStatus: LinLuoDetailedStatus | null;
  isOpen: boolean;
  onToggle: () => void;
}

interface StatDisplayProps {
  iconName: IconName;
  label: string;
  value: string | number | undefined;
  unit?: string;
  barValue?: number; // 0-100 for progress bar
  barColorClass?: string;
  iconColorClass?: string;
  valueColorClass?: string;
}

const StatDisplay: React.FC<StatDisplayProps> = ({
  iconName,
  label,
  value,
  unit = '',
  barValue,
  barColorClass = 'bg-pink-500',
  iconColorClass = 'text-pink-400',
  valueColorClass = 'text-pink-300',
}) => {
  const displayValue = value === undefined || value === null ? '未设定' : `${value}${unit}`;
  const barPercentage = typeof barValue === 'number' ? Math.min(100, Math.max(0, barValue)) : 0;

  return (
    <div className="text-xs mb-1.5" title={`${label}: ${displayValue}`}>
      <div className="flex justify-between items-center mb-0.5">
        <span className={`flex items-center text-gray-300 truncate w-2/5 ${iconColorClass}`}>
          <Icon name={iconName} className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
          {label}
        </span>
        <span className={`font-medium truncate w-3/5 text-right ${valueColorClass}`}>{displayValue}</span>
      </div>
      {typeof barValue === 'number' && (
        <div className="w-full bg-gray-700 rounded-full h-1.5 shadow-inner">
          <div 
            className={`h-1.5 rounded-full transition-all duration-300 ease-out ${barColorClass}`} 
            style={{ width: `${barPercentage}%` }}
            role="progressbar"
            aria-valuenow={barPercentage}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`${label}进度`}
          ></div>
        </div>
      )}
    </div>
  );
};


export const StatusMonitorPanel: React.FC<StatusMonitorPanelProps> = ({ linLuoStatus, isOpen, onToggle }) => {
  if (!linLuoStatus) {
    return (
      <div className="mb-2 p-2.5 bg-gray-900/70 rounded-lg border border-purple-700/50">
        <button 
          onClick={onToggle}
          className="w-full flex justify-between items-center text-xs font-semibold text-purple-400 mb-1 hover:text-purple-300"
          aria-expanded={isOpen}
          aria-controls="status-panel-content"
        >
          <span className="flex items-center"><Icon name="Activity" className="w-4 h-4 mr-1.5 text-pink-400"/>姐姐的状态面板</span>
          {isOpen ? <Icon name="ChevronUp" className="w-4 h-4"/> : <Icon name="ChevronDown" className="w-4 h-4"/>}
        </button>
        {isOpen && <p className="text-xs text-gray-400 text-center py-1">状态数据加载中...</p>}
      </div>
    );
  }

  return (
    <div className="mb-2 p-2.5 bg-gray-900/70 rounded-lg border border-purple-700/50">
      <button 
        onClick={onToggle}
        className="w-full flex justify-between items-center text-xs font-semibold text-purple-400 mb-1 hover:text-purple-300"
        aria-expanded={isOpen}
        aria-controls="status-panel-content"
      >
        <span className="flex items-center"><Icon name="Activity" className="w-4 h-4 mr-1.5 text-pink-400"/>姐姐的状态面板</span>
        {isOpen ? <Icon name="ChevronUp" className="w-4 h-4"/> : <Icon name="ChevronDown" className="w-4 h-4"/>}
      </button>
      {isOpen && (
        <div id="status-panel-content" className="space-y-1 text-xs pt-1 border-t border-purple-800/50 max-h-60 overflow-y-auto custom-scrollbar pr-1">
          <StatDisplay iconName="Heart" label="心潮起伏" value={linLuoStatus.arousal} unit="%" barValue={linLuoStatus.arousal} barColorClass="bg-pink-500" iconColorClass="text-pink-400" valueColorClass="text-pink-300"/>
          <StatDisplay iconName="Smile" label="当前心境" value={linLuoStatus.mood || "平静"} iconColorClass="text-yellow-400" valueColorClass="text-yellow-300"/>
          <StatDisplay iconName="Brain" label="清明值" value={linLuoStatus.sanityPoints} unit="%" barValue={linLuoStatus.sanityPoints} barColorClass="bg-sky-500" iconColorClass="text-sky-400" valueColorClass="text-sky-300"/>
          <StatDisplay iconName="Sparkles" label="感知度" value={linLuoStatus.sensitivityLevel} unit="%" barValue={linLuoStatus.sensitivityLevel} barColorClass="bg-teal-500" iconColorClass="text-teal-400" valueColorClass="text-teal-300"/>
          <StatDisplay iconName="ThumbsUp" label="默契度" value={linLuoStatus.obedienceScore} unit="%" barValue={linLuoStatus.obedienceScore} barColorClass="bg-green-500" iconColorClass="text-green-400" valueColorClass="text-green-300"/>
          <StatDisplay iconName="Heart" label="娇羞度" value={linLuoStatus.shameLevel} unit="%" barValue={linLuoStatus.shameLevel} barColorClass="bg-rose-400" iconColorClass="text-rose-300" valueColorClass="text-rose-200"/>
          <StatDisplay iconName="Droplet" label="灵感浓郁度" value={linLuoStatus.wetnessLevel} unit="%" barValue={linLuoStatus.wetnessLevel} barColorClass="bg-cyan-500" iconColorClass="text-cyan-400" valueColorClass="text-cyan-300"/>
          <StatDisplay iconName="Zap" label="心流合一度" value={linLuoStatus.orgasmProximity} unit="%" barValue={linLuoStatus.orgasmProximity} barColorClass="bg-purple-500" iconColorClass="text-purple-400" valueColorClass="text-purple-300"/>
          <StatDisplay iconName="Gauge" label="灵能储备" value={linLuoStatus.specialFluidGauge} unit="%" barValue={linLuoStatus.specialFluidGauge} barColorClass="bg-amber-500" iconColorClass="text-amber-400" valueColorClass="text-amber-300"/>
          <StatDisplay iconName="Settings" label="界面扩张" value={linLuoStatus.interfaceExpansion} unit="%" barValue={linLuoStatus.interfaceExpansion} barColorClass="bg-slate-500" iconColorClass="text-slate-400" valueColorClass="text-slate-300"/>
          <StatDisplay iconName="Repeat" label="默契行为计数" value={linLuoStatus.obedienceActionCount} iconColorClass="text-indigo-400" valueColorClass="text-indigo-300"/>
        </div>
      )}
    </div>
  );
};
