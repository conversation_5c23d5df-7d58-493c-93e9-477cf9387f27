
// src/components/AbsoluteTerritoryPage.tsx
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import type { 
    AppSettings, ChatMessage, GlobalQuickCommandItem, ChatDiscussionAreaRef, AITaskStatus, 
    PropItem, CostumeItem, PoseItem, CMSType, CMSItemBase, AIResponseWithStatus, 
    AudioPlaybackState, SoundName, LinLuoDetailedStatus, CoreMemory, XPHypothesisProposalData, 
    RolePlayingCard, ScriptAsset, SceneCardAsset, ScriptChoice, AnyLoadedAsset, AssetType, 
    TGCRoleCardAsset, AiCallContext, LinLuoBodyDevelopment, Achievement, UserAchievement,
    AbsoluteTerritoryPageProps as OriginalAbsoluteTerritoryPageProps, LinLuoSystemUpdatePayload 
} from '@/types';
import { TerritoryChat } from '@/components/chat/TerritoryChat'; // UPDATED IMPORT
import { AbsoluteTerritorySidebar } from '@/components/at_sidebar/AbsoluteTerritorySidebar'; 
import { Icon } from '@/components/common/Icon'; 
import { parseMessageTextForTheme } from '@/chatConstants'; 
import { GenericModal } from '@/components/GenericModal';
import { CMSManagementModal } from '@/components/cms/CMSManagementModal';
import { RoleCardEditModal } from '@/components/cms/RoleCardEditModal';
import { MagicMirrorDisplay } from '@/components/at_sidebar/MagicMirrorDisplay';
import { StatusMonitorPanel } from '@/components/at_sidebar/StatusMonitorPanel';
import { DevelopmentBlueprintPanel } from '@/components/at_sidebar/DevelopmentBlueprintPanel';
import { AchievementHallModal } from '@/components/AchievementHallModal';
import { ALL_BODY_ZONES } from '@/types';
import { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';
import { runTrainingRoomDiagnostics } from '@/utils/trainingRoomDiagnostics';

interface AbsoluteTerritoryPageProps extends Omit<OriginalAbsoluteTerritoryPageProps, 'settings'> {
  settings: AppSettings; 
}


const INITIAL_MESSAGES_LOAD_COUNT = 50;
const OLDER_MESSAGES_LOAD_COUNT = 30;

const DEFAULT_LINLUO_DETAILED_STATUS: LinLuoDetailedStatus = {
  arousal: 30,
  mood: "平静",
  sanityPoints: 80,
  sensitivityLevel: 50,
  obedienceScore: 20,
  shameLevel: 10,
  wetnessLevel: 10,
  orgasmProximity: 0,
  interfaceExpansion: 0,
  specificBodyPartStatus: {},
  specialFluidGauge: 0,
  obedienceActionCount: 0,
};

const EventCGOverlay: React.FC<{imageUrl: string | null; show: boolean; onClose: () => void}> = ({ imageUrl, show, onClose }) => {
    const [internalShow, setInternalShow] = useState(false);
    const [opacityClass, setOpacityClass] = useState('opacity-0');
    const timerRef = useRef<number | null>(null);

    useEffect(() => {
        if (show && imageUrl) {
            setInternalShow(true);
            setTimeout(() => setOpacityClass('opacity-100'), 50); 
            if (timerRef.current) clearTimeout(timerRef.current);
            timerRef.current = window.setTimeout(() => { 
                setOpacityClass('opacity-0'); 
                setTimeout(() => { setInternalShow(false); onClose(); }, 500); 
            }, 3000); 
        } else if (!show && internalShow) { 
            setOpacityClass('opacity-0');
             setTimeout(() => { setInternalShow(false); }, 500);
        }
        return () => { if (timerRef.current) clearTimeout(timerRef.current); };
    }, [show, imageUrl, onClose, internalShow]);

    if (!internalShow || !imageUrl) return null;

    return (
        <div 
            className={`fixed inset-0 z-[200] flex items-center justify-center bg-black/80 transition-opacity duration-500 ease-in-out ${opacityClass}`}
            onClick={() => { 
                 if (timerRef.current) clearTimeout(timerRef.current);
                 setOpacityClass('opacity-0');
                 setTimeout(() => { setInternalShow(false); onClose(); }, 500);
            }}
        >
            <img 
                src={imageUrl} 
                alt="Event CG" 
                className="max-w-[95vw] max-h-[95vh] object-contain rounded-lg shadow-2xl border-2 border-purple-500"
                onClick={e => e.stopPropagation()} 
            />
        </div>
    );
};

const getCleanText = (html: string): string => {
    if (typeof DOMParser === 'undefined') return html.replace(/<[^>]*>?/gm, '').trim();
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return (doc.body.textContent || "").trim();
    } catch (e) { return html.replace(/<[^>]*>?/gm, '').trim(); }
};


export const AbsoluteTerritoryPage: React.FC<AbsoluteTerritoryPageProps> = ({
  globalQuickCommands,
  isAIServiceReady,
  onAddCoreMemory,
  settings, 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoadingInitial, setIsLoadingInitial] = useState(true); 
  const [isLoadingOlder, setIsLoadingOlder] = useState(false);
  const [canLoadOlder, setCanLoadOlder] = useState(true);
  const [aiTaskStatus, setAITaskStatus] = useState<AITaskStatus>('idle');
  const chatAreaRef = useRef<ChatDiscussionAreaRef>(null);
  const audioPlayerRef = useRef<HTMLAudioElement>(null);
  const backgroundDivRef = useRef<HTMLDivElement>(null);

  const [propsItems, setPropsItems] = useState<PropItem[]>([]);
  const [costumesItems, setCostumesItems] = useState<CostumeItem[]>([]);
  const [posesItems, setPosesItems] = useState<PoseItem[]>([]);
  const [isLoadingCMS, setIsLoadingCMS] = useState(false);

  const [cgImageUrl, setCgImageUrl] = useState<string | null>(null);
  const [showCgOverlay, setShowCgOverlay] = useState(false);
  const [currentArmoryOwner, setCurrentArmoryOwner] = useState<'master' | 'queen'>('master');
  
  const [currentAudioState, setCurrentAudioState] = useState<AudioPlaybackState>(
    settings.training_room_audio_state || { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }
  );
  const [linLuoStatus, setLinLuoStatus] = useState<LinLuoDetailedStatus>(DEFAULT_LINLUO_DETAILED_STATUS);
  const [showSaveChatModal, setShowSaveChatModal] = useState(false);
  const [saveChatFormat, setSaveChatFormat] = useState<'md' | 'html'>('md');
  
  const [gameplayMode, setGameplayMode] = useState<'director' | 'script'>('director');
  const [availableScripts, setAvailableScripts] = useState<ScriptAsset[]>([]);
  const [availableSceneCards, setAvailableSceneCards] = useState<SceneCardAsset[]>([]);
  const [dbRoleCards, setDbRoleCards] = useState<RolePlayingCard[]>([]); 
  const [tgcRoleCards, setTgcRoleCards] = useState<TGCRoleCardAsset[]>([]);

  const [selectedScript, setSelectedScript] = useState<ScriptAsset | null>(null);
  const [currentScriptSceneId, setCurrentScriptSceneId] = useState<string | null>(null);
  const [currentScriptChoices, setCurrentScriptChoices] = useState<ScriptChoice[] | null>(null);
  const [userSelectedRoleCardId, setUserSelectedRoleCardId] = useState<string | null>(null);
  const [userSelectedSceneCard, setUserSelectedSceneCard] = useState<SceneCardAsset | null>(null);
  
  const initialRoleCardIdFromLocation = (location.state as any)?.selectedRoleCardId as string | null;

  const [isRoleCardEditModalOpen, setIsRoleCardEditModalOpen] = useState(false);
  const [editingRoleCard, setEditingRoleCard] = useState<RolePlayingCard | null>(null);

  const [bodyDevelopment, setBodyDevelopment] = useState<LinLuoBodyDevelopment[]>([]);
  const [isLoadingBodyDevelopment, setIsLoadingBodyDevelopment] = useState(false);
  const [allLoadedCMSAssets, setAllLoadedCMSAssets] = useState<AnyLoadedAsset[]>([]);
  const [newlyUnlockedItems, setNewlyUnlockedItems] = useState<Set<string>>(new Set());

  const [isStatusMonitorOpen, setIsStatusMonitorOpen] = useState(true);
  const [isDevelopmentBlueprintOpen, setIsDevelopmentBlueprintOpen] = useState(true);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [diagnosticsReport, setDiagnosticsReport] = useState<string>('');


  const currentUserName = "龙公子"; 
  const apiKeyIsAvailable = !!settings.apiKey && settings.apiKey.trim() !== "";

  const fetchBodyDevelopment = useCallback(async () => {
    setIsLoadingBodyDevelopment(true);
    try {
      const devData = await window.api.database.getAllBodyDevelopment();
      setBodyDevelopment(devData || []);
    } catch (error) { console.error("Failed to fetch body development data:", error); setBodyDevelopment([]); } 
    finally { setIsLoadingBodyDevelopment(false); }
  }, []);

  useEffect(() => {
    fetchBodyDevelopment();
    const intervalId = setInterval(fetchBodyDevelopment, 30000); 
    return () => clearInterval(intervalId);
  }, [fetchBodyDevelopment]);

  useEffect(() => {
    const handlePlaybackStateChanged = (newState: Partial<AudioPlaybackState>) => {
        setCurrentAudioState(prev => ({...prev, ...newState}));
    };
    const unsubscribe = window.api.audio.onPlaybackStateChanged(handlePlaybackStateChanged);
    window.api.audio.getPlaybackState().then(initialRemoteState => {
        setCurrentAudioState(prev => ({...prev, ...initialRemoteState}));
    });
    return () => { if (unsubscribe) unsubscribe(); };
  }, []);

  useEffect(() => {
    const audioEl = audioPlayerRef.current;
    if (audioEl) {
        audioEl.volume = currentAudioState.volume;
        audioEl.loop = currentAudioState.isLooping;
        if (currentAudioState.isPlaying && currentAudioState.currentSound) {
            const soundSrc = currentAudioState.currentSound.startsWith('tgc-asset://') || currentAudioState.currentSound.startsWith('at-asset://') ? currentAudioState.currentSound : `/audio/${currentAudioState.currentSound}.mp3`;
            if (audioEl.src !== (soundSrc.startsWith('tgc-asset://') || soundSrc.startsWith('at-asset://') ? soundSrc : window.location.origin + soundSrc)) {
                audioEl.src = soundSrc; audioEl.load(); 
                audioEl.play().catch(e => console.error("Audio play error:", e));
            } else if (audioEl.paused) { audioEl.play().catch(e => console.error("Audio play error (resuming):", e));}
        } else if (!audioEl.paused) { audioEl.pause(); }
    }
  }, [currentAudioState]);

  const fetchAllTGCAssets = useCallback(async () => {
    console.log('🔄 开始加载 TGC 资产...');
    setIsLoadingCMS(true);
    try {
      // 先检查 CMS 数据库中的资产
      console.log('🔍 检查 CMS 数据库中的资产...');
      const cmsProps = await window.api.cms.getCMSItems('props');
      const cmsCostumes = await window.api.cms.getCMSItems('costumes');
      const cmsPoses = await window.api.cms.getCMSItems('poses');

      console.log(`📊 CMS 数据库资产: 道具 ${cmsProps?.length || 0} 个, 服装 ${cmsCostumes?.length || 0} 个, 姿势 ${cmsPoses?.length || 0} 个`);

      // 检查资产的字段
      if (cmsProps && cmsProps.length > 0) {
        console.log('🔍 检查道具字段:', cmsProps[0]);
        const ownerTypes = cmsProps.map(p => ({ id: p.id, name: p.name, owner: p.owner, type: p.type }));
        console.log('🔍 道具 owner 字段:', ownerTypes.slice(0, 3)); // 显示前3个
        console.log('🔍 唯一的 owner 值:', [...new Set(cmsProps.map(p => p.owner))]);
        console.log('🔍 唯一的 type 值:', [...new Set(cmsProps.map(p => p.type))]);
      }

      // 再获取合并后的资产
      const tgcAssets = await window.api.assets.getLoadedAssets();
      console.log('📦 TGC 资产加载结果:', tgcAssets);

      const props = (tgcAssets.props || []) as PropItem[];
      const costumes = (tgcAssets.costumes || []) as CostumeItem[];
      const poses = (tgcAssets.poses || []) as PoseItem[];

      console.log(`📊 合并后资产统计: 道具 ${props.length} 个, 服装 ${costumes.length} 个, 姿势 ${poses.length} 个`);

      // 如果合并后的资产为空，但 CMS 中有数据，直接使用 CMS 数据
      if (props.length === 0 && cmsProps && cmsProps.length > 0) {
        console.log('⚠️ 合并资产为空，直接使用 CMS 数据');

        // 为 CMS 数据添加缺失的 type 字段
        const propsWithType = cmsProps.map(item => ({ ...item, type: 'prop' as const }));
        const costumesWithType = (cmsCostumes || []).map(item => ({ ...item, type: 'costume' as const }));
        const posesWithType = (cmsPoses || []).map(item => ({ ...item, type: 'pose' as const }));

        console.log('🔧 为 CMS 数据添加 type 字段');
        console.log('🔍 修复后的 type 值:', [...new Set([...propsWithType.map(p => p.type), ...costumesWithType.map(c => c.type), ...posesWithType.map(p => p.type)])]);

        console.log('🔧 设置状态数据:', {
          propsCount: propsWithType.length,
          costumesCount: costumesWithType.length,
          posesCount: posesWithType.length
        });

        setPropsItems(propsWithType as PropItem[]);
        setCostumesItems(costumesWithType as CostumeItem[]);
        setPosesItems(posesWithType as PoseItem[]);
        setAllLoadedCMSAssets([...propsWithType, ...costumesWithType, ...posesWithType] as AnyLoadedAsset[]);
      } else {
        setPropsItems(props);
        setCostumesItems(costumes);
        setPosesItems(poses);
        setAllLoadedCMSAssets([...props, ...costumes, ...poses] as AnyLoadedAsset[]);
      }

      setAvailableScripts((tgcAssets.scripts || []) as ScriptAsset[]);
      setAvailableSceneCards((tgcAssets.scene_cards || []) as SceneCardAsset[]);
      setTgcRoleCards((tgcAssets.role_cards || []) as TGCRoleCardAsset[]);
    } catch (error) {
      console.error('❌ 加载 TGC 资产失败:', error);
    }
    finally {
      setIsLoadingCMS(false);
      console.log('✅ TGC 资产加载完成');
    }
  }, []);

  const fetchDbRoleCards = useCallback(async () => {
    try {
      const cards = await window.api.cms.getRolePlayingCards(); 
      setDbRoleCards(cards || []);
    } catch (error) { console.error("Error fetching DB Role Cards:", error); }
  }, []);

  useEffect(() => { fetchAllTGCAssets(); fetchDbRoleCards(); }, [fetchAllTGCAssets, fetchDbRoleCards]);

  const allCombinedRoleCards = useMemo(() => {
    const combined = [...dbRoleCards, ...tgcRoleCards.map(tgcCard => ({
        ...tgcCard,
        initial_status_override_json: tgcCard.initial_status_override_json || '{}',
        persona_snippet_override: tgcCard.persona_snippet_override || '',
        createdAt: new Date().toISOString(), 
        lastModifiedAt: new Date().toISOString(),
    }))];
    const uniqueCards = Array.from(new Map(combined.map(card => [card.id, card])).values());
    return uniqueCards.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0) || a.name.localeCompare(b.name));
  }, [dbRoleCards, tgcRoleCards]);

  const loadInitialATMessages = useCallback(async () => {
    setIsLoadingInitial(true); setCanLoadOlder(true);
    try {
      const initialMsgs = await window.api.database.getAbsoluteTerritoryMessages(INITIAL_MESSAGES_LOAD_COUNT);
      setMessages(initialMsgs || []);
      if ((initialMsgs || []).length < INITIAL_MESSAGES_LOAD_COUNT) setCanLoadOlder(false);
    } catch (error) { console.error(`Error loading initial AT messages:`, error); setMessages([]); setCanLoadOlder(false); } 
    finally { setIsLoadingInitial(false); setTimeout(() => chatAreaRef.current?.scrollToBottom("auto"), 100); }
  }, []);

  useEffect(() => { loadInitialATMessages(); }, [loadInitialATMessages]);

  const loadOlderATMessages = useCallback(async () => {
    if (messages.length === 0 || !canLoadOlder || isLoadingOlder) return;
    setIsLoadingOlder(true);
    try {
      const oldestTimestamp = messages[0].timestamp;
      const olderMsgs = await window.api.database.getAbsoluteTerritoryMessages(OLDER_MESSAGES_LOAD_COUNT, oldestTimestamp);
      if (olderMsgs && olderMsgs.length > 0) setMessages(prev => [...olderMsgs, ...prev]);
      if (!olderMsgs || olderMsgs.length < OLDER_MESSAGES_LOAD_COUNT) setCanLoadOlder(false);
    } catch (error) { console.error(`Error loading older AT messages:`, error); } 
    finally { setIsLoadingOlder(false); }
  }, [messages, canLoadOlder, isLoadingOlder]);

  const handleSaveUserMessage = async (message: ChatMessage) => {
    try {
      const messageWithAvatar = { ...message, avatarPath: settings.user_avatar_path || null };
      const savedMessage = await window.api.database.addAbsoluteTerritoryMessage(messageWithAvatar);
      if (savedMessage) setMessages(prev => [...prev, savedMessage]);
      else console.error(`Failed to save user message (backend returned null).`);
    } catch (err) { console.error(`Exception saving user message:`, err); }
  };
  
  const addSystemMessage = useCallback(async (text: string, isError: boolean = false) => {
    const systemMessage: ChatMessage = { id: crypto.randomUUID(), sender: 'system', senderName: isError ? '系统错误' : '系统提示', text: text, timestamp: new Date().toISOString(), isError };
    await handleSaveUserMessage(systemMessage);
  }, [handleSaveUserMessage]); 

  useEffect(() => {
    if (initialRoleCardIdFromLocation && allCombinedRoleCards.length > 0) {
      const card = allCombinedRoleCards.find(rc => rc.id === initialRoleCardIdFromLocation);
      if (card) {
        setUserSelectedRoleCardId(initialRoleCardIdFromLocation); 
        addSystemMessage(`已激活角色卡：【${card.name}】。林珞姐姐将以此人设与您互动。`);
        if (card.initial_status_override_json) {
          try {
            const statusOverrides = JSON.parse(card.initial_status_override_json);
            setLinLuoStatus(prev => ({...DEFAULT_LINLUO_DETAILED_STATUS, ...prev, ...statusOverrides})); 
          } catch (e) { console.error("Error parsing role card status override:", e); }
        }
        navigate(location.pathname, { replace: true, state: {} }); 
      }
    }
  }, [initialRoleCardIdFromLocation, allCombinedRoleCards, addSystemMessage, navigate, location.pathname]);

   const processSystemUpdates = useCallback(async (updates: LinLuoSystemUpdatePayload) => {
    if (updates.bodyZoneUpdate) {
        const { zone, pointsAdded } = updates.bodyZoneUpdate;
        await fetchBodyDevelopment(); 
        addSystemMessage(`姐姐的【${BODY_ZONE_DISPLAY_NAMES[zone] || zone}】开发度提升 ${pointsAdded} 点！`);
    }
    if (updates.triggeredAchievementId) {
        const achievement = await window.api.database.getAchievementById(updates.triggeredAchievementId);
        if (achievement) addSystemMessage(`恭喜！解锁成就：【${achievement.title}】！`);
    }
    if (updates.unlockedCmsItem) {
        setNewlyUnlockedItems(prev => new Set(prev).add(updates.unlockedCmsItem!.id));
        addSystemMessage(`新内容已解锁：【${updates.unlockedCmsItem.name}】(${updates.unlockedCmsItem.type})！`);
        await fetchAllTGCAssets(); 
    }
  }, [addSystemMessage, fetchBodyDevelopment, fetchAllTGCAssets]);


  const handleCallTerritoryAI = useCallback(
    async (userPromptText?: string, currentMessagesFromCaller?: ChatMessage[], _isRagActive?: boolean, context?: AiCallContext) => {
    if (aiTaskStatus !== 'idle') { console.warn(`AT AI task status is ${aiTaskStatus}. Call skipped.`); return; }
    if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('linluo_thinking');
    if (!isAIServiceReady || !apiKeyIsAvailable) { addSystemMessage(!isAIServiceReady ? "姐姐大人AI服务正在初始化或连接中。" : "请先在【天工阁设置】中配置您的Gemini API Key。"); setAITaskStatus('idle'); if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); return; }
    
    setAITaskStatus('generating_llm');
    try {
      const historyForAI = (currentMessagesFromCaller || messages).filter(msg => msg.sender !== 'system' && msg.sender !== 'system_summary').map(msg => ({ role: msg.sender === 'user' ? 'user' : 'model', parts: [{ text: getCleanText(msg.text) }] }));
      const currentUserQueryText = userPromptText?.trim() || (gameplayMode === 'script' && selectedScript ? "继续。" : "姐姐，您在吗？");
      
      const aiCallContextPayload: AiCallContext = { 
          ...context, history: historyForAI, contextType: 'training_room',
          currentLinLuoDetailedStatus: linLuoStatus, 
          selectedRoleCardId: userSelectedRoleCardId,
          selectedSceneCardId: userSelectedSceneCard?.id || selectedScript?.scenario_card_id || null,
          scriptModeInfo: gameplayMode === 'script' && selectedScript && currentScriptSceneId ? { scriptId: selectedScript.id, currentSceneId: currentScriptSceneId, userChoiceText: userPromptText } : undefined,
      };
      
      const aiResult = await window.api.ai.invokeTerritoryRequest({
          prompt: currentUserQueryText,
          history: historyForAI as any, 
          persona: 'LinLuo', 
          otherTerritoryContext: aiCallContextPayload 
      });
      
      if (aiResult.text !== null) { 
        const { theme: aiTheme, finalText: aiFinalText } = parseMessageTextForTheme(aiResult.text);
        const aiResponseMsg: ChatMessage = {
          id: crypto.randomUUID(), sender: 'ai', senderName: '天工阁主-林珞',
          text: aiFinalText, timestamp: new Date().toISOString(), theme: aiTheme,
          avatarPath: settings.linluo_avatar_path || null,
          isHypothesisProposal: !!aiResult.json?.hypothesisId, 
          hypothesisData: aiResult.json?.hypothesisId ? aiResult.json : undefined
        };
        await handleSaveUserMessage(aiResponseMsg);
      }

      if (aiResult.status) setLinLuoStatus(prev => ({ ...prev, ...aiResult.status }));
      if (aiResult.systemUpdate) processSystemUpdates(aiResult.systemUpdate);
      if (aiResult.scriptChoices) setCurrentScriptChoices(aiResult.scriptChoices);
      if (aiResult.newSceneCardId) {
        const newCard = availableSceneCards.find(sc => sc.id === aiResult.newSceneCardId);
        setUserSelectedSceneCard(newCard || null);
        if (newCard) {
            addSystemMessage(`场景已切换至：【${newCard.name}】。`);
            if(newCard.default_bgm_path && window.api?.audio?.playSound) {
                window.api.audio.playSound(newCard.default_bgm_path as SoundName, true);
            }
        }
      }
      
      if (gameplayMode === 'script' && selectedScript && aiResult.text) { 
        const currentScene = selectedScript.scenes.find(s => s.id === currentScriptSceneId);
        if (currentScene && !currentScene.user_choices && selectedScript.scenes.indexOf(currentScene) < selectedScript.scenes.length - 1) {
            setCurrentScriptSceneId(selectedScript.scenes[selectedScript.scenes.indexOf(currentScene) + 1].id);
        } else if (!currentScene?.user_choices && selectedScript.scenes.indexOf(currentScene!) === selectedScript.scenes.length - 1) {
            addSystemMessage("剧本章节已结束。"); 
        }
      }

    } catch (error: any) { addSystemMessage(`调用姐姐大人时发生错误: ${error.message}`, true); } 
    finally { setAITaskStatus('idle'); if (settings.onAiThinkingStateChange) settings.onAiThinkingStateChange('idle'); }
  }, [messages, settings, isAIServiceReady, apiKeyIsAvailable, aiTaskStatus, linLuoStatus, userSelectedRoleCardId, userSelectedSceneCard, gameplayMode, selectedScript, currentScriptSceneId, addSystemMessage, processSystemUpdates, availableSceneCards]);

  const handleHypothesisFeedback = async (hypothesisId: string, choice: string, modification?: string, originalElements?: any) => {
    const feedbackPrompt = `关于假设 ${hypothesisId} (元素: ${JSON.stringify(originalElements)}), 我选择: ${choice}. ${modification ? '我的修改意见: ' + modification : ''}`;
    await handleSaveUserMessage({ id: crypto.randomUUID(), sender: 'user', text: `(关于假设 ${hypothesisId}，我选择了 '${choice}' 并说明：${modification || '无额外说明'})`, timestamp: new Date().toISOString(), senderName: currentUserName, avatarPath: settings.user_avatar_path});
    await handleCallTerritoryAI(feedbackPrompt, messages, false, { action: 'SUBMIT_XP_HYPOTHESIS_FEEDBACK', feedbackData: { hypothesisId, choice, modification, originalElements } });
  };

  const handleCMSItemSelected = (item: AnyLoadedAsset, type: AssetType) => {
    console.log('🎮 道具点击事件触发:', { item: item.name, type, id: item.id, owner: item.owner });

    let itemPrompt = type === 'prop' ? (item as PropItem).prompt_for_master :
                     type === 'costume' ? (item as CostumeItem).prompt_for_master :
                     type === 'pose' ? (item as PoseItem).prompt_for_master :
                     `使用 ${item.name}`;

    console.log('📝 生成的提示词:', itemPrompt);
    console.log('🖼️ 资产路径:', { icon: item.icon_path, cg: item.cg_image_path });

    const message: ChatMessage = {
      id: crypto.randomUUID(),
      sender: 'user',
      senderName: currentUserName,
      text: itemPrompt,
      timestamp: new Date().toISOString(),
      itemIconPath: item.icon_path,
      itemCgPath: item.cg_image_path,
      avatarPath: settings.user_avatar_path,
    };

    console.log('💬 创建的消息:', message);
    handleSaveUserMessage(message);
    handleCallTerritoryAI(itemPrompt, [...messages, message]);
    if (item.cg_image_path && item.cg_image_path.startsWith('tgc-asset://')) { setCgImageUrl(item.cg_image_path); setShowCgOverlay(true); }
  };

  const handleScriptChoice = (choice: ScriptChoice) => {
    const userMessage: ChatMessage = {
      id: crypto.randomUUID(), sender: 'user', senderName: currentUserName,
      text: choice.text, timestamp: new Date().toISOString(), avatarPath: settings.user_avatar_path,
    };
    handleSaveUserMessage(userMessage);
    setCurrentScriptChoices(null); 
    handleCallTerritoryAI(choice.text, [...messages, userMessage]); 
  };

  const handleSelectScript = (script: ScriptAsset | null) => {
    setSelectedScript(script);
    if (script && script.scenes.length > 0) {
      setCurrentScriptSceneId(script.scenes[0].id); 
      setUserSelectedRoleCardId(script.role_card_id || null); 
      const sceneCardForScript = availableSceneCards.find(sc => sc.id === script.scenario_card_id);
      setUserSelectedSceneCard(sceneCardForScript || null); 
      setCurrentScriptChoices(script.scenes[0].user_choices || null);
      addSystemMessage(`剧本模式已启动：【${script.name}】。`);
      
      if (script.scenes[0].plot_text && !script.scenes[0].user_choices) { 
          handleCallTerritoryAI(script.scenes[0].plot_text); 
      } else if (script.scenes[0].plot_text && script.scenes[0].user_choices) {
          handleCallTerritoryAI(script.scenes[0].plot_text);
      }
    } else {
      setCurrentScriptSceneId(null); setCurrentScriptChoices(null);
      const directorSelectedRoleCard = allCombinedRoleCards.find(rc => rc.id === userSelectedRoleCardId); 
      if (directorSelectedRoleCard) setUserSelectedRoleCardId(directorSelectedRoleCard.id); else setUserSelectedRoleCardId(null);
      const directorSelectedSceneCard = availableSceneCards.find(sc => sc.id === userSelectedSceneCard?.id); 
      if (directorSelectedSceneCard) setUserSelectedSceneCard(directorSelectedSceneCard); else setUserSelectedSceneCard(null);
    }
  };
  
  const handleSelectRoleCardForDirector = (cardId: string | null) => {
    setUserSelectedRoleCardId(cardId);
    const card = allCombinedRoleCards.find(c => c.id === cardId);
    if (card) {
      addSystemMessage(`角色卡已切换为：【${card.name}】。`);
      if(card.initial_status_override_json) {
        try {
            const statusOverrides = JSON.parse(card.initial_status_override_json);
            setLinLuoStatus(prev => ({...DEFAULT_LINLUO_DETAILED_STATUS, ...prev, ...statusOverrides}));
        } catch (e) { console.error("Error parsing role card status override on select:", e); }
      }
    } else {
      addSystemMessage(`已切换回林珞姐姐默认人格。`);
      setLinLuoStatus(DEFAULT_LINLUO_DETAILED_STATUS); 
    }
  };
  
  const handlePouchChangeForSidebar = (pouchType: AssetType) => {
    console.log("AT Page: Active pouch changed in sidebar to -", pouchType);
  };

  const handleRunDiagnostics = async () => {
    try {
      setShowDiagnostics(true);
      setDiagnosticsReport('正在运行诊断...');
      const report = await runTrainingRoomDiagnostics();
      setDiagnosticsReport(report);
    } catch (error) {
      setDiagnosticsReport(`诊断失败: ${error.message}`);
    }
  };
  
  const handleClearHistory = async () => {
    if (window.confirm("确定要焚毁所有私密记录吗？此操作无法撤销！")) {
      const result = await window.api.database.clearAbsoluteTerritoryHistory();
      if (result.success) { setMessages([]); addSystemMessage("所有记录已成功焚毁。"); }
      else { addSystemMessage(`焚毁记录失败: ${result.error}`, true); }
    }
  };

  const handleSaveChatAction = async () => {
    setShowSaveChatModal(false);
    if (!window.api?.fs?.exportChatHistory) { addSystemMessage("错误：导出对话历史功能接口未准备就绪。", true); return; }
    try {
      const result = await window.api.fs.exportChatHistory(messages, saveChatFormat, "绝对领域记录");
      if (result.success) addSystemMessage(`对话已成功导出到: ${result.path}`);
      else addSystemMessage(`导出失败: ${result.error || "未知错误"}`, true);
    } catch (exportError: any) { addSystemMessage(`导出对话时发生意外错误: ${exportError.message}`, true); }
  };

  const currentSelectedRoleCardDetails = allCombinedRoleCards.find(c => c.id === userSelectedRoleCardId);

  return (
    <div className="flex h-full bg-cover bg-center bg-no-repeat relative" style={{ backgroundImage: userSelectedSceneCard?.background_image_path ? `url(${userSelectedSceneCard.background_image_path})` : 'none', backgroundColor: userSelectedSceneCard ? 'rgba(0,0,0,0.5)' : 'var(--color-bg-primary)' }}>
      <div className={`absolute inset-0 bg-black transition-opacity duration-500 ${userSelectedSceneCard ? 'opacity-50' : 'opacity-0'}`} />
      <audio ref={audioPlayerRef} />

      <div className="w-1/4 min-w-[280px] max-w-[360px] p-3 flex flex-col space-y-3 overflow-y-auto custom-scrollbar z-10 backdrop-blur-sm bg-black/30">
        <MagicMirrorDisplay selectedRoleCard={currentSelectedRoleCardDetails || null} linLuoStatus={linLuoStatus} settings={settings}/>
        <StatusMonitorPanel linLuoStatus={linLuoStatus} isOpen={isStatusMonitorOpen} onToggle={() => setIsStatusMonitorOpen(!isStatusMonitorOpen)} />
        <DevelopmentBlueprintPanel
            bodyDevelopment={bodyDevelopment}
            allCMSItems={allLoadedCMSAssets}
            isLoadingBodyDevelopment={isLoadingBodyDevelopment}
            isOpen={isDevelopmentBlueprintOpen}
            onToggle={() => setIsDevelopmentBlueprintOpen(!isDevelopmentBlueprintOpen)}
        />

        {/* 开发模式诊断工具 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
            <button
              onClick={handleRunDiagnostics}
              className="w-full px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm"
            >
              🔍 运行功能诊断
            </button>
          </div>
        )}
      </div>

      <div className="flex-grow flex flex-col z-10 p-3 overflow-hidden relative">
        <TerritoryChat
          ref={chatAreaRef}
          messages={messages}
          onSaveUserMessage={handleSaveUserMessage}
          onUpdateUserMessage={async (msg) => { 
             const updatedMsg = await window.api.database.updateChatMessage(msg); 
             if (updatedMsg) {
                setMessages(prev => prev.map(m => m.id === updatedMsg.id ? updatedMsg : m));
             }
          }} 
          aiTaskStatus={aiTaskStatus}
          currentUserName={currentUserName}
          settings={settings} 
          globalQuickCommands={globalQuickCommands}
          isAIServiceReady={isAIServiceReady}
          onClearHistory={handleClearHistory}
          onReplayCg={(cgPath) => { setCgImageUrl(cgPath); setShowCgOverlay(true); }}
          onAddToCoreMemory={(message) => { const content = getCleanText(message.text); if(content) onAddCoreMemory({ memory_content: content, persona_target: 'LinLuo', memory_type: 'at_snippet', importance: 'medium' }); }}
          onHypothesisFeedback={handleHypothesisFeedback}
          scriptChoices={currentScriptChoices}
          onScriptChoiceMade={handleScriptChoice}
          onStatusUpdate={(status) => {
            console.log('📊 收到状态更新:', status);
            setLinLuoStatus(prev => ({ ...prev, ...status }));
          }}
          onSystemUpdate={(systemUpdate) => {
            console.log('🎯 收到系统更新:', systemUpdate);
            processSystemUpdates(systemUpdate);
          }}
          isLoadingATInitial={isLoadingInitial}
          isLoadingOlderAT={isLoadingOlder}
          canLoadOlderAT={canLoadOlder}
          onLoadOlderATMessages={loadOlderATMessages}
        />
      </div>
      
       <div className={`transition-all duration-300 ease-in-out z-10 ${isSidebarCollapsed ? 'w-14' : 'w-1/4 min-w-[240px] max-w-[320px]'} backdrop-blur-sm bg-black/30`}>
        <AbsoluteTerritorySidebar
            propsItems={propsItems}
            costumesItems={costumesItems}
            posesItems={posesItems}
            roleCards={allCombinedRoleCards}
            onCMSItemSelected={handleCMSItemSelected}
            isLoadingCMS={isLoadingCMS}
            settings={settings}
            currentAudioStateFromPage={currentAudioState}
            onPlaySoundRequest={(sound, loop) => window.api?.audio?.playSound(sound!, loop)}
            onStopSoundRequest={(sound) => window.api?.audio?.stopSound(sound || undefined)}
            onSetVolumeRequest={(vol) => window.api?.audio?.setVolume(vol)}
            onToggleLoopRequest={() => { }}
            linLuoStatus={linLuoStatus}
            currentArmoryOwner={currentArmoryOwner}
            onArmoryOwnerChange={setCurrentArmoryOwner}
            onManageATAssets={() => navigate('/settings')} 
            onOpenRoleCardEditor={(card) => { setEditingRoleCard(card as RolePlayingCard); setIsRoleCardEditModalOpen(true); }}
            onDeleteRoleCard={async (cardId) => { if(window.confirm("确认删除此角色卡？")) { await window.api.cms.deleteRolePlayingCard(cardId); fetchDbRoleCards(); }}}
            gameplayMode={gameplayMode}
            onSetGameplayMode={setGameplayMode}
            availableScripts={availableScripts}
            availableSceneCards={availableSceneCards}
            selectedScript={selectedScript}
            onSelectScript={handleSelectScript}
            userSelectedRoleCardId={userSelectedRoleCardId}
            onRoleCardSelected={handleSelectRoleCardForDirector}
            userSelectedSceneCard={userSelectedSceneCard}
            onSelectSceneCard={setUserSelectedSceneCard}
            onPouchChange={handlePouchChangeForSidebar}
            onOpenAchievementHall={() => document.getElementById('achievement-hall-modal-trigger-placeholder')?.click()}
            currentScriptSceneId={currentScriptSceneId}
            bodyDevelopment={bodyDevelopment}
            newlyUnlockedItems={newlyUnlockedItems}
            setNewlyUnlockedItems={setNewlyUnlockedItems}
        />
      </div>

      <EventCGOverlay imageUrl={cgImageUrl} show={showCgOverlay} onClose={() => setShowCgOverlay(false)} />
      
      {isRoleCardEditModalOpen && (
        <RoleCardEditModal
          isOpen={isRoleCardEditModalOpen}
          onClose={() => { setIsRoleCardEditModalOpen(false); setEditingRoleCard(null); }}
          existingCard={editingRoleCard}
          onSaveSuccess={() => { fetchDbRoleCards(); }}
        />
      )}
      <button id="achievement-hall-modal-trigger-placeholder" style={{display: 'none'}} onClick={() => console.log("Triggered placeholder for Achievement Hall")}></button>

       <GenericModal
        isOpen={showSaveChatModal}
        onClose={() => setShowSaveChatModal(false)}
        title="保存私密记录"
        size="sm"
        footerContent={
          <>
            <button onClick={() => setShowSaveChatModal(false)} className="py-2 px-3 text-xs bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary">取消</button>
            <button onClick={handleSaveChatAction} className="py-2 px-3 text-xs bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover">确认导出</button>
          </>
        }
      >
        <p className="text-sm text-tg-text-secondary mb-3">请选择导出的文件格式：</p>
        <div className="space-y-2">
          <label className="flex items-center space-x-2 p-2 rounded-md hover:bg-tg-bg-tertiary cursor-pointer">
            <input type="radio" name="saveFormat" value="md" checked={saveChatFormat === 'md'} onChange={() => setSaveChatFormat('md')} className="form-radio text-tg-accent-primary bg-tg-bg-tertiary border-tg-border-primary focus:ring-tg-accent-primary"/>
            <span className="text-sm text-tg-text-primary">Markdown (.md)</span>
          </label>
          <label className="flex items-center space-x-2 p-2 rounded-md hover:bg-tg-bg-tertiary cursor-pointer">
            <input type="radio" name="saveFormat" value="html" checked={saveChatFormat === 'html'} onChange={() => setSaveChatFormat('html')} className="form-radio text-tg-accent-primary bg-tg-bg-tertiary border-tg-border-primary focus:ring-tg-accent-primary"/>
            <span className="text-sm text-tg-text-primary">HTML (.html)</span>
          </label>
        </div>
      </GenericModal>

      {/* 诊断结果模态框 */}
      {showDiagnostics && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-tg-bg-secondary border border-tg-border-primary rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-tg-text-primary">🔍 训练室功能诊断报告</h2>
              <button
                onClick={() => setShowDiagnostics(false)}
                className="px-4 py-2 bg-tg-accent-secondary text-white rounded-lg hover:bg-tg-accent-secondary-hover transition-colors"
              >
                关闭
              </button>
            </div>
            <pre className="text-sm text-tg-text-primary bg-tg-bg-tertiary p-4 rounded-lg overflow-auto whitespace-pre-wrap">
              {diagnosticsReport}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};
