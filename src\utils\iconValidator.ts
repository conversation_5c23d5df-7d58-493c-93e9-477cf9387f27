// src/utils/iconValidator.ts
// 验证图标名称是否在 lucide-react 中存在

import { icons } from 'lucide-react';

// 常用的图标名称列表
const COMMON_ICONS = [
  'House',
  'Settings',
  'Package',
  'Shirt',
  'User',
  'Anchor',
  'Archive',
  'Heart',
  'Star',
  'Plus',
  'Minus',
  'X',
  'Check',
  'Search',
  'Edit',
  'Trash2',
  'Download',
  'Upload',
  'RefreshCw',
  'Loader',
  'AlertTriangle',
  'Info',
  'HelpCircle',
  'Shield',
  'Crown',
  'Sparkles',
  'Zap',
  'CircleUser',
  'UserCircle',
  'LoaderCircle',
  'CheckCircle2',
  'Ellipsis',
  'Send',
  'CheckSquare',
  'Undo2',
  'ChevronsUpDown',
  'FolderOpen',
  'FolderPlus',
  'SquareTerminal',
  'Clipboard',
  'Code',
  'Database',
  'GitBranch',
  'MessageSquare',
  'Palette',
  'List',
  'Tag',
  'Sliders',
  'Droplet',
  'Eye',
  'EyeOff'
];

// 验证图标是否存在
export function validateIcon(iconName: string): boolean {
  return iconName in icons;
}

// 验证所有常用图标
export function validateCommonIcons(): {
  valid: string[];
  invalid: string[];
} {
  const valid: string[] = [];
  const invalid: string[] = [];

  COMMON_ICONS.forEach(iconName => {
    if (validateIcon(iconName)) {
      valid.push(iconName);
    } else {
      invalid.push(iconName);
    }
  });

  return { valid, invalid };
}

// 查找相似的图标名称
export function findSimilarIcons(iconName: string): string[] {
  const allIconNames = Object.keys(icons);
  const lowerIconName = iconName.toLowerCase();
  
  return allIconNames.filter(name => 
    name.toLowerCase().includes(lowerIconName) ||
    lowerIconName.includes(name.toLowerCase())
  ).slice(0, 5); // 只返回前5个匹配项
}

// 获取推荐的图标替换
export function getIconRecommendation(iconName: string): string | null {
  const recommendations: Record<string, string> = {
    'LoaderCircle': 'Loader',
    'Loader2': 'Loader',
    'UserCircle': 'CircleUser',
    'Home': 'House',
    'Ship': 'Anchor',
    'Blush': 'Heart',
    'Droplets': 'Droplet',
    'Settings2': 'Settings',
    'SlidersHorizontal': 'Sliders',
    'PersonStanding': 'User',
    'CheckCircle': 'CheckCircle2',
    'RotateCcw': 'RotateCcw',
    'RefreshCcw': 'RefreshCw',
    'EllipsisVertical': 'Ellipsis',
    'MoreVertical': 'Ellipsis',
    'MoreHorizontal': 'Ellipsis',
    'SendHorizontal': 'Send',
    'PlusCircle': 'Plus',
    'ListPlus': 'Plus',
    'ListChecks': 'CheckSquare',
    'Undo': 'Undo2',
    'RotateCw': 'RefreshCw',
    'ExclamationTriangle': 'AlertTriangle',
    'AdjustmentsHorizontal': 'Sliders',
    'UploadCloud': 'Upload',
    'DownloadCloud': 'Download',
    'TerminalSquare': 'SquareTerminal',
    'ArrowPath': 'RotateCw',
    'ClipboardList': 'Clipboard',
    'CodeSquare': 'Code',
    'DatabaseZap': 'Database',
    'FileSearch': 'Search',
    'GitFork': 'GitBranch',
    'MessagesSquare': 'MessageSquare',
    'PencilLine': 'Edit',
    'Paintbrush': 'Palette'
  };

  return recommendations[iconName] || null;
}

// 运行完整的图标验证
export function runIconValidation(): void {
  console.log('🔍 开始验证图标...');
  
  const { valid, invalid } = validateCommonIcons();
  
  console.log(`✅ 有效图标 (${valid.length}):`, valid);
  
  if (invalid.length > 0) {
    console.log(`❌ 无效图标 (${invalid.length}):`, invalid);
    
    invalid.forEach(iconName => {
      const recommendation = getIconRecommendation(iconName);
      const similar = findSimilarIcons(iconName);
      
      console.log(`\n📝 "${iconName}" 的建议:`);
      if (recommendation) {
        console.log(`   推荐替换: ${recommendation}`);
      }
      if (similar.length > 0) {
        console.log(`   相似图标: ${similar.join(', ')}`);
      }
    });
  }
  
  console.log('\n🎉 图标验证完成!');
}

// 在开发模式下自动运行验证
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，避免影响应用启动
  setTimeout(runIconValidation, 1000);
}
