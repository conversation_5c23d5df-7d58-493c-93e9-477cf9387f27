{"name": "tiangong-pavilion-genesis-framework-electron", "version": "1.0.0", "description": "A Windows desktop-style application framework for project management, ideation, and AI-assisted discussion, with SQLite backend.", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "rebuild": "electron-rebuild -f -w better-sqlite3", "postinstall": "electron-rebuild -f -w better-sqlite3"}, "keywords": ["react", "electron", "tailwindcss", "sqlite", "ai", "gemini"], "author": "天工阁", "license": "UNLICENSED", "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/react-beautiful-dnd": "^13.1.8", "@vitejs/plugin-react": "^4.5.1", "electron": "^29.1.5", "electron-builder": "^24.13.3", "@electron/rebuild": "^3.6.0", "vite": "^5.4.19", "vite-plugin-electron": "^0.28.7", "vite-plugin-electron-renderer": "^0.14.5", "vite-plugin-static-copy": "^3.0.0", "typescript": "^5.4.5"}, "dependencies": {"@google/genai": "^0.12.0", "better-sqlite3": "^9.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-router-dom": "^6.25.1", "react-syntax-highlighter": "^15.5.0", "@tiptap/react": "^2.5.7", "@tiptap/pm": "^2.5.7", "@tiptap/starter-kit": "^2.5.7", "@tiptap/extension-highlight": "^2.5.7", "@tiptap/extension-text-style": "^2.5.7", "@tiptap/extension-color": "^2.5.7", "@tiptap/extension-placeholder": "^2.5.7", "react-window": "^1.8.10", "monaco-editor": "^0.49.0", "@monaco-editor/react": "^4.6.0", "react-beautiful-dnd": "^13.1.1", "lucide-react": "^0.417.0"}, "build": {"appId": "com.tiangong.pavilion.genesis", "productName": "天工阁·创世框架", "files": ["dist/**/*", "dist-electron/**/*"], "directories": {"buildResources": "assets", "output": "release/${version}"}, "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}, "asarUnpack": ["node_modules/better-sqlite3/**"]}}