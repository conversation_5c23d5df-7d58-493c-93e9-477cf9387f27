// electron/utils/jsonParser.ts
console.log('JSON_PARSER_TS: File execution started.');

/**
 * Parses a JSON string with custom enhancements:
 * 1. Removes top-level keys starting with an underscore (comments).
 * 2. Joins array-of-strings values into a single string with newlines.
 * @param jsonString The JSON string to parse.
 * @returns The parsed and cleaned JavaScript object, or throws an error if JSO<PERSON> is invalid.
 */
export function parseTgcJson(jsonString: string): any {
  if (typeof jsonString !== 'string') {
    throw new Error('Invalid input: jsonString must be a string.');
  }

  let parsedObject;
  try {
    parsedObject = JSON.parse(jsonString);
  } catch (error: any) {
    console.error('JSON_PARSER_TS: Error parsing JSON string:', error.message);
    throw new Error(`Invalid JSON format: ${error.message}`);
  }

  if (typeof parsedObject !== 'object' || parsedObject === null) {
    return parsedObject; 
  }

  // Step 1: Remove comment keys (top-level only)
  for (const key in parsedObject) {
    if (Object.prototype.hasOwnProperty.call(parsedObject, key) && key.startsWith('_')) {
      delete parsedObject[key];
    }
  }

  // Step 2: Join array-of-strings values (recursively for all levels)
  function processObjectValues(obj: any) {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key];
        if (Array.isArray(value) && value.every(item => typeof item === 'string')) {
          obj[key] = value.join('\n');
        } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) { // Ensure it's an object, not an array (unless it's array of strings handled above)
          processObjectValues(value); // Recurse for nested objects
        } else if (Array.isArray(value)) { // Handle arrays of objects/mixed types
            value.forEach(item => {
                if (typeof item === 'object' && item !== null) {
                    processObjectValues(item);
                }
            });
        }
      }
    }
  }

  processObjectValues(parsedObject);

  console.log('JSON_PARSER_TS: Successfully parsed and processed TGC JSON.');
  return parsedObject;
}

console.log('JSON_PARSER_TS: File execution finished. parseTgcJson defined.');