
// electron/constants/default-organization.ts
console.log('DEFAULT_ORGANIZATION_TS: File execution started.');

declare var global: any; // Added to address TypeScript "Cannot find name 'global'" error

import fs from 'node:fs';
import path from 'node:path';
import os from 'node:os';
import { app } from 'electron'; 
import { parseTgcJson } from '../utils/jsonParser';
import type { Character, Post } from '../../src/types';
import { CORE_POSTS as DEFAULT_POSTS_IMPORTED } from './posts';

const AVATAR_SYSTEM_PREFIX = 'system_packaged/';
const SYSTEM_RESOURCES_PATH_SEGMENT = 'system'; 
const DEFAULTS_PATH_SEGMENT = 'defaults';     
const CHARACTERS_DIR_SEGMENT = 'characters';  

function getDefaultsBasePathForCharacters(): string {
  let charactersDirPath: string;
  const baseAppPath = app.getAppPath();
  
  if (app.isPackaged) {
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Packaged Mode. AppPath: ${baseAppPath}`);
    // Strategy: viteStaticCopy places 'resources/system' into 'dist-electron/system'.
    // In a packaged app, 'dist-electron' is often within a 'resources/app.asar.unpacked' or similar,
    // or directly alongside the main executable.
    // __dirname of the bundled main.js (which is in dist-electron) is a good starting point.
    // global.__dirname is typically the directory of the currently executing script file.
    const mainProcessDir = global.__dirname || path.dirname(app.getPath('exe')); // Changed process.execPath to app.getPath('exe')
    
    // Try path relative to where main.js is (dist-electron)
    charactersDirPath = path.join(mainProcessDir, SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, CHARACTERS_DIR_SEGMENT);
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 1 (relative to main script dir): ${charactersDirPath}`);

    if (!fs.existsSync(charactersDirPath)) {
        // Fallback for when mainProcessDir is the executable dir and dist-electron is a subdir.
        // This can happen if the app isn't asar packed or structure differs.
        charactersDirPath = path.join(mainProcessDir, 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, CHARACTERS_DIR_SEGMENT);
        console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 2 (relative to execPath + dist-electron): ${charactersDirPath}`);
    }
     if (!fs.existsSync(charactersDirPath)) {
        // Fallback for asar.unpacked structure
        // app.getAppPath() is /path/to/your.app/Contents/Resources/app.asar in mac, or similar in win/linux
        // We need to go up from app.asar and then into the unpacked resources
        const asarUnpackedPath = path.join(app.getAppPath(), '..', 'app.asar.unpacked', 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, CHARACTERS_DIR_SEGMENT);
        if (fs.existsSync(asarUnpackedPath)) {
            charactersDirPath = asarUnpackedPath;
            console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 3 (asar.unpacked): ${charactersDirPath}`);
        } else {
            // One more common pattern is that dist-electron is copied directly into Resources
            const resourcesPath = path.join(app.getAppPath(), '..', 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, CHARACTERS_DIR_SEGMENT);
             if (fs.existsSync(resourcesPath)) {
                charactersDirPath = resourcesPath;
                console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Attempt 4 (Resources/dist-electron): ${charactersDirPath}`);
            } else {
                console.warn(`DEFAULT_ORGANIZATION_TS: All fallback paths for characters in packaged mode failed. Path resolution might be incorrect for current packaging structure.`);
            }
        }
    }
  } else { 
    // In development mode, read directly from the source 'resources' directory.
    // app.getAppPath() should return the project root in dev.
    charactersDirPath = path.resolve(baseAppPath, 'resources', SYSTEM_RESOURCES_PATH_SEGMENT, DEFAULTS_PATH_SEGMENT, CHARACTERS_DIR_SEGMENT);
    console.log(`DEFAULT_ORGANIZATION_TS (getDefaultsBasePathForCharacters) - Development Mode: Derived characters path: ${charactersDirPath}`);
  }
  return charactersDirPath;
}


function loadDefaultCharactersSync(): Character[] {
  const charactersDirPath = getDefaultsBasePathForCharacters();
  console.log(`DEFAULT_ORGANIZATION_TS (loadDefaultCharactersSync): Attempting to synchronously load characters from ${charactersDirPath}`);
  const loadedCharacters: Character[] = [];

  try {
    if (!fs.existsSync(charactersDirPath)) {
      console.error(`DEFAULT_ORGANIZATION_TS: CRITICAL - Characters directory NOT FOUND at resolved path: ${charactersDirPath}. This path is derived based on app packaging status. Returning empty array for DEFAULT_CHARACTERS.`);
      return [];
    }
    console.log(`DEFAULT_ORGANIZATION_TS: Characters directory EXISTS at: ${charactersDirPath}`);

    const files = fs.readdirSync(charactersDirPath);
    console.log(`DEFAULT_ORGANIZATION_TS: Found ${files.length} files/dirs in ${charactersDirPath}. Filtering for '.character.pack.json'...`);
    if (files.length === 0) console.warn(`DEFAULT_ORGANIZATION_TS: No files found in characters directory ${charactersDirPath}.`);


    for (const file of files) {
      if (file.endsWith('.character.pack.json')) {
        const filePath = path.join(charactersDirPath, file);
        console.log(`DEFAULT_ORGANIZATION_TS: Processing character file: ${filePath}`);
        let fileContent: string;
        try {
          fileContent = fs.readFileSync(filePath, 'utf-8');
          console.log(`DEFAULT_ORGANIZATION_TS: Successfully READ file ${file}. Content length: ${fileContent.length}`);
        } catch (readErr: any) {
          console.error(`DEFAULT_ORGANIZATION_TS: Failed to READ character file ${filePath}. Reason: ${readErr.message}. Skipping.`);
          continue;
        }
        
        try {
          const charData = parseTgcJson(fileContent) as Omit<Character, 'persona_prompt'> & { persona_prompt: string | string[] };
          console.log(`DEFAULT_ORGANIZATION_TS: Successfully PARSED JSON for ${file}. ID: ${charData?.id}, Name: ${charData?.name}`);
          
          if (charData && typeof charData === 'object' && charData.id && charData.name && charData.type === 'character') {
            let finalAvatarPath = charData.avatar_path;
            if (finalAvatarPath && typeof finalAvatarPath === 'string' &&
                !finalAvatarPath.startsWith('http') && 
                !finalAvatarPath.startsWith('app-avatar://') && 
                !finalAvatarPath.startsWith(AVATAR_SYSTEM_PREFIX)) {
              const cleanedRelativePath = finalAvatarPath.startsWith('/') ? finalAvatarPath.substring(1) : finalAvatarPath;
              finalAvatarPath = `${AVATAR_SYSTEM_PREFIX}${cleanedRelativePath.replace(/\\/g, '/')}`;
            } else if (finalAvatarPath && typeof finalAvatarPath === 'string') {
              finalAvatarPath = finalAvatarPath.replace(/\\/g, '/');
            }

            const personaPromptString = Array.isArray(charData.persona_prompt) 
              ? charData.persona_prompt.join('\n') 
              : (typeof charData.persona_prompt === 'string' ? charData.persona_prompt : '');

            loadedCharacters.push({
              ...charData,
              avatar_path: finalAvatarPath || null,
              persona_prompt: personaPromptString,
            } as Character);
            console.log(`DEFAULT_ORGANIZATION_TS: VALIDATED and ADDED character: ${charData.name} (ID: ${charData.id}), Avatar: ${finalAvatarPath}`);
          } else {
            console.warn(`DEFAULT_ORGANIZATION_TS: Invalid or incomplete character data structure in ${file} after parsing. ID: ${charData?.id}, Name: ${charData?.name}, Type: ${charData?.type}. Skipping.`);
          }
        } catch (parseErr: any) {
          console.error(`DEFAULT_ORGANIZATION_TS: Failed to PARSE JSON for character file ${filePath}. Reason: ${parseErr.message}. Content snippet (first 100 chars): "${fileContent.substring(0,100)}". Skipping.`);
        }
      } else {
         console.log(`DEFAULT_ORGANIZATION_TS: Skipping file (does not match suffix '.character.pack.json'): ${file}`);
      }
    }
  } catch (err: any) {
    console.error(`DEFAULT_ORGANIZATION_TS: CRITICAL - Failed to access or read characters directory ${charactersDirPath}. Reason: ${err.message}. Returning empty array.`);
    return [];
  }
  console.log(`DEFAULT_ORGANIZATION_TS: Successfully loaded ${loadedCharacters.length} default characters.`);
  if(loadedCharacters.length === 0) {
    console.warn("DEFAULT_ORGANIZATION_TS: WARNING - No characters were loaded. Participants panel will be empty. Please check viteStaticCopy in vite.config.ts and the resolved charactersDirPath logs.");
  }
  return loadedCharacters;
}

export const DEFAULT_POSTS: Post[] = DEFAULT_POSTS_IMPORTED;
export const DEFAULT_CHARACTERS: Character[] = loadDefaultCharactersSync();

console.log('DEFAULT_ORGANIZATION_TS: File execution finished. DEFAULT_POSTS and DEFAULT_CHARACTERS exported.');
console.log(`DEFAULT_ORGANIZATION_TS: Exported ${DEFAULT_POSTS.length} posts and ${DEFAULT_CHARACTERS.length} characters.`);

// Log details of each loaded character for verification
if (DEFAULT_CHARACTERS.length > 0) {
    console.log("DEFAULT_ORGANIZATION_TS: Verifying loaded characters...");
    DEFAULT_CHARACTERS.forEach(char => {
        console.log(`  Character Loaded - ID: ${char.id}, Name: ${char.name}, Avatar: ${char.avatar_path}, Default Post ID: ${char.default_post_id}, Persona Snippet: ${(char.persona_prompt || '').substring(0, 50)}...`);
    });
} else {
    console.warn("DEFAULT_ORGANIZATION_TS: No characters were loaded into DEFAULT_CHARACTERS array. This will affect participant lists.");
}
