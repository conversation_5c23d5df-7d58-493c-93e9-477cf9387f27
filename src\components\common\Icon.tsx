
// src/components/common/Icon.tsx
import React from 'react';
import { icons, type LucideProps, ShieldX as FallbackIcon, AlertTriangle, HelpCircle } from 'lucide-react';

// 在开发模式下验证图标
if (process.env.NODE_ENV === 'development') {
  import('../../utils/iconValidator').then(({ runIconValidation }) => {
    runIconValidation();
  });
}

// Standard icon sizes for consistency
export const ICON_SIZES = {
  xs: 12,    // 0.75rem - Very small icons
  sm: 16,    // 1rem - Small icons
  md: 20,    // 1.25rem - Default size
  lg: 24,    // 1.5rem - Large icons
  xl: 32,    // 2rem - Extra large icons
  '2xl': 48, // 3rem - Very large icons
} as const;

export type IconSize = keyof typeof ICON_SIZES | number | string;

// Define props type, where name property is the union of all lucide-react icon names
export interface IconProps extends Omit<LucideProps, 'name' | 'size'> {
  name: keyof typeof icons;
  size?: IconSize;
  fallback?: 'shield' | 'alert' | 'help' | 'none'; // Allow different fallback options
  showTooltipOnError?: boolean; // Show tooltip when fallback is used
}

// 移除图标映射表，直接使用原始图标名称

export const Icon: React.FC<IconProps> = ({
  name,
  color,
  size,
  className,
  fallback = 'shield',
  showTooltipOnError = false,
  ...props
}) => {
  // 直接使用原始图标名称，不进行映射
  let LucideIcon = icons[name];
  let actualIconName = name;
  let isUsingFallback = false;

  // If still not found, use fallback
  if (!LucideIcon) {
    isUsingFallback = true;

    switch (fallback) {
      case 'alert':
        LucideIcon = AlertTriangle;
        break;
      case 'help':
        LucideIcon = HelpCircle;
        break;
      case 'none':
        return null;
      case 'shield':
      default:
        LucideIcon = FallbackIcon;
        break;
    }

    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Icon Component] Warning: Icon with name "${String(name)}" not found. Using fallback "${fallback}".`);
    }
  }

  // Resolve size to actual pixel value
  const resolvedSize = typeof size === 'string' && size in ICON_SIZES
    ? ICON_SIZES[size as keyof typeof ICON_SIZES]
    : size || ICON_SIZES.md;

  const iconProps = {
    color: color || "currentColor",
    size: resolvedSize,
    className: isUsingFallback && showTooltipOnError
      ? `${className || ''} cursor-help`
      : className,
    title: isUsingFallback && showTooltipOnError
      ? `图标 "${String(name)}" 未找到，显示替代图标`
      : props.title,
    ...props
  };

  return <LucideIcon {...iconProps} />;
};
