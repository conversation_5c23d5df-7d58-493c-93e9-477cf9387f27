// electron/services/aiCodeAssistService.js
console.log('AI_CODE_ASSIST_SERVICE_JS: File execution started.');

import * as aiKernelService from './aiKernelService'; 
import * as dbService from './databaseService'; 
import { GEMINI_TEXT_MODEL } from '../../src/config/globalConfig'; 

const constructBasePromptContext = async (context) => {
    let promptContextText = "";
    if (context.filePath) {
        promptContextText += `\n\n[[当前文件上下文: ${context.filePath}]]`;
    }
    if (context.language) {
        promptContextText += `\n[[编程语言: ${context.language}]]`;
    }
    if (context.projectId && typeof dbService.findRelevantMemories === 'function' && typeof aiKernelService.embedContentInternal === 'function' && context.queryForMemory) {
        try {
            const appSettings = await dbService.getSettings();
            if (appSettings && appSettings.apiKey && appSettings.embeddingModel) {
                 const queryEmbedding = await aiKernelService.embedContentInternal( 
                    context.queryForMemory, 
                    'RETRIEVAL_QUERY', 
                    undefined, 
                    appSettings.apiKey 
                    // embeddingModel is handled by kernelService
                );

                if (typeof queryEmbedding !== 'string' && queryEmbedding !== null) {
                    const relevantMemories = await dbService.findRelevantMemories(
                        queryEmbedding,
                        {
                            personaTarget: 'XiaoLan', 
                            projectContextId: context.projectId,
                            contextType: 'code_assistance',
                            currentTopicKeywords: context.keywordsForMemory || [],
                            desiredMemoryTypes: ['technical_snippet', 'coding_preference', 'api_usage_example', 'architecture_decision', 'debug_log_solution']
                        },
                        3 
                    );

                    let memoryContext = "";
                    if (relevantMemories && relevantMemories.direct && relevantMemories.direct.length > 0) {
                        memoryContext += "\n\n[[相关核心记忆碎片 (供你参考)]]:\n";
                        relevantMemories.direct.forEach(mem => {
                            memoryContext += `- ${mem.memory_content} (重要性: ${mem.importance}, 类型: ${mem.memory_type || '未知'})\n`;
                        });
                    }
                    promptContextText += memoryContext;
                }
            } else {
                console.warn("AI_CODE_ASSIST_SERVICE: API key or embedding model not configured. Skipping memory retrieval for RAG context.");
            }
        } catch (memError) {
            console.error("AI_CODE_ASSIST_SERVICE: Error retrieving memories for context:", memError);
        }
    }
    return promptContextText;
};

/**
 * Generates code based on context and instruction.
 * @param {GenerateCodeContext} context - The context for code generation.
 * @returns {Promise<string>} The generated code.
 */
export async function generateCode(context) {
    console.log("AI_CODE_ASSIST_SERVICE: generateCode called with context:", context);
    const { currentSelection, surroundingCode, language, userInstruction, filePath, projectId } = context;

    const basePromptContext = await constructBasePromptContext({ 
        filePath, language, projectId, 
        queryForMemory: userInstruction + (currentSelection || "") + (surroundingCode || ""),
        keywordsForMemory: (userInstruction.match(/\b\w+\b/g) || []).slice(0,5)
    });

    let prompt = `你是一位专业、精通多种编程语言的AI代码助手“小岚”。请根据用户的指令和提供的上下文生成或修改代码。
你的目标是提供准确、高效、可读性强的代码。

[[用户指令]]:
${userInstruction}
`;

    if (currentSelection) {
        prompt += `
[[当前选中的代码片段 (待修改或作为参考)]]:
\`\`\`${language}
${currentSelection}
\`\`\`
`;
    }

    if (surroundingCode) {
        prompt += `
[[选中代码周围的上下文代码 (供参考)]]:
\`\`\`${language}
${surroundingCode}
\`\`\`
`;
    }
    
    prompt += basePromptContext;
    prompt += "\n请直接输出生成的代码块，如果需要多个文件或片段，请使用清晰的注释（如 // FILE: path/to/file.ext）来区分。避免在代码之外添加不必要的解释，除非用户明确要求。";
    
    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) return "错误：API Key未配置。";

        const response = await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.3, topK: 40, topP: 0.95 }, 
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
        const fenceRegex = /^```(?:\w*\n)?([\s\S]*?)\n?```$/;
        const match = response.match(fenceRegex);
        return match && match[1] ? match[1].trim() : response.trim();
    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (generateCode) Error:", error);
        return `代码生成失败: ${error.message}`;
    }
}

export async function generateModifiedCode(context) {
    console.log("AI_CODE_ASSIST_SERVICE: generateModifiedCode called for filePath:", context.filePath);
    const { filePath, userInstruction, originalCode } = context;
    const language = filePath.split('.').pop()?.toLowerCase() || 'plaintext';

    const prompt = `
你是一位精通代码重构和修改的AI代码助手“小岚”。
你的任务是根据用户的指令，对提供的原始代码进行修改，并返回修改后的【完整文件内容】。

[[用户指令]]:
${userInstruction}

[[文件路径]]:
${filePath}

[[编程语言]]:
${language}

[[原始代码 (完整内容)]]:
\`\`\`${language}
${originalCode}
\`\`\`

请严格按照用户指令修改上述原始代码。
你的输出【必须且只能是】修改后的【完整文件代码】，不要包含任何额外的解释、聊天对话、或Markdown标记（例如 \`\`\`${language} ... \`\`\` 这样的代码块标记也不要）。
确保返回的代码可以直接写入文件替换原有内容。
如果用户指令不清晰或无法安全执行，请返回原始代码并附带一条简短的注释说明原因，例如：
// AI_COMMENT: 用户指令不清晰，无法执行修改。
// [原始代码...]
`;

    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) {
            return { error: "错误：API Key未配置。" };
        }
        
        const responseText = await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.2, topK: 30, topP: 0.9 },
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
        
        return { newCode: responseText.trim() };

    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (generateModifiedCode) Error:", error);
        return { error: `代码修改失败: ${error.message}` };
    }
}


export async function explainCode(context) {
    console.log("AI_CODE_ASSIST_SERVICE: explainCode called with context:", context);
    const { codeToExplain, language, filePath, projectId } = context;

    const basePromptContext = await constructBasePromptContext({ 
        filePath, language, projectId,
        queryForMemory: codeToExplain,
        keywordsForMemory: (codeToExplain.match(/\b\w+\b/g) || []).slice(0,10)
    });

    const prompt = `你是一位AI代码助手“小岚”。请用简洁明了的语言解释以下 ${language} 代码片段的功能和主要逻辑。
如果可能，请提供一个简单的使用示例或说明其在典型场景中的作用。

[[待解释的代码片段]]:
\`\`\`${language}
${codeToExplain}
\`\`\`
${basePromptContext}

请专注于代码本身，提供清晰、准确的解释。
`;

    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) return "错误：API Key未配置。";
        
        return await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.5 }, 
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (explainCode) Error:", error);
        return `代码解释失败: ${error.message}`;
    }
}

export async function generateDocForCode(context) {
    console.log("AI_CODE_ASSIST_SERVICE: generateDocForCode called with context:", context);
    const { codeToDoc, language, filePath, projectId } = context;
    
    const basePromptContext = await constructBasePromptContext({ 
        filePath, language, projectId,
        queryForMemory: codeToDoc,
         keywordsForMemory: (codeToDoc.match(/\b\w+\b/g) || []).filter(kw => kw.length > 2).slice(0,5)
    });

    const prompt = `你是一位AI代码助手“小岚”。请为以下 ${language} 代码片段生成标准格式的文档注释 (例如 JSDoc, Python Docstrings, JavaDoc等，根据语言选择合适的风格)。
文档应清晰描述其功能、参数（如有）、返回值（如有）和任何重要的注意事项。

[[待生成文档的代码片段]]:
\`\`\`${language}
${codeToDoc}
\`\`\`
${basePromptContext}

请仅输出生成的文档注释块，使其可以直接插入到代码中。
`;
    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) return "错误：API Key未配置。";

        return await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.2 }, 
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (generateDocForCode) Error:", error);
        return `文档生成失败: ${error.message}`;
    }
}

export async function reviewCode(context) {
    console.log("AI_CODE_ASSIST_SERVICE: reviewCode called with context:", context);
    const { codeToReview, language, filePath, projectId } = context;

    const basePromptContext = await constructBasePromptContext({ 
        filePath, language, projectId,
        queryForMemory: codeToReview
    });

    const prompt = `你是一位经验丰富的AI代码审查员“小岚”。请仔细审查以下 ${language} 代码片段，并提供反馈。
你的反馈应包括：
1.  潜在的Bug或逻辑错误。
2.  代码风格和可读性方面的改进建议。
3.  性能优化建议（如果适用）。
4.  遵循的最佳实践或安全注意事项。

[[待审查的代码片段]]:
\`\`\`${language}
${codeToReview}
\`\`\`
${basePromptContext}

请提供具体、可操作的建议。如果代码质量很好，也请说明。
`;
    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) return "错误：API Key未配置。";
        
        return await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.6 }, 
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (reviewCode) Error:", error);
        return `代码审查失败: ${error.message}`;
    }
}

export async function analyzeErrorLog(context) {
    console.log("AI_CODE_ASSIST_SERVICE: analyzeErrorLog called with context:", context);
    const { errorLog, language, filePath, projectId, surroundingCode } = context;

    const basePromptContext = await constructBasePromptContext({ 
        filePath, language, projectId,
        queryForMemory: errorLog + (surroundingCode || ""),
        keywordsForMemory: (errorLog.match(/\b[A-Za-z_][A-Za-z0-9_]*Error\b|\b\w+Exception\b/g) || [] )
    });

    let prompt = `你是一位AI调试助手“小岚”。我遇到了一个错误，这是相关的日志：

[[错误日志]]:
\`\`\`
${errorLog}
\`\`\`
`;
    if (surroundingCode) {
        prompt += `
[[可能相关的代码上下文 (${language || '未知语言'})]]:
\`\`\`${language || ''}
${surroundingCode}
\`\`\`
`;
    }
    prompt += basePromptContext;
    prompt += `
请帮助我分析这个错误日志：
1.  指出错误的根本原因可能是什么。
2.  提供一些可能的解决方案或调试步骤。
3.  如果日志信息不足，请指出还需要哪些额外信息。
`;
    try {
        const appSettings = await dbService.getSettings();
        if (!appSettings.apiKey) return "错误：API Key未配置。";
        
        return await aiKernelService.generateContentInternal( 
            [{ role: 'user', parts: [{ text: prompt }] }],
            { temperature: 0.7 }, 
            GEMINI_TEXT_MODEL,
            appSettings.apiKey
        );
    } catch (error) {
        console.error("AI_CODE_ASSIST_SERVICE (analyzeErrorLog) Error:", error);
        return `错误日志分析失败: ${error.message}`;
    }
}

console.log('AI_CODE_ASSIST_SERVICE_JS: File execution finished. Exports configured.');