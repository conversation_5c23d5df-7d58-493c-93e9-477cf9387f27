// src/pages/TaskCockpitPage.tsx
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate, useOutletContext, Link } from 'react-router-dom'; // Added Link
import type { 
    Project, Task, AppSettings, TaskResourceLink, ChatMessage as AppChatMessage, CommandExecutionEvent, FileNode as AppFileNode, 
    CoreMemory, AILearningLog, RouteUserIntentResponse, AiCallContext, ProjectWorkspacePageOutletContext
} from '@/types';
import { TaskBriefingPanel } from '@/components/task_cockpit/TaskBriefingPanel';
import { MainInteractionStream } from '@/components/task_cockpit/MainInteractionStream';
import { DynamicContextArea } from '@/components/task_cockpit/DynamicContextArea';
import { Icon } from '@/components/common/Icon'; 
import { TaskCard } from '@/components/TaskCard'; // Added for default task list

export interface StreamLogEntry {
  id: string;
  type: 'log_chunk';
  timestamp: string;
  logType: 'stdout' | 'stderr' | 'system';
  text: string;
  command?: string; 
  systemPid?: number; 
}

export interface StreamCodeDiff {
  id: string;
  type: 'code_diff';
  timestamp: string;
  originalCode: string;
  modifiedCode: string;
  language: string;
  filePath?: string; 
  userInstruction?: string; 
}
export interface StreamTaskUpdate {
  id: string;
  type: 'task_update';
  timestamp: string;
  message: string;
  taskId?: string;
  newStatus?: Task['status'];
}

export type StreamItem = AppChatMessage | StreamLogEntry | StreamCodeDiff | StreamTaskUpdate;

export const TaskCockpitPage: React.FC = () => {
  const context = useOutletContext<ProjectWorkspacePageOutletContext>();
  const { project: currentProjectFromContext, settings, isAIServiceReady, onUpdateTaskStatusInApp, onOpenWisdomPouch } = context;
  
  const { projectId, taskId } = useParams<{ projectId: string; taskId?: string }>();
  const navigate = useNavigate();

  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [interactionStream, setInteractionStream] = useState<StreamItem[]>([]);
  const [isAiProcessing, setIsAiProcessing] = useState(false);

  const [rightPaneView, setRightPaneView] = useState<'code_editor' | 'log_viewer' | 'knowledge_viewer' | null>(null);
  const [rightPaneFile, setRightPaneFile] = useState<{ path: string; content: string } | null>(null);
  const [rightPaneLogs, setRightPaneLogs] = useState<StreamLogEntry[]>([]); 
  const [rightPaneKnowledgeContent, setRightPaneKnowledgeContent] = useState<string | null>(null);
  const [currentEditorSelection, setCurrentEditorSelection] = useState<string>('');
  const [projectTasksForDefaultView, setProjectTasksForDefaultView] = useState<Task[]>([]);


  const commandOutputListeners = useRef<Map<string, { unsubscribe: () => void, commandString: string }>>(new Map());

  const addSystemMessageToStream = useCallback((text: string, isError: boolean = false) => {
    const systemMessage: AppChatMessage = {
      id: crypto.randomUUID(),
      sender: 'system',
      senderName: isError ? '系统错误' : '系统提示',
      text: text,
      timestamp: new Date().toISOString(),
      isError: isError,
    };
    setInteractionStream(prev => [...prev, systemMessage]);
  }, []);

  useEffect(() => {
    if (!projectId) {
      setError("项目ID缺失。");
      setIsLoading(false);
      return;
    }
    
    if (!currentProjectFromContext || currentProjectFromContext.id !== projectId) {
      setIsLoading(true); 
      return;
    }

    // Fetch all tasks for the project for the default left panel view
    const fetchAllProjectTasks = async () => {
      if (currentProjectFromContext.tasks && currentProjectFromContext.tasks.length > 0) {
        setProjectTasksForDefaultView(currentProjectFromContext.tasks);
      } else if (typeof window.api?.tasks?.getTasksByProjectId === 'function') {
        try {
          const allTasks = await window.api.tasks.getTasksByProjectId(projectId);
          setProjectTasksForDefaultView(allTasks || []);
        } catch (err) {
          console.error("Failed to fetch tasks for default view:", err);
          setError("加载项目任务列表失败。");
        }
      }
    };
    fetchAllProjectTasks();


    if (!taskId) { 
        setCurrentTask(null);
        setIsLoading(false);
        if (interactionStream.length === 0) { 
          addSystemMessageToStream("欢迎来到任务驾驶舱。请从左侧列表选择一个任务以开始，或直接在此下达通用指令。");
        }
        return;
    }
    
    const task = (currentProjectFromContext.tasks || []).find(t => t.task_id === taskId);
    if (!task) {
      window.api.tasks.getTaskById(taskId).then(fetchedTask => {
        if (fetchedTask) {
          setCurrentTask(fetchedTask);
        } else {
          setError(`任务 (ID: ${taskId}) 在项目 "${currentProjectFromContext.name}" 中未找到。`);
        }
        setIsLoading(false);
      }).catch(fetchErr => {
        setError(`获取任务详情失败: ${fetchErr.message}`);
        setIsLoading(false);
      });
    } else {
      setCurrentTask(task);
      setIsLoading(false);
    }
  }, [projectId, taskId, currentProjectFromContext, addSystemMessageToStream, interactionStream.length]);
  
  const handleCommandStreamEvent = useCallback((event: CommandExecutionEvent) => {
    const listenerInfo = commandOutputListeners.current.get(event.internalPid);
    const logEntry: StreamLogEntry = {
      id: crypto.randomUUID(),
      type: 'log_chunk',
      timestamp: new Date().toISOString(),
      logType: event.type === 'stdout' ? 'stdout' : event.type === 'stderr' ? 'stderr' : 'system',
      text: event.data || (event.type === 'exit' ? `命令结束，退出码: ${event.code ?? 'N/A'}${event.signal ? `, 信号: ${event.signal}` : ''}` : (event.error ? `命令执行错误: ${event.error}` : '未知命令事件')),
      command: listenerInfo?.commandString, 
      systemPid: event.systemPid,
    };
    setInteractionStream(prev => [...prev, logEntry]);
    if (rightPaneView !== 'log_viewer') setRightPaneLogs([]); 
    setRightPaneView('log_viewer'); 
    setRightPaneLogs(prevLogs => [...prevLogs, logEntry].slice(-100)); 

    if (event.type === 'exit' || event.type === 'error_event') {
      const unsubscribeFunc = listenerInfo?.unsubscribe;
      if (typeof unsubscribeFunc === 'function') {
        unsubscribeFunc();
      }
      commandOutputListeners.current.delete(event.internalPid);
    }
  }, [rightPaneView]);

  useEffect(() => {
    if (typeof window.api?.command?.onEvent === 'function') {
      const unsubscribeGlobal = window.api.command.onEvent(handleCommandStreamEvent);
      return () => {
        unsubscribeGlobal();
        commandOutputListeners.current.forEach(listener => {
          if (typeof listener.unsubscribe === 'function') {
            listener.unsubscribe();
          }
        });
        commandOutputListeners.current.clear();
      };
    }
  }, [handleCommandStreamEvent]);


  const handleSendCommand = async (commandText: string) => {
    if (!currentProjectFromContext) {
        addSystemMessageToStream("错误：当前项目信息丢失，无法处理指令。", true);
        return;
    }

    const userMessage: AppChatMessage = {
      id: crypto.randomUUID(),
      sender: 'user',
      senderName: settings.user_avatar_path ? '我' : '用户', 
      text: commandText,
      timestamp: new Date().toISOString(),
      avatarPath: settings.user_avatar_path,
    };
    setInteractionStream(prev => [...prev, userMessage]);
    setIsAiProcessing(true);

    try {
      if (!window.api?.ai?.routeUserIntent) {
        throw new Error("AI 意图路由服务 (routeUserIntent) 不可用。");
      }
      const contextForAI: AiCallContext = { 
        projectId: currentProjectFromContext.id,
        taskId: currentTask?.task_id || undefined, 
        currentFileInRightPanePath: rightPaneFile?.path,
        selectedTextInRightPaneEditor: currentEditorSelection,
        currentRightPaneView: rightPaneView,
        workspaceRoot: currentProjectFromContext.sourceCodePath || undefined,
      };

      const response: RouteUserIntentResponse = await window.api.ai.routeUserIntent(commandText, contextForAI);
      
      if (response.type === 'error') {
        addSystemMessageToStream(response.data.message || 'AI处理指令时发生未知错误。', true);
      } else if (response.type === 'chat') {
        const aiChatMessage: AppChatMessage = {
          id: response.interactionId || crypto.randomUUID(),
          sender: 'ai',
          senderName: response.aiPersona === 'LinLuo' ? '林珞姐姐' : response.aiPersona === 'XiaoLan' ? '小岚' : 'AI助手',
          text: response.data.text,
          timestamp: new Date().toISOString(),
          avatarPath: response.aiPersona === 'LinLuo' ? settings.linluo_avatar_path : (response.aiPersona === 'XiaoLan' ? settings.xiaolan_avatar_path : null),
        };
        setInteractionStream(prev => [...prev, aiChatMessage]);
      } else if (response.type === 'code_diff') {
        const diffItem: StreamCodeDiff = {
          id: response.interactionId || crypto.randomUUID(),
          type: 'code_diff',
          timestamp: new Date().toISOString(),
          originalCode: response.data.originalCode,
          modifiedCode: response.data.modifiedCode,
          language: response.data.language,
          filePath: response.data.filePath,
          userInstruction: response.data.userInstruction,
        };
        setInteractionStream(prev => [...prev, diffItem]);
        if (response.data.filePath && response.data.originalCode !== undefined) { 
             setRightPaneFile({ path: response.data.filePath, content: response.data.originalCode });
        }
        setRightPaneView('code_editor'); 
      } else if (response.type === 'task_update') {
        const updateItem: StreamTaskUpdate = {
          id: response.interactionId || crypto.randomUUID(),
          type: 'task_update',
          timestamp: new Date().toISOString(),
          message: response.data.message,
          taskId: response.data.taskId,
          newStatus: response.data.newStatus,
        };
        setInteractionStream(prev => [...prev, updateItem]);
        if (response.data.taskId && response.data.newStatus && response.data.taskId === currentTask?.task_id && onUpdateTaskStatusInApp) {
          const updatedTask = await onUpdateTaskStatusInApp(response.data.taskId, response.data.newStatus, currentProjectFromContext.id);
          if (updatedTask) setCurrentTask(updatedTask);
        }
      } else if (response.type === 'file_content' && response.data.path && typeof response.data.content === 'string') {
        setRightPaneFile({ path: response.data.path, content: response.data.content });
        setRightPaneView('code_editor');
        addSystemMessageToStream(`文件 "${response.data.path.split('/').pop()}" 已在右侧打开。`);
      } else if (response.type === 'log_stream_started' && response.data.internalPid) {
         if (typeof window.api?.command?.onEvent === 'function') { 
            commandOutputListeners.current.set(response.data.internalPid, { 
                unsubscribe: () => { /* Global listener will handle this now */ },
                commandString: `${response.data.command} ${(response.data.args || []).join(' ')}`
            });
            addSystemMessageToStream(`命令执行开始 (InternalPID: ${response.data.internalPid}): ${response.data.command} ${(response.data.args || []).join(' ')}`);
            if (rightPaneView !== 'log_viewer') setRightPaneLogs([]); 
            setRightPaneView('log_viewer');
         } else {
            addSystemMessageToStream("错误: 实时日志监听器或命令执行服务不可用。", true);
         }
      } else if (response.type === 'unknown_intent'){
         addSystemMessageToStream(`小岚不太明白您的意思：“${response.originalCommand || commandText}”。要不换个说法？`, true);
      }

    } catch (err: any) {
      addSystemMessageToStream(`处理指令失败: ${err.message}`, true);
    } finally {
      setIsAiProcessing(false);
    }
  };

  const handleResourceLinkClick = async (resource: TaskResourceLink) => {
    if (resource.resource_type === 'file_path' && currentProjectFromContext?.sourceCodePath) {
      const fullPath = await window.api.utils.combinePaths(currentProjectFromContext.sourceCodePath, resource.resource_identifier);
      try {
        const contentResult = await window.api.fs.readFile(fullPath); 
        if (typeof contentResult === 'string') {
          setRightPaneFile({ path: fullPath, content: contentResult });
          setRightPaneView('code_editor');
        } else {
          addSystemMessageToStream(`无法读取文件 "${resource.resource_identifier}": ${contentResult.error}`, true);
        }
      } catch (e:any) {
        addSystemMessageToStream(`打开文件 "${resource.resource_identifier}" 失败: ${e.message}`, true);
      }
    } else if (resource.resource_type === 'url') {
      window.open(resource.resource_identifier, '_blank');
    } else {
      addSystemMessageToStream(`资源类型 "${resource.resource_type}" 暂不支持在驾驶舱内直接打开。`);
    }
  };

  const handleApplyCodeChanges = async (originalPath: string, newCode: string, userInstruction?: string) => {
    if (!currentProjectFromContext || !originalPath ) { 
        addSystemMessageToStream("无法应用代码更改：项目或文件路径丢失。", true);
        return;
    }
    if (typeof window.api?.fs?.archiveFileVersion !== 'function' || typeof window.api?.fs?.writeFile !== 'function') {
      addSystemMessageToStream("文件操作服务不可用，无法存档或保存更改。", true);
      return;
    }

    setIsAiProcessing(true);
    try {
        const originalContentResult = rightPaneFile?.path === originalPath ? rightPaneFile.content : await window.api.fs.readFile(originalPath);
        
        let originalContent: string;
        if (typeof originalContentResult === 'string') {
          originalContent = originalContentResult;
        } else if (originalContentResult && 'error' in originalContentResult) {
           throw new Error(`无法获取原始文件内容: ${originalContentResult.error}`);
        } else {
           throw new Error("无法获取原始文件内容，返回类型未知。");
        }

        await window.api.fs.archiveFileVersion(originalPath, originalContent, userInstruction || "AI 代码修改 (驾驶舱)");
        const writeResult = await window.api.fs.writeFile(originalPath, newCode);
        if (!writeResult.success) throw new Error(writeResult.error || "写入文件失败。");
        
        setRightPaneFile({ path: originalPath, content: newCode }); 
        addSystemMessageToStream(`代码修改已成功应用于 "${originalPath.split('/').pop()}" 并已保存。原始版本已存档。`);
        
        if (typeof window.api?.database?.addLearningLog === 'function') {
            const logEntry: Omit<AILearningLog, 'log_id' | 'timestamp'> = {
                ai_persona: 'xiaolan', 
                task_type: 'code_modification_applied',
                triggering_input_summary: `驾驶舱应用代码修改: ${originalPath}`,
                context_snapshot: JSON.stringify({filePath: originalPath, userInstruction}),
                ai_processing_summary: "Code diff applied by user from cockpit",
                ai_generated_output_summary: `Applied code changes to ${originalPath} (new length: ${newCode.length})`,
                user_feedback_explicit: 'accepted_code_diff_cockpit', 
                success_metric_value: 1.0,
                file_path: originalPath,
            };
            await window.api.database.addLearningLog(logEntry);
        }

    } catch (e: any) {
        addSystemMessageToStream(`应用代码修改失败: ${e.message}`, true);
    } finally {
        setIsAiProcessing(false);
    }
  };


  if (isLoading) {
    return <div className="p-6 text-center text-tg-text-secondary"><Icon name="arrow-path" className="w-8 h-8 animate-spin mx-auto"/> 加载驾驶舱数据中...</div>;
  }
  if (error) {
    return <div className="p-6 text-center text-red-400 bg-red-900/20 rounded-md"><Icon name="exclamation-triangle" className="w-8 h-8 mx-auto mb-2"/>错误: {error}</div>;
  }
  if (!currentProjectFromContext && projectId) { 
    return <div className="p-6 text-center text-tg-text-secondary">未能加载项目信息。</div>;
  }

  return (
    <div className="flex h-full w-full bg-tg-bg-primary text-tg-text-primary overflow-hidden">
      <div className="w-1/4 min-w-[300px] max-w-[400px] h-full flex-shrink-0 border-r border-tg-border-primary p-3 flex flex-col">
        {currentTask ? (
          <TaskBriefingPanel task={currentTask} onResourceLinkClick={handleResourceLinkClick} />
        ) : (
          <div className="h-full flex flex-col bg-tg-bg-secondary p-3 rounded-lg shadow-md border border-tg-border-primary overflow-hidden">
            <h2 className="text-lg font-semibold text-tg-text-primary mb-1 flex items-center">
              <Icon name="list-bullet" className="w-5 h-5 mr-2 text-tg-accent-primary" />
              项目任务总览
            </h2>
            <p className="text-xs text-tg-text-secondary mb-3">
              {currentProjectFromContext?.name || "当前项目"}的全部任务。点击任务可进入驾驶舱。
            </p>
            {projectTasksForDefaultView.length === 0 && (
              <div className="flex-grow flex items-center justify-center text-tg-text-placeholder">
                此项目暂无任务。
              </div>
            )}
            <div className="flex-grow overflow-y-auto custom-scrollbar space-y-2 pr-1">
              {projectTasksForDefaultView.sort((a,b) => a.priority - b.priority || new Date(a.created_at).getTime() - new Date(b.created_at).getTime()).map(task => (
                <TaskCard
                  key={task.task_id}
                  task={task}
                  onClick={() => navigate(`/project/${projectId}/cockpit/${task.task_id}`)}
                  onEnterCockpit={(e) => { e.stopPropagation(); navigate(`/project/${projectId}/cockpit/${task.task_id}`);}}
                  onDragStart={() => {}}
                />
              ))}
            </div>
          </div>
        )}
      </div>
      <div className={`flex-grow h-full flex flex-col p-3 ${!currentTask ? 'w-2/3' : ''}`}>
        <MainInteractionStream
          streamItems={interactionStream}
          onSendCommand={handleSendCommand}
          isAiProcessing={isAiProcessing}
          settings={settings}
          currentUserName={settings.user_avatar_path ? "我" : "用户"}
          onApplyDiffChanges={handleApplyCodeChanges}
          onOpenWisdomPouch={onOpenWisdomPouch}
        />
      </div>
      <div className={`h-full flex-shrink-0 border-l border-tg-border-primary p-3 flex flex-col ${!currentTask ? 'w-1/3 min-w-[350px] max-w-[500px]' : 'w-1/3 min-w-[350px] max-w-[500px]'}`}>
        <DynamicContextArea
          viewType={rightPaneView}
          fileData={rightPaneFile}
          logData={rightPaneLogs}
          knowledgeData={rightPaneKnowledgeContent}
          onEditorContentChange={(newContent) => {
            if (rightPaneFile) setRightPaneFile(f => f ? {...f, content: newContent} : null);
          }}
          onEditorSelectionChange={setCurrentEditorSelection}
          onSwitchView={setRightPaneView}
          settings={settings}
        />
      </div>
    </div>
  );
};