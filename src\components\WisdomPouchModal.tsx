// src/components/WisdomPouchModal.tsx
import React, { useState } from 'react';
import type { Project, NoteItem, ImportanceLevel } from '@/types';
import { WisdomPouchType } from '@/types'; // WisdomPouchType is now both value and type
import { NotesList } from '@/components/NotesList';
import { Icon } from '@/components/common/Icon';
import { GenericModal } from '@/components/GenericModal';

interface WisdomPouchModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project;
  addNoteToProject: (pouchType: WisdomPouchType, text: string, importance?: ImportanceLevel) => void;
  updateNoteInProject: (pouchType: WisdomPouchType, updatedNote: NoteItem) => void;
  deleteNoteFromProject: (pouchType: WisdomPouchType, noteId: string) => void;
  onSendNoteToChat: (noteText: string) => void;
}

type ActivePouchTab = WisdomPouchType; // Use WisdomPouchType directly as type

const POUCH_DEFINITIONS_MODAL = [
  { type: WisdomPouchType.INSPIRATION, label: "灵感池", iconName: "Lightbulb" as const }, // Added Inspiration
  { type: WisdomPouchType.BUGS, label: "Bug备忘录", iconName: "Bug" as const },
  { type: WisdomPouchType.COMMANDS, label: "快捷指令板", iconName: "Terminal" as const }
];

export const WisdomPouchModal: React.FC<WisdomPouchModalProps> = ({
  isOpen,
  onClose,
  project,
  addNoteToProject,
  updateNoteInProject,
  deleteNoteFromProject,
  onSendNoteToChat
}) => {
  const [activeTab, setActiveTab] = useState<ActivePouchTab>(WisdomPouchType.INSPIRATION); // Default to Inspiration

  const getTabClass = (tabType: ActivePouchTab) => {
    return `py-2 px-4 text-sm font-medium rounded-t-md transition-colors focus:outline-none flex items-center
            ${activeTab === tabType 
                ? 'bg-tg-bg-secondary text-tg-accent-primary border-b-2 border-tg-accent-primary' 
                : 'text-tg-text-secondary hover:bg-tg-bg-tertiary hover:text-tg-text-primary border-b-2 border-transparent'}`;
  };

  return (
    <GenericModal
      isOpen={isOpen}
      onClose={onClose}
      title="智慧锦囊"
      size="xl" 
      footerContent={
        <button onClick={onClose} className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary">
          关闭
        </button>
      }
    >
      <div className="flex flex-col h-[70vh]"> 
        <nav className="flex border-b border-tg-border-primary mb-3">
          {POUCH_DEFINITIONS_MODAL.map(pouch => (
            <button
              key={pouch.type}
              onClick={() => setActiveTab(pouch.type as ActivePouchTab)} // Cast to ActivePouchTab
              className={getTabClass(pouch.type as ActivePouchTab)} // Cast to ActivePouchTab
            >
              <Icon name={pouch.iconName} className="w-5 h-5 mr-2" />
              {pouch.label}
            </button>
          ))}
        </nav>

        <div className="flex-grow overflow-y-auto p-1">
          {activeTab === WisdomPouchType.INSPIRATION && (
            <NotesList
              title="灵感池"
              notes={project.inspirationNotes}
              addNote={(text, importance) => addNoteToProject(WisdomPouchType.INSPIRATION, text, importance)}
              updateNote={(note) => updateNoteInProject(WisdomPouchType.INSPIRATION, note)}
              deleteNote={(noteId) => deleteNoteFromProject(WisdomPouchType.INSPIRATION, noteId)}
              icon={<Icon name="Lightbulb" className="w-5 h-5 mr-2 text-tg-accent-primary" />}
              pouchType={WisdomPouchType.INSPIRATION}
              onSendNoteToChat={onSendNoteToChat}
            />
          )}
          {activeTab === WisdomPouchType.BUGS && (
            <NotesList
              title="Bug备忘录/优化清单"
              notes={project.bugMemoNotes}
              addNote={(text, importance) => addNoteToProject(WisdomPouchType.BUGS, text, importance)}
              updateNote={(note) => updateNoteInProject(WisdomPouchType.BUGS, note)}
              deleteNote={(noteId) => deleteNoteFromProject(WisdomPouchType.BUGS, noteId)}
              icon={<Icon name="Bug" className="w-5 h-5 mr-2 text-tg-danger" />}
              pouchType={WisdomPouchType.BUGS}
              onSendNoteToChat={onSendNoteToChat}
            />
          )}
          {activeTab === WisdomPouchType.COMMANDS && (
            <NotesList
              title="快捷指令板"
              notes={project.quickCommandsNotes}
              addNote={(text) => addNoteToProject(WisdomPouchType.COMMANDS, text)}
              updateNote={(note) => updateNoteInProject(WisdomPouchType.COMMANDS, note)}
              deleteNote={(noteId) => deleteNoteFromProject(WisdomPouchType.COMMANDS, noteId)}
              icon={<Icon name="Terminal" className="w-5 h-5 mr-2 text-tg-success" />}
              pouchType={WisdomPouchType.COMMANDS}
              onSendNoteToChat={onSendNoteToChat}
            />
          )}
        </div>
      </div>
    </GenericModal>
  );
};