// src/components/ActionPlanModal.tsx
import React from 'react';
import type { ActionPlanModalProps, TaskCreationData, TaskPriority } from '@/types';
import { GenericModal } from '@/components/GenericModal';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

const getPriorityLabel = (priority?: TaskPriority): string => {
  if (priority === undefined) return '常规';
  switch (priority) {
    case 0: return '十万火急';
    case 1: return '优先处理';
    case 2: return '常规';
    case 3: return '低优先级';
    default: return '未知';
  }
};

const getPriorityColorClass = (priority?: TaskPriority): string => {
  if (priority === undefined) return 'bg-sky-500';
  switch (priority) {
    case 0: return 'bg-red-600';
    case 1: return 'bg-yellow-500 text-yellow-900';
    case 2: return 'bg-sky-500';
    case 3: return 'bg-gray-500';
    default: return 'bg-gray-400';
  }
};

export const ActionPlanModal: React.FC<ActionPlanModalProps> = ({
  isOpen,
  onClose,
  tasks,
  onApprovePlan,
  isApproving,
}) => {
  return (
    <GenericModal
      isOpen={isOpen}
      onClose={onClose}
      title="行动计划草案预览"
      size="lg"
      footerContent={
        <>
          <button 
            onClick={onClose} 
            className="py-2 px-4 text-sm bg-tg-bg-hover text-tg-text-secondary rounded border border-tg-border-primary hover:bg-tg-bg-tertiary"
            disabled={isApproving}
          >
            取消
          </button>
          <button 
            onClick={onApprovePlan} 
            className="py-2 px-4 text-sm bg-tg-accent-primary text-white rounded hover:bg-tg-accent-primary-hover flex items-center disabled:opacity-60"
            disabled={isApproving || tasks.length === 0}
          >
            {isApproving ? (
              <Icon name="Loader2" className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Icon name="CheckCircle" className="w-4 h-4 mr-2" />
            )}
            {isApproving ? "批准中..." : "批准并写入罗盘"}
          </button>
        </>
      }
    >
      {tasks.length === 0 ? (
        <p className="text-tg-text-secondary text-center py-4">
          AI未能解析出可执行的任务，或需求内容不包含明确的行动项。
        </p>
      ) : (
        <div className="space-y-3 max-h-[60vh] overflow-y-auto custom-scrollbar pr-2">
          <p className="text-sm text-tg-text-secondary mb-2">
            AI已将需求分解为以下任务草案。请审阅后批准写入“神谕罗盘”。
          </p>
          {tasks.map((task, index) => (
            <div key={index} className="p-3 bg-tg-bg-tertiary rounded-md border border-tg-border-primary">
              <div className="flex justify-between items-start mb-1">
                <h4 className="text-md font-semibold text-tg-text-primary flex items-center">
                  <Icon name="List" className="w-4 h-4 mr-2 text-tg-accent-secondary flex-shrink-0"/>
                  {task.title || "未命名任务"}
                </h4>
                <span className={`px-2 py-0.5 text-xs rounded-full text-white ${getPriorityColorClass(task.priority)}`}>
                  {getPriorityLabel(task.priority)}
                </span>
              </div>
              {task.description && (
                <p className="text-xs text-tg-text-secondary ml-6 whitespace-pre-wrap">
                  {task.description}
                </p>
              )}
            </div>
          ))}
        </div>
      )}
    </GenericModal>
  );
};