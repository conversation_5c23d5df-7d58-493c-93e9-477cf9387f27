// src/components/bridge_command_center/CaptainsDecisionCenter.tsx
import React from 'react';
import type { Project, Task, TaskStatus } from '@/types';
import { Link } from 'react-router-dom';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

interface CaptainsDecisionCenterProps {
  tasks: Task[];
  project: Project | null;
  onUpdateTaskStatus: (taskId: string, newStatus: TaskStatus) => Promise<void>;
}

export const CaptainsDecisionCenter: React.FC<CaptainsDecisionCenterProps> = ({ tasks, project, onUpdateTaskStatus }) => {
  const [isUpdating, setIsUpdating] = React.useState<string | null>(null); // Store taskId being updated

  const decisionTasks = tasks.filter(t => 
    t.status === 'pending_review' || 
    t.status === 'blocked' || 
    (t.status === 'todo' && t.priority === 0)
  );

  const handleAction = async (taskId: string, newStatus: TaskStatus) => {
    setIsUpdating(taskId);
    try {
      await onUpdateTaskStatus(taskId, newStatus);
    } catch (error) {
      console.error(`Error updating task ${taskId} to ${newStatus}:`, error);
      // Error state can be handled by parent or displayed here
    } finally {
      setIsUpdating(null);
    }
  };

  const getActionForTask = (task: Task) => {
    if (task.status === 'pending_review') {
      return { 
        text: '[ 同意并通过 ]', 
        color: 'bg-green-500 hover:bg-green-600', 
        iconName: "CheckCircle2" as any, 
        actionStatus: 'done' as TaskStatus
      };
    }
    if (task.status === 'blocked') {
      return { 
        text: '[ 解除阻塞 ]', 
        color: 'bg-blue-500 hover:bg-blue-600', 
        iconName: "Lightbulb" as any, 
        actionStatus: 'todo' as TaskStatus 
      };
    }
    if (task.status === 'todo' && task.priority === 0) {
      return {
        text: '[ 标记为处理中 ]',
        color: 'bg-orange-500 hover:bg-orange-600',
        iconName: "Lightbulb" as any, 
        actionStatus: 'doing' as TaskStatus
      };
    }
    return null;
  };

  if (!project) {
     return (
      <div className="p-4 bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary h-full flex flex-col items-center justify-center">
        <p className="text-sm text-tg-text-placeholder">请先在指挥中心选择一个项目。</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary h-full flex flex-col">
      <h2 className="text-xl font-semibold text-tg-text-primary mb-3 flex items-center">
        <Icon name="AlertTriangle" className="w-6 h-6 mr-2 text-yellow-400" />
        舰长决策中心 - {project.name}
      </h2>
      {decisionTasks.length === 0 ? (
        <p className="text-sm text-tg-text-placeholder flex-grow flex items-center justify-center">此项目当前无待处理的审批或决策任务。</p>
      ) : (
        <div className="space-y-3 overflow-y-auto custom-scrollbar pr-1 flex-grow">
          {decisionTasks.map(task => {
            const actionDetails = getActionForTask(task);
            if (!actionDetails) return null;
            const isCurrentTaskUpdating = isUpdating === task.task_id;
            return (
              <div key={task.task_id} className="p-3.5 bg-tg-bg-tertiary rounded-md border border-tg-border-primary/50 shadow hover:shadow-lg transition-shadow">
                <h3 className="text-md font-medium text-tg-text-primary mb-1 truncate" title={task.title}>{task.title}</h3>
                <p className="text-xs text-tg-text-secondary mb-1.5">
                  状态: {task.status === 'pending_review' ? '待审批' : (task.status === 'blocked' ? '已阻塞' : '高优待办')}
                </p>
                <p className="text-xs text-tg-text-placeholder mb-2 line-clamp-2">
                  {task.description || '暂无详细描述。'}
                </p>
                <div className="flex items-center space-x-2">
                    <button 
                        onClick={() => handleAction(task.task_id, actionDetails.actionStatus)}
                        disabled={isCurrentTaskUpdating}
                        className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-white transition-colors ${actionDetails.color} disabled:opacity-60`}
                    >
                        {isCurrentTaskUpdating ? <Icon name="Loader2" className="w-4 h-4 mr-1.5 animate-spin"/> : <Icon name={actionDetails.iconName} className="w-4 h-4 mr-1.5"/>}
                        {isCurrentTaskUpdating ? "处理中..." : actionDetails.text}
                    </button>
                    <Link 
                        to={`/project/${task.project_id}/task-board?taskId=${task.task_id}`}
                        className="px-3 py-1.5 text-xs font-medium rounded-md text-tg-text-secondary border border-tg-border-primary hover:bg-tg-bg-hover hover:text-tg-text-primary"
                    >
                        查看详情
                    </Link>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};