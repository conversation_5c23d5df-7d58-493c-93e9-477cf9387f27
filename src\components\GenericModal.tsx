// src/components/GenericModal.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon'; // Updated to use new central Icon

interface GenericModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footerContent?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnClickOutside?: boolean;
}

export const GenericModal: React.FC<GenericModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footerContent,
  size = 'md',
  closeOnClickOutside = true,
}) => {
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100] p-4 backdrop-blur-sm"
      onClick={closeOnClickOutside ? onClose : undefined}
      role="dialog"
      aria-modal="true"
      aria-labelledby="generic-modal-title"
    >
      <div
        className={`bg-tg-bg-secondary p-5 md:p-6 rounded-lg shadow-xl border border-tg-border-primary max-h-[90vh] flex flex-col ${sizeClasses[size]} w-full`}
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 pb-3 border-b border-tg-border-primary">
          <h3 id="generic-modal-title" className="text-xl font-semibold text-tg-accent-primary">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="p-1.5 rounded-full text-tg-text-placeholder hover:bg-tg-bg-hover hover:text-tg-text-primary transition-colors"
            title="关闭模态框"
            aria-label="关闭模态框"
          >
            <Icon name="X" className="w-5 h-5" />
          </button>
        </div>

        <div className="overflow-y-auto flex-grow pr-1 custom-scrollbar">
          {children}
        </div>

        {footerContent && (
          <div className="mt-5 pt-4 border-t border-tg-border-primary flex justify-end space-x-3">
            {footerContent}
          </div>
        )}
      </div>
    </div>
  );
};