// src/components/common/VisualUtils.tsx
// 视觉层次工具组件和函数

import React from 'react';
import { Icon, IconProps, IconSize } from './Icon';

// 视觉层次级别定义
export type VisualLevel = 'primary' | 'secondary' | 'tertiary' | 'accent' | 'muted';
export type VisualContext = 'navigation' | 'content' | 'action' | 'status' | 'decoration';

// 根据视觉层次和上下文获取图标尺寸
export const getIconSizeForContext = (context: VisualContext, level: VisualLevel): IconSize => {
  const sizeMap: Record<VisualContext, Record<VisualLevel, IconSize>> = {
    navigation: {
      primary: 'xl',
      secondary: 'lg', 
      tertiary: 'md',
      accent: '2xl',
      muted: 'sm'
    },
    content: {
      primary: 'lg',
      secondary: 'md',
      tertiary: 'sm',
      accent: 'xl',
      muted: 'xs'
    },
    action: {
      primary: 'lg',
      secondary: 'md',
      tertiary: 'sm',
      accent: 'xl',
      muted: 'xs'
    },
    status: {
      primary: 'md',
      secondary: 'sm',
      tertiary: 'xs',
      accent: 'lg',
      muted: 'xs'
    },
    decoration: {
      primary: 'lg',
      secondary: 'md',
      tertiary: 'sm',
      accent: '2xl',
      muted: 'xs'
    }
  };

  return sizeMap[context][level];
};

// 根据视觉层次获取颜色类
export const getColorClassForLevel = (level: VisualLevel, context: VisualContext): string => {
  const colorMap: Record<VisualLevel, Record<VisualContext, string>> = {
    primary: {
      navigation: 'text-tg-accent-primary',
      content: 'text-tg-text-primary',
      action: 'text-white',
      status: 'text-tg-accent-primary',
      decoration: 'text-gradient'
    },
    secondary: {
      navigation: 'text-tg-text-primary',
      content: 'text-tg-text-secondary',
      action: 'text-tg-text-primary',
      status: 'text-tg-text-secondary',
      decoration: 'text-tg-accent-primary'
    },
    tertiary: {
      navigation: 'text-tg-text-secondary',
      content: 'text-tg-text-placeholder',
      action: 'text-tg-text-secondary',
      status: 'text-tg-text-placeholder',
      decoration: 'text-tg-text-secondary'
    },
    accent: {
      navigation: 'text-gradient',
      content: 'text-tg-accent-primary',
      action: 'text-tg-accent-primary',
      status: 'text-tg-success',
      decoration: 'text-gradient'
    },
    muted: {
      navigation: 'text-tg-text-placeholder',
      content: 'text-tg-text-placeholder',
      action: 'text-tg-text-placeholder',
      status: 'text-tg-text-placeholder',
      decoration: 'text-tg-text-placeholder'
    }
  };

  return colorMap[level][context];
};

// 智能图标组件 - 自动应用视觉层次
interface SmartIconProps extends Omit<IconProps, 'size' | 'className'> {
  level?: VisualLevel;
  context?: VisualContext;
  className?: string;
  glow?: boolean;
  animate?: 'none' | 'spin' | 'pulse' | 'float' | 'bounce';
}

export const SmartIcon: React.FC<SmartIconProps> = ({
  level = 'secondary',
  context = 'content',
  className = '',
  glow = false,
  animate = 'none',
  ...props
}) => {
  const size = getIconSizeForContext(context, level);
  const colorClass = getColorClassForLevel(level, context);
  
  const animationClass = {
    none: '',
    spin: 'animate-spin',
    pulse: 'animate-pulse',
    float: 'animate-float',
    bounce: 'animate-bounce-subtle'
  }[animate];

  const glowClass = glow ? 'glow-on-hover' : '';

  // 如果传入了自定义 className 且包含颜色类，则优先使用自定义颜色
  const hasCustomColor = className && (className.includes('text-') || className.includes('!text-'));
  const finalColorClass = hasCustomColor ? '' : colorClass;

  const combinedClassName = `${finalColorClass} ${animationClass} ${glowClass} ${className}`.trim();

  return <Icon {...props} size={size} className={combinedClassName} />;
};

// 视觉层次容器组件
interface VisualContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  level?: VisualLevel;
  variant?: 'card' | 'panel' | 'section' | 'inline';
  glow?: boolean;
  float?: boolean;
  children: React.ReactNode;
}

export const VisualContainer: React.FC<VisualContainerProps> = ({
  level = 'secondary',
  variant = 'card',
  glow = false,
  float = false,
  className = '',
  children,
  ...props
}) => {
  const baseClasses = 'transition-all duration-300';
  
  const variantClasses = {
    card: 'card-enhanced rounded-2xl p-6',
    panel: 'glass-effect rounded-xl p-4 backdrop-blur-md',
    section: 'bg-tg-bg-secondary rounded-lg p-4',
    inline: 'inline-flex items-center space-x-2'
  }[variant];

  const levelClasses = {
    primary: 'shadow-xl border-2 border-tg-accent-primary',
    secondary: 'shadow-lg border border-tg-border-primary',
    tertiary: 'shadow-md border border-tg-border-primary/50',
    accent: 'shadow-2xl border-2 border-gradient bg-gradient-card',
    muted: 'shadow-sm border border-tg-border-primary/30'
  }[level];

  const glowClass = glow ? 'glow-on-hover' : '';
  const floatClass = float ? 'animate-float' : '';
  
  const combinedClassName = `${baseClasses} ${variantClasses} ${levelClasses} ${glowClass} ${floatClass} ${className}`.trim();

  return (
    <div className={combinedClassName} {...props}>
      {children}
    </div>
  );
};

// 视觉层次标题组件
interface VisualHeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: VisualLevel;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  gradient?: boolean;
  children: React.ReactNode;
}

export const VisualHeading: React.FC<VisualHeadingProps> = ({
  level = 'primary',
  size = 'lg',
  gradient = false,
  className = '',
  children,
  ...props
}) => {
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl'
  }[size];

  const levelClasses = {
    primary: 'font-bold',
    secondary: 'font-semibold',
    tertiary: 'font-medium',
    accent: 'font-bold',
    muted: 'font-normal'
  }[level];

  const colorClass = gradient ? 'text-gradient' : getColorClassForLevel(level, 'content');
  
  const combinedClassName = `${sizeClasses} ${levelClasses} ${colorClass} ${className}`.trim();

  return (
    <h1 className={combinedClassName} {...props}>
      {children}
    </h1>
  );
};

export default {
  SmartIcon,
  VisualContainer,
  VisualHeading,
  getIconSizeForContext,
  getColorClassForLevel
};
