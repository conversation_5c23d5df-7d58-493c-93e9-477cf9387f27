// src/types/projectTypes.ts

// 我们将WisdomPouchType定义为一个常量对象。
// 这确保了它在编译后，依然是一个可用的JavaScript对象。
export const WisdomPouchType = {
  INSPIRATION: 'inspiration',
  BUGS: 'bugs',
  COMMANDS: 'commands',
} as const;

// 我们再从这个常量对象中，反向推导出它的联合类型。
// 这为我们的代码提供了完美的类型提示和保护。
export type WisdomPouchType = typeof WisdomPouchType[keyof typeof WisdomPouchType];

// 其他与项目相关的类型定义可以继续放在这里...
// For now, based on "完整替换 ... 的所有内容", we ensure only WisdomPouchType is defined here
// and previous content like NoteItem, MindNode etc. if they were here, are now expected
// to be in their respective more specific type files or re-exported correctly via src/types.ts barrel.
// To be safe and align with "完整替换", we will assume this file *only* contains WisdomPouchType for now.
// If other types were in the original projectTypes.ts, they should be moved to more appropriate files
// or their exports re-evaluated in src/types.ts.

// Re-adding other types that were previously in projectTypes.ts, as "其他与项目相关的类型定义可以继续放在这里..."
// suggests they should remain if they are project-specific.
import type { ImportanceLevel, ChatMessage, KnowledgeTome, GlobalQuickCommandItem, DevelopmentTask, Task } from './'; // Assuming index.ts in the same folder (src/types) re-exports these

export interface NoteItem {
  id: string;
  text: string;
  createdAt: string;
  lastModifiedAt?: string;
  importance?: ImportanceLevel;
  isEditing?: boolean;
}

export interface MindNode {
  id: string;
  text: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
}

export interface MindConnection {
  id: string;
  fromNodeId: string;
  toNodeId: string;
}

export interface ProjectKnowledgeTome {
  id: string;
  projectId?: string; 
  title: string;
  content: string;
  projectCategory: string; 
  tags: string[];
  createdAt: string;
  lastModifiedAt: string;
}

export interface Project {
  id: string;
  name: string;
  coverImageUrl?: string | null;
  createdAt: string;
  lastModifiedAt: string;
  sourceCodePath?: string | null;
  discussionMessages: ChatMessage[];
  inspirationNotes: NoteItem[];
  bugMemoNotes: NoteItem[];
  quickCommandsNotes: NoteItem[];
  mindNodes?: MindNode[];
  mindConnections?: MindConnection[];
  projectKnowledgeTomes?: ProjectKnowledgeTome[];
  projectKnowledgeCategories?: string[]; 
  developmentTasks?: DevelopmentTask[]; 
  tasks?: Task[]; 
  isDeleted?: boolean;
}

export type NewProjectDataForApi = Pick<Project,
  'id' |
  'name' |
  'createdAt' |
  'lastModifiedAt' |
  'isDeleted'
> & {
  coverImageUrl?: string | null; 
  sourceCodePath?: string | null; 
};