// src/components/bridge_command_center/CurrentBattleSituation.tsx
import React from 'react';
import type { Project, Task } from '@/types';
import { Link } from 'react-router-dom';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

interface CurrentBattleSituationProps {
  tasks: Task[]; 
  project: Project | null;
}

const getTaskProgress = (status: Task['status']): number => {
  switch (status) {
    case 'doing': return 50;
    case 'pending_review': return 75;
    case 'todo': return 10;
    case 'blocked': return 5;
    case 'done': return 100;
    default: return 0;
  }
};

export const CurrentBattleSituation: React.FC<CurrentBattleSituationProps> = ({ tasks, project }) => {
  const activeTasks = tasks.filter(t => t.status === 'doing' || t.status === 'pending_review');

  if (!project) {
    return (
      <div className="p-4 bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary h-full flex flex-col items-center justify-center">
        <p className="text-sm text-tg-text-placeholder">请先在指挥中心选择一个项目。</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-tg-bg-secondary rounded-lg shadow-md border border-tg-border-primary h-full flex flex-col">
      <h2 className="text-xl font-semibold text-tg-text-primary mb-3 flex items-center">
        <Icon name="Flame" className="w-6 h-6 mr-2 text-red-500" />
        当前战况 - {project.name}
      </h2>
      {activeTasks.length === 0 ? (
        <p className="text-sm text-tg-text-placeholder flex-grow flex items-center justify-center">此项目暂无进行中或待审核的任务。</p>
      ) : (
        <div className="space-y-3 overflow-y-auto custom-scrollbar pr-1 flex-grow">
          {activeTasks.map(task => (
            <div key={task.task_id} className="p-3 bg-tg-bg-tertiary rounded-md border border-tg-border-interactive/30">
              <div className="flex justify-between items-center mb-1">
                <Link to={`/project/${task.project_id}/task-board?taskId=${task.task_id}`} className="text-sm font-medium text-tg-text-primary hover:text-tg-accent-primary truncate" title={task.title}>
                  {task.title}
                </Link>
                <span className={`text-xs px-2 py-0.5 rounded-full ${task.status === 'doing' ? 'bg-blue-500 text-white' : 'bg-yellow-500 text-yellow-900'}`}>
                  {task.status === 'doing' ? '进行中' : '待审核'}
                </span>
              </div>
              <div className="text-xs text-tg-text-secondary mb-1.5">
                执行者: {task.assignee_id || '未分配'}
              </div>
              <div className="w-full bg-tg-bg-primary rounded-full h-2.5 border border-tg-border-primary">
                <div 
                  className={`h-full rounded-full transition-all duration-300 ease-out ${task.status === 'doing' ? 'bg-blue-500' : 'bg-yellow-500'}`}
                  style={{ width: `${getTaskProgress(task.status)}%` }}
                  role="progressbar"
                  aria-valuenow={getTaskProgress(task.status)}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  aria-label={`任务进度: ${getTaskProgress(task.status)}%`}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};