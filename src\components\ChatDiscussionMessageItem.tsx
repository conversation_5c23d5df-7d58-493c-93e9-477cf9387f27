// src/components/chat/ChatDiscussionMessageItem.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import type { Editor } from '@tiptap/core';
import type { ChatMessage, ExtractedChatMessageItemProps, AppSettings, XPHypothesisChoice, Character, Post, Assignment } from '@/types';
import { WisdomPouchType } from '@/types';
import { Icon } from '@/components/common/Icon';
import { themeDefinitions } from '@/chatConstants';
import { TiptapEditor } from '@/components/tiptap/TiptapEditor';
import { EditorToolbar } from '@/components/tiptap/EditorToolbar';

// Redefined ChatMessageItemProps using a type intersection
type ChatMessageItemProps = ExtractedChatMessageItemProps & {
  settings: AppSettings;
  allAvailableCharacters?: Character[]; 
  allPosts?: Post[]; 
  allAssignments?: Assignment[]; 
  onSendToToDoList?: (message: ChatMessage) => void; 
};


const summaryBlockRegex = /:::总结开始:::\s*\n?(.*?)\n?\s*:::总结结束:::/s;
const atIconRegex = /<img[^>]*alt="圣痕图标"[^>]*>/g;
const proposalMarkerRegex = /\[\[PROPOSE_HYPOTHESIS:.*?\]\]/s;

const getCleanText = (html: string): string => {
    if (typeof DOMParser === 'undefined') {
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return (doc.body.textContent || "").trim();
    } catch (e) {
        console.error("Error in getCleanText:", e);
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
};


export const ChatDiscussionMessageItem: React.FC<ChatMessageItemProps> = ({
    message, isOwnMessage, onUpdateMessage,
    onToggleStar, onTogglePin, onCopy,
    isRichEditingActive, onRequestRichEdit,
    onReplayCg,
    onAddToCoreMemory,
    onResendMessage,
    onRefreshAiResponse,
    onForwardMessage,
    onSendToPouch,
    isWorkspaceContext,
    settings,
    currentUserName,
    onHypothesisFeedback,
    onDecomposeRequirement,
    onConvertToTask,
    allAvailableCharacters, 
    allPosts, 
    allAssignments, 
    onSendToToDoList, 
 }) => {
  const [initialTextBeforeEdit, setInitialTextBeforeEdit] = useState(message.text);
  const tiptapEditorRef = useRef<Editor | null>(null);
  const [showPouchSelectorMenu, setShowPouchSelectorMenu] = useState(false);
  const pouchSelectorButtonRef = useRef<HTMLButtonElement>(null);
  const [xpModificationText, setXpModificationText] = useState('');
  const [showXpModificationInput, setShowXpModificationInput] = useState(false);


  const parseMainContent = (text: string) => text.replace(summaryBlockRegex, '').replace(proposalMarkerRegex, '').trim();
  const parseSummaryContent = (text: string) => {
    const match = text.match(summaryBlockRegex);
    return match ? match[1].replace(/^\*\*本段小结：\*\*\s*\n?/, '').trim() : '';
  };

  const mainContentHtml = parseMainContent(message.text);
  let initialMainContentForTiptap: string = mainContentHtml;

  useEffect(() => {
    if (isRichEditingActive) {
      setInitialTextBeforeEdit(message.text);
    }
  }, [isRichEditingActive, message.text]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
        if (pouchSelectorButtonRef.current && pouchSelectorButtonRef.current.contains(event.target as Node)) {
            return;
        }
        if (showPouchSelectorMenu) {
           setShowPouchSelectorMenu(false);
        }
    };
    if (showPouchSelectorMenu) {
        document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
        document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPouchSelectorMenu]);



  const handleTiptapContentChange = useCallback((newMainHtmlFromEditor: string) => {
    let newFullText = newMainHtmlFromEditor.trim();
    const currentSummary = parseSummaryContent(initialTextBeforeEdit);

    if (currentSummary.trim()) {
      const summaryBlockFormatted = `:::总结开始:::\n**本段小结：**\n${currentSummary.trim()}\n:::总结结束:::`;
      if (newFullText) {
        newFullText += `\n\n${summaryBlockFormatted}`;
      } else {
        newFullText = summaryBlockFormatted;
      }
    }
    if (message.isHypothesisProposal && message.hypothesisData) {
        const proposalMarker = `[[PROPOSE_HYPOTHESIS:${JSON.stringify(message.hypothesisData)}]]`;
        newFullText = `${newFullText.replace(proposalMarkerRegex, '').trim()}\n${proposalMarker}`;
    }
    onUpdateMessage({ messageId: message.id, newHtml: newFullText });
  }, [message.id, onUpdateMessage, initialTextBeforeEdit, message.isHypothesisProposal, message.hypothesisData]);


  const handleEditToggle = () => {
    if (isRichEditingActive) {
      onRequestRichEdit(null);
    } else {
      onRequestRichEdit(message.id);
    }
  };

  const formatTimestamp = (isoString: string) => {
    return new Date(isoString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const themeDef = message.theme ? themeDefinitions[message.theme] : null;

  let avatarSrc: string | null = null;
  let finalSenderName = message.senderName;
  let senderNameColorClass = 'text-tg-accent-secondary';
  let specificBorderColorClass = 'border-tg-border-primary'; 

  if (message.sender === 'user') {
    avatarSrc = settings.user_avatar_path ? `app-avatar://${settings.user_avatar_path}?t=${Date.now()}` : null;
    finalSenderName = currentUserName || message.senderName;
    senderNameColorClass = 'text-blue-300';
  } else if (message.sender === 'ai' || message.sender === 'roundtable_ai') {
    let character: Character | undefined;
    let characterIdToUse = message.characterId;

    if (!characterIdToUse && message.sender === 'ai' && allAvailableCharacters) { 
        if (message.senderName.includes("林珞")) characterIdToUse = "linluo";
        else if (message.senderName.includes("小岚")) characterIdToUse = "xiaolan";
        else if (message.senderName.includes("语镜")) characterIdToUse = "yujing";
    }
    
    if (characterIdToUse && allAvailableCharacters) {
        character = allAvailableCharacters.find(c => c.id === characterIdToUse);
    }

    if (character) {
        const assignment = allAssignments?.find(a => a.character_id === character!.id);
        const post = assignment ? allPosts?.find(p => p.id === assignment.post_id) : null;
        finalSenderName = post ? `[${post.name}] - ${character.name}` : character.name;
        avatarSrc = character.avatar_path ? `app-avatar://${character.avatar_path}?t=${Date.now()}` : null;

        if (character.id === 'linluo') {
            specificBorderColorClass = 'border-[var(--color-linluo-border)]';
            senderNameColorClass = 'text-[var(--color-linluo-name)]';
        } else if (character.id === 'xiaolan') {
            specificBorderColorClass = 'border-[var(--color-xiaolan-border)]';
            senderNameColorClass = 'text-[var(--color-xiaolan-name)]';
        } else if (character.id === 'yujing') { 
            specificBorderColorClass = 'border-[var(--color-yujing-border)]';
            senderNameColorClass = 'text-[var(--color-yujing-name)]';
        }
    } else { 
        finalSenderName = message.senderName; 
        if (message.senderName && message.senderName.includes("林珞")) {
            avatarSrc = settings.linluo_avatar_path ? `app-avatar://${settings.linluo_avatar_path}?t=${Date.now()}` : null;
            specificBorderColorClass = 'border-[var(--color-linluo-border)]';
            senderNameColorClass = 'text-[var(--color-linluo-name)]';
        } else if (message.senderName && message.senderName.includes("小岚")) {
            avatarSrc = settings.xiaolan_avatar_path ? `app-avatar://${settings.xiaolan_avatar_path}?t=${Date.now()}` : null;
            specificBorderColorClass = 'border-[var(--color-xiaolan-border)]';
            senderNameColorClass = 'text-[var(--color-xiaolan-name)]';
        } else if (message.senderName && message.senderName.includes("语镜")) {
            avatarSrc = null; 
            specificBorderColorClass = 'border-[var(--color-yujing-border)]';
            senderNameColorClass = 'text-[var(--color-yujing-name)]';
        }
    }
  }


  if (message.avatarPathOverride) {
    avatarSrc = `app-avatar://${message.avatarPathOverride}?t=${Date.now()}`;
  }

  let messageBubbleClass = 'transition-shadow duration-200 ';
  let borderColorClass = 'border-transparent';

  if (message.sender === 'system_summary') {
    messageBubbleClass += 'bg-tg-bg-tertiary border-dashed border-tg-border-primary text-tg-text-secondary text-sm p-3 rounded-lg self-stretch mx-auto my-2 w-full max-w-full text-left shadow-sm';
  } else if (themeDef) {
    messageBubbleClass += `${themeDef.bubbleClass} self-start`;
    borderColorClass = themeDef.borderColorClass;
  } else if (message.sender === 'system') {
    messageBubbleClass += 'bg-tg-bg-secondary text-tg-text-secondary self-center text-xs italic';
  } else if (isOwnMessage) {
    messageBubbleClass += 'bg-tg-accent-primary text-white self-end';
    borderColorClass = 'border-transparent';
  } else if (message.sender === 'ai' || message.sender === 'roundtable_ai') {
    messageBubbleClass += 'bg-tg-bg-tertiary text-tg-text-primary self-start';
    borderColorClass = specificBorderColorClass; 
  }

  let finalBorderWidthClass = 'border-2';
  if (message.isPinned && !isRichEditingActive && message.sender !== 'system_summary') {
    borderColorClass = 'border-tg-warning';
    finalBorderWidthClass = 'border-2';
  } else if (isOwnMessage || message.sender === 'system' || message.sender === 'system_summary' || themeDef) {
    finalBorderWidthClass = 'border';
  }

  messageBubbleClass += ` ${finalBorderWidthClass} ${borderColorClass}`;

  if (isRichEditingActive) messageBubbleClass += ' ring-2 ring-tg-accent-primary ring-offset-2 ring-offset-tg-bg-secondary shadow-xl';

  const textAlignClass = themeDef || message.sender === 'system_summary' ? 'text-left' : (isOwnMessage ? 'text-right' : (message.sender === 'system' ? 'text-center' : 'text-left'));
  const isATItemUsageMessage = message.sender === 'user' && message.itemIconPath;
  const handleIconClick = () => { if (isATItemUsageMessage && onReplayCg && message.itemCgPath) onReplayCg(message.itemCgPath); };

  let finalHtmlForDisplay: string;
  const mainContentCleanedTextForDisplayCheck = getCleanText(mainContentHtml.replace(atIconRegex, ''));


  if (isATItemUsageMessage && message.itemIconPath && !isRichEditingActive) {
      const iconHtml = `<img src="at-asset://${message.itemIconPath}?t=${Date.now()}" alt="圣痕图标" style="max-width: 24px; max-height: 24px; border-radius: 4px; object-fit: cover; display: inline-block; vertical-align: middle; margin-right: 6px; cursor: pointer;" data-replay-cg="${message.itemCgPath || ''}" title="点击重温CG" />`;
      const originalTextPartHtml = mainContentHtml.replace(atIconRegex, '').trim();
      const originalTextPartCleaned = getCleanText(originalTextPartHtml);

      if (originalTextPartCleaned === "0" || originalTextPartCleaned === "") {
          finalHtmlForDisplay = iconHtml;
      } else {
          finalHtmlForDisplay = iconHtml + originalTextPartHtml;
      }
  } else if (message.sender === 'system_summary') {
      finalHtmlForDisplay = message.text;
  } else {
      if (mainContentCleanedTextForDisplayCheck === "" && !mainContentHtml.includes("<img")) {
          finalHtmlForDisplay = "<p>&nbsp;</p>";
      } else {
          finalHtmlForDisplay = mainContentHtml;
      }
  }


  const handleHypothesisChoiceClick = (choiceId: string) => {
    if (message.hypothesisData && onHypothesisFeedback) {
        if (choiceId === 'B') { setShowXpModificationInput(true); }
        else { onHypothesisFeedback(message.hypothesisData.hypothesisId, choiceId, undefined, message.hypothesisData.elements); setShowXpModificationInput(false); }
    }
  };

  const submitHypothesisModification = () => {
    if (message.hypothesisData && onHypothesisFeedback) {
        onHypothesisFeedback(message.hypothesisData.hypothesisId, 'B', xpModificationText, message.hypothesisData.elements);
        setShowXpModificationInput(false); setXpModificationText('');
    }
  };

  const handleDecompose = () => {
    if (onDecomposeRequirement && message.projectId) {
        const cleanText = getCleanText(mainContentHtml);
        onDecomposeRequirement(cleanText, message.projectId, message.id);
    }
  };

  const handleConvertToTaskInternal = () => {
    if (onConvertToTask) {
        onConvertToTask(message);
    }
  };

  const isLinLuoMessage = (message.sender === 'ai' && message.senderName.includes("林珞")) || (message.sender === 'roundtable_ai' && message.characterId === 'linluo');
  const canDecompose = isLinLuoMessage && !message.isHypothesisProposal && !!onDecomposeRequirement && isWorkspaceContext;


  const actionButtonBaseClass = "p-1.5 text-tg-text-placeholder hover:bg-tg-bg-hover rounded-full transition-opacity";
  const actionIconClass = "w-4 h-4";

  return (
    <div className={`flex mb-1 group relative ${isOwnMessage ? 'flex-row-reverse' : ''} items-start space-x-2 ${isOwnMessage ? 'space-x-reverse' : ''}`}>
      {(message.sender === 'user' || message.sender === 'ai' || message.sender === 'roundtable_ai') && (
        <div className={`flex-shrink-0 mt-1`}>
            {avatarSrc ? <img src={avatarSrc} alt={`${finalSenderName} Avatar`} className="w-8 h-8 rounded-full object-cover shadow-sm"/> : <Icon name="CircleUser" className="w-8 h-8 text-tg-text-placeholder" />}
        </div>
      )}

      <div className={`flex flex-col ${isOwnMessage ? 'items-end' : 'items-start'} max-w-[calc(85%-2.5rem)] md:max-w-[calc(75%-2.5rem)] relative`}>
        {(message.sender === 'user' || message.sender === 'ai' || message.sender === 'roundtable_ai') &&
          !themeDef && !isRichEditingActive && typeof finalSenderName === 'string' && finalSenderName.trim() !== '' &&
          ( <p className={`text-xs mb-1 font-semibold ${senderNameColorClass}`} title={finalSenderName}> {finalSenderName} </p> )}

        <div className={`p-3 rounded-lg shadow-sm ${messageBubbleClass} ${message.sender === 'system_summary' ? 'w-full max-w-full' : ''}`} onClick={(e) => { const target = e.target as HTMLElement; if (target.tagName === 'IMG' && target.dataset.replayCg && onReplayCg) onReplayCg(target.dataset.replayCg); }} >
          {message.isPinned && !isRichEditingActive && message.sender !== 'system_summary' && ( <Icon name="Bookmark" className="w-4 h-4 absolute -top-2 -left-2 text-tg-warning bg-tg-bg-tertiary p-0.5 rounded-full shadow-lg" title="已置顶" /> )}
          {themeDef && !isRichEditingActive && ( <div className={`flex items-center text-sm font-semibold mb-2 pb-1 border-b ${themeDef.borderColorClass} ${themeDef.headerClass}`}> <span className="mr-2 text-lg">{themeDef.icon}</span> <span>{themeDef.label}</span> </div> )}
          {message.sender === 'system_summary' && ( <div className="flex items-center text-sm font-semibold mb-2 text-tg-text-secondary"> <Icon name="File" className="w-4 h-4 mr-2 text-tg-text-placeholder flex-shrink-0" /> {message.senderName} </div> )}
          {parseSummaryContent(message.text) && message.sender !== 'system_summary' && (
            <div className={`p-3 my-1 rounded-md border-2 border-sky-500 bg-sky-700 bg-opacity-30 shadow-md ${isRichEditingActive ? 'opacity-70' : ''}`}>
              <p className="text-base font-bold mb-1.5 text-sky-300 flex items-center"> <Icon name="FileSearch" className="w-5 h-5 mr-2 flex-shrink-0"/> 本段小结： </p>
              <div className="prose-sm prose-invert max-w-none [&_strong]:text-sky-200 [&_em]:text-sky-300 p-1" dangerouslySetInnerHTML={{ __html: parseSummaryContent(message.text) }} style={{ whiteSpace: 'pre-wrap' }} />
            </div>
          )}

          {isRichEditingActive ? ( <> <EditorToolbar editor={tiptapEditorRef.current} /> <TiptapEditor editorRef={tiptapEditorRef} initialContent={initialMainContentForTiptap} onContentChange={handleTiptapContentChange} isEditable={true} /> </>
          ) : ( <div className={`prose-sm max-w-none ${isOwnMessage && !themeDef && message.sender !== 'system_summary' ? 'prose-invert' : (themeDef ? themeDef.textColorClass.replace('text-', 'prose-') : (message.sender === 'system_summary' ? 'prose-tg-text-primary' : 'prose-tg-text-primary'))} [&_strong]:font-bold [&_em]:italic ${themeDef ? themeDef.textColorClass : (message.sender === 'system_summary' ? 'text-tg-text-secondary' : '')} ${message.sender === 'system_summary' ? 'text-sm' : ''} `} style={{ whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: finalHtmlForDisplay }} />
          )}

          {message.isHypothesisProposal && message.hypothesisData && !isOwnMessage && (
            <div className="mt-3 pt-2 border-t border-purple-500/50">
                <p className="text-xs text-purple-300 mb-1.5">姐姐的提议，要不要试试看？</p>
                <div className="flex flex-wrap gap-2">
                    {message.hypothesisData.choices.map(choice => ( <button key={choice.id} onClick={() => handleHypothesisChoiceClick(choice.id)} className="px-3 py-1 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors shadow-sm"> {choice.text} </button> ))}
                </div>
                {showXpModificationInput && ( <div className="mt-2"> <textarea value={xpModificationText} onChange={e => setXpModificationText(e.target.value)} placeholder="说说你想怎么改动..." rows={2} className="w-full p-1.5 text-xs bg-gray-700 text-gray-100 border border-purple-500 rounded focus:border-purple-400 focus:ring-purple-400 resize-none"/> <button onClick={submitHypothesisModification} className="mt-1 px-2 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded-md">确认修改</button> </div> )}
            </div>
          )}
        </div>
        {!isRichEditingActive && (
          <span className={`text-xs mt-1 ${textAlignClass} ${isOwnMessage && !themeDef ? 'text-tg-text-placeholder' : 'text-tg-text-secondary'}`}>
              {isATItemUsageMessage && onReplayCg && message.itemCgPath && ( <Icon name="Eye" className="w-3 h-3 inline-block mr-1 mb-0.5 text-purple-400 hover:text-purple-300 cursor-pointer" title="重温CG" onClick={handleIconClick} /> )}
              {formatTimestamp(message.timestamp)}
              {message.isStarred && message.sender !== 'system_summary' && <Icon name="Star" className="w-3 h-3 inline-block ml-1 mb-0.5 text-tg-warning" title="已星标"/>}
          </span>
        )}
        {(message.sender === 'user' || message.sender === 'ai' || message.sender === 'roundtable_ai') && !isRichEditingActive && (
            <div
                className={`absolute top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 flex flex-col space-y-0.5 bg-tg-bg-secondary/80 backdrop-blur-sm p-0.5 rounded-md shadow-lg
                            ${isOwnMessage ? 'right-[calc(100%+8px)]' : 'left-[calc(100%+8px)]'}`}
            >
                <button onClick={handleEditToggle} className={`${actionButtonBaseClass} hover:text-tg-warning`} title="编辑"><Icon name="Pencil" className={actionIconClass}/></button>
                <button onClick={() => onCopy(message.text)} className={`${actionButtonBaseClass} hover:text-tg-accent-primary`} title="复制"><Icon name="Copy" className={actionIconClass}/></button>
                {onAddToCoreMemory && <button onClick={() => onAddToCoreMemory(message)} className={`${actionButtonBaseClass} hover:text-purple-400`} title="添加到核心记忆"><Icon name="Sparkles" className={`${actionIconClass} text-purple-500`}/></button>}
                {onConvertToTask && isWorkspaceContext && (
                  <button onClick={handleConvertToTaskInternal} className={`${actionButtonBaseClass} hover:text-teal-400`} title="转化为任务">
                    <Icon name="ListPlus" className={`${actionIconClass} text-teal-500`} />
                  </button>
                )}
                {onSendToPouch && (
                    <div className="relative">
                        <button ref={pouchSelectorButtonRef} onClick={() => setShowPouchSelectorMenu(prev => !prev)} className={`${actionButtonBaseClass} hover:text-green-400`} title="发送到百宝囊"><Icon name="SendHorizontal" className={`${actionIconClass} text-green-500`}/></button>
                        {showPouchSelectorMenu && (
                            <div className={`absolute ${isOwnMessage ? 'right-full mr-1' : 'left-full ml-1'} bottom-0 w-32 bg-tg-bg-tertiary border border-tg-border-interactive rounded-md shadow-lg py-1 z-30`}>
                                {(Object.values(WisdomPouchType) as WisdomPouchType[]).map(pouch => (
                                    <button key={pouch} onClick={() => { if(onSendToPouch) onSendToPouch(message, pouch); setShowPouchSelectorMenu(false);}} className="w-full text-left px-2.5 py-1.5 text-xs hover:bg-tg-bg-hover text-tg-text-primary">
                                        {pouch === WisdomPouchType.INSPIRATION ? '灵感池' : pouch === WisdomPouchType.BUGS ? 'Bug备忘录' : '指令板'}
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                )}
                {onSendToToDoList && isWorkspaceContext && ( 
                  <button onClick={() => onSendToToDoList(message)} className={`${actionButtonBaseClass} hover:text-lime-400`} title="发送到待办列表">
                    <Icon name="ListChecks" className={`${actionIconClass} text-lime-500`} />
                  </button>
                )}
                {isOwnMessage && onResendMessage && <button onClick={() => {if (onResendMessage) onResendMessage(message);}} className={`${actionButtonBaseClass} hover:text-blue-400`} title="重发"><Icon name="Undo" className={`${actionIconClass} text-blue-500`}/></button>}
                {(!isOwnMessage && (message.sender === 'ai' || message.sender === 'roundtable_ai')) && onRefreshAiResponse && <button onClick={() => {onRefreshAiResponse(message);}} className={`${actionButtonBaseClass} hover:text-teal-400`} title="刷新AI回复"><Icon name="RotateCw" className={`${actionIconClass} text-teal-500`}/></button>}
                 {(!isOwnMessage && (message.sender === 'ai' || message.sender === 'roundtable_ai')) && onForwardMessage && isWorkspaceContext && (
                    message.characterId === 'linluo' || message.senderName.includes("林珞") ?
                        <button onClick={() => { onForwardMessage(message, 'XiaoLan');}} className={`${actionButtonBaseClass} hover:text-indigo-400`} title="转发给小岚"><Icon name="Forward" className={`${actionIconClass} text-indigo-500`}/></button> 
                    : (message.characterId === 'xiaolan' || message.senderName.includes("小岚")) &&
                        <button onClick={() => { onForwardMessage(message, 'LinLuo');}} className={`${actionButtonBaseClass} hover:text-pink-400`} title="转发给林珞"><Icon name="Forward" className={`${actionIconClass} text-pink-500`}/></button> 
                )}
                {canDecompose && (
                  <button onClick={handleDecompose} className={`${actionButtonBaseClass} hover:text-cyan-400`} title="转化为行动计划">
                    <Icon name="Columns" className={`${actionIconClass} text-cyan-500`}/>
                  </button>
                )}
            </div>
          )}
      </div>
    </div>
  );
};