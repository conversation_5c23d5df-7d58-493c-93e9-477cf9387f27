
// src/components/at_sidebar/ControlsPanel.tsx
import React from 'react';
import type { CMSType, SoundName, AudioPlaybackState, AssetType } from '@/types'; 
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon

interface ControlsPanelProps {
  isCollapsed: boolean;
  currentArmoryOwner: 'master' | 'queen';
  onArmoryOwnerChange: (owner: 'master' | 'queen') => void;
  onManageATAssets: () => void;
  onOpenAchievementHall: () => void;
  onPouchChange: (pouch: AssetType) => void; 
  activePouch: AssetType; 
}

export const ControlsPanel: React.FC<ControlsPanelProps> = ({
  isCollapsed, currentArmoryOwner, onArmoryOwnerChange, onManageATAssets, onOpenAchievementHall,
  onPouchChange, activePouch
}) => {
  
  const controlButtonBaseClass = "p-2 rounded-md transition-colors flex items-center justify-center text-xs";
  const activeControlButtonClass = "bg-purple-600 text-white";
  const inactiveControlButtonClass = "bg-gray-700 hover:bg-purple-800 text-gray-300";

  const pouchTypes: AssetType[] = ['prop', 'costume', 'pose']; 

  if (isCollapsed) {
    return (
      <div className="space-y-2">
        <button onClick={() => onPouchChange('prop')} className={`${controlButtonBaseClass} ${activePouch === 'prop' ? activeControlButtonClass : inactiveControlButtonClass}`} title="道具"><Icon name="Beaker" className="w-4 h-4" /></button>
        <button onClick={() => onPouchChange('costume')} className={`${controlButtonBaseClass} ${activePouch === 'costume' ? activeControlButtonClass : inactiveControlButtonClass}`} title="服装"><Icon name="Sparkles" className="w-4 h-4" /></button> {}
        <button onClick={() => onPouchChange('pose')} className={`${controlButtonBaseClass} ${activePouch === 'pose' ? activeControlButtonClass : inactiveControlButtonClass}`} title="姿势"><Icon name="Accessibility" className="w-4 h-4" /></button> {}
        <button onClick={onOpenAchievementHall} className={`${controlButtonBaseClass} ${inactiveControlButtonClass}`} title="成就殿堂"><Icon name="Trophy" className="w-4 h-4" /></button>
      </div>
    );
  }

  return (
    <div className="p-2.5 bg-gray-900/70 rounded-lg border border-purple-700/50 mb-2">
      <div className="mb-2">
        <p className="text-xs font-semibold text-purple-400 mb-1">军械库归属</p>
        <div className="flex space-x-1.5">
          <button onClick={() => onArmoryOwnerChange('master')} className={`${controlButtonBaseClass} flex-1 ${currentArmoryOwner === 'master' ? activeControlButtonClass : inactiveControlButtonClass}`}>主人用</button>
          <button onClick={() => onArmoryOwnerChange('queen')} className={`${controlButtonBaseClass} flex-1 ${currentArmoryOwner === 'queen' ? activeControlButtonClass : inactiveControlButtonClass}`}>女王用</button>
        </div>
      </div>
      <div className="mb-2">
        <p className="text-xs font-semibold text-purple-400 mb-1">资产分类</p>
         <div className="flex space-x-1.5">
          <button onClick={() => onPouchChange('prop')} className={`${controlButtonBaseClass} flex-1 ${activePouch === 'prop' ? activeControlButtonClass : inactiveControlButtonClass}`}><Icon name="Beaker" className="w-3.5 h-3.5 mr-1"/>道具</button>
          <button onClick={() => onPouchChange('costume')} className={`${controlButtonBaseClass} flex-1 ${activePouch === 'costume' ? activeControlButtonClass : inactiveControlButtonClass}`}><Icon name="Sparkles" className="w-3.5 h-3.5 mr-1"/>服装</button> {}
          <button onClick={() => onPouchChange('pose')} className={`${controlButtonBaseClass} flex-1 ${activePouch === 'pose' ? activeControlButtonClass : inactiveControlButtonClass}`}><Icon name="Accessibility" className="w-3.5 h-3.5 mr-1"/>姿势</button> {}
        </div>
      </div>
      {}
      {/* 军械库和成就功能已移至设置页面 */}
    </div>
  );
};
