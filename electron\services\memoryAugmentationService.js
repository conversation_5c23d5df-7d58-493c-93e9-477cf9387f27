// electron/services/memoryAugmentationService.js
console.log('MEMORY_AUGMENTATION_SERVICE_JS: File execution started.');

// dbService and aiKernelService will be passed as arguments or imported if circular dependencies are managed.
// For this refactor, let's assume they are passed if direct import causes issues.

export async function retrieveAndFormatMemories(
    queryText,
    personaTarget,
    projectId,
    contextType,
    keywords,
    desiredMemoryTypes,
    apiKey,
    embeddingModelName,
    dbService, // Passed instance
    aiKernelService // Passed instance
) {
    if (!queryText || !queryText.trim()) {
        // console.log("MEMORY_AUGMENTATION_SERVICE: No query text provided, skipping memory retrieval.");
        return "";
    }

    const queryEmbeddingResult = await aiKernelService.embedContentInternal(queryText, 'RETRIEVAL_QUERY', undefined, apiKey, embeddingModelName);

    if (typeof queryEmbeddingResult === 'string' || queryEmbeddingResult === null) {
        console.warn("MEMORY_AUGMENTATION_SERVICE: Embedding for memory retrieval failed or returned null/string error:", queryEmbeddingResult);
        return "";
    }
    
    const queryEmbedding = queryEmbeddingResult;

    const relevantMemories = await dbService.findRelevantMemories(
        queryEmbedding,
        {
            personaTarget: personaTarget,
            projectContextId: projectId || null,
            contextType: contextType || 'general',
            currentTopicKeywords: keywords || [],
            desiredMemoryTypes: desiredMemoryTypes || []
        },
        5 // Default limit for RAG context
    );

    let memoryContext = "";
    if (relevantMemories.direct.length > 0) {
        memoryContext += "\n\n[[相关核心记忆碎片]]:\n";
        relevantMemories.direct.forEach(mem => {
            memoryContext += `- ${mem.memory_content} (重要性: ${mem.importance}, 类型: ${mem.memory_type || '未知'})\n`;
        });
    }
    if (relevantMemories.associated.length > 0) {
        // Maybe limit associated memories shown in prompt to 1-2 for brevity
        const limitedAssociated = relevantMemories.associated.slice(0, 2);
        if (limitedAssociated.length > 0) {
            memoryContext += "\n[[相关联想记忆碎片]]:\n";
            limitedAssociated.forEach(mem => {
                memoryContext += `- ${mem.memory_content}\n`;
            });
        }
    }

    // console.log("MEMORY_AUGMENTATION_SERVICE: Formatted memory context string length:", memoryContext.length);
    return memoryContext.trim();
}

console.log('MEMORY_AUGMENTATION_SERVICE_JS: File execution finished. Exports configured.');
