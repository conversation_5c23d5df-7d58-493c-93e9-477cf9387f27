// electron/services/organizationService.ts
console.log('ORGANIZATION_SERVICE_TS: File execution started.');

import { db } from './databaseCore'; 
// tgcLoaderService is no longer needed for default characters and posts
// import * as tgcLoaderService from './tgcLoaderService'; 
import { DEFAULT_POSTS, DEFAULT_CHARACTERS } from '../constants/default-organization'; // Import from new central source
import type { Character, Post, Assignment } from '../../src/types'; 

let loadedCharacters: Character[] = [];
let loadedPosts: Post[] = []; // For consistency, though CORE_POSTS is used directly by getPosts
let isInitialized = false;

export function initializeOrganizationService(): void { // Can be synchronous now
  if (isInitialized) {
    console.log('ORGANIZATION_SERVICE: Already initialized.');
    return;
  }
  console.log('ORGANIZATION_SERVICE: Initializing - assigning default characters and posts...');
  try {
    loadedCharacters = DEFAULT_CHARACTERS;
    loadedPosts = DEFAULT_POSTS; // Assign DEFAULT_POSTS (re-exported CORE_POSTS)
    isInitialized = true;
    console.log(`ORGANIZATION_SERVICE: Initialization complete. Using ${loadedPosts.length} posts and ${loadedCharacters.length} characters from constants.`);
  } catch (error: any) {
    console.error('ORGANIZATION_SERVICE_ERROR: Failed to initialize by assigning constants:', error.message);
    loadedCharacters = []; 
    loadedPosts = [];
  }
}

function ensureInitialized(): void {
  if (!isInitialized) {
    console.warn('ORGANIZATION_SERVICE: Service accessed before `initializeOrganizationService` completed. Data may be incomplete. Ensure `initializeOrganizationService` is called at application startup.');
    // Attempt to initialize if not already, though ideally it's done at startup
    if (!isInitialized) { // Double check, as it might be called by multiple functions
        initializeOrganizationService();
        if (!isInitialized) { // If still not initialized after attempt (e.g., error during load)
            throw new Error("OrganizationService failed to initialize properly.");
        }
    }
  }
}

export function getPosts(): Post[] {
  ensureInitialized();
  console.log('ORGANIZATION_SERVICE: getPosts called, returning from memory.');
  return loadedPosts;
}

export async function getCharacters(): Promise<Character[]> { 
  ensureInitialized(); 
  console.log('ORGANIZATION_SERVICE: getCharacters called, returning from memory (async wrapper).');
  return Promise.resolve(loadedCharacters); // Wrap in Promise.resolve for IPC compatibility
}

export function getAssignments(): Assignment[] {
  ensureInitialized(); 
  if (!db) {
    console.error("ORGANIZATION_SERVICE_ERROR: getAssignments - db not available.");
    return [];
  }
  try {
    const rows = db.prepare('SELECT post_id, character_id FROM assignments').all() as Assignment[];
    console.log(`ORGANIZATION_SERVICE: Retrieved ${rows.length} assignments from DB.`);
    return rows;
  } catch (error: any) {
    console.error('ORGANIZATION_SERVICE_ERROR: Error getting assignments from DB:', error.message);
    return [];
  }
}

export function getAssignmentByPostId(postId: string): string | null {
  ensureInitialized();
  if (!db) {
    console.error(`ORGANIZATION_SERVICE_ERROR: getAssignmentByPostId(${postId}) - db not available.`);
    return null;
  }
  try {
    const row = db.prepare('SELECT character_id FROM assignments WHERE post_id = ?').get(postId) as { character_id: string } | undefined;
    if (row) {
      console.log(`ORGANIZATION_SERVICE: Assignment for post ${postId} is character ${row.character_id}.`);
      return row.character_id;
    }
    console.log(`ORGANIZATION_SERVICE: No assignment found for post ${postId} in DB.`);
    return null;
  } catch (error: any) {
    console.error(`ORGANIZATION_SERVICE_ERROR: Error getting assignment for post ${postId} from DB:`, error.message);
    return null;
  }
}

export function setAssignment(postId: string, characterId: string): { success: boolean; assignment?: Assignment; error?: string } {
  ensureInitialized();
  if (!db) {
    console.error(`ORGANIZATION_SERVICE_ERROR: setAssignment(${postId}, ${characterId}) - db not available.`);
    return { success: false, error: "Database not available." };
  }
  
  if (!loadedPosts.find(p => p.id === postId)) {
    return { success: false, error: `Invalid post_id: ${postId}. Not found in loaded posts.` };
  }
  if (!loadedCharacters.find(c => c.id === characterId)) {
    return { success: false, error: `Invalid character_id: ${characterId}. Not found in loaded characters.` };
  }

  try {
    const stmt = db.prepare('INSERT OR REPLACE INTO assignments (post_id, character_id) VALUES (?, ?)');
    stmt.run(postId, characterId);
    console.log(`ORGANIZATION_SERVICE: Set assignment for post ${postId} to character ${characterId} in DB.`);
    return { success: true, assignment: { post_id: postId, character_id: characterId } };
  } catch (error: any) {
    console.error('ORGANIZATION_SERVICE_ERROR: Error setting assignment in DB:', error.message);
    return { success: false, error: error.message };
  }
}

console.log('ORGANIZATION_SERVICE_TS: File execution finished. Service functions defined.');
