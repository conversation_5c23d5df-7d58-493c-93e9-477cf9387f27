
// electron/services/achievementDbService.js
console.log('ACHIEVEMENT_DB_SERVICE_JS: File execution started.');

import { db, crypto } from './databaseCore.js';

export function getAllAchievements() {
    if (!db) { console.error("ACHIEVEMENT_DB_ERROR: getAllAchievements - db not available."); return []; }
    try {
        const rows = db.prepare('SELECT * FROM achievements ORDER BY title ASC').all();
        console.log(`ACHIEVEMENT_DB: Retrieved ${rows.length} achievements definitions.`);
        return rows;
    } catch (error) {
        console.error('ACHIEVEMENT_DB_ERROR: Error getting all achievements definitions:', error);
        return [];
    }
}

export function getAchievementById(achievementId) {
    if (!db) { console.error(`ACHIEVEMENT_DB_ERROR: getAchievementById(${achievementId}) - db not available.`); return null; }
    try {
        const row = db.prepare('SELECT * FROM achievements WHERE id = ?').get(achievementId);
        return row || null;
    } catch (error) {
        console.error(`ACHIEVEMENT_DB_ERROR: Error getting achievement by ID ${achievementId}:`, error);
        return null;
    }
}

export function getUserAchievements(userId) {
    if (!db) { console.error(`ACHIEVEMENT_DB_ERROR: getUserAchievements(${userId}) - db not available.`); return []; }
    try {
        const rows = db.prepare('SELECT ua.*, a.title, a.description, a.icon_path FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = ? ORDER BY ua.unlocked_at DESC').all(userId);
        console.log(`ACHIEVEMENT_DB: Retrieved ${rows.length} achievements for user ${userId}.`);
        return rows;
    } catch (error) {
        console.error(`ACHIEVEMENT_DB_ERROR: Error getting achievements for user ${userId}:`, error);
        return [];
    }
}

export function unlockAchievement(userId, achievementId) {
    if (!db) { console.error(`ACHIEVEMENT_DB_ERROR: unlockAchievement(${userId}, ${achievementId}) - db not available.`); return { success: false, error: "Database not available." }; }
    
    // Check if achievement exists
    const achievementDef = db.prepare('SELECT id FROM achievements WHERE id = ?').get(achievementId);
    if (!achievementDef) {
        console.warn(`ACHIEVEMENT_DB_WARN: Attempted to unlock non-existent achievement ID: ${achievementId}`);
        return { success: false, error: "Achievement definition not found." };
    }

    // Check if already unlocked
    const existingUnlock = db.prepare('SELECT id FROM user_achievements WHERE user_id = ? AND achievement_id = ?').get(userId, achievementId);
    if (existingUnlock) {
        console.log(`ACHIEVEMENT_DB: Achievement ${achievementId} already unlocked for user ${userId}.`);
        return { success: true, message: "Already unlocked." }; // Not an error, just already done.
    }

    const newUnlockId = crypto.randomUUID();
    const unlockedAt = new Date().toISOString();
    const stmt = db.prepare('INSERT INTO user_achievements (id, user_id, achievement_id, unlocked_at) VALUES (?, ?, ?, ?)');
    
    try {
        stmt.run(newUnlockId, userId, achievementId, unlockedAt);
        console.log(`ACHIEVEMENT_DB: Unlocked achievement ${achievementId} for user ${userId}.`);
        return { success: true };
    } catch (error) {
        console.error(`ACHIEVEMENT_DB_ERROR: Error unlocking achievement ${achievementId} for user ${userId}:`, error);
        return { success: false, error: error.message };
    }
}

console.log('ACHIEVEMENT_DB_SERVICE_JS: File execution finished. Exports configured.');
