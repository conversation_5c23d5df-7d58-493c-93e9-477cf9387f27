// src/components/settings/PersonnelAssignmentPage.tsx
import React, { useState, useEffect, useCallback } from 'react';
import type { Post, Character, Assignment } from '@/types';
import { Icon } from '@/components/common/Icon';

export const PersonnelAssignmentPage: React.FC = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saveStatus, setSaveStatus] = useState<Record<string, 'idle' | 'saving' | 'saved' | 'error'>>({});

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [fetchedPosts, fetchedCharacters, fetchedAssignments] = await Promise.all([
        window.api.organization.getPosts(),
        window.api.organization.getCharacters(),
        window.api.organization.getAssignments(),
      ]);
      setPosts(fetchedPosts || []);
      setCharacters(fetchedCharacters || []);
      setAssignments(fetchedAssignments || []);
    } catch (err: any) {
      setError(`加载组织架构数据失败: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleAssignmentChange = async (postId: string, characterId: string) => {
    setSaveStatus(prev => ({ ...prev, [postId]: 'saving' }));
    setError(null);
    try {
      const result = await window.api.organization.setAssignment(postId, characterId);
      if (result.success && result.assignment) {
        setAssignments(prev => {
          const existingIndex = prev.findIndex(a => a.post_id === postId);
          if (existingIndex > -1) {
            const updated = [...prev];
            updated[existingIndex] = result.assignment!;
            return updated;
          }
          return [...prev, result.assignment!];
        });
        setSaveStatus(prev => ({ ...prev, [postId]: 'saved' }));
      } else {
        throw new Error(result.error || "保存任命失败。");
      }
    } catch (err: any) {
      setError(`为岗位 ${postId} 保存任命失败: ${err.message}`);
      setSaveStatus(prev => ({ ...prev, [postId]: 'error' }));
    } finally {
      setTimeout(() => setSaveStatus(prev => ({ ...prev, [postId]: 'idle' })), 2000);
    }
  };
  
  const getCharacterNameById = (characterId: string): string => {
    return characters.find(c => c.id === characterId)?.name || '未知角色';
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center text-tg-text-placeholder">
        <Icon name="Loader2" className="w-8 h-8 animate-spin mx-auto" />
        <p>加载人员配置信息...</p>
      </div>
    );
  }

  if (error) {
    return <div className="p-6 text-center text-red-400 bg-red-900/20 rounded-md">{error}</div>;
  }

  return (
    <div className="p-6 bg-tg-bg-primary rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold text-tg-text-primary mb-1">舰桥人员配置中心</h2>
      <p className="text-sm text-tg-text-secondary mb-6">为天工阁的各个核心【岗位】指派合适的【AI角色】。这将直接影响AI在执行对应岗位职责时的行为模式与人格展现。</p>
      
      <div className="space-y-6">
        {posts.map(post => {
          const currentAssignment = assignments.find(a => a.post_id === post.id);
          const currentPostSaveStatus = saveStatus[post.id] || 'idle';
          
          return (
            <div key={post.id} className="p-4 bg-tg-bg-secondary rounded-lg shadow-sm border border-tg-border-primary">
              <h3 className="text-lg font-semibold text-tg-accent-primary mb-1">{post.name}</h3>
              <p className="text-xs text-tg-text-secondary mb-3 leading-relaxed">{post.description}</p>
              
              <div className="flex items-center space-x-3">
                <label htmlFor={`assignment-${post.id}`} className="text-sm text-tg-text-secondary whitespace-nowrap">指派角色:</label>
                <select
                  id={`assignment-${post.id}`}
                  value={currentAssignment?.character_id || ''}
                  onChange={(e) => handleAssignmentChange(post.id, e.target.value)}
                  className="flex-grow p-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-secondary focus:ring-1 focus:ring-tg-accent-secondary/50 text-sm"
                  disabled={currentPostSaveStatus === 'saving'}
                >
                  <option value="" disabled>选择一个角色...</option>
                  {characters.map(character => (
                    <option key={character.id} value={character.id}>
                      {character.name}
                    </option>
                  ))}
                </select>
                
                {currentPostSaveStatus === 'saving' && <Icon name="Loader2" className="w-5 h-5 animate-spin text-tg-accent-primary" />}
                {currentPostSaveStatus === 'saved' && <Icon name="CheckCircle" className="w-5 h-5 text-green-500" title="已保存"/>}
                {currentPostSaveStatus === 'error' && <Icon name="AlertTriangle" className="w-5 h-5 text-red-500" title="保存失败"/>}
              </div>
              {currentAssignment && (
                <p className="text-xs text-tg-text-placeholder mt-2">
                  当前任命: <span className="font-semibold text-tg-text-secondary">{getCharacterNameById(currentAssignment.character_id)}</span>
                </p>
              )}
            </div>
          );
        })}
      </div>
      {posts.length === 0 && <p className="text-tg-text-placeholder text-center mt-4">暂无可用岗位定义。</p>}
    </div>
  );
};