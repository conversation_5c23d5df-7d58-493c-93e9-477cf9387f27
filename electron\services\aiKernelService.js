// electron/services/aiKernelService.js
console.log('AI_KERNEL_SERVICE_JS: File execution started.');

import { GoogleGenAI, HarmCategory, HarmBlockThreshold } from "@google/genai";
// Use direct relative path for globalConfig
import { GEMINI_TEXT_MODEL, GEMINI_EMBEDDING_MODEL } from '../../src/config/globalConfig'; 

let sharedAIAgent = null;
let currentApiKey = null;

export const safetySettings = [
    { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
    { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE },
];

export function initializeSharedAIAgent(apiKey) {
    if (!apiKey) {
        console.warn("AI_KERNEL_SERVICE: Attempted to initialize with no API key. Shared agent will be null.");
        sharedAIAgent = null;
        currentApiKey = null;
        return false;
    }
    if (apiKey === currentApiKey && sharedAIAgent) {
        // console.log("AI_KERNEL_SERVICE: API key unchanged, re-using existing shared agent.");
        return true;
    }
    try {
        sharedAIAgent = new GoogleGenAI({ apiKey });
        currentApiKey = apiKey;
        console.log("AI_KERNEL_SERVICE: Shared AI Agent initialized/updated successfully.");
        return true;
    } catch (error) {
        console.error("AI_KERNEL_SERVICE_ERROR: Failed to initialize GoogleGenAI with API key:", error);
        sharedAIAgent = null;
        currentApiKey = null;
        return false;
    }
}

export function getAIAgent(apiKeyFromArgs) {
    if (apiKeyFromArgs) {
        try {
            return new GoogleGenAI({ apiKey: apiKeyFromArgs });
        } catch (error) {
            console.error("AI_KERNEL_SERVICE_ERROR: Failed to initialize temporary GoogleGenAI with provided API key:", error);
            return null;
        }
    }
    return sharedAIAgent;
}

export function isAgentInitialized() {
    return !!sharedAIAgent;
}
export function getCurrentApiKey() {
    return currentApiKey;
}


export async function generateContentInternal(contents, config, modelName, apiKey) {
    const aiForCall = getAIAgent(apiKey);
    const modelToUse = modelName || GEMINI_TEXT_MODEL;

    if (!aiForCall) {
        return "AI service not initialized or API key missing.";
    }
     if (!apiKey && !currentApiKey && !process.env.API_KEY) { 
        return "API Key not provided and not set in environment.";
    }

    try {
        // console.log(`AI_KERNEL_SERVICE: Generating content with model ${modelToUse}. Config:`, config, "Content (first part):", contents[0]?.parts[0]?.text?.substring(0,100));
        const response = await aiForCall.models.generateContent({
            model: modelToUse,
            contents: contents,
            config: { ...config, safetySettings }, // Ensure safetySettings are always applied
        });
        // console.log("AI_KERNEL_SERVICE: Raw response from LLM:", response);
        return response.text; // Per guideline, access .text directly
    } catch (error) {
        console.error(`AI_KERNEL_SERVICE_ERROR (generateContentInternal with ${modelToUse}):`, error);
        return `Error generating content: ${error.message}`;
    }
}

export async function embedContentInternal(textChunk, taskType = 'RETRIEVAL_DOCUMENT', title, apiKey) {
    if (!textChunk || typeof textChunk !== 'string' || textChunk.trim() === "") {
        console.warn("AI_KERNEL_SERVICE (embed): Attempted to embed empty or invalid textChunk. Returning null.");
        return null; // Explicitly return null for empty/invalid input
    }
    
    const aiForCall = getAIAgent(apiKey);
    // Use GEMINI_EMBEDDING_MODEL directly, without "models/" prefix, as per Gemini API guidelines.
    const embeddingModelToUse = GEMINI_EMBEDDING_MODEL; 

    if (!aiForCall) {
        console.error("AI_KERNEL_SERVICE (embed): AI service not properly initialized or API key missing.");
        return "AI service not initialized for embedding.";
    }
     if (!apiKey && !currentApiKey && !process.env.API_KEY) {
        return "API Key not provided and not set in environment for embedding.";
    }

    try {
        console.log(`AI_KERNEL_SERVICE: Embedding content with model ${embeddingModelToUse}. Text (first 50): "${textChunk.substring(0,50)}..." TaskType: ${taskType}, Title: ${title}`);
        const response = await aiForCall.models.embedContent({
            model: embeddingModelToUse, // SDK handles 'models/' prefix for embedding models
            content: { parts: [{ text: textChunk }], role: "user" }, 
            taskType: taskType,
            title: title, 
        });
        return response.embedding.values;
    } catch (error) {
        console.error(`AI_KERNEL_SERVICE_ERROR (embedContentInternal with ${embeddingModelToUse}):`, error);
        if (error.message && error.message.includes("API key not valid")) {
             return "无效的API Key导致内容向量化失败。";
        }
        return `内容向量化失败: ${error.message}`;
    }
}

console.log('AI_KERNEL_SERVICE_JS: File execution finished. Exports configured.');
