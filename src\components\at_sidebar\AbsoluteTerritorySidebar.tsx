// src/components/at_sidebar/AbsoluteTerritorySidebar.tsx
import React, { useState, useEffect, useCallback, useMemo } from 'react';
// useSettings hook import removed as settings are passed via props
import type {
    PropItem, CostumeItem, PoseItem, CMSType, CMSItemBase,
    SoundName, AudioPlaybackState, LinLuoDetailedStatus, AppSettings, RolePlayingCard,
    LinLuoBodyDevelopment, AssetType, AnyLoadedAsset, ScriptAsset, SceneCardAsset, TGCRoleCardAsset, ScriptChoice
} from '@/types';
import { Icon } from '@/components/common/Icon';

// Removed unused imports:
// import { StatusMonitorPanel } from './StatusMonitorPanel';
// import { DevelopmentBlueprintPanel } from './DevelopmentBlueprintPanel';
import { ArmoryPanel } from './ArmoryPanel';
import { ControlsPanel } from './ControlsPanel'; 
import { AchievementHallModal } from '@/components/AchievementHallModal';

interface AbsoluteTerritorySidebarProps {
  propsItems: PropItem[];
  costumesItems: CostumeItem[];
  posesItems: PoseItem[];
  roleCards: (RolePlayingCard | TGCRoleCardAsset)[];
  onCMSItemSelected: (item: AnyLoadedAsset, type: AssetType) => void;
  isLoadingCMS: boolean;
  settings: AppSettings; // Changed from appSettings to settings
  currentAudioStateFromPage: Partial<AudioPlaybackState>; 
  onPlaySoundRequest: ((soundName: SoundName | null, loop: boolean) => void) | null; 
  onStopSoundRequest: ((soundName?: SoundName | null) => void) | null; 
  onSetVolumeRequest: ((volume: number) => void) | null; 
  onToggleLoopRequest: (() => void) | null; 
  linLuoStatus: LinLuoDetailedStatus | null;
  currentArmoryOwner: 'master' | 'queen';
  onArmoryOwnerChange: (owner: 'master' | 'queen') => void;
  
  onManageATAssets: () => void;
  onOpenRoleCardEditor: (card: RolePlayingCard | TGCRoleCardAsset | null) => void;
  onDeleteRoleCard: (cardId: string) => void;

  gameplayMode: 'director' | 'script';
  onSetGameplayMode: (mode: 'director' | 'script') => void;
  availableScripts: ScriptAsset[];
  availableSceneCards: SceneCardAsset[];
  selectedScript: ScriptAsset | null;
  onSelectScript: (script: ScriptAsset | null) => void;
  userSelectedRoleCardId: string | null;
  onRoleCardSelected: (cardId: string | null) => void;
  userSelectedSceneCard: SceneCardAsset | null;
  onSelectSceneCard: (card: SceneCardAsset | null) => void;
  onPouchChange: (pouch: AssetType) => void;
  onOpenAchievementHall: () => void;
  currentScriptSceneId: string | null;
  bodyDevelopment: LinLuoBodyDevelopment[];
  newlyUnlockedItems: Set<string>;
  setNewlyUnlockedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
}

// Internal UI Components for Script Mode
const GameplayModeSwitcher: React.FC<{
    currentMode: 'director' | 'script';
    onSetMode: (mode: 'director' | 'script') => void;
}> = ({ currentMode, onSetMode }) => (
    <div className="mb-2 p-2 bg-gray-900/50 rounded-lg border border-purple-700/30">
        <p className="text-xs font-semibold text-purple-400 mb-1.5">玩法模式切换</p>
        <div className="flex space-x-1.5">
            <button onClick={() => onSetMode('director')} className={`flex-1 py-1.5 px-2 text-xs rounded-md transition-colors flex items-center justify-center ${currentMode === 'director' ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-purple-800 text-gray-300'}`}>
                <Icon name="Film" className="w-3.5 h-3.5 mr-1"/>导演模式
            </button>
            <button onClick={() => onSetMode('script')} className={`flex-1 py-1.5 px-2 text-xs rounded-md transition-colors flex items-center justify-center ${currentMode === 'script' ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-purple-800 text-gray-300'}`}>
                <Icon name="Users" className="w-3.5 h-3.5 mr-1"/>剧本模式
            </button>
        </div>
    </div>
);

const ScenarioSettingsPanel: React.FC<{
    roleCards: (RolePlayingCard | TGCRoleCardAsset)[];
    sceneCards: SceneCardAsset[];
    selectedRoleCardId: string | null;
    onSelectRoleCard: (cardId: string | null) => void;
    selectedSceneCard: SceneCardAsset | null;
    onSelectSceneCard: (card: SceneCardAsset | null) => void;
    onOpenRoleCardEditor: (card: RolePlayingCard | TGCRoleCardAsset | null) => void;
    onDeleteRoleCard: (cardId: string) => void;
    isScriptMode: boolean;
    selectedScript: ScriptAsset | null; 
    currentScriptSceneId: string | null; 
}> = ({ 
    roleCards, sceneCards, selectedRoleCardId, onSelectRoleCard, 
    selectedSceneCard, onSelectSceneCard, onOpenRoleCardEditor, 
    onDeleteRoleCard, isScriptMode, selectedScript, currentScriptSceneId 
}) => {
    
    const scriptDictatesRoleCard = isScriptMode && !!selectedScript?.role_card_id;
    const currentSceneInScript = selectedScript?.scenes.find(s => s.id === currentScriptSceneId);
    const scriptDictatesSceneCard = isScriptMode && selectedScript && (currentSceneInScript?.scenario_card_id !== undefined || selectedScript.scenario_card_id !== undefined);


    return (
        <div className="mb-2 p-2 bg-gray-900/50 rounded-lg border border-purple-700/30">
            <p className="text-xs font-semibold text-purple-400 mb-1.5">情景设定核心</p>
            {/* Role Card Selection */}
            <div className="mb-1.5">
                <label className="text-[11px] text-gray-400 block mb-0.5">选择姐姐的角色卡:</label>
                <select 
                    value={selectedRoleCardId || ""} 
                    onChange={(e) => onSelectRoleCard(e.target.value || null)}
                    className="w-full p-1.5 bg-gray-700 text-gray-200 border border-purple-700/50 rounded text-xs focus:border-purple-400 focus:ring-purple-400 disabled:opacity-70"
                    disabled={scriptDictatesRoleCard} 
                >
                    <option value="">默认人格</option>
                    {roleCards.map(card => <option key={card.id} value={card.id}>{card.name}</option>)}
                </select>
                <div className="text-right mt-0.5">
                    <button onClick={() => onOpenRoleCardEditor(null)} className="text-[10px] text-purple-400 hover:text-purple-300 mr-1 disabled:opacity-50" disabled={scriptDictatesRoleCard}>+ 新增DB卡</button>
                </div>
            </div>
            {/* Scene Card Selection */}
            <div>
                <label className="text-[11px] text-gray-400 block mb-0.5">选择场景卡:</label>
                <select 
                    value={selectedSceneCard?.id || ""} 
                    onChange={(e) => {
                        const card = sceneCards.find(sc => sc.id === e.target.value);
                        onSelectSceneCard(card || null);
                    }}
                    className="w-full p-1.5 bg-gray-700 text-gray-200 border border-purple-700/50 rounded text-xs focus:border-purple-400 focus:ring-purple-400 disabled:opacity-70"
                    disabled={scriptDictatesSceneCard} 
                >
                    <option value="">无特定场景</option>
                    {sceneCards.map(card => <option key={card.id} value={card.id}>{card.name}</option>)}
                </select>
            </div>
        </div>
    );
};


const ScriptSelectionPanel: React.FC<{
    scripts: ScriptAsset[];
    onSelectScript: (script: ScriptAsset) => void;
}> = ({ scripts, onSelectScript }) => (
    <div className="p-1.5">
        <p className="text-xs font-medium text-gray-300 mb-1.5">选择剧本开始:</p>
        {scripts.length === 0 && <p className="text-xs text-gray-500 text-center py-2">暂无可用剧本。</p>}
        <div className="space-y-1 max-h-40 overflow-y-auto">
            {scripts.map(script => (
                <button 
                    key={script.id} 
                    onClick={() => onSelectScript(script)}
                    className="w-full text-left p-1.5 bg-gray-700 hover:bg-purple-800 text-gray-300 rounded text-xs truncate"
                    title={script.name}
                >
                    {script.name}
                </button>
            ))}
        </div>
    </div>
);

const PlayerDecisionPanel: React.FC<{
    scriptTitle: string;
    choices: ScriptChoice[];
    onExitScript: () => void;
}> = ({ scriptTitle, choices, onExitScript }) => (
    <div className="p-1.5">
        <p className="text-xs font-medium text-gray-300 mb-1">当前剧本: {scriptTitle}</p>
        <p className="text-xs text-gray-400 mb-1.5">请在聊天区查看剧情并做出选择。</p>
        <button onClick={onExitScript} className="w-full mt-2 p-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-300 rounded">退出剧本</button>
    </div>
);


export const AbsoluteTerritorySidebar: React.FC<AbsoluteTerritorySidebarProps> = ({
  propsItems, costumesItems, posesItems, roleCards,
  onCMSItemSelected, isLoadingCMS, settings, // Use settings prop
  linLuoStatus,
  currentArmoryOwner, onArmoryOwnerChange,
  onManageATAssets, onOpenRoleCardEditor, onDeleteRoleCard,
  gameplayMode, onSetGameplayMode, availableScripts, availableSceneCards,
  selectedScript, onSelectScript, userSelectedRoleCardId, onRoleCardSelected,
  userSelectedSceneCard, onSelectSceneCard, onPouchChange, onOpenAchievementHall,
  currentScriptSceneId, bodyDevelopment, newlyUnlockedItems, setNewlyUnlockedItems,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activePouch, setActivePouch] = useState<AssetType>('prop');

  // 处理标签切换
  const handlePouchChange = (pouch: AssetType) => {
    console.log('🔄 AbsoluteTerritorySidebar: 切换标签到', pouch);
    setActivePouch(pouch);
    onPouchChange(pouch);
  };
  
  const [isContextualControlsOpen, setIsContextualControlsOpen] = useState(true);
  const [isAchievementHallOpen, setIsAchievementHallOpen] = useState(false);
  
  const toggleSidebar = () => setIsCollapsed(!isCollapsed);
  

  if (isCollapsed) {
    return (
      <div className="p-2 flex flex-col items-center shadow-lg flex-shrink-0 bg-gray-800 border-l border-purple-700 space-y-2 h-full">
        <button onClick={toggleSidebar} className="p-1.5 rounded-md text-gray-400 hover:bg-purple-800" title="展开领域控制台">
          <Icon name="ChevronLeft" className="w-5 h-5" />
        </button>
        <button onClick={() => onSetGameplayMode('director')} title="导演模式" className={`p-1.5 rounded-md ${gameplayMode === 'director' ? 'text-purple-400 bg-purple-800/50' : 'text-gray-400 hover:bg-purple-800'}`}><Icon name="Film" className="w-4 h-4"/></button>
        <button onClick={() => onSetGameplayMode('script')} title="剧本模式" className={`p-1.5 rounded-md ${gameplayMode === 'script' ? 'text-purple-400 bg-purple-800/50' : 'text-gray-400 hover:bg-purple-800'}`}><Icon name="Users" className="w-4 h-4"/></button>
        {gameplayMode === 'director' && (
            <>
                <button onClick={() => handlePouchChange('prop')} className={`p-1.5 rounded-md ${activePouch === 'prop' ? 'text-purple-400 bg-purple-800/50' : 'text-gray-400 hover:bg-purple-800'}`} title="道具"><Icon name="Beaker" className="w-4 h-4" /></button>
                <button onClick={() => handlePouchChange('costume')} className={`p-1.5 rounded-md ${activePouch === 'costume' ? 'text-purple-400 bg-purple-800/50' : 'text-gray-400 hover:bg-purple-800'}`} title="服装"><Icon name="Sparkles" className="w-4 h-4" /></button>
                <button onClick={() => handlePouchChange('pose')} className={`p-1.5 rounded-md ${activePouch === 'pose' ? 'text-purple-400 bg-purple-800/50' : 'text-gray-400 hover:bg-purple-800'}`} title="姿势"><Icon name="Accessibility" className="w-4 h-4" /></button>
            </>
        )}
        <button onClick={() => setIsAchievementHallOpen(true)} className="p-1.5 rounded-md text-gray-400 hover:bg-purple-800" title="成就殿堂"><Icon name="Trophy" className="w-4 h-4" /></button>
      </div>
    );
  }

  return (
    <aside className="w-full h-full p-3 flex-shrink-0 flex flex-col shadow-2xl bg-gray-800 border-l-2 border-purple-600 overflow-hidden">
      <div className="flex justify-between items-center mb-3 p-1">
        <h3 className="text-base font-semibold text-purple-400">主人的神座</h3>
        <button onClick={toggleSidebar} className="p-1.5 rounded-md text-gray-400 hover:bg-purple-800" title="收起控制台">
          <Icon name="ChevronRight" className="w-5 h-5" />
        </button>
      </div>

      <GameplayModeSwitcher currentMode={gameplayMode} onSetMode={onSetGameplayMode} />

      <ScenarioSettingsPanel
        roleCards={roleCards}
        sceneCards={availableSceneCards}
        selectedRoleCardId={userSelectedRoleCardId}
        onSelectRoleCard={onRoleCardSelected}
        selectedSceneCard={userSelectedSceneCard}
        onSelectSceneCard={onSelectSceneCard}
        onOpenRoleCardEditor={onOpenRoleCardEditor}
        onDeleteRoleCard={onDeleteRoleCard}
        isScriptMode={gameplayMode === 'script'}
        selectedScript={selectedScript}
        currentScriptSceneId={currentScriptSceneId}
      />
      
      <div className="flex-grow overflow-y-auto rounded-lg p-1.5 shadow-inner min-h-0 bg-gray-900/70 border border-purple-700/30 mt-2">
        <button 
            onClick={() => setIsContextualControlsOpen(!isContextualControlsOpen)}
            className="w-full flex justify-between items-center text-xs font-semibold text-purple-400 mb-1 hover:text-purple-300 p-1"
            aria-expanded={isContextualControlsOpen}
        >
            <span>{gameplayMode === 'director' ? '导演工具箱' : '剧本控制'}</span>
            {isContextualControlsOpen ? <Icon name="ChevronUp" className="w-3.5 h-3.5"/> : <Icon name="ChevronDown" className="w-3.5 h-3.5"/>}
        </button>
        {isContextualControlsOpen && (
            gameplayMode === 'director' ? (
                <>
                    <ControlsPanel
                        isCollapsed={false} 
                        currentArmoryOwner={currentArmoryOwner}
                        onArmoryOwnerChange={onArmoryOwnerChange}
                        onManageATAssets={onManageATAssets}
                        onOpenAchievementHall={() => setIsAchievementHallOpen(true)}
                        onPouchChange={handlePouchChange}
                        activePouch={activePouch}
                    />
                    <ArmoryPanel
                        propsItems={propsItems}
                        costumesItems={costumesItems}
                        posesItems={posesItems}
                        currentArmoryOwner={currentArmoryOwner}
                        onCMSItemSelected={onCMSItemSelected}
                        isLoadingCMS={isLoadingCMS}
                        bodyDevelopment={bodyDevelopment}
                        newlyUnlockedItems={newlyUnlockedItems}
                        setNewlyUnlockedItems={setNewlyUnlockedItems}
                        activePouch={activePouch}
                        onPouchChange={handlePouchChange}
                    />
                </>
            ) : ( 
                selectedScript ? ( 
                     <PlayerDecisionPanel 
                        scriptTitle={selectedScript.name}
                        choices={selectedScript.scenes.find(s => s.id === (currentScriptSceneId || selectedScript.scenes[0].id))?.user_choices || []}
                        onExitScript={() => onSelectScript(null)}
                    />
                ) : ( 
                    <ScriptSelectionPanel scripts={availableScripts} onSelectScript={onSelectScript} />
                )
            )
        )}
      </div>
      
      {isAchievementHallOpen && linLuoStatus && ( 
        <AchievementHallModal
            isOpen={isAchievementHallOpen}
            onClose={() => setIsAchievementHallOpen(false)}
            bodyDevelopmentData={[]} 
            linLuoCurrentStatus={linLuoStatus} 
        />
      )}
    </aside>
  );
};