

// electron/services/aiLearningLogDbService.js
console.log('AI_LEARNING_LOG_DB_SERVICE_JS: File execution started.');

import { db, crypto } from './databaseCore';

export async function addLearningLog(logData) {
    if (!db) {
        console.error("AI_LEARNING_LOG_DB_ERROR: addLearningLog - db not available.");
        return null;
    }
    const newLog = {
        log_id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        ...logData,
    };

    // Ensure JSON fields are stringified
    if (typeof newLog.context_snapshot !== 'string' && newLog.context_snapshot !== null && newLog.context_snapshot !== undefined) {
        newLog.context_snapshot = JSON.stringify(newLog.context_snapshot);
    }
    if (typeof newLog.ai_processing_summary !== 'string' && newLog.ai_processing_summary !== null && newLog.ai_processing_summary !== undefined) {
        newLog.ai_processing_summary = JSON.stringify(newLog.ai_processing_summary);
    }
     if (typeof newLog.user_feedback_implicit_flags !== 'string' && newLog.user_feedback_implicit_flags !== null && newLog.user_feedback_implicit_flags !== undefined) {
        newLog.user_feedback_implicit_flags = JSON.stringify(newLog.user_feedback_implicit_flags);
    }


    const stmt = db.prepare(`
        INSERT INTO ai_learning_logs (
            log_id, timestamp, ai_persona, task_type, triggering_input_summary, 
            context_snapshot, ai_processing_summary, ai_generated_output_summary, 
            user_feedback_explicit, user_feedback_implicit_flags, 
            success_metric_value, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    try {
        stmt.run(
            newLog.log_id,
            newLog.timestamp,
            newLog.ai_persona,
            newLog.task_type,
            newLog.triggering_input_summary,
            newLog.context_snapshot,
            newLog.ai_processing_summary,
            newLog.ai_generated_output_summary,
            newLog.user_feedback_explicit,
            newLog.user_feedback_implicit_flags,
            newLog.success_metric_value,
            newLog.notes
        );
        console.log(`AI_LEARNING_LOG_DB: Added learning log ${newLog.log_id} for ${newLog.ai_persona} - ${newLog.task_type}.`);
        return newLog;
    } catch (error) {
        console.error('AI_LEARNING_LOG_DB_ERROR: Error adding learning log:', error);
        return null;
    }
}

export async function getLearningLogs(filters = {}, limit = 50, offset = 0) {
    if (!db) {
        console.error("AI_LEARNING_LOG_DB_ERROR: getLearningLogs - db not available.");
        return [];
    }
    try {
        let query = 'SELECT * FROM ai_learning_logs';
        const params = [];
        const conditions = [];

        if (filters.ai_persona && filters.ai_persona !== 'all') {
            conditions.push('ai_persona = ?');
            params.push(filters.ai_persona);
        }
        if (filters.task_type && filters.task_type.trim() !== '') {
            conditions.push('task_type LIKE ?');
            params.push(`%${filters.task_type.trim()}%`);
        }
        if (filters.min_success_metric && typeof filters.min_success_metric === 'number') {
            conditions.push('success_metric_value >= ?');
            params.push(filters.min_success_metric);
        }
        if (filters.max_success_metric && typeof filters.max_success_metric === 'number') {
            conditions.push('success_metric_value <= ?');
            params.push(filters.max_success_metric);
        }


        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
        params.push(limit, offset);

        const rows = db.prepare(query).all(...params);
        console.log(`AI_LEARNING_LOG_DB: Retrieved ${rows.length} learning logs.`);
        return rows.map(row => ({
            ...row,
            context_snapshot: row.context_snapshot ? JSON.parse(row.context_snapshot) : null,
            ai_processing_summary: row.ai_processing_summary ? JSON.parse(row.ai_processing_summary) : null,
            user_feedback_implicit_flags: row.user_feedback_implicit_flags ? JSON.parse(row.user_feedback_implicit_flags) : null,
        }));
    } catch (error) {
        console.error('AI_LEARNING_LOG_DB_ERROR: Error getting learning logs:', error);
        return [];
    }
}


console.log('AI_LEARNING_LOG_DB_SERVICE_JS: File execution finished. Exports configured.');