
// src/types/uiTypes.ts
import type * as React from 'react';
import type { Editor } from '@tiptap/core'; // Corrected import
import type {
    ChatMessage, AppSettings, NoteItem, WisdomPouchType, ImportanceLevel,
    KnowledgeTome, ProjectKnowledgeTome,
    GlobalQuickCommandItem, Project, TaskCreationData, Character, RoundtableParticipant, ToDoItem, AiCallContext,
    Task, TaskStatus, Post, Assignment, AIResponseWithStatus, XPHypothesisProposalData, ScriptChoice // Added AIResponseWithStatus, XPHypothesisProposalData, ScriptChoice
} from './index'; 
import type { CoreMemory, AIInteractionHistoryItem } from './aiTypes'; 
import type { MindNode, MindConnection } from './projectTypes'; 

export interface FloatingStyleToolbarProps {
  isVisible: boolean;
  top: number;
  left: number;
  onApplyStyle: (command: string, value?: string) => void;
  onClose: () => void;
}

export type ActiveMainTabType = 'discussion' | 'project-knowledge-base' | 'source-code' | 'mind-workshop' | 'timeline' | 'cockpit' | 'glazed-workshop' | 'task-board';


export interface AICodeAssistPanelProps {
  isVisible: boolean;
  title: string;
  content: string | null;
  isLoading: boolean;
  error: string | null;
  onClose: () => void;
  projectId?: string;
  projectName?: string;
  projectCategories?: string[];
  currentOperationDescription?: string | null;
  onTriggerAIAssist?: (type: AIInteractionHistoryItem['type'], instruction?: string, codeContext?: string) => Promise<void>;
  history?: AIInteractionHistoryItem[];
  onHistoryChange?: (newHistory: AIInteractionHistoryItem[]) => void;
  currentTaskForContext?: Task | null;
  onUpdateTaskStatus?: (taskId: string, newStatus: TaskStatus, projectIdToUpdate: string) => Promise<Task | null>;
  currentFilePath?: string | null;
  currentFileContent?: string | null;
  onApplyNewCode?: (newCode: string) => void;
  settings: AppSettings; 
}

export interface SaveToKnowledgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTitle: string;
  initialContent: string;
  projectId?: string;
  projectName?: string;
  projectCategories: string[]; 
  onSaveGlobal: (tomeData: Omit<KnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<void>;
  onSaveProject: (tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt' | 'projectId'>) => Promise<void>;
}

export interface ChatDiscussionAreaRef {
  insertTextAtFocus: (textToInsert: string) => void;
  scrollToBottom: (behavior?: ScrollBehavior) => void;
  focusTextarea?: () => void;
  editor?: Editor | null; // Optional editor instance for rich text
}

// Common props for both SandboxChat and TerritoryChat, can be extended by specific props
interface BaseChatAreaProps {
  messages: ChatMessage[];
  onUpdateUserMessage: (message: ChatMessage) => Promise<void>;
  aiTaskStatus: any; 
  currentUserName: string;
  settings: AppSettings; 
  globalQuickCommands: GlobalQuickCommandItem[];
  isAIServiceReady: boolean;
  onAddToCoreMemory?: (message: ChatMessage) => void;
  onOpenWisdomPouch?: () => void;
}

export interface SandboxChatProps extends BaseChatAreaProps {
  // Project-specific props
  projectId: string;
  projectName: string;
  onSaveUserMessage: (projectId: string, message: ChatMessage) => Promise<void>; // Specifically for project
  onCallAI?: (userPromptText?: string, currentMessages?: ChatMessage[], isRagActive?: boolean, context?: AiCallContext) => Promise<void | any>;
  onDecomposeRequirement?: (requirementText: string, projectId: string, originalMessageId: string) => Promise<void>; 
  onConvertToTask?: (message: ChatMessage) => void; 
  onResendMessage?: (message: ChatMessage) => void; // Often user messages
  onRefreshAiResponse?: (aiMessage: ChatMessage) => void; // Often AI messages
  onForwardMessage?: (message: ChatMessage, targetAi: 'LinLuo' | 'XiaoLan' | 'YuJing') => void; // For workspace multi-AI
  onSendToPouch?: (message: ChatMessage, pouchType?: WisdomPouchType) => void; // Workspace specific

  // Roundtable specific props (can be optional if not all sandboxes use roundtable)
  allAvailableCharacters?: Character[]; 
  allPosts?: Post[];
  allAssignments?: Assignment[]; 
  roundtableToDoItems?: ToDoItem[];
  setRoundtableToDoItems?: React.Dispatch<React.SetStateAction<ToDoItem[]>>;
  roundtableSharedDraft?: string;
  setRoundtableSharedDraft?: (draft: string) => void;
  onSendToToDoList?: (message: ChatMessage) => void; 
}

export interface TerritoryChatProps extends BaseChatAreaProps {
  // AT-specific props
  onSaveUserMessage: (message: ChatMessage) => Promise<void>; // Specifically for AT (no projectId)
  onCallAI: (userPromptText?: string, currentMessages?: ChatMessage[], isRagActive?: boolean, context?: AiCallContext) => Promise<void | AIResponseWithStatus>;
  onClearHistory?: () => Promise<{success: boolean, error?: string} | undefined>;
  onReplayCg?: (cgPath: string) => void;
  onHypothesisFeedback?: (hypothesisId: string, choice: string, modification?: string, originalElements?: XPHypothesisProposalData['elements']) => Promise<void>;
  scriptChoices?: ScriptChoice[] | null; 
  onScriptChoiceMade?: (choice: ScriptChoice) => void; 
  isLoadingATInitial?: boolean;
  isLoadingOlderAT?: boolean;
  canLoadOlderAT?: boolean;
  onLoadOlderATMessages?: () => Promise<void>;
  onOpenWisdomPouch?: () => void; 
}


export interface ProjectWorkspacePageOutletContext {
  project: Project | null;
  projects: Project[]; 
  settings: AppSettings; 
  globalQuickCommands: GlobalQuickCommandItem[];
  isAIServiceReady: boolean;
  onAddCoreMemory: (memoryData: Omit<CoreMemory, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => Promise<CoreMemory | null>;
  onUpdateTaskStatusInApp: (taskId: string, newStatus: TaskStatus, projectId: string) => Promise<Task | null>;
  onAideProjectImported?: (projectId: string, newTasksData: TaskCreationData[]) => Promise<void>;
  handleDecomposeRequirement: (requirementText: string, currentProjectId: string, originalMessageId: string) => Promise<void>;
  handleConvertToTask: (message: ChatMessage) => void;
  onSaveNewChatMessage: (projectId: string, message: ChatMessage) => Promise<void>;
  onUpdateExistingChatMessage: (message: ChatMessage) => Promise<void>; // Corrected: Now expects 1 arg
  onMessagesReplacedBySummary: (projectId: string, summaryMessage: ChatMessage, replacedMessageIds: string[]) => void;
  chatAreaRef: React.RefObject<ChatDiscussionAreaRef>;
  addNoteToProject: (pouchType: WisdomPouchType, text: string, importance?: ImportanceLevel) => Promise<NoteItem | null>;
  updateNoteInProject: (pouchType: WisdomPouchType, note: NoteItem) => void;
  deleteNoteFromProject: (pouchType: WisdomPouchType, noteId: string) => void;
  updateProjectMindMap: (nodes: MindNode[], connections: MindConnection[]) => void;
  addProjectKnowledgeTome: (tomeData: Omit<ProjectKnowledgeTome, 'id' | 'createdAt' | 'lastModifiedAt'>) => Promise<ProjectKnowledgeTome | undefined>;
  updateProjectKnowledgeTome: (updatedTome: ProjectKnowledgeTome) => void;
  deleteProjectKnowledgeTome: (tomeId: string) => void;
  addProjectKnowledgeCategory: (category: string) => Promise<string | undefined>;
  removeProjectKnowledgeCategory: (category: string) => void;
  updateProjectGeneral: (updatedProject: Project) => void;
  taskToOpenInEditor: Task | null;
  onEditorOpenedTask: () => void;
  onStartCraftingFromTask: (task: Task, targetView?: ActiveMainTabType) => void;
  aiTaskStatus: any; 
  onOpenWisdomPouch?: () => void;
  allCharacters: Character[]; 
  allPosts: Post[]; 
  allAssignments: Assignment[]; 
  onCallAI?: (userPromptText?: string, currentMessages?: ChatMessage[], isRagActive?: boolean, context?: AiCallContext) => Promise<void | any>; // Keep this optional if some direct children of ProjectWorkspacePage use it, but SandboxChat will use its internal.
}


export interface AbsoluteTerritoryPageProps {
  settings: AppSettings; 
  globalQuickCommands: GlobalQuickCommandItem[];
  isAIServiceReady: boolean;
  onAddCoreMemory: (memoryData: Omit<any, 'id' | 'created_at' | 'last_accessed_at' | 'access_count' | 'user_feedback_score' | 'similarityScore' | 'contextAffinityScore' | 'timelinessScore' | 'totalScore' | 'embedding' | 'retrieval_source'>) => Promise<any | null>; // CoreMemory
}

export interface ActionPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  tasks: Partial<Omit<TaskCreationData, 'project_id'>>[];
  onApprovePlan: () => Promise<void>;
  isApproving: boolean;
}


export interface TaskDetailModalProps {
  isOpen: boolean;
  onClose: (refresh?: boolean) => void;
  task: Task | null;
  projectId: string;
  allProjectTasks: Task[];
  onTaskUpdated: (updatedTask: Task) => void;
  onStartCrafting: (task: Task, targetView?: ActiveMainTabType) => void;
}


export interface CreateTaskFromChatMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTitle: string;
  initialDescription?: string;
  projectId: string;
  onTaskCreated: (taskTitle: string) => void; 
}

export interface ProjectSidebarProps {
  projectId: string;
}

// Original ChatDiscussionAreaOutletContextType and ChatDiscussionAreaRoundtableContext are removed
// as ChatDiscussionArea itself is removed. SandboxChat will use ProjectWorkspacePageOutletContext.
// TerritoryChat takes props directly.
export interface ChatDiscussionAreaRoundtableContext {
  isRoundtableModeActive: boolean;
  setIsRoundtableModeActive: (isActive: boolean) => void;
  roundtableParticipants: RoundtableParticipant[];
  setRoundtableParticipants: React.Dispatch<React.SetStateAction<RoundtableParticipant[]>>;
  activeRoundtableParticipantIds: string[];
  roundtableDiscussionRounds: number;
  setRoundtableDiscussionRounds: (rounds: number) => void;
  isMeetingInProgress: boolean;
  setIsMeetingInProgress: (inProgress: boolean) => void;
  currentRoundtableTurn: number;
  currentRoundtableSpeakerId: string | null;
  roundtableChatHistory: ChatMessage[];
  roundtableToDoItems: ToDoItem[];
  setRoundtableToDoItems: React.Dispatch<React.SetStateAction<ToDoItem[]>>;
  roundtableSharedDraft: string;
  setRoundtableSharedDraft: (draft: string) => void;
  startNewRoundtableMeeting: (initialPrompt: string) => Promise<void>;
  sendCaptainIntervention: (interventionText: string) => Promise<void>;
  meetingId: string | null;
  topic: string | null;
  onSaveRoundtableMessage: (message: ChatMessage) => Promise<void>;
  onUpdateRoundtableMessage?: (message: ChatMessage) => Promise<void>;
  onPublishSingleToDo?: (item: ToDoItem) => Promise<void>;
  onPublishAllToDos?: (items: ToDoItem[]) => Promise<void>;
  allCharactersForPanel: Character[]; 
}
      