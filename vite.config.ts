
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'node:path';
import electron from 'vite-plugin-electron/simple'; 
import pkg from './package.json';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig(({ command }) => {
  const isServe = command === 'serve';
  const isBuild = command === 'build';
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG;

  const mainExternals = [
    'electron', 
    'better-sqlite3', 
    // 'js-yaml' is removed as it's no longer a dependency
    ...Object.keys(pkg.dependencies || {}).filter(dep => !['react', 'react-dom', 'react-router-dom'].includes(dep) && dep !== 'node-pty') 
  ];

  return {
    base: isBuild ? './' : '/', 
    plugins: [
      react(),
      electron({
        main: {
          entry: 'electron/main.ts', 
          onstart(args) {
            if (isServe) {
              console.log('✅ [electron-main] starting...');
              args.startup(); 
            }
          },
          vite: {
            // Added resolve.alias here for the main process build
            resolve: {
              alias: {
                '@': path.resolve(__dirname, './src'),
              },
            },
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron', 
              rollupOptions: {
                external: mainExternals,
              },
            },
          },
        },
        preload: {
          input: path.join(__dirname, 'electron/preload.ts'), 
          onstart(args) {
            if (isServe) {
              console.log('✅ [electron-preload] reloading...');
              args.reload(); 
            }
          },
           vite: {
            // Added resolve.alias here for the preload process build as well, for consistency
            resolve: {
              alias: {
                '@': path.resolve(__dirname, './src'),
              },
            },
            build: {
              sourcemap: sourcemap ? 'inline' : undefined,
              minify: isBuild,
              outDir: 'dist-electron',
              rollupOptions: {
                external: mainExternals,
              },
              lib: {
                entry: path.join(__dirname, 'electron/preload.ts'),
                formats: ['es'], 
                fileName: () => 'preload.mjs', 
              },
            },
          },
        },
        renderer: {}, 
      }),
      viteStaticCopy({ 
        targets: [
           { 
            src: 'public/default_at_cms_assets/**/*', // Assets like images for AT CMS
            dest: 'system/default_at_cms_assets' // Copied to dist-electron/system/default_at_cms_assets
          },
          {
            src: 'resources/system/**/*', // Includes characters, themes, and now at_cms_definitions JSONs
            dest: 'system' // Copied to dist-electron/system
          }
        ],
        hook: 'writeBundle' 
      })
    ],
    server: {
      port: 5173, 
    },
    build: {
      outDir: 'dist', 
      sourcemap: isServe, 
      rollupOptions: {
      }
    },
    resolve: { // This is the top-level alias for the renderer process
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    optimizeDeps: {
      include: ['lucide-react'], 
      exclude: ['better-sqlite3'] 
    }
  };
});
