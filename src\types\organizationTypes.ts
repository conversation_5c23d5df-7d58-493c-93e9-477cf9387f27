// src/types/organizationTypes.ts

export interface Post {
  id: string;
  name: string;
  description: string;
}

export interface Character {
  id: string;
  name: string;
  type: "character"; // Added type property
  persona_prompt: string; // Remains string, parseTgcJson will handle array from JSON
  avatar_path?: string | null;
  default_post_id?: string;
}

export interface Assignment {
  post_id: string;
  character_id: string;
}