// src/components/task_cockpit/LogOutputDisplay.tsx
import React from 'react';
import type { StreamLogEntry } from '@/pages/TaskCockpitPage';
import { Icon } from '@/components/common/Icon';

interface LogOutputDisplayProps {
  logEntry: StreamLogEntry;
  isFocusedView?: boolean; // True if this component is rendering the entire log view in the right panel
}

export const LogOutputDisplay: React.FC<LogOutputDisplayProps> = ({ logEntry, isFocusedView = false }) => {
  const getLogColor = () => {
    switch (logEntry.logType) {
      case 'stdout': return 'text-tg-text-primary';
      case 'stderr': return 'text-red-400';
      case 'system': return 'text-sky-400 italic';
      default: return 'text-tg-text-secondary';
    }
  };
  
  const timestamp = new Date(logEntry.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });

  if (isFocusedView) { // Detailed view for right panel
    return (
      <div className={`py-0.5 px-1 text-xs font-mono flex ${getLogColor()}`}>
        <span className="text-gray-500 mr-2 flex-shrink-0 w-16">[{timestamp}]</span>
        {logEntry.systemPid && <span className="text-purple-400 mr-1.5 flex-shrink-0 w-10">(P:{logEntry.systemPid})</span>}
        <span className="whitespace-pre-wrap break-all flex-grow">{logEntry.text}</span>
      </div>
    );
  }

  // Compact view for main interaction stream
  return (
    <div className={`p-2 rounded-md text-xs font-mono max-w-[85%] self-start my-1 shadow-sm border
                    ${logEntry.logType === 'stderr' ? 'bg-red-900/30 border-red-700/50' : 
                      logEntry.logType === 'system' ? 'bg-sky-900/30 border-sky-700/50' : 
                      'bg-tg-bg-tertiary border-tg-border-primary/70'}`}>
      <div className="flex justify-between items-center text-[10px] text-tg-text-placeholder mb-1">
        <span>日志条目 (命令: {logEntry.command ? `"${logEntry.command.substring(0,15)}..."` : '未知'})</span>
        <span>{timestamp}</span>
      </div>
      <pre className={`whitespace-pre-wrap break-all overflow-x-auto custom-scrollbar p-1 rounded bg-tg-bg-primary ${getLogColor()}`}>
        <code>{logEntry.text}</code>
      </pre>
    </div>
  );
};