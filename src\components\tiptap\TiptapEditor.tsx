import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react'; // Removed Editor from here
import type { Editor } from '@tiptap/core'; // Added type import from core
import StarterKit from '@tiptap/starter-kit';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import Placeholder from '@tiptap/extension-placeholder';

interface TiptapEditorProps {
  initialContent: string;
  onContentChange: (newHtml: string) => void; // Added onContentChange prop
  editorRef?: React.MutableRefObject<Editor | null>; 
  isEditable?: boolean;
  placeholder?: string;
}

export const TiptapEditor: React.FC<TiptapEditorProps> = ({ 
  initialContent, 
  onContentChange, // Destructure onContentChange
  editorRef,
  isEditable = true,
  placeholder = "输入内容..."
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false,
        // placeholder: false, // Removed: Placeholder extension handles this
      }),
      Highlight.configure({ multicolor: true }),
      TextStyle,
      Color,
      Placeholder.configure({
        placeholder: placeholder,
      }),
    ],
    content: initialContent,
    editable: isEditable,
    onUpdate: ({ editor: currentEditor }) => { // Use onUpdate to call onContentChange
      onContentChange(currentEditor.getHTML());
    },
  });

  React.useEffect(() => {
    if (editor && editorRef) {
      editorRef.current = editor;
    }
    return () => {
      if (editorRef) {
        editorRef.current = null;
      }
    };
  }, [editor, editorRef]);

  React.useEffect(() => {
    if (editor && initialContent !== editor.getHTML()) {
      // Only update if content is truly different AND if it's meant to be empty.
      // Tiptap's default empty content is '<p></p>'
      const isTiptapEmpty = editor.getHTML() === '<p></p>';
      if (initialContent === '' && !isTiptapEmpty) { // If new initial content is empty string
        editor.commands.setContent('', false); // Explicitly set to empty string
      } else if (initialContent === '' && isTiptapEmpty) {
        // Do nothing if new initial is empty AND tiptap is already empty (<p></p>)
      }
      else if (initialContent !== '') { // If new initial content is not empty
         editor.commands.setContent(initialContent, false); 
      }
    }
  }, [initialContent, editor]);

  React.useEffect(() => {
    if (editor && editor.isEditable !== isEditable) {
      editor.setEditable(isEditable);
    }
  }, [isEditable, editor]);


  if (!editor) {
    return null;
  }

  return (
    <EditorContent 
        editor={editor} 
        className="ProseMirror flex-grow" // Added flex-grow for better layout within flex container
        // data-placeholder is handled by the Placeholder extension now
    />
  );
};