// src/components/ProjectSidebar.tsx
import React, { useState, useRef, useEffect } from 'react';
import { NavLink, useParams, useLocation } from 'react-router-dom';
import { Icon } from '@/components/common/Icon';

interface ProjectSidebarProps {
  projectId: string;
}

const mainNavItems = [
  { name: '战略沙盘', path: 'discussion', iconName: 'MessagesSquare' as const },
  { name: '神谕罗盘', path: 'task-board', iconName: 'ClipboardList' as const },
  { name: '任务驾驶舱', path: 'cockpit', iconName: 'Rocket' as const },
  { name: '源码洞天', path: 'source-code', iconName: 'CodeSquare' as const }, 
];

const toolboxItems = [
  { name: '思路工坊', path: 'mind-workshop', iconName: 'Share2' as const },
  { name: '项目时间轴', path: 'timeline', iconName: 'Clock3' as const }, // Changed from Clock to Clock3
  { name: '琉璃坊', path: 'glazed-workshop', iconName: 'Palette' as const }, // Changed from Paintbrush
  { name: '项目知库', path: 'project-knowledge-base', iconName: 'Database' as const },
];

export const ProjectSidebar: React.FC<ProjectSidebarProps> = ({ projectId }) => {
  const [isToolboxOpen, setIsToolboxOpen] = useState(false);
  const toolboxButtonRef = useRef<HTMLButtonElement>(null);
  const toolboxPanelRef = useRef<HTMLDivElement>(null);
  const location = useLocation(); 
  const { '*': currentSubPath = 'discussion' } = useParams<{ '*': string }>();


  const getNavLinkClass = (pathSegment: string): string => {
    const baseClasses = 'flex flex-col items-center justify-center p-3 rounded-lg transition-colors duration-150 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-tg-accent-primary focus:ring-offset-tg-bg-primary w-full text-center';
    // For project sidebar, active state depends on the full path including /project/:projectId/
    const fullPath = `/project/${projectId}/${pathSegment}`;
    // Check if the current location.pathname starts with the fullPath.
    // For the 'discussion' path, it might be the index route, so also check if the path is exactly /project/:projectId
    const isActive = location.pathname.startsWith(fullPath) || (pathSegment === 'discussion' && location.pathname === `/project/${projectId}`);
    
    const activeClasses = 'bg-tg-accent-primary text-white shadow-lg';
    const inactiveClasses = 'text-tg-text-secondary hover:bg-tg-bg-hover hover:text-tg-text-primary';
    return `${baseClasses} ${isActive ? activeClasses : inactiveClasses}`;
  };
  
  const getToolboxNavLinkClass = (pathSegment?: string): string => {
    const baseClasses = 'flex items-center w-[calc(100%-0.5rem)] mx-1 px-3 py-2 text-sm rounded-md transition-colors';
    const fullPath = pathSegment ? `/project/${projectId}/${pathSegment}` : '';
    // Exact match for toolbox items or startsWith for paths that might have further sub-routes
    const isActive = pathSegment && (location.pathname === fullPath || location.pathname.startsWith(`${fullPath}/`));
    
    const activeClasses = 'bg-tg-accent-secondary text-white';
    const inactiveClasses = 'text-tg-text-primary hover:bg-tg-bg-hover';
    return `${baseClasses} ${isActive ? activeClasses : inactiveClasses}`;
  };


  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isToolboxOpen &&
        toolboxButtonRef.current && !toolboxButtonRef.current.contains(event.target as Node) &&
        toolboxPanelRef.current && !toolboxPanelRef.current.contains(event.target as Node)
      ) {
        setIsToolboxOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isToolboxOpen]);


  return (
    <div className="w-24 bg-tg-bg-secondary p-2 flex flex-col items-center space-y-2.5 border-r border-tg-border-primary shadow-md flex-shrink-0">
      {mainNavItems.map(item => (
        <NavLink
          key={item.path}
          to={`/project/${projectId}/${item.path}`}
          className={getNavLinkClass(item.path)}
          title={item.name}
          aria-label={item.name}
        >
          <Icon name={item.iconName} className="w-6 h-6 mb-0.5" />
          <span className="text-[11px] font-medium leading-tight">{item.name}</span>
        </NavLink>
      ))}
      <div className="mt-auto w-full relative">
        <button
          ref={toolboxButtonRef}
          onClick={() => setIsToolboxOpen(!isToolboxOpen)}
          className={`${getNavLinkClass('')} w-full ${isToolboxOpen ? 'bg-tg-accent-secondary text-white' : ''}`} 
          title="百宝袋"
          aria-label="打开百宝袋"
          aria-haspopup="true"
          aria-expanded={isToolboxOpen}
        >
          <Icon name="Wrench" className="w-6 h-6 mb-0.5" />
          <span className="text-[11px] font-medium leading-tight">百宝袋</span>
        </button>
        {isToolboxOpen && (
          <div
            ref={toolboxPanelRef}
            className="absolute bottom-0 left-full ml-2 mb-0 w-48 bg-tg-bg-tertiary border border-tg-border-primary rounded-lg shadow-xl py-1.5 z-50"
            role="menu"
          >
            {toolboxItems.map(item => (
              <NavLink
                key={item.path}
                to={`/project/${projectId}/${item.path}`}
                onClick={() => setIsToolboxOpen(false)}
                className={getToolboxNavLinkClass(item.path)}
                role="menuitem"
              >
                <Icon name={item.iconName} className="w-4 h-4 mr-2.5 flex-shrink-0" />
                {item.name}
              </NavLink>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};