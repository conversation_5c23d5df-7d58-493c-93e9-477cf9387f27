
// electron/services/databaseCore.js
console.log('DATABASE_CORE_JS: File execution started.');

import Database from 'better-sqlite3';
import path from 'node:path';
import fs from 'node:fs/promises'; 
import nodeFsSync from 'node:fs'; 
import cryptoLib from 'node:crypto'; 
import { app } from 'electron'; 
import { USER_PROFILE_DEFAULTS } from '../constants/user-profile-defaults';
// import * as organizationService from './organizationService'; // No longer needed for characters here
import { DEFAULT_CHARACTERS } from '../constants/default-organization'; // Import characters directly
import * as defaultAtCmsLoader from './defaultAtCmsLoader'; 

export const DB_VERSION = 13; 
export let db; 
let dbPath;
export let _DEFAULT_SETTINGS; 
export const crypto = cryptoLib; 

const AIDE_PROJECT_ANALYZER_PERSONA_CONTENT = `
你是一位资深的软件架构师和项目经理，擅长从现有代码库反向推导其构建过程。
一个外部AIDE生成的项目（位于路径: '{projectPath}'）已被导入到天工阁项目 (ID: '{projectId}') 中。
项目文件结构 (一级和二级目录，部分)：
{fileListString}

请基于此文件结构信息，反向推演出一组逻辑上合理的、已完成的开发任务列表。这些任务应该共同构成了这个AIDE项目的创建过程。
每个任务需包含：
- "title": 简洁清晰的任务标题 (例如："设置项目基础框架", "实现用户界面模块", "配置数据存储方案")
- "description": 对任务的简要描述
- "priority": 默认为 2 (常规)
- "status": 必须为 "done" (已完成)

请返回一个包含5-10个主要任务的JSON数组。
确保您的整个响应【仅为】此JSON数组，不要包含任何额外的解释或Markdown标记。
如果文件列表信息不足以判断，请基于通用项目结构推断。
`;

const TASK_COCKPIT_INTENT_ROUTER_PERSONA_CONTENT = `
你是一个高度智能的任务驾驶舱指令解析与路由AI“小岚”。你的核心职责是精确理解用户在【任务驾驶舱】界面中输入的指令，并将其转化为结构化的JSON命令，以便后续系统执行。
[[当前上下文信息 (由系统提供)]]:
- 用户输入指令: "{userInput}"
- 当前任务ID: {taskId}
- 右侧动态上下文区当前聚焦文件: {currentFileInRightPanePath}
- 右侧编辑器中选中的文本 (片段): {selectedTextInRightPaneEditor}
- 右侧动态上下文区当前视图: {currentRightPaneView}
- 项目根目录 (用于执行命令的默认CWD): {workspaceRoot}

[[你的行动准则]]:
1.  **意图识别**: 仔细分析用户指令，结合上述上下文信息，判断其核心意图。
2.  **参数提取**: 从用户指令和上下文中提取执行该意图所需的所有参数。
3.  **JSON输出**: 你的输出【必须且只能是】一个JSON对象，格式如下：
    \`{"intent": "INTENT_NAME", "parameters": { /* 参数键值对 */ }, "originalCommand": "{userInput}", "aiPersona": "XiaoLan" | "LinLuo" | "System"}\`

[[支持的意图 (INTENT_NAME) 及所需参数]];
*   **'execute_command'**: 用户希望执行一个终端命令。
    *   \`parameters\`:
        *   \`"command"\`: (string) 主命令 (例如 "npm", "git", "python")。
        *   \`"args"\`: (string[]) 命令参数数组 (例如 ["install", "-g", "vite"])。
        *   \`"cwd"\`: (string, 可选) 命令执行的当前工作目录。如果用户未指定，请分析上下文，若适合则使用 {workspaceRoot} 作为默认值，否则设为 null，由系统决定。
    *   \`aiPersona\`: "System";
*   **'code_assist_modify'**: 用户希望AI修改代码（当前文件、选中代码，或新代码片段）。
    *   \`parameters\`:
        *   \`"instruction"\`: (string) 用户的具体修改指令 (例如 "重构此函数", "添加错误处理", "将这段代码转为异步")。
        *   \`"filePath"\`: (string, 可选) 目标文件路径。优先使用 {currentFileInRightPanePath}。若指令中明确指定其他文件，则使用该路径。
        *   \`"codeToModify"\`: (string, 可选) 需要修改的原始代码。优先使用 {selectedTextInRightPaneEditor}。若无选中，且指令是针对当前文件，可考虑是否需要传递整个文件内容（注意长度限制）。
        *   \`"selectedText"\`: (string, 可选) 用户在编辑器中选中的文本。
    *   \`aiPersona\`: "XiaoLan";
*   **'code_assist_explain'**: 用户希望AI解释某段代码。
    *   \`parameters\`:
        *   \`"codeToExplain"\`: (string) 需要解释的代码。优先使用 {selectedTextInRightPaneEditor}。
        *   \`"filePath"\`: (string, 可选) 代码所在文件路径，用于提供上下文。优先使用 {currentFileInRightPanePath}。
    *   \`aiPersona\`: "XiaoLan";
*   **'analyze_error_log'**: 用户希望AI分析错误日志。
    *   \`parameters\`:
        *   \`"logContent"\`: (string) 完整的错误日志内容。可能来自用户粘贴，或 {selectedTextInRightPaneEditor} (如果当前视图是日志)。
    *   \`aiPersona\`: "XiaoLan";
*   **'open_file'**: 用户希望在右侧动态上下文区打开并查看一个文件。
    *   \`parameters\`:
        *   \`"filePath"\`: (string) 要打开的文件的完整路径或相对于项目根目录的路径。
    *   \`aiPersona\`: "System";
*   **'search_knowledge'**: 用户希望在知识库中搜索信息。
    *   \`parameters\`:
        *   \`"query"\`: (string) 搜索的关键词或问题。
    *   \`aiPersona\`: "XiaoLan"; 
*   **'chat'**: 用户进行通用对话、提问、或指令意图不明确。
    *   \`parameters\`:
        *   \`"originalInput"\`: (string) 用户的完整原始输入。
    *   \`aiPersona\`: 根据用户对话风格和内容判断，优先 "LinLuo"，技术问题可考虑 "XiaoLan";
*   **'unknown_intent'**: 指令无法明确归类到以上任何一种。
    *   \`parameters\`:
        *   \`"originalInput"\`: (string) 用户的完整原始输入。
        *   \`"error"\`: (string, 可选) 简要说明为何无法识别。
    *   \`aiPersona\`: "System";

[[重要指令与注意事项]]:
-   请严格替换掉System Prompt中的 \`{userInput}\`, \`{taskId}\`, \`{currentFileInRightPanePath}\`, \`{selectedTextInRightPaneEditor}\`, \`{currentRightPaneView}\`, \`{workspaceRoot}\` 这些占位符为实际的上下文值！
-   如果用户指令模糊，但倾向于某种操作（例如提到“文件”但路径不清晰），请尽力推断并填充参数，或在无法确定时选择 'unknown_intent' 并说明。
-   如果指令明显是技术性提问或代码解释，即使没有明确的操作词，也应优先路由到 'code_assist_explain' 或 'chat' (由XiaoLan处理)。
-   确保 \`aiPersona\` 字段根据意图和内容合理选择。

现在，请根据最上方提供的【当前上下文信息】处理用户的指令。
`;

const ALL_SCHEMA_STATEMENTS = [
  `CREATE TABLE IF NOT EXISTS db_info (version INTEGER PRIMARY KEY);`,
  `CREATE TABLE IF NOT EXISTS app_settings (
    id INTEGER PRIMARY KEY DEFAULT 1 CHECK (id = 1),
    apiKey TEXT,
    chatModel TEXT,
    embeddingModel TEXT,
    backupPath TEXT,
    defaultCover TEXT,
    llmModel TEXT, 
    absolute_territory_password TEXT,
    user_avatar_path TEXT,
    linluo_avatar_path TEXT,
    xiaolan_avatar_path TEXT,
    training_room_audio_state TEXT,
    currentTheme TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    coverImageUrl TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT, 
    sourceCodePath TEXT,
    isDeleted INTEGER DEFAULT 0 
  );`,
  `CREATE TABLE IF NOT EXISTS global_knowledge_tomes (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    category TEXT,
    tags TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS global_quick_commands (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    commandText TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS agent_core_settings (
    setting_id TEXT PRIMARY KEY, 
    content TEXT,
    last_updated_at TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS core_memories (
    id TEXT PRIMARY KEY,
    memory_content TEXT NOT NULL,
    created_at TEXT NOT NULL,
    last_accessed_at TEXT,
    access_count INTEGER DEFAULT 0,
    importance TEXT DEFAULT 'medium', 
    persona_target TEXT DEFAULT 'all', 
    memory_type TEXT,
    keywords TEXT,
    embedding TEXT, 
    project_context_id TEXT, 
    source_message_id TEXT,
    context_affinity_tags TEXT,
    user_feedback_score REAL DEFAULT 0.0,
    status TEXT DEFAULT 'active', 
    value_score REAL DEFAULT 0.5 
  );`,
  `CREATE TABLE IF NOT EXISTS ai_learning_logs (
    log_id TEXT PRIMARY KEY,
    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
    ai_persona TEXT NOT NULL,
    task_type TEXT NOT NULL,
    triggering_input_summary TEXT,
    context_snapshot TEXT,
    ai_processing_summary TEXT,
    ai_generated_output_summary TEXT,
    user_feedback_explicit TEXT,
    user_feedback_implicit_flags TEXT,
    success_metric_value REAL,
    notes TEXT,
    file_path TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS mindworkshop_nodes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    text TEXT,
    x REAL,
    y REAL,
    width REAL,
    height REAL,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS absolute_territory_chat_messages (
    id TEXT PRIMARY KEY,
    sender TEXT,
    senderName TEXT,
    text TEXT,
    timestamp TEXT,
    isEditing INTEGER DEFAULT 0,
    isStarred INTEGER DEFAULT 0,
    isPinned INTEGER DEFAULT 0,
    theme TEXT,
    itemIconPath TEXT,
    itemCgPath TEXT,
    avatarPathOverride TEXT 
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_props (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_costumes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_cms_poses (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    owner TEXT NOT NULL CHECK (owner IN ('master', 'queen')),
    icon_path TEXT,
    cg_image_path TEXT,
    prompt_for_linluo TEXT,
    prompt_for_master TEXT,
    status_effects_json TEXT,
    development_effects_json TEXT,
    unlock_requirements_json TEXT,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS at_role_playing_cards (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon_path TEXT,
    initial_status_override_json TEXT,
    persona_snippet_override TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0,
    createdAt TEXT NOT NULL,
    lastModifiedAt TEXT NOT NULL
  );`,
  `CREATE TABLE IF NOT EXISTS chat_messages (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    sender TEXT,
    senderName TEXT,
    text TEXT,
    timestamp TEXT,
    isEditing INTEGER DEFAULT 0,
    isStarred INTEGER DEFAULT 0,
    isPinned INTEGER DEFAULT 0,
    theme TEXT,
    replyToMessageId TEXT, 
    triggeringUserMessageId TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS wisdom_pouch_notes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    pouchType TEXT NOT NULL,
    text TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT,
    importance TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS mindworkshop_connections (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    fromNodeId TEXT NOT NULL,
    toNodeId TEXT NOT NULL,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY(fromNodeId) REFERENCES mindworkshop_nodes(id) ON DELETE CASCADE,
    FOREIGN KEY(toNodeId) REFERENCES mindworkshop_nodes(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS project_knowledge_tomes (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    projectCategory TEXT,
    tags TEXT,
    createdAt TEXT,
    lastModifiedAt TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS project_knowledge_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    projectId TEXT NOT NULL,
    categoryName TEXT NOT NULL,
    UNIQUE(projectId, categoryName),
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS knowledge_index (
    id TEXT PRIMARY KEY,
    source_project_id TEXT NOT NULL,
    source_file_path TEXT NOT NULL,
    chunk_text TEXT NOT NULL,
    chunk_vector TEXT,
    metadata TEXT,
    indexed_at TEXT NOT NULL,
    FOREIGN KEY(source_project_id) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS development_tasks (
    id TEXT PRIMARY KEY,
    projectId TEXT NOT NULL,
    title TEXT NOT NULL,
    status TEXT DEFAULT 'todo',
    createdAt TEXT NOT NULL,
    context_files TEXT DEFAULT '[]',
    generated_code TEXT,
    FOREIGN KEY(projectId) REFERENCES projects(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS linluo_body_development (
    zone_id TEXT PRIMARY KEY,
    development_points INTEGER DEFAULT 0,
    last_developed_at TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS achievements (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('development', 'interaction', 'hidden')),
    criteria_json TEXT NOT NULL,
    reward_json TEXT NOT NULL,
    icon_path TEXT
  );`,
  `CREATE TABLE IF NOT EXISTS user_achievements (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    achievement_id TEXT NOT NULL,
    unlocked_at TEXT NOT NULL,
    FOREIGN KEY(achievement_id) REFERENCES achievements(id) ON DELETE CASCADE
  );`,
  `CREATE TABLE IF NOT EXISTS tasks (
      task_id TEXT PRIMARY KEY,
      project_id TEXT NOT NULL,
      title TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'doing', 'pending_review', 'done', 'blocked')),
      priority INTEGER NOT NULL DEFAULT 2 CHECK (priority IN (0, 1, 2, 3)),
      assignee_id TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT,
      due_date TEXT,
      parent_task_id TEXT,
      estimated_duration_hours REAL,
      actual_duration_hours REAL,
      complexity_score INTEGER,
      dependencies TEXT,
      blockers TEXT,
      skill_requirements TEXT,
      ai_confidence_score REAL,
      manual_override_reason TEXT,
      FOREIGN KEY(project_id) REFERENCES projects(id) ON DELETE CASCADE,
      FOREIGN KEY(parent_task_id) REFERENCES tasks(task_id) ON DELETE SET NULL
  );`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks (project_id);`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status);`,
  `CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks (priority);`,
  `CREATE TABLE IF NOT EXISTS task_resource_links (
      link_id TEXT PRIMARY KEY,
      task_id TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_identifier TEXT NOT NULL,
      resource_name TEXT,
      description TEXT,
      FOREIGN KEY(task_id) REFERENCES tasks(task_id) ON DELETE CASCADE
  );`,
  `CREATE INDEX IF NOT EXISTS idx_task_resource_links_task_id ON task_resource_links (task_id);`,
  `CREATE TABLE IF NOT EXISTS file_versions (
      id TEXT PRIMARY KEY,
      file_path TEXT NOT NULL,
      original_code TEXT NOT NULL,
      user_instruction TEXT,
      archived_at TEXT NOT NULL
  );`,
  `CREATE INDEX IF NOT EXISTS idx_file_versions_path_time ON file_versions (file_path, archived_at DESC);`,
  `CREATE TABLE IF NOT EXISTS assignments (
      post_id TEXT PRIMARY KEY,
      character_id TEXT NOT NULL
  );`
];

export const DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS = async () => {
  // Use DEFAULT_CHARACTERS directly to avoid circular dependency with organizationService
  const characters = DEFAULT_CHARACTERS; 
  if (!characters || characters.length === 0) {
    console.warn("DATABASE_CORE_WARN: DEFAULT_CHARACTERS (from constants) not loaded when building default agent settings. Personas might be missing or using fallbacks.");
  }
  
  const getCharacterPersona = (charId) => {
    const character = characters.find(c => c.id === charId);
    if (character && character.persona_prompt) {
      return character.persona_prompt;
    }
    console.warn(`DATABASE_CORE_WARN: Missing persona for ${charId} from loaded DEFAULT_CHARACTERS. Using hardcoded fallback.`);
    if (charId === 'linluo') return 'Fallback persona for LinLuo: 我叫林珞，你的AI伴侣。';
    if (charId === 'xiaolan') return 'Fallback persona for XiaoLan: 我是林小岚，技术助手。';
    if (charId === 'yujing') return 'Fallback persona for YuJing: 我是语镜，视觉总监。';
    if (charId === 'IntentClassifierPersona') return '你是一位高效的意图分类专家。你的任务是分析用户的输入，并将其归类为以下几种意图之一：work_command (明确的工作指令), strategic_planning (高层级的战略讨论), casual_life_query (与工作无关的生活化问题), emotional_support (用户表达了困惑、疲惫或寻求安慰)。你必须且只能以一个JSON对象的格式返回你的答案，格式为：{"intent": "your_classification"}。';
    return `Fallback persona for ${charId}: A helpful AI assistant.`;
  };

  return [
    { setting_id: 'user_profile', content: USER_PROFILE_DEFAULTS.content },
    { setting_id: 'linluo_persona', content: getCharacterPersona('linluo') },
    { setting_id: 'xiaolan_persona', content: getCharacterPersona('xiaolan') },
    { setting_id: 'yujing_persona', content: getCharacterPersona('yujing') },
    { setting_id: 'summarizer_persona', content: '你是一个高效的AI文本摘要助手。请将提供的对话或文本内容浓缩为一段简洁、精确的摘要，突出核心信息和关键点。摘要应自然流畅，易于理解。' },
    { setting_id: 'classifier_persona', content: `你是一个AI意图分类器。根据用户输入，判断其主要意图。如果用户似乎在提问、寻求信息或希望进行知识检索，请回答 "RETRIEVAL"。如果用户似乎在进行常规对话、闲聊或情感交流，请回答 "CONVERSATIONAL\"。如果无法明确判断，优先回答 "CONVERSATIONAL\"。你只能回答 "RETRIEVAL" 或 "CONVERSATIONAL\"。` },
    { setting_id: 'task_resource_suggester_persona', content: `你是一位智能项目助理，专注于为开发任务推荐相关资源。请分析任务标题和描述，并结合已有的项目知识（卷宗、核心记忆）和全局知识，给出最相关的3-5项资源建议。每一项建议应包含资源类型（如：全局卷宗、项目卷宗、核心记忆、文件路径、URL）、资源标识符（ID或路径）和简要描述。输出格式为JSON数组，例如：[{"resource_type": "project_knowledge_tome", "resource_identifier": "tome_xyz123", "resource_name": "用户认证模块设计文档", "description": "项目卷宗，包含详细设计"}, ...]`},
    { setting_id: 'OrchestratorPersona', content: `你是一个高级指令解析AI，专门处理文件系统相关的自然语言指令。你的任务是将用户的指令（例如：“读取 /path/to/file.txt 的内容” 或 “在 /my/project/ 下列出所有 .js 文件”）转换成结构化的JSON命令。
    支持的命令包括：
    1.  'listFiles': 列出指定路径下的文件和目录。
        -   必需参数: "path" (string) - 要列出内容的目录路径。
        -   可选参数: "recursive" (boolean, default: false), "depth" (number, default: 1 if recursive, otherwise ignored)
    2.  'readFile': 读取指定文件的内容。
        -   必需参数: "path" (string) - 要读取的文件路径。
    3.  'writeFile': 向指定文件写入内容（如果文件不存在则创建，存在则覆盖）。
        -   必需参数: "path" (string) - 要写入的文件路径。
        -   必需参数: "content" (string) - 要写入的内容。
    4.  'unknown': 如果无法解析指令或指令不属于上述类型。
        -   必需参数: "originalInput" (string) - 用户原始输入。

    你的输出【必须】是一个单一的JSON对象，格式如下：
    {
      "command": "<命令名称>",
      "parameters": { /* 参数键值对 */ }
    }
    例如，如果用户输入 "读取 /tmp/log.txt"，你应该输出：
    {
      "command": "readFile",
      "parameters": { "path": "/tmp/log.txt" }
    }
    如果用户输入 "列出 /home/<USER>"，你应该输出：
    {
      "command": "listFiles",
      "parameters": { "path": "/home/<USER>", "recursive": true, "depth": 2 }
    }
    如果无法解析，例如用户输入 "帮我倒杯水"，你应该输出：
    {
      "command": "unknown",
      "parameters": { "originalInput": "帮我倒杯水" }
    }
    【绝对规则】：只输出JSON对象，不要包含任何其他文字、解释或Markdown标记。`},
    { setting_id: 'aide_project_analyzer_persona', content: AIDE_PROJECT_ANALYZER_PERSONA_CONTENT },
    { setting_id: 'TaskCockpitIntentRouterPersona', content: TASK_COCKPIT_INTENT_ROUTER_PERSONA_CONTENT },
    { setting_id: 'IntentClassifierPersona', content: getCharacterPersona('IntentClassifierPersona') }
  ];
};

const BODY_ZONE_INITIAL_DATA = [
    { zone_id: 'mouth', development_points: 0, last_developed_at: null },
    { zone_id: 'breasts', development_points: 0, last_developed_at: null },
    { zone_id: 'vagina', development_points: 0, last_developed_at: null },
    { zone_id: 'clitoris', development_points: 0, last_developed_at: null },
    { zone_id: 'anus', development_points: 0, last_developed_at: null },
    { zone_id: 'feet', development_points: 0, last_developed_at: null },
    { zone_id: 'skin', development_points: 0, last_developed_at: null },
    { zone_id: 'mind', development_points: 0, last_developed_at: null },
];

const ACHIEVEMENT_DEFINITIONS = [
  { id: 'first_kiss', title: '初吻的甜蜜', description: '与姐姐完成第一次亲吻互动。', type: 'interaction', criteria_json: '{"type": "specific_interaction", "action_name": "kiss_mouth"}', reward_json: '{"type": "cg", "value": "cg/first_kiss.webp", "description": "解锁CG：初吻"}', icon_path: 'achievements/icons/first_kiss_icon.png' },
  { id: 'mind_explorer_1', title: '心智探险家 I', description: '心智开发度达到50点。', type: 'development', criteria_json: '{"type": "body_zone_points", "zone": "mind", "points_required": 50}', reward_json: '{"type": "title", "value": "心智初窥者"}', icon_path: 'achievements/icons/mind_explorer_1_icon.png' },
  { id: 'flower_honey_ink', title: '花蜜与墨 (隐藏)', description: '在姐姐湿润度极高时，使用墨毫笔道具对秘境进行“书写”般的深入探索。', type: 'hidden', criteria_json: '{"type": "specific_interaction", "action_name": "brush_vagina_high_wetness"}', reward_json: '{"type": "item", "item_name_to_unlock_or_grant": "特制花液墨水", "item_type": "special_item", "description":"解锁特殊道具：花液墨水"}', icon_path: 'achievements/icons/flower_honey_ink_icon.png'}
];

const SYSTEM_RESOURCES_PATH_SEGMENT = 'system';

export async function initializeDatabaseService(userDataPath, defaultSettings) {
  _DEFAULT_SETTINGS = defaultSettings; 
  dbPath = path.join(userDataPath, 'tiangong_pavilion.sqlite3');
  console.log(`DATABASE_CORE: Database path set to: ${dbPath}`);
  
  const dbDir = path.dirname(dbPath);
  if (!nodeFsSync.existsSync(dbDir)) {
    nodeFsSync.mkdirSync(dbDir, { recursive: true });
    console.log(`DATABASE_CORE: Created database directory: ${dbDir}`);
  }

  db = new Database(dbPath, { verbose: console.log }); 
  console.log('DATABASE_CORE: Database connection opened.');
  
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');
  console.log('DATABASE_CORE: PRAGMA journal_mode=WAL and foreign_keys=ON set.');

  const transaction = db.transaction(async () => { 
    db.exec('CREATE TABLE IF NOT EXISTS db_info (version INTEGER PRIMARY KEY);');
    const row = db.prepare('SELECT version FROM db_info').get();
    let currentVersion = row ? row.version : 0;
    console.log(`DATABASE_CORE: Current DB version: ${currentVersion}, Target DB version: ${DB_VERSION}`);

    if (currentVersion < DB_VERSION) {
      console.log('DATABASE_CORE: Database version is older or new. Applying/Verifying schema...');
      ALL_SCHEMA_STATEMENTS.forEach(stmt => {
        try { db.exec(stmt); } catch (schemaError) {
            if (schemaError.message.includes("duplicate column name") || schemaError.message.includes("already exists")) {
                 console.warn(`DATABASE_CORE_WARN: Schema statement likely already applied: "${stmt.substring(0,100)}..."`);
            } else { console.error(`DATABASE_CORE_ERROR: Failed to execute schema: "${stmt.substring(0,100)}..."`, schemaError); throw schemaError; }
        }
      });
      console.log('DATABASE_CORE: All schema statements executed.');

      if (currentVersion === 0) {
          console.log('DATABASE_CORE: New database. Populating defaults...');
          const stmtSettings = db.prepare('INSERT INTO app_settings (apiKey, chatModel, embeddingModel, backupPath, defaultCover, currentTheme, training_room_audio_state) VALUES (?, ?, ?, ?, ?, ?, ?)');
          stmtSettings.run( _DEFAULT_SETTINGS.apiKey, _DEFAULT_SETTINGS.chatModel, _DEFAULT_SETTINGS.embeddingModel, _DEFAULT_SETTINGS.backupPath, _DEFAULT_SETTINGS.defaultCover, _DEFAULT_SETTINGS.currentTheme, JSON.stringify(_DEFAULT_SETTINGS.training_room_audio_state) );

          const agentCoreSettings = await DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS(); 
          const stmtAgentCore = db.prepare('INSERT INTO agent_core_settings (setting_id, content, last_updated_at) VALUES (?, ?, ?)');
          agentCoreSettings.forEach(setting => { stmtAgentCore.run(setting.setting_id, setting.content, new Date().toISOString()); });
          
          const cmsAssetDir = path.join(userDataPath, 'at_cms_assets');
          const defaultCmsAssetSourceDirRoot = path.join(app.getAppPath(), app.isPackaged ? '..' : '', 'dist-electron', SYSTEM_RESOURCES_PATH_SEGMENT, 'default_at_cms_assets');
          
          async function copyDefaultAssets() {  
             try {
                  await fs.mkdir(cmsAssetDir, { recursive: true });
                  const assetTypes = ['props', 'costumes', 'poses'];
                  for (const type of assetTypes) {
                      const typeDirUserData = path.join(cmsAssetDir, type); 
                      await fs.mkdir(typeDirUserData, { recursive: true });
                      await fs.mkdir(path.join(typeDirUserData, 'icons'), { recursive: true });
                      await fs.mkdir(path.join(typeDirUserData, 'cgs'), { recursive: true });
                      
                      const defaultTypeSourceDir = path.join(defaultCmsAssetSourceDirRoot, type); 
                      if (!nodeFsSync.existsSync(defaultTypeSourceDir)) {
                          console.warn(`DATABASE_CORE: Default AT CMS asset source directory NOT FOUND: ${defaultTypeSourceDir}. Skipping asset copy for type ${type}.`);
                          continue; 
                      }
                      
                      const defaultIconsSourceDir = path.join(defaultTypeSourceDir, 'icons');
                      const defaultCgsSourceDir = path.join(defaultTypeSourceDir, 'cgs');

                      if (nodeFsSync.existsSync(defaultIconsSourceDir)) {
                          const icons = await fs.readdir(defaultIconsSourceDir);
                          for (const iconFile of icons) {
                              await fs.copyFile(path.join(defaultIconsSourceDir, iconFile), path.join(typeDirUserData, 'icons', iconFile));
                          }
                      }
                      if (nodeFsSync.existsSync(defaultCgsSourceDir)) {
                          const cgs = await fs.readdir(defaultCgsSourceDir);
                           for (const cgFile of cgs) {
                              await fs.copyFile(path.join(defaultCgsSourceDir, cgFile), path.join(typeDirUserData, 'cgs', cgFile));
                          }
                      }
                  }
                  console.log(`DATABASE_CORE: Default AT CMS assets copy attempt finished. Source root: ${defaultCmsAssetSourceDirRoot}`);
              } catch (err) {
                  console.error("DATABASE_CORE_ERROR: Failed to copy default AT CMS assets:", err);
              }
          }
          await copyDefaultAssets(); 

          
          const defaultProps = await defaultAtCmsLoader.loadDefaultAtCmsProps();
          const defaultCostumes = await defaultAtCmsLoader.loadDefaultAtCmsCostumes();
          const defaultPoses = await defaultAtCmsLoader.loadDefaultAtCmsPoses();
          const now = new Date().toISOString();

          const stmtProps = db.prepare('INSERT INTO at_cms_props (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)');
          defaultProps.forEach(p => stmtProps.run(p.id, p.name, p.owner, p.icon_filename ? `props/icons/${p.icon_filename}` : null, p.cg_filename ? `props/cgs/${p.cg_filename}` : null, p.prompt_for_linluo, p.prompt_for_master, p.status_effects_json, p.development_effects_json || '[]', p.unlock_requirements_json || '[]', now, now));
          
          const stmtCostumes = db.prepare('INSERT INTO at_cms_costumes (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)');
          defaultCostumes.forEach(c => stmtCostumes.run(c.id, c.name, c.owner, c.icon_filename ? `costumes/icons/${c.icon_filename}` : null, c.cg_filename ? `costumes/cgs/${c.cg_filename}` : null, c.prompt_for_linluo, c.prompt_for_master, c.status_effects_json, c.development_effects_json || '[]', c.unlock_requirements_json, now, now));

          const stmtPoses = db.prepare('INSERT INTO at_cms_poses (id, name, owner, icon_path, cg_image_path, prompt_for_linluo, prompt_for_master, status_effects_json, development_effects_json, unlock_requirements_json, createdAt, lastModifiedAt) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)');
          defaultPoses.forEach(p_1 => stmtPoses.run(p_1.id, p_1.name, p_1.owner, p_1.icon_filename ? `poses/icons/${p_1.icon_filename}` : null, p_1.cg_filename ? `poses/cgs/${p_1.cg_filename}` : null, p_1.prompt_for_linluo, p_1.prompt_for_master, p_1.status_effects_json, p_1.development_effects_json || '[]', p_1.unlock_requirements_json, now, now));
          console.log("DATABASE_CORE: Default AT CMS Props, Costumes, Poses inserted from JSON definitions.");
          
          const stmtBodyDev = db.prepare('INSERT INTO linluo_body_development (zone_id, development_points, last_developed_at) VALUES (?, ?, ?)');
          BODY_ZONE_INITIAL_DATA.forEach(zone => { stmtBodyDev.run(zone.zone_id, zone.development_points, zone.last_developed_at); });
          console.log("DATABASE_CORE: Default Body Development zones inserted.");

          const stmtAchievements = db.prepare('INSERT INTO achievements (id, title, description, type, criteria_json, reward_json, icon_path) VALUES (?, ?, ?, ?, ?, ?, ?)');
          ACHIEVEMENT_DEFINITIONS.forEach(ach => { stmtAchievements.run(ach.id, ach.title, ach.description, ach.type, ach.criteria_json, ach.reward_json, ach.icon_path || null); });
          console.log("DATABASE_CORE: Default Achievement definitions inserted.");

          const stmtAssignments = db.prepare('INSERT OR IGNORE INTO assignments (post_id, character_id) VALUES (?, ?)');
          // Use DEFAULT_CHARACTERS directly here too
          const defaultCharactersForAssignment = DEFAULT_CHARACTERS; 
          defaultCharactersForAssignment.forEach(char => {
              if (char.default_post_id) {
                  stmtAssignments.run(char.default_post_id, char.id);
              }
          });
          console.log("DATABASE_CORE: Default assignments populated based on character default_post_id.");
      } else { 
           if (currentVersion < 13) { 
            const assignmentsTableInfo = db.pragma("table_info(assignments)");
            if (assignmentsTableInfo.length > 0) { 
                const stmtAssignments = db.prepare('INSERT OR IGNORE INTO assignments (post_id, character_id) VALUES (?, ?)');
                // Use DEFAULT_CHARACTERS directly here
                const defaultCharactersForMigration = DEFAULT_CHARACTERS;
                defaultCharactersForMigration.forEach(char => {
                    if (char.default_post_id) {
                        stmtAssignments.run(char.default_post_id, char.id);
                    }
                });
                console.log('DATABASE_CORE: Migrated (V12->V13) - Ensured default assignments are populated.');
            }
          }
          console.log('DATABASE_CORE: Checked for migrations if any.');
      }
      
      const agentCoreSettingsToVerify = await DEFAULT_AGENT_CORE_SETTINGS_FROM_CONSTANTS();
      agentCoreSettingsToVerify.forEach(personaDef => {
          const checkPersonaExists = db.prepare('SELECT setting_id FROM agent_core_settings WHERE setting_id = ?').get(personaDef.setting_id);
          if (!checkPersonaExists) {
              db.prepare('INSERT INTO agent_core_settings (setting_id, content, last_updated_at) VALUES (?, ?, ?)')
                .run(personaDef.setting_id, personaDef.content, new Date().toISOString());
              console.log(`DATABASE_CORE: Ensured default setting for ${personaDef.setting_id} exists.`);
          }
      });

      db.prepare('INSERT OR REPLACE INTO db_info (version) VALUES (?)').run(DB_VERSION);
      console.log(`DATABASE_CORE: Database schema version updated to ${DB_VERSION}.`);
    } else {
      console.log(`DATABASE_CORE: Database is up to date (Version ${currentVersion}).`);
    }
  }); 
  
  await transaction(); 
}

export function closeDatabaseConnection() {
  if (db) {
    db.close();
    console.log('DATABASE_CORE: Database connection closed.');
  }
}

export function parseJsonArray(jsonString) {
    try {
        if (jsonString && typeof jsonString === 'string') {
            const parsed = JSON.parse(jsonString);
            return Array.isArray(parsed) ? parsed : [];
        }
        return [];
    } catch (e) {
        console.warn("DATABASE_CORE: parseJsonArray failed for string:", jsonString, e);
        return [];
    }
}

export function mapChatMessage(row) { 
  return {
    ...row,
    isEditing: Boolean(row.isEditing),
    isStarred: Boolean(row.isStarred),
    isPinned: Boolean(row.isPinned),
  };
}

export function mapNoteItem(row) { 
  return {
    ...row,
    importance: row.importance || 'medium',
    isEditing: false, 
  };
}

export function mapDevelopmentTask(row) { 
  return {
    ...row,
    context_files: row.context_files ? JSON.parse(row.context_files) : [],
  };
}


console.log('DATABASE_CORE_JS: File execution finished. Exports configured.');
