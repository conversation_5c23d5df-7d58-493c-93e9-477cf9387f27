
// electron/services/universalAssetService.js
console.log('UNIVERSAL_ASSET_SERVICE_JS: File execution started.');

import { app, protocol } from 'electron';
import nodeFs from 'node:fs';
import fsPromises from 'node:fs/promises';
import path from 'node:path';
import yaml from 'js-yaml'; // Assuming esm.sh/js-yaml can be imported like this

const ASSET_PACKS_DIR_NAME = 'asset_packs';
const TGC_ASSET_PROTOCOL = 'tgc-asset';

let globalAssetsCache = {
  props: [],
  costumes: [],
  poses: [],
  scene_cards: [],
  achievements: [],
  scripts: [],
  role_cards: [], 
};

let isInitialized = false;
let userDataPathInstance;

function validateAsset(assetData, filePath) {
  const requiredFields = ['id', 'type', 'name', 'description'];
  for (const field of requiredFields) {
    if (assetData[field] === undefined || assetData[field] === null) {
      console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Missing required field: ${field}`);
      return false;
    }
  }
  if (typeof assetData.id !== 'string' || !assetData.id.trim()) {
      console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'id'.`); return false;
  }
  if (typeof assetData.type !== 'string' || !assetData.type.trim()) {
      console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'type'.`); return false;
  }
  if (typeof assetData.name !== 'string' || !assetData.name.trim()) {
      console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for asset in ${filePath}. Invalid 'name'.`); return false;
  }
  
  // Type-specific validations
  if (assetData.type === 'scene_card' && (typeof assetData.background_image_path !== 'string' || !assetData.background_image_path.trim())) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for scene_card in ${filePath}. Missing or invalid 'background_image_path'.`); return false;
  }
  if (assetData.type === 'script' && (typeof assetData.title !== 'string' || !assetData.title.trim())) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for script in ${filePath}. Missing or invalid 'title'.`); return false;
  }
  if (assetData.type === 'script' && (!Array.isArray(assetData.scenes) || assetData.scenes.length === 0)) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Validation failed for script in ${filePath}. Missing or empty 'scenes' array.`); return false;
  }


  return true;
}

function resolveAssetPath(assetPackPath, relativePathInYaml) {
    if (!relativePathInYaml) return null;
    // assetPackPath is the full path to the .pack.yaml file
    // relativePathInYaml is the path string from the YAML (e.g., "./images/my_icon.png")
    
    const packDir = path.dirname(assetPackPath); // Directory containing the .pack.yaml
    
    // Important: Use path.resolve to correctly join packDir and relativePathInYaml.
    // This handles cases like relativePathInYaml being an absolute path (though not typical for packs)
    // or navigating up with "../"
    const absoluteDiskPath = path.resolve(packDir, relativePathInYaml);
    
    // To create the tgc-asset:// URL, we need a path relative to the assetPacksPath (userData/asset_packs)
    // but identify which pack it came from.
    // A simple way is: tgc-asset://<pack_file_name_without_extension>/<path_relative_to_pack_root_dir>
    // Example: if assetPackPath is /Users/<USER>/userData/asset_packs/my_stuff.pack.yaml
    // and relativePathInYaml is images/icon.png
    // packName = my_stuff
    // protocolPath = my_stuff/images/icon.png
    // URL = tgc-asset://my_stuff/images/icon.png
    
    const packFileName = path.basename(assetPackPath);
    const packNameWithoutExt = packFileName.replace(/\.pack\.yaml$/, '');

    // The relativePathInYaml is already relative to the YAML's directory (packDir).
    // We just need to make sure it's normalized for the URL.
    const normalizedRelativePath = relativePathInYaml.replace(/\\/g, '/'); // Ensure forward slashes for URL
    
    return `${TGC_ASSET_PROTOCOL}://${packNameWithoutExt}/${normalizedRelativePath}`;
}


async function loadAssetPacks() {
  if (!userDataPathInstance) {
    console.error('UNIVERSAL_ASSET_SERVICE: User data path not set. Cannot load asset packs.');
    return;
  }
  const assetPacksPath = path.join(userDataPathInstance, ASSET_PACKS_DIR_NAME);
  console.log(`UNIVERSAL_ASSET_SERVICE: Scanning for asset packs in: ${assetPacksPath}`);

  try {
    await fsPromises.mkdir(assetPacksPath, { recursive: true }); // Ensure directory exists
    const files = await fsPromises.readdir(assetPacksPath);
    const packFiles = files.filter(file => file.endsWith('.pack.yaml'));

    console.log(`UNIVERSAL_ASSET_SERVICE: Found ${packFiles.length} .pack.yaml files.`);

    const newGlobalAssetsCache = {
      props: [],
      costumes: [],
      poses: [],
      scene_cards: [],
      achievements: [],
      scripts: [],
      role_cards: [],
    };

    for (const packFile of packFiles) {
      const filePath = path.join(assetPacksPath, packFile);
      try {
        console.log(`UNIVERSAL_ASSET_SERVICE: Reading asset pack: ${filePath}`);
        const fileContent = await fsPromises.readFile(filePath, 'utf8');
        const packData = yaml.load(fileContent);

        if (!packData || typeof packData !== 'object') {
            console.warn(`UNIVERSAL_ASSET_SERVICE: Invalid or empty YAML content in ${filePath}. Skipping.`);
            continue;
        }
        
        const assetsInPack = Array.isArray(packData) ? packData : (packData.assets || []);

        if (!Array.isArray(assetsInPack)) {
            console.warn(`UNIVERSAL_ASSET_SERVICE: 'assets' key in ${filePath} is not an array or pack is not an array itself. Skipping.`);
            continue;
        }
        
        console.log(`UNIVERSAL_ASSET_SERVICE: Processing ${assetsInPack.length} assets from ${filePath}`);

        for (const asset of assetsInPack) {
          if (validateAsset(asset, filePath)) {
            // Resolve relative paths for common fields
            if (asset.icon_path) {
                asset.icon_path = resolveAssetPath(filePath, asset.icon_path);
            }
            if (asset.cg_image_path) {
                asset.cg_image_path = resolveAssetPath(filePath, asset.cg_image_path);
            }
            // Type-specific path resolutions
            if (asset.type === 'scene_card') {
                if (asset.background_image_path) {
                    asset.background_image_path = resolveAssetPath(filePath, asset.background_image_path);
                }
                if (asset.default_bgm_path) {
                    asset.default_bgm_path = resolveAssetPath(filePath, asset.default_bgm_path);
                }
            }
            if (asset.type === 'role_card') { // TGC Role Card
                asset.initial_status_override_json = asset.initial_status_override_json || '{}';
                asset.persona_snippet_override = asset.persona_snippet_override || '';
                // base_avatar_clothing_key is a string, no path resolution needed
            }
             if (asset.type === 'achievement' && asset.icon_path) { // Achievement icon
                asset.icon_path = resolveAssetPath(filePath, asset.icon_path);
            }


            const assetTypeKey = `${asset.type}s`; // e.g., 'props', 'costumes', 'role_cards'
            if (newGlobalAssetsCache[assetTypeKey]) {
              newGlobalAssetsCache[assetTypeKey].push(asset);
            } else {
              console.warn(`UNIVERSAL_ASSET_SERVICE: Unknown asset type '${asset.type}' in ${filePath}. Storing under 'unknown'.`);
              newGlobalAssetsCache.unknown = newGlobalAssetsCache.unknown || [];
              newGlobalAssetsCache.unknown.push(asset);
            }
          }
        }
      } catch (fileError) {
        console.error(`UNIVERSAL_ASSET_SERVICE: Error processing asset pack ${filePath}:`, fileError.message);
      }
    }
    globalAssetsCache = newGlobalAssetsCache; 
    console.log('UNIVERSAL_ASSET_SERVICE: Asset packs loaded. Cache updated.');
    logCacheSummary();

  } catch (dirError) {
    console.error(`UNIVERSAL_ASSET_SERVICE: Error reading asset packs directory ${assetPacksPath}:`, dirError.message);
  }
}

function logCacheSummary() {
    console.log("UNIVERSAL_ASSET_SERVICE: Current Global Assets Cache Summary:");
    for (const key in globalAssetsCache) {
        if (globalAssetsCache[key] && Array.isArray(globalAssetsCache[key])) {
            console.log(`  - ${key}: ${globalAssetsCache[key].length} items`);
        }
    }
}


export function initializeUniversalAssetService(appUserDataPath) {
  if (isInitialized) {
    console.warn('UNIVERSAL_ASSET_SERVICE: Already initialized.');
    return;
  }
  userDataPathInstance = appUserDataPath;
  console.log('UNIVERSAL_ASSET_SERVICE: Initializing with userDataPath:', userDataPathInstance);

  if (protocol && typeof protocol.registerFileProtocol === 'function') {
    const assetPacksRootPath = path.join(userDataPathInstance, ASSET_PACKS_DIR_NAME);
    
    if (!protocol.isProtocolRegistered(TGC_ASSET_PROTOCOL)) {
        protocol.registerFileProtocol(TGC_ASSET_PROTOCOL, (request, callback) => {
        try {
            // request.url will be like: tgc-asset://my_props_pack/images/icon.png
            // We need to extract "my_props_pack/images/icon.png"
            const urlPath = request.url.slice(`${TGC_ASSET_PROTOCOL}://`.length).split('?')[0];
            const decodedUrlPath = decodeURI(urlPath); // e.g., my_props_pack/images/icon.png
            
            // The full path on disk will be userDataPath/asset_packs/my_props_pack/images/icon.png
            const absoluteDiskPath = path.join(assetPacksRootPath, decodedUrlPath);

            if (nodeFs.existsSync(absoluteDiskPath)) {
            callback({ path: absoluteDiskPath });
            } else {
            console.error(`UNIVERSAL_ASSET_SERVICE (${TGC_ASSET_PROTOCOL}): File not found at ${absoluteDiskPath} (requested: ${request.url})`);
            callback({ error: -6 }); // FILE_NOT_FOUND
            }
        } catch (e) {
            console.error(`UNIVERSAL_ASSET_SERVICE (${TGC_ASSET_PROTOCOL}): Error resolving path for ${request.url}`, e);
            callback({ error: -2 }); // GENERIC_FAILURE
        }
        });
        console.log(`UNIVERSAL_ASSET_SERVICE: '${TGC_ASSET_PROTOCOL}' custom protocol registered. Root: ${assetPacksRootPath}`);
    } else {
        console.log(`UNIVERSAL_ASSET_SERVICE: '${TGC_ASSET_PROTOCOL}' custom protocol ALREADY registered.`);
    }
  } else {
    console.error('UNIVERSAL_ASSET_SERVICE: Electron protocol module not available. Asset paths may not work.');
  }
  
  loadAssetPacks(); 
  isInitialized = true;
  console.log('UNIVERSAL_ASSET_SERVICE: Initialization complete.');
}

export function getAssetsByType(assetType) {
  const assetTypeKey = `${assetType}s`;
  return globalAssetsCache[assetTypeKey] || [];
}

export function getAllLoadedAssets() {
  return globalAssetsCache;
}

export function getAssetById(assetId) {
  if (!assetId) return null;
  for (const assetTypeKey in globalAssetsCache) {
    if (globalAssetsCache.hasOwnProperty(assetTypeKey)) {
      const assets = globalAssetsCache[assetTypeKey];
      if (Array.isArray(assets)) {
        const foundAsset = assets.find(asset => asset.id === assetId);
        if (foundAsset) {
          return foundAsset;
        }
      }
    }
  }
  console.warn(`UNIVERSAL_ASSET_SERVICE: Asset with ID '${assetId}' not found in cache.`);
  return null;
}

export function refreshAssetPacks() {
    console.log('UNIVERSAL_ASSET_SERVICE: Refreshing asset packs...');
    return loadAssetPacks();
}

console.log('UNIVERSAL_ASSET_SERVICE_JS: File execution finished. Exports configured.');
