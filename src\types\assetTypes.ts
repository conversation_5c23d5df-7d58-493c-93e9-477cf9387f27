// src/types/assetTypes.ts
// import type { ScriptChoice } from './'; // Removed problematic circular import

export interface ScriptChoice {
  id: string;
  text: string;
  next_scene_id?: string;
  action?: {
    end_script?: boolean;
    end_message?: string;
    // other potential actions
  };
}

export type AssetType = 'prop' | 'costume' | 'pose' | 'role_card' | 'scene_card' | 'achievement' | 'script' | 'theme'; // Added 'theme'

export interface AssetBase {
  id: string;
  type: AssetType;
  name: string;
  description: string; // Will be a string after parseTgcJson
  icon_path?: string | null;
  cg_image_path?: string | null;
  unlock_requirements_json?: string | null; 
}

export interface PropAsset extends AssetBase {
  type: 'prop';
  owner: 'master' | 'queen';
  prompt_for_linluo: string; // Will be a string after parseTgcJson
  prompt_for_master: string; // Will be a string after parseTgcJson
  status_effects_json?: string;
  development_effects_json?: string;
  unlock_requirements_json?: string;
}

export interface CostumeAsset extends AssetBase {
  type: 'costume';
  owner: 'master' | 'queen';
  prompt_for_linluo: string;
  prompt_for_master: string;
  status_effects_json?: string;
  development_effects_json?: string;
  unlock_requirements_json?: string;
}

export interface PoseAsset extends AssetBase {
  type: 'pose';
  owner: 'master' | 'queen';
  prompt_for_linluo: string;
  prompt_for_master: string;
  status_effects_json?: string;
  development_effects_json?: string;
  unlock_requirements_json?: string;
}

export interface TGCRoleCardAsset extends AssetBase {
  type: 'role_card';
  initial_status_override_json?: string | null;
  persona_snippet_override: string;
  sort_order?: number;
  base_avatar_clothing_key?: string;
}

export interface SceneCardAsset extends AssetBase {
  type: 'scene_card';
  background_image_path: string;
  default_bgm_path?: string | null;
  opening_dialogue: string;
}

export interface SceneNodeTGC {
  id: string;
  plot_text: string;
  user_choices?: ScriptChoice[];
  scenario_card_id?: string | null;
}

export interface ScriptAsset extends AssetBase {
  type: 'script';
  title: string; 
  scenario_card_id?: string | null;
  role_card_id?: string | null;
  scenes: SceneNodeTGC[];
}

export interface AchievementAsset extends AssetBase {
  type: 'achievement';
  criteria_json: string;
  reward_json: string;
}

export interface ThemeAssetCoreColors {
  accent_primary: string;
  accent_primary_hover: string;
  accent_secondary: string;
  text_primary: string;
  text_secondary: string;
  text_placeholder: string;
  bg_primary: string;
  bg_secondary: string;
  bg_tertiary: string;
  bg_hover: string;
  border_primary: string;
  border_interactive: string;
  danger: string;
  success: string;
  warning: string;
  [key: string]: string; 
}

export interface ThemeAssetCoreTerms {
  app_title_short: string;
  app_title_full: string;
  [key: string]: string; 
}

export interface ThemeAsset extends Omit<AssetBase, 'id' | 'icon_path' | 'cg_image_path' | 'unlock_requirements_json' > { // Removed description from Omit as it's part of AssetBase now
  type: 'theme';
  theme_id: string; 
  name: string;
  description: string; // Will be string after JSON processing
  core_colors: ThemeAssetCoreColors;
  core_terms: ThemeAssetCoreTerms;
  font_family?: string;
}


export type AnyLoadedAsset = PropAsset | CostumeAsset | PoseAsset | SceneCardAsset | ScriptAsset | AchievementAsset | TGCRoleCardAsset | ThemeAsset;