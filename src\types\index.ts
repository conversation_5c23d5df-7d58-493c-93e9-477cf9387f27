
// src/types/index.ts
// This file acts as a barrel file for the types defined within the src/types/ directory.
// It re-exports types so that other files within src/types/ can import them using './'.

export * from './commonTypes';
export * from './projectTypes';
export * from './chatTypes';
export * from './cmsTypes';
export * from './knowledgeTypes';
export * from './taskTypes';
export * from './settingsTypes';
export * from './aiTypes'; // This should correctly export InvokeSandboxRequestArgs and InvokeTerritoryRequestArgs
export * from './electronAPITypes';
export * from './uiTypes';
export * from './assetTypes';
export * from './organizationTypes';
export * from './roundtableTypes'; // Added roundtable types export

// Note: Specific constant re-exports (like KNOWLEDGE_TOME_CATEGORIES_MAP)
// are typically handled by a higher-level barrel file (e.g., src/types.ts if it exists)
// or imported directly by consuming modules. This file focuses on type re-exports
// for internal resolution within the src/types/ directory.

// Specific constant re-exports
export { KNOWLEDGE_TOME_CATEGORIES_MAP } from '@/features/knowledge_tomes/knowledgeConstants';
export { BODY_ZONE_DISPLAY_NAMES } from '@/features/absolute_territory/atConstants';
export { ALL_BODY_ZONES } from './commonTypes'; // Updated path to commonTypes
export { TASK_STATUS_ORDER, PRIORITY_LABELS, RESOURCE_TYPE_DISPLAY_NAMES } from './taskTypes';

// Explicit re-export of potentially problematic types for renderer.d.ts
export type { InvokeSandboxRequestArgs, InvokeTerritoryRequestArgs } from './aiTypes'; 
export type { ScriptModeInfo } from './aiTypes'; 


export {}; // Ensure this file is treated as a module
