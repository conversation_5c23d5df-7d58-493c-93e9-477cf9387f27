
import React, { useState, useEffect, useCallback } from 'react';
import type { DevelopmentTask, Project, FileNode, AppSettings, AIResponseWithStatus } from '@/types';
import { Icon } from '@/components/common/Icon';
import { FileTreeNode } from '@/components/FileTreeNode';
import { PrismAsyncLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
// Import common languages for syntax highlighting
import javascript from 'react-syntax-highlighter/dist/esm/languages/prism/javascript';
import jsx from 'react-syntax-highlighter/dist/esm/languages/prism/jsx';
import typescript from 'react-syntax-highlighter/dist/esm/languages/prism/typescript';
import tsx from 'react-syntax-highlighter/dist/esm/languages/prism/tsx';
import python from 'react-syntax-highlighter/dist/esm/languages/prism/python';
import css from 'react-syntax-highlighter/dist/esm/languages/prism/css';
import json from 'react-syntax-highlighter/dist/esm/languages/prism/json';
import markdown from 'react-syntax-highlighter/dist/esm/languages/prism/markdown';

SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('jsx', jsx);
SyntaxHighlighter.registerLanguage('typescript', typescript);
SyntaxHighlighter.registerLanguage('tsx', tsx);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('css', css);
SyntaxHighlighter.registerLanguage('json', json);
SyntaxHighlighter.registerLanguage('markdown', markdown);


interface DevTaskModuleProps {
  project: Project | null; 
  settings: AppSettings; // Added settings to access API key
}

const getRelativePath = (rootPath: string, absolutePath: string): string => {
  if (!rootPath || !absolutePath) return absolutePath;
  if (absolutePath.startsWith(rootPath)) {
    return absolutePath.substring(rootPath.length + (rootPath.endsWith('/') ? 0 : 1));
  }
  return absolutePath;
};

export const DevTaskModule: React.FC<DevTaskModuleProps> = ({ project, settings }) => {
  const [tasks, setTasks] = useState<DevelopmentTask[]>([]);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [selectedTask, setSelectedTask] = useState<DevelopmentTask | null>(null);
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [fileTree, setFileTree] = useState<FileNode[] | null>(null);
  const [isLoadingFileTree, setIsLoadingFileTree] = useState(false);
  const [fileTreeError, setFileTreeError] = useState<string | null>(null);

  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [generationError, setGenerationError] = useState<string | null>(null);

  const apiKeyAvailable = !!settings.apiKey && settings.apiKey.trim() !== "";

  const loadTasks = useCallback(async () => {
    if (!project?.id) return;
    setIsLoadingTasks(true);
    setError(null);
    try {
      const fetchedTasks = await window.api.database.getAllDevelopmentTasks();
      setTasks(fetchedTasks.filter(task => task.projectId === project.id));
    } catch (err: any) {
      setError(`获取开发任务失败: ${err.message}`);
    } finally {
      setIsLoadingTasks(false);
    }
  }, [project?.id]);

  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  const loadFileTreeForProject = useCallback(async (path: string) => {
    if (!path) {
        setFileTree(null);
        setFileTreeError(null);
        return;
    }
    setIsLoadingFileTree(true);
    setFileTreeError(null);
    try {
      const result = await window.api.fs.readDirectory(path);
      if ('error' in result) {
        setFileTreeError(`无法读取项目目录 '${path}': ${result.error}`);
        setFileTree(null);
      } else {
        setFileTree(result);
      }
    } catch (e: any) {
      setFileTreeError(`扫描项目目录时发生意外错误: ${e.message}`);
      setFileTree(null);
    } finally {
      setIsLoadingFileTree(false);
    }
  }, []);

  useEffect(() => {
    if (selectedTask && project?.sourceCodePath) {
      loadFileTreeForProject(project.sourceCodePath);
    } else {
      setFileTree(null);
      setFileTreeError(null);
    }
  }, [selectedTask, project?.sourceCodePath, loadFileTreeForProject]);

  const handleAddTask = async () => {
    if (!newTaskTitle.trim() || !project?.id) return;
    try {
      const addedTask = await window.api.database.addDevelopmentTask(project.id, newTaskTitle.trim());
      if (addedTask) {
        setTasks(prev => [addedTask, ...prev]);
        setNewTaskTitle('');
      }
    } catch (err: any) {
      setError(`添加任务失败: ${err.message}`);
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    if (window.confirm("确定要删除此开发任务吗？")) {
      try {
        const result = await window.api.database.deleteDevelopmentTask(taskId);
        if (result.success) {
          setTasks(prev => prev.filter(t => t.id !== taskId));
          if (selectedTask?.id === taskId) setSelectedTask(null);
        } else {
          setError(result.error || "删除任务失败。");
        }
      } catch (err: any) {
        setError(`删除任务时发生错误: ${err.message}`);
      }
    }
  };

  const toggleContextFile = async (filePath: string) => {
    if (!selectedTask) return;
    const relativePath = getRelativePath(project?.sourceCodePath || '', filePath);
    const newContextFiles = selectedTask.context_files.includes(relativePath)
      ? selectedTask.context_files.filter(f => f !== relativePath)
      : [...selectedTask.context_files, relativePath];
    
    try {
      const result = await window.api.database.updateDevelopmentTaskContextFiles(selectedTask.id, newContextFiles);
      if (result.success) {
        const updatedTask = { ...selectedTask, context_files: newContextFiles };
        setSelectedTask(updatedTask);
        setTasks(prev => prev.map(t => t.id === updatedTask.id ? updatedTask : t));
      } else {
         setError(result.error || "更新上下文文件失败。");
      }
    } catch (err: any) {
      setError(`更新上下文文件时发生错误: ${err.message}`);
    }
  };

  const handleGenerateCode = async () => {
    if (!selectedTask || !apiKeyAvailable) return;
    setIsGeneratingCode(true);
    setGenerationError(null);

    try {
      let contextContent = "";
      for (const relativeFilePath of selectedTask.context_files) {
        const absolutePath = project?.sourceCodePath ? `${project.sourceCodePath}/${relativeFilePath}` : relativeFilePath;
        try {
            const fileContentResult = await window.api.fs.readFileContent(absolutePath);
            if (typeof fileContentResult === 'string') {
                contextContent += `\n\n--- 文件: ${relativeFilePath} ---\n${fileContentResult}`;
            } else {
                console.warn(`无法读取上下文文件 ${relativeFilePath}: ${fileContentResult.error}`);
                contextContent += `\n\n--- 文件: ${relativeFilePath} (无法加载内容) ---`;
            }
        } catch (readErr: any) {
            console.warn(`读取上下文文件 ${relativeFilePath} 时出错: ${readErr.message}`);
            contextContent += `\n\n--- 文件: ${relativeFilePath} (读取错误) ---`;
        }
      }

      const prompt = `小岚，请为任务 "${selectedTask.title}" 生成代码。
请仔细参考以下提供的上下文文件内容（如果存在）。
确保代码逻辑清晰、高效，并符合常规的工程实践。
如果任务描述不够清晰，或上下文不足以生成完整代码，请明确指出并尝试提供一个基础框架或伪代码。

上下文文件内容如下：
${contextContent || "(无特定上下文文件提供，请根据任务标题和你的通用知识进行生成)"}

请直接输出生成的代码，无需额外解释或对话。如果需要区分文件，请使用类似 \`// FILE: path/to/your/file.ext\` 的注释标记。
`;
      
      const aiResult = await window.api.ai.callAI(prompt, "XiaoLan", { projectId: project?.id });
      let generatedCodeText: string;
        if (typeof aiResult === 'string') {
            generatedCodeText = aiResult;
        } else if (aiResult && typeof aiResult.text === 'string') {
            generatedCodeText = aiResult.text;
        } else {
            throw new Error("AI返回了意外的响应格式。");
        }

      // Clean up Gemini's markdown fences if present
      const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
      const match = generatedCodeText.match(fenceRegex);
      if (match && match[2]) {
        generatedCodeText = match[2].trim();
      }
      
      const updateResult = await window.api.database.updateDevelopmentTaskGeneratedCode(selectedTask.id, generatedCodeText);
      if (updateResult.success) {
        const updatedTask = { ...selectedTask, generated_code: generatedCodeText };
        setSelectedTask(updatedTask);
        setTasks(prev => prev.map(t => t.id === updatedTask.id ? updatedTask : t));
      } else {
        setGenerationError(updateResult.error || "保存生成代码失败。");
      }

    } catch (err: any) {
      setGenerationError(`代码生成过程中出错: ${err.message}`);
    } finally {
      setIsGeneratingCode(false);
    }
  };

  if (!project) {
    return <div className="p-4 text-tg-text-placeholder">请先从主殿选择一个项目。</div>;
  }

  const getLanguageFromPath = (filePath: string) => {
    const extension = filePath.split('.').pop()?.toLowerCase() || 'plaintext';
    // Simple mapping, can be expanded
    if (['js', 'jsx'].includes(extension)) return 'javascript';
    if (['ts', 'tsx'].includes(extension)) return 'typescript';
    if (['py'].includes(extension)) return 'python';
    if (['css', 'scss', 'less'].includes(extension)) return 'css';
    if (['json'].includes(extension)) return 'json';
    if (['md'].includes(extension)) return 'markdown';
    return extension;
  };


  return (
    <div className="flex h-full bg-tg-bg-secondary text-tg-text-primary p-3 rounded-lg shadow-inner">
      {/* Task List Panel */}
      <div className="w-1/3 lg:w-1/4 border-r border-tg-border-primary pr-3 mr-3 flex flex-col">
        <h4 className="text-md font-semibold mb-2 flex items-center text-tg-accent-secondary">
          <Icon name="Wrench" className="w-5 h-5 mr-2"/>开发任务列表
        </h4>
        <div className="mb-3 flex">
          <input
            type="text"
            value={newTaskTitle}
            onChange={e => setNewTaskTitle(e.target.value)}
            placeholder="新任务标题..."
            className="flex-grow p-2 bg-tg-bg-tertiary border border-tg-border-primary rounded-l-md text-xs focus:border-tg-accent-secondary focus:ring-1 focus:ring-tg-accent-secondary/30"
            onKeyPress={e => e.key === 'Enter' && handleAddTask()}
          />
          <button onClick={handleAddTask} className="p-2 bg-tg-accent-secondary text-white rounded-r-md hover:brightness-110 text-xs" title="添加新任务">
            <Icon name="Plus" className="w-4 h-4" />
          </button>
        </div>
        {isLoadingTasks && <p className="text-xs text-tg-text-placeholder">加载任务中...</p>}
        {error && <p className="text-xs text-red-400">{error}</p>}
        <ul className="space-y-1.5 overflow-y-auto flex-grow">
          {tasks.map(task => (
            <li key={task.id}
              className={`p-2 rounded-md cursor-pointer text-xs transition-colors flex justify-between items-center
                         ${selectedTask?.id === task.id ? 'bg-tg-accent-secondary text-white' : 'bg-tg-bg-tertiary hover:bg-tg-bg-hover'}`}
              onClick={() => setSelectedTask(task)}
            >
              <span className="truncate" title={task.title}>{task.title}</span>
              <button onClick={(e) => { e.stopPropagation(); handleDeleteTask(task.id);}} 
                      className={`p-0.5 rounded hover:bg-red-700/50 ${selectedTask?.id === task.id ? 'text-white hover:text-red-200' : 'text-red-500 hover:text-red-300' }`} title="删除任务">
                <Icon name="Trash2" className="w-3.5 h-3.5"/>
              </button>
            </li>
          ))}
           {tasks.length === 0 && !isLoadingTasks && <p className="text-xs text-tg-text-placeholder text-center py-2">暂无开发任务。</p>}
        </ul>
      </div>

      {/* Task Detail Panel */}
      <div className="flex-grow flex flex-col overflow-hidden">
        {!selectedTask ? (
          <div className="flex-grow flex items-center justify-center text-tg-text-placeholder">
            <Icon name="Wrench" className="w-12 h-12 mb-3 text-tg-border-primary"/>
            <p>选择一个任务以查看详情或开始炼制代码。</p>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            <h3 className="text-lg font-semibold mb-2 text-tg-accent-primary truncate" title={selectedTask.title}>{selectedTask.title}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3 flex-shrink-0">
              {/* Context Files Selection */}
              <div className="border border-tg-border-primary rounded-md p-2.5 bg-tg-bg-primary">
                <h5 className="text-sm font-medium mb-1.5 text-tg-text-secondary flex items-center">
                    <Icon name="Link" className="w-4 h-4 mr-1.5 text-tg-accent-primary"/>关联上下文文件 (可选)
                </h5>
                {!project.sourceCodePath && <p className="text-xs text-tg-text-placeholder p-1">请先在“源码洞天”中设置项目根目录以选择文件。</p>}
                {project.sourceCodePath && isLoadingFileTree && <p className="text-xs text-tg-text-placeholder p-1">加载文件树...</p>}
                {project.sourceCodePath && fileTreeError && <p className="text-xs text-red-400 p-1">{fileTreeError}</p>}
                {project.sourceCodePath && fileTree && !isLoadingFileTree && !fileTreeError && (
                  <div className="max-h-40 overflow-y-auto text-xs border border-tg-border-primary rounded-sm bg-tg-bg-tertiary p-1">
                    {fileTree.map(node => (
                      <FileTreeNode 
                        key={node.path} node={node} level={0}
                        onFileSelect={(filePath) => toggleContextFile(filePath)}
                        selectedFilePath={null} // Not used for selection highlight here, but for click action
                      />
                    ))}
                  </div>
                )}
                {selectedTask.context_files.length > 0 && (
                  <div className="mt-1.5">
                    <p className="text-xs text-tg-text-secondary mb-0.5">已选文件:</p>
                    <ul className="text-xs space-y-0.5">
                      {selectedTask.context_files.map(file => (
                        <li key={file} className="flex justify-between items-center bg-tg-bg-tertiary p-1 rounded-sm">
                          <span className="truncate" title={file}>{file}</span>
                          <button onClick={() => toggleContextFile(file)} className="text-red-500 hover:text-red-300 p-0.5" title="移除文件">
                            <Icon name="X" className="w-3 h-3"/>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Action Panel */}
              <div className="border border-tg-border-primary rounded-md p-2.5 bg-tg-bg-primary flex flex-col justify-between">
                 <div>
                    <h5 className="text-sm font-medium mb-1.5 text-tg-text-secondary flex items-center">
                        <Icon name="Sparkles" className="w-4 h-4 mr-1.5 text-tg-accent-secondary"/>代码炼制炉
                    </h5>
                    {!apiKeyAvailable && <p className="text-xs text-tg-warning flex items-center"><Icon name="AlertTriangle" className="w-3 h-3 mr-1"/>API Key未配置，无法炼制代码。</p>}
                 </div>
                <button
                  onClick={handleGenerateCode}
                  disabled={isGeneratingCode || !apiKeyAvailable}
                  className="w-full mt-2 px-3 py-2 text-sm font-medium rounded-md flex items-center justify-center bg-tg-accent-secondary text-white hover:brightness-110 transition-colors disabled:opacity-50"
                >
                  {isGeneratingCode ? <Icon name="Loader" className="w-4 h-4 mr-2 animate-spin"/> : <Icon name="Wrench" className="w-4 h-4 mr-2"/>}
                  {isGeneratingCode ? "小岚炼制中..." : "召唤小岚炼制代码"}
                </button>
                {generationError && <p className="text-xs text-red-400 mt-1">{generationError}</p>}
              </div>
            </div>
            
            {/* Generated Code Display */}
            <div className="flex-grow overflow-auto border border-tg-border-primary rounded-md bg-tg-bg-primary p-0 relative">
              {selectedTask.generated_code ? (
                  <SyntaxHighlighter 
                    language={getLanguageFromPath(selectedTask.context_files[0] || '')} // Guess lang from first context file
                    style={atomDark} 
                    showLineNumbers 
                    wrapLines
                    customStyle={{ margin: 0, height: '100%', overflowY: 'auto', fontSize: '0.8rem', backgroundColor: 'var(--color-bg-primary)' }}
                    lineNumberStyle={{ color: 'var(--color-text-placeholder)', fontSize: '0.7rem', minWidth: '2.5em', paddingRight: '0.5em' }}
                  >
                    {selectedTask.generated_code}
                  </SyntaxHighlighter>
              ) : (
                <div className="flex items-center justify-center h-full text-tg-text-placeholder text-sm p-4">
                  {isGeneratingCode ? '小岚正在努力炼制代码...' : (generationError ? '代码生成失败。' : '暂无生成的代码。请选择上下文文件并召唤小岚。')}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};