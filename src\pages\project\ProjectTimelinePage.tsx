// src/pages/project/ProjectTimelinePage.tsx
import React from 'react';
import { Icon } from '@/components/common/Icon'; // Updated to use unified Icon
import type { IconProps } from '@/components/common/Icon'; // Import IconProps

interface TimelineEvent {
  id: string;
  type: 'task' | 'milestone' | 'decision';
  timestamp: string;
  title: string;
  description?: string;
  actor?: string;
  iconName: React.ComponentProps<typeof Icon>['name']; 
  colorClass: string;
  details?: React.ReactNode;
}

const mockTimelineEvents: TimelineEvent[] = [
  {
    id: 'event-1',
    type: 'task',
    timestamp: '2024-07-28 10:00 AM',
    title: '需求文档初稿完成',
    description: '完成了项目核心需求V1.0的编写。',
    actor: '林小岚',
    iconName: "FileText",
    colorClass: 'border-sky-500',
    details: <p className="text-xs text-tg-text-secondary">负责人: 林小岚</p>
  },
  {
    id: 'event-2',
    type: 'milestone',
    timestamp: '2024-07-29 03:00 PM',
    title: 'Alpha原型发布',
    description: '第一个可交互原型已部署到测试环境。',
    iconName: "Star",
    colorClass: 'border-yellow-400 bg-yellow-400/10',
    details: <p className="text-xs text-tg-text-secondary">状态: <span className="font-semibold text-yellow-400">重要里程碑</span></p>
  },
  {
    id: 'event-3',
    type: 'decision',
    timestamp: '2024-07-30 11:00 AM',
    title: '技术选型确定',
    description: '经过研讨，最终确定采用“天工统一框架 V3”作为项目基石。',
    iconName: "Lightbulb",
    colorClass: 'border-purple-400',
    details: (
      <a href="#" onClick={(e) => e.preventDefault()} className="text-xs text-tg-accent-secondary hover:underline flex items-center">
        <Icon name="MessageSquare" className="w-3 h-3 mr-1"/> 查看研讨区相关记录 (模拟)
      </a>
    )
  },
   {
    id: 'event-4',
    type: 'task',
    timestamp: '2024-07-31 09:00 AM',
    title: '数据库结构设计完毕',
    description: '完成了核心数据表的设计和关联关系定义。',
    actor: '数据库团队',
    iconName: "FileText",
    colorClass: 'border-sky-500',
    details: <p className="text-xs text-tg-text-secondary">状态: 已完成</p>
  },
];

export const ProjectTimelinePage: React.FC = () => {
  return (
    <div className="p-4 md:p-6 h-full overflow-y-auto bg-tg-bg-primary text-tg-text-primary custom-scrollbar">
      <h2 className="text-2xl font-bold text-tg-text-primary mb-6">项目时间轴</h2>
      <div className="relative pl-8 border-l-2 border-tg-border-interactive">
        {mockTimelineEvents.map((event, index) => {
          let iconColorClass = "text-gray-400"; // Default
          if (event.type === 'milestone') iconColorClass = "text-yellow-400";
          else if (event.type === 'decision') iconColorClass = "text-purple-400";
          else if (event.colorClass.includes('sky')) iconColorClass = "text-sky-400";


          return (
            <div key={event.id} className="mb-8 relative">
              {/* Dot on the timeline */}
              <div className={`absolute -left-[13px] top-1 w-6 h-6 rounded-full bg-tg-bg-secondary border-2 ${event.colorClass} flex items-center justify-center`}>
                <Icon name={event.iconName} className={`w-3.5 h-3.5 ${iconColorClass}`} />
              </div>

              <div className="ml-4">
                <div className={`p-4 rounded-lg shadow-md border ${event.colorClass} bg-tg-bg-secondary hover:shadow-xl transition-shadow`}>
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="text-lg font-semibold text-tg-text-primary">{event.title}</h3>
                    <span className="text-xs text-tg-text-secondary whitespace-nowrap flex items-center">
                      <Icon name="Clock" className="w-3.5 h-3.5 mr-1"/> {event.timestamp}
                    </span>
                  </div>
                  {event.description && (
                    <p className="text-sm text-tg-text-secondary mb-2">{event.description}</p>
                  )}
                  {event.details && <div className="mt-2">{event.details}</div>}
                </div>
              </div>
            </div>
          );
        })}
        <div className="absolute -left-2.5 top-0 h-full w-1.5 bg-tg-border-interactive" aria-hidden="true" />
      </div>
    </div>
  );
};