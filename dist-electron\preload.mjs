import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
const exposedAPI = {
  testPreload: () => console.log("Preload script is alive!"),
  database: {
    getSettings: () => ipcRenderer.invoke("db:getSettings"),
    saveSettings: (settings) => ipcRenderer.invoke("db:saveSettings", settings),
    getAllProjects: () => ipcRenderer.invoke("db:getAllProjects"),
    addProject: (projectData) => ipcRenderer.invoke("db:addProject", projectData),
    getProjectById: (projectId) => ipcRenderer.invoke("db:getProjectById", projectId),
    updateProject: (projectData) => ipcRenderer.invoke("db:updateProject", projectData),
    deleteProject: (projectId) => ipcRenderer.invoke("db:deleteProject", projectId),
    duplicateProject: (projectId) => ipc<PERSON>enderer.invoke("db:duplicateProject", projectId),
    addNoteToProject: (projectId, pouchType, noteData) => ipcRenderer.invoke("db:addNoteToProject", projectId, pouchType, noteData),
    updateNoteInProject: (pouchType, noteData) => ipcRenderer.invoke("db:updateNoteInProject", pouchType, noteData),
    deleteNoteFromProject: (pouchType, noteId) => ipcRenderer.invoke("db:deleteNoteFromProject", pouchType, noteId),
    updateProjectMindMap: (projectId, nodes, connections) => ipcRenderer.invoke("db:updateProjectMindMap", projectId, nodes, connections),
    getAllGlobalKnowledgeTomes: () => ipcRenderer.invoke("db:getAllGlobalKnowledgeTomes"),
    addGlobalKnowledgeTome: (tomeData) => ipcRenderer.invoke("db:addGlobalKnowledgeTome", tomeData),
    updateGlobalKnowledgeTome: (tomeData) => ipcRenderer.invoke("db:updateGlobalKnowledgeTome", tomeData),
    deleteGlobalKnowledgeTome: (tomeId) => ipcRenderer.invoke("db:deleteGlobalKnowledgeTome", tomeId),
    addProjectKnowledgeTome: (projectId, tomeData) => ipcRenderer.invoke("db:addProjectKnowledgeTome", projectId, tomeData),
    updateProjectKnowledgeTome: (projectId, tomeData) => ipcRenderer.invoke("db:updateProjectKnowledgeTome", projectId, tomeData),
    deleteProjectKnowledgeTome: (projectId, tomeId) => ipcRenderer.invoke("db:deleteProjectKnowledgeTome", projectId, tomeId),
    addProjectKnowledgeCategory: (projectId, categoryName) => ipcRenderer.invoke("db:addProjectKnowledgeCategory", projectId, categoryName),
    removeProjectKnowledgeCategory: (projectId, categoryName) => ipcRenderer.invoke("db:removeProjectKnowledgeCategory", projectId, categoryName),
    getAllDevelopmentTasks: () => ipcRenderer.invoke("db:getAllDevelopmentTasks"),
    addDevelopmentTask: (projectId, title) => ipcRenderer.invoke("db:addDevelopmentTask", projectId, title),
    createDevelopmentTaskFromChat: (payload) => ipcRenderer.invoke("db:createDevelopmentTaskFromChat", payload),
    deleteDevelopmentTask: (taskId) => ipcRenderer.invoke("db:deleteDevelopmentTask", taskId),
    updateDevelopmentTaskContextFiles: (taskId, contextFiles) => ipcRenderer.invoke("db:updateDevelopmentTaskContextFiles", taskId, contextFiles),
    updateDevelopmentTaskGeneratedCode: (taskId, generatedCode) => ipcRenderer.invoke("db:updateDevelopmentTaskGeneratedCode", taskId, generatedCode),
    getAgentCoreSetting: (settingId) => ipcRenderer.invoke("db:getAgentCoreSetting", settingId),
    getAllAgentCoreSettings: () => ipcRenderer.invoke("db:getAllAgentCoreSettings"),
    saveAgentCoreSetting: (settingId, content) => ipcRenderer.invoke("db:saveAgentCoreSetting", settingId, content),
    findRelevantMemories: (queryEmbedding, contextInfo, limit) => ipcRenderer.invoke("db:findRelevantMemories", queryEmbedding, contextInfo, limit),
    getCoreMemories: (personaTarget, projectContextId, limit, importanceThreshold, keywords) => ipcRenderer.invoke("db:getCoreMemories", personaTarget, projectContextId, limit, importanceThreshold, keywords),
    getAllCoreMemories: (filters, sort, pagination) => ipcRenderer.invoke("db:getAllCoreMemories", filters, sort, pagination),
    addCoreMemory: (memoryData) => ipcRenderer.invoke("db:addCoreMemory", memoryData),
    addCoreMemoryFromChat: (messageText, personaTarget, projectId) => ipcRenderer.invoke("db:addCoreMemoryFromChat", messageText, personaTarget, projectId),
    updateCoreMemory: (memoryData) => ipcRenderer.invoke("db:updateCoreMemory", memoryData),
    deleteCoreMemory: (memoryId) => ipcRenderer.invoke("db:deleteCoreMemory", memoryId),
    getCoreMemoryById: (memoryId) => ipcRenderer.invoke("db:getCoreMemoryById", memoryId),
    getAbsoluteTerritoryMessages: (limit, beforeTimestamp) => ipcRenderer.invoke("db:getAbsoluteTerritoryMessages", limit, beforeTimestamp),
    addAbsoluteTerritoryMessage: (message) => ipcRenderer.invoke("db:addAbsoluteTerritoryMessage", message),
    clearAbsoluteTerritoryHistory: () => ipcRenderer.invoke("db:clearAbsoluteTerritoryHistory"),
    addLearningLog: (logData) => ipcRenderer.invoke("db:addLearningLog", logData),
    getLearningLogs: (filters, limit, offset) => ipcRenderer.invoke("db:getLearningLogs", filters, limit, offset),
    getBodyDevelopment: (zone_id) => ipcRenderer.invoke("db:getBodyDevelopment", zone_id),
    getAllBodyDevelopment: () => ipcRenderer.invoke("db:getAllBodyDevelopment"),
    updateBodyDevelopment: (zone_id, pointsToAdd) => ipcRenderer.invoke("db:updateBodyDevelopment", zone_id, pointsToAdd),
    getAllAchievements: () => ipcRenderer.invoke("db:getAllAchievements"),
    getUserAchievements: (userId) => ipcRenderer.invoke("db:getUserAchievements", userId),
    unlockAchievement: (userId, achievementId) => ipcRenderer.invoke("db:unlockAchievement", userId, achievementId),
    getAchievementById: (achievementId) => ipcRenderer.invoke("db:getAchievementById", achievementId),
    saveChatMessage: (projectId, message) => ipcRenderer.invoke("db:saveChatMessage", projectId, message),
    updateChatMessage: (message) => ipcRenderer.invoke("db:updateChatMessage", message),
    getInitialChatMessages: (projectId, limit) => ipcRenderer.invoke("db:getInitialChatMessages", projectId, limit),
    getOlderChatMessages: (projectId, beforeTimestamp, limit) => ipcRenderer.invoke("db:getOlderChatMessages", projectId, beforeTimestamp, limit),
    summarizeAndReplaceMessages: (projectId, messageIds, summaryMessage) => ipcRenderer.invoke("db:summarizeAndReplaceMessages", projectId, messageIds, summaryMessage),
    getAllGlobalQuickCommands: () => ipcRenderer.invoke("db:getAllGlobalQuickCommands"),
    addGlobalQuickCommand: (commandData) => ipcRenderer.invoke("db:addGlobalQuickCommand", commandData),
    updateGlobalQuickCommand: (commandData) => ipcRenderer.invoke("db:updateGlobalQuickCommand", commandData),
    deleteGlobalQuickCommand: (commandId) => ipcRenderer.invoke("db:deleteGlobalQuickCommand", commandId)
  },
  settings: {
    getAbsoluteTerritoryPassword: () => ipcRenderer.invoke("settings:getAbsoluteTerritoryPassword"),
    setAbsoluteTerritoryPassword: (password) => ipcRenderer.invoke("settings:setAbsoluteTerritoryPassword", password),
    verifyAbsoluteTerritoryPassword: (password) => ipcRenderer.invoke("settings:verifyAbsoluteTerritoryPassword", password)
  },
  tasks: {
    getTaskById: (taskId) => ipcRenderer.invoke("tasks:getTaskById", taskId),
    getTasksByProjectId: (projectId, filters, sortOptions) => ipcRenderer.invoke("tasks:getTasksByProjectId", projectId, filters, sortOptions),
    getTasksByStatus: (projectId, statusArray) => ipcRenderer.invoke("tasks:getTasksByStatus", projectId, statusArray),
    addTask: (taskData) => ipcRenderer.invoke("tasks:addTask", taskData),
    updateTask: (taskId, updates) => ipcRenderer.invoke("tasks:updateTask", taskId, updates),
    deleteTask: (taskId) => ipcRenderer.invoke("tasks:deleteTask", taskId),
    addResourceLinkToTask: (taskId, resourceData) => ipcRenderer.invoke("tasks:addResourceLink", taskId, resourceData),
    getResourceLinksForTask: (taskId) => ipcRenderer.invoke("tasks:getResourceLinks", taskId),
    removeResourceLinkFromTask: (linkId) => ipcRenderer.invoke("tasks:removeResourceLink", linkId),
    suggestResourcesForTask: (taskId, taskTitle, taskDescription, projectId) => ipcRenderer.invoke("tasks:suggestResources", taskId, taskTitle, taskDescription, projectId)
  },
  ai: {
    invokeSandboxRequest: (args) => ipcRenderer.invoke("ai:invoke-sandbox-request", args),
    invokeTerritoryRequest: (args) => ipcRenderer.invoke("ai:invoke-territory-request", args),
    getAvailableModels: () => ipcRenderer.invoke("ai:getAvailableModels"),
    callAI: (prompt, persona, context) => ipcRenderer.invoke("ai:callAI", prompt, persona, context),
    discussWithAI: (contents, config, modelName, apiKey) => ipcRenderer.invoke("ai:discussWithAI", contents, config, modelName, apiKey),
    summarizeConversation: (historyChunk) => ipcRenderer.invoke("ai:summarizeConversation", historyChunk),
    decomposeRequirementToTasks: (requirementText, projectId) => ipcRenderer.invoke("ai:decomposeRequirementToTasks", requirementText, projectId),
    analyzeAndDecomposeAideProject: (projectId, projectPath) => ipcRenderer.invoke("ai:analyzeAndDecomposeAideProject", projectId, projectPath),
    routeUserIntent: (userInputText, context) => ipcRenderer.invoke("ai:routeUserIntent", userInputText, context),
    startRoundtableMeeting: (initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory) => ipcRenderer.invoke("ai:startRoundtableMeeting", initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory),
    assist: {
      generateCode: (context) => ipcRenderer.invoke("aiCodeAssist:generateCode", context),
      explainCode: (context) => ipcRenderer.invoke("aiCodeAssist:explainCode", context),
      generateDocForCode: (context) => ipcRenderer.invoke("aiCodeAssist:generateDocForCode", context),
      reviewCode: (context) => ipcRenderer.invoke("aiCodeAssist:reviewCode", context),
      analyzeErrorLog: (context) => ipcRenderer.invoke("aiCodeAssist:analyzeErrorLog", context),
      modifyCode: (context) => ipcRenderer.invoke("ai:assist:modifyCode", context)
    }
  },
  bridgeAi: {
    processIntent: (userInputText) => ipcRenderer.invoke("bridgeAi:processIntent", userInputText)
  },
  fs: {
    openDirectoryDialog: () => ipcRenderer.invoke("fs:openDirectoryDialog"),
    readDirectory: (dirPath) => ipcRenderer.invoke("fs:readDirectory", dirPath),
    readFileContent: (filePath) => ipcRenderer.invoke("fs:readFileContent", filePath),
    saveFileContent: (filePath, content) => ipcRenderer.invoke("fs:saveFileContent", filePath, content),
    openFileDialog: (options) => ipcRenderer.invoke("fs:openFileDialog", options),
    openMultipleFilesDialog: (extensions) => ipcRenderer.invoke("fs:openMultipleFilesDialog", extensions),
    copyFileToUserData: (sourcePath, targetSubdir, targetFilename) => ipcRenderer.invoke("fs:copyFileToUserData", sourcePath, targetSubdir, targetFilename),
    exportChatHistory: (messages, format, defaultFileName) => ipcRenderer.invoke("fs:exportChatHistory", messages, format, defaultFileName),
    listFiles: (directoryPath, recursive, depth) => ipcRenderer.invoke("fs:listFiles", directoryPath, recursive, depth),
    readFile: (filePath) => ipcRenderer.invoke("fs:readFile", filePath),
    writeFile: (filePath, content) => ipcRenderer.invoke("fs:writeFile", filePath, content),
    archiveFileVersion: (filePath, originalCode, userInstruction) => ipcRenderer.invoke("fs:archiveFileVersion", filePath, originalCode, userInstruction),
    isDirectoryEmpty: (directoryPath) => ipcRenderer.invoke("fs:isDirectoryEmpty", directoryPath),
    copyDirectoryContents: (sourceDir, targetDir) => ipcRenderer.invoke("fs:copyDirectoryContents", sourceDir, targetDir)
  },
  file: {
    readPackageJsonScripts: (projectPath) => ipcRenderer.invoke("file:read-package-json-scripts", projectPath)
  },
  rag: {
    runProjectIndexing: (projectId, projectPath, options) => ipcRenderer.invoke("rag:runProjectIndexing", projectId, projectPath, options),
    retrieveRelevantChunks: (queryText, projectId, apiKey, embeddingModelName, topK) => ipcRenderer.invoke("rag:retrieveRelevantChunks", queryText, projectId, apiKey, embeddingModelName, topK),
    indexFileContent: (projectId, filePath, fileContent, options) => ipcRenderer.invoke("rag:indexFileContent", projectId, filePath, fileContent, options)
  },
  audio: {
    playSound: (soundName, loop) => ipcRenderer.invoke("audio:playSound", soundName, loop),
    stopSound: (soundName) => ipcRenderer.invoke("audio:stopSound", soundName),
    setVolume: (volume) => ipcRenderer.invoke("audio:setVolume", volume),
    getPlaybackState: () => ipcRenderer.invoke("audio:getPlaybackState"),
    onPlaybackStateChanged: (callback) => {
      const handler = (event, newState) => callback(newState);
      ipcRenderer.on("audio:playbackStateChanged", handler);
      return () => ipcRenderer.removeListener("audio:playbackStateChanged", handler);
    }
  },
  electronUtils: {
    getPlatform: () => ipcRenderer.invoke("electronUtils:getPlatform")
  },
  utils: {
    combinePaths: (basePath, relativePath) => ipcRenderer.invoke("utils:combinePaths", basePath, relativePath)
  },
  assets: {
    getLoadedAssets: () => ipcRenderer.invoke("assets:getLoadedAssets"),
    refreshAssetPacks: () => ipcRenderer.invoke("assets:refreshAssetPacks"),
    createAssetPack: (fileName, assetPackData) => ipcRenderer.invoke("assets:createAssetPack", fileName, assetPackData),
    deleteAssetPack: (fileName) => ipcRenderer.invoke("assets:deleteAssetPack", fileName)
  },
  command: {
    execute: (params) => ipcRenderer.invoke("command:execute", params),
    kill: (internalPid) => ipcRenderer.invoke("command:kill", internalPid),
    onEvent: (callback) => {
      const handler = (event, commandEvent) => callback(commandEvent);
      ipcRenderer.on("command:event", handler);
      return () => ipcRenderer.removeListener("command:event", handler);
    },
    analyzeLog: (logContent) => ipcRenderer.invoke("command:analyze-log", logContent)
  },
  cms: {
    getCMSItems: (type) => ipcRenderer.invoke("cms:getCMSItems", type),
    addCMSItem: (type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => ipcRenderer.invoke("cms:addCMSItem", type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson),
    updateCMSItem: (type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => ipcRenderer.invoke("cms:updateCMSItem", type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson),
    deleteCMSItem: (type, itemId) => ipcRenderer.invoke("cms:deleteCMSItem", type, itemId),
    triggerHuntingTime: () => ipcRenderer.invoke("cms:triggerHuntingTime"),
    getRolePlayingCards: () => ipcRenderer.invoke("cms:getRolePlayingCards"),
    getRolePlayingCardById: (cardId) => ipcRenderer.invoke("cms:getRolePlayingCardById", cardId),
    addRolePlayingCard: (cardData) => ipcRenderer.invoke("cms:addRolePlayingCard", cardData),
    updateRolePlayingCard: (cardData) => ipcRenderer.invoke("cms:updateRolePlayingCard", cardData),
    deleteRolePlayingCard: (cardId) => ipcRenderer.invoke("cms:deleteRolePlayingCard", cardId)
  },
  organization: {
    getPosts: () => ipcRenderer.invoke("org:getPosts"),
    getCharacters: () => ipcRenderer.invoke("org:getCharacters"),
    getAssignments: () => ipcRenderer.invoke("org:getAssignments"),
    setAssignment: (postId, characterId) => ipcRenderer.invoke("org:setAssignment", postId, characterId),
    getAssignmentByPostId: (postId) => ipcRenderer.invoke("org:getAssignmentByPostId", postId)
  },
  ipc: {
    onAIServiceReady: (callback) => {
      const handler = () => callback();
      ipcRenderer.on("ai-service-ready", handler);
      return () => ipcRenderer.removeListener("ai-service-ready", handler);
    },
    removeAIServiceReadyListener: (callback) => {
      ipcRenderer.removeListener("ai-service-ready", callback);
    },
    onRoundtableMessage: (callback) => {
      const handler = (event, message) => callback(message);
      ipcRenderer.on("roundtable:newMessage", handler);
      return () => ipcRenderer.removeListener("roundtable:newMessage", handler);
    }
  }
};
contextBridge.exposeInMainWorld("api", exposedAPI);
console.log("Preload script context bridge exposed `api`.");
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
