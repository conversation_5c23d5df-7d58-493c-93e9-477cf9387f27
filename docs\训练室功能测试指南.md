# 绝对领域训练室功能测试指南

## 🎯 测试目标
验证训练室的道具使用、状态变化、身体发展、角色卡切换等核心功能是否正常工作。

## 📋 测试前准备

### 1. 确保 AI 服务配置
- ✅ 检查设置页面中的 API Key 是否已配置
- ✅ 确认 AI 服务状态显示为"就绪"
- ✅ 测试基本 AI 对话功能是否正常

### 2. 导入测试资产
- ✅ 进入设置页面 → 军械库 → 资产管理中心
- ✅ 导入"雾岛玲奈资产包"
- ✅ 导入"林珞自然骚魂版资产包"
- ✅ 确认导入成功，显示资产数量

### 3. 检查资产显示
- ✅ 在资产管理中心各标签页查看导入的资产
- ✅ 确认道具、服装、姿势都正确显示
- ✅ 检查角色卡标签页显示新导入的角色

## 🧪 核心功能测试

### 测试 1：基础训练室功能
1. **进入训练室**
   - 导航到"绝对领域训练室"
   - 确认界面正常加载
   - 检查左侧状态面板、右侧军械库是否显示

2. **检查初始状态**
   - 查看林珞状态监控面板
   - 记录初始数值：arousal、mood、sanityPoints 等
   - 确认身体发展蓝图面板显示各部位开发度

### 测试 2：角色卡切换功能
1. **切换到雾岛玲奈**
   - 在右侧面板选择"雾岛玲奈 (SC001)"角色卡
   - 观察是否显示："角色卡已切换为：【雾岛玲奈】"
   - 检查状态是否根据角色卡设定更新

2. **切换到林珞自然骚魂版**
   - 选择"林珞 · 自然骚魂版"角色卡
   - 观察状态变化和系统消息
   - 确认角色人格切换成功

### 测试 3：道具使用功能
1. **使用雾岛玲奈专属道具**
   - 确保选择了雾岛玲奈角色卡
   - 点击"高级产品展示箱"道具
   - 观察以下反应：
     - ✅ 用户消息显示道具使用描述
     - ✅ AI 响应符合雾岛玲奈人格
     - ✅ 状态数值发生变化（arousal +5, trust +3）
     - ✅ 如有身体发展效果，显示开发度提升消息

2. **使用林珞自然骚魂版道具**
   - 切换到林珞自然骚魂版角色卡
   - 点击"灵感激发器"道具
   - 观察以下反应：
     - ✅ AI 响应体现自然骚魂版人格特色
     - ✅ 状态更新（arousal +15, creativity +20, focus +10）
     - ✅ 林珞的反应符合角色设定

### 测试 4：服装和姿势功能
1. **服装切换**
   - 在军械库中切换到"服装"标签
   - 点击任意服装
   - 观察 AI 反应和状态变化

2. **姿势变化**
   - 切换到"姿势"标签
   - 选择不同姿势
   - 确认 AI 响应符合姿势描述

### 测试 5：身体发展系统
1. **使用有开发效果的道具**
   - 寻找 development_effects_json 不为空的道具
   - 使用后观察是否显示："姐姐的【XX】开发度提升 X 点！"
   - 检查身体发展蓝图面板数值是否更新

2. **成就解锁测试**
   - 持续使用道具提升开发度
   - 观察是否触发成就解锁消息
   - 检查成就大厅是否有新成就

## 🔍 问题诊断

### 如果道具没有效果：
1. **检查控制台日志**
   - 按 F12 打开开发者工具
   - 查看 Console 标签页的错误信息
   - 寻找 AI 响应、状态解析相关日志

2. **检查 AI 响应格式**
   - 观察 AI 响应是否包含 `[[STATUS:{}]]` 标记
   - 确认状态 JSON 格式是否正确
   - 查看是否有 `[[DEVELOP_BODY_ZONE:{}]]` 指令

3. **验证资产数据**
   - 在资产管理中心检查道具的效果配置
   - 确认 status_effects_json 和 development_effects_json 格式正确

### 如果角色卡切换无效：
1. **检查角色卡数据**
   - 确认角色卡已正确导入到数据库
   - 检查 initial_status_override_json 配置
   - 验证 persona_snippet_override 是否生效

2. **检查 AI 人格加载**
   - 观察 AI 响应风格是否符合角色设定
   - 确认角色专属提示词是否生效

### 如果状态不更新：
1. **检查状态监控面板**
   - 确认面板是否展开显示
   - 刷新页面重新进入训练室
   - 检查数据库连接是否正常

## 📊 预期测试结果

### 正常工作的表现：
- ✅ 道具点击后有 AI 响应
- ✅ 状态数值实时更新
- ✅ 身体发展度正确提升
- ✅ 角色卡切换改变 AI 人格
- ✅ 系统消息正确显示
- ✅ 成就系统正常触发

### 异常情况处理：
- ❌ 如果 AI 无响应 → 检查 API Key 配置
- ❌ 如果状态不变 → 检查状态解析逻辑
- ❌ 如果开发度不提升 → 检查身体发展系统
- ❌ 如果角色卡无效 → 检查数据库导入

## 🎮 高级测试

### 组合效果测试：
1. **连续使用多个道具**
   - 观察状态累积效果
   - 测试不同道具的组合反应

2. **跨角色卡测试**
   - 在不同角色卡间切换
   - 使用对应的专属道具
   - 验证人格一致性

3. **长期发展测试**
   - 持续提升某个部位的开发度
   - 观察是否解锁新内容
   - 测试成就系统完整性

## 📝 测试记录模板

```
测试时间：____
测试功能：____
预期结果：____
实际结果：____
问题描述：____
解决方案：____
```

完成测试后，请记录遇到的任何问题，这将帮助我们进一步优化系统！
