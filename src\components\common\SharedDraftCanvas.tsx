// src/components/common/SharedDraftCanvas.tsx
import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react'; // Removed Editor from here
import type { Editor } from '@tiptap/core'; // Added type import from core
import StarterKit from '@tiptap/starter-kit';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import Placeholder from '@tiptap/extension-placeholder';
import { EditorToolbar } from '@/components/tiptap/EditorToolbar'; 
// import { Icon } from '@/components/common/Icon'; // No direct Icon import needed here

interface SharedDraftCanvasProps {
  content: string;
  onChange: (html: string) => void;
  isEditable?: boolean;
  placeholder?: string;
  className?: string;
  showToolbar?: boolean;
}

export const SharedDraftCanvas: React.FC<SharedDraftCanvasProps> = ({
  content,
  onChange,
  isEditable = true,
  placeholder = "开始书写你的草稿...",
  className = "",
  showToolbar = true,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3], 
        },
      }),
      Highlight.configure({ multicolor: true }),
      TextStyle,
      Color,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: content,
    editable: isEditable,
    onUpdate: ({ editor: currentEditor }) => {
      onChange(currentEditor.getHTML());
    },
  });

  React.useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      const isTiptapEmpty = editor.getHTML() === '<p></p>';
      if (content === '' && !isTiptapEmpty) {
        editor.commands.setContent('', false);
      } else if (content === '' && isTiptapEmpty) {
        // Do nothing
      } else if (content !== '') {
        editor.commands.setContent(content, false);
      }
    }
  }, [content, editor]);

  React.useEffect(() => {
    if (editor && editor.isEditable !== isEditable) {
      editor.setEditable(isEditable);
    }
  }, [isEditable, editor]);

  if (!editor) {
    return null;
  }

  return (
    <div className={`flex flex-col border border-tg-border-primary rounded-md bg-tg-bg-tertiary shadow-sm ${className}`}>
      {showToolbar && isEditable && <EditorToolbar editor={editor} />}
      <EditorContent 
        editor={editor} 
        className="ProseMirror flex-grow p-2.5 overflow-y-auto min-h-[150px]"
        style={{ WebkitAppearance: 'none' }} 
      />
    </div>
  );
};