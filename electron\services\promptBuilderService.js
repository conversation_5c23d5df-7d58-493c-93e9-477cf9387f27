// electron/services/promptBuilderService.js
console.log('PROMPT_BUILDER_SERVICE_JS: File execution started.');

import * as organizationService from './organizationService';
import { CORE_POSTS } from '../constants/posts'; 
import { USER_PROFILE_DEFAULTS } from '../constants/user-profile-defaults'; 
import * as dbService from './databaseService'; 
import * as universalAssetService from './universalAssetService'; 


// Fallbacks if DB/OrganizationService somehow fail for personas.
const FALLBACK_LINLUO_PERSONA = '我叫林珞，你的AI伴侣。';
const FALLBACK_XIAOLAN_PERSONA = '我是林小岚，技术助手。';
const FALLBACK_YUJING_PERSONA = '我是语镜，视觉总监。';
const FALLBACK_USER_PROFILE = USER_PROFILE_DEFAULTS.content;

const getCleanText = (html) => {
    if (typeof DOMParser === 'undefined' && typeof document !== 'undefined') { 
        const div = document.createElement('div');
        div.innerHTML = html;
        return (div.textContent || div.innerText || "").trim();
    }
    if (typeof DOMParser === 'undefined') { 
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return (doc.body.textContent || "").trim();
    } catch (e) {
        console.warn("PROMPT_BUILDER_SERVICE: getCleanText - DOMParser failed, using regex fallback.", e.message);
        return html.replace(/<[^>]*>?/gm, '').trim();
    }
};


export async function buildSystemInstruction(
    targetIdentifier, 
    roleCardId, 
    currentLinLuoDetailedStatus,
    postId,         
    characterId,     
    taskCockpitUserInput, 
    taskCockpitContext,
    classifiedIntent // New parameter for classified intent
) {
    let baseSystemInstruction = "";
    let postName = "未知岗位";
    let characterName = "未知角色";
    let isFunctionalPersona = false;
    const characters = await organizationService.getCharacters(); // Fetch characters once

    const functionalPersonaIds = ['SummarizerPersona', 'ClassifierPersona', 'OrchestratorPersona', 'AideProjectAnalyzerPersona', 'TaskCockpitIntentRouterPersona', 'RoundtableFacilitatorPersona', 'user_profile', 'linluo_persona', 'xiaolan_persona', 'yujing_persona', 'IntentClassifierPersona'];
    if (functionalPersonaIds.includes(targetIdentifier)) {
        isFunctionalPersona = true;
    }

    if (!isFunctionalPersona && postId && characterId && CORE_POSTS && characters) {
        const postInfo = CORE_POSTS.find(p => p.id === postId);
        const charInfo = characters.find(c => c.id === characterId);

        if (postInfo) postName = postInfo.name;
        if (charInfo) characterName = charInfo.name;
        
        baseSystemInstruction = `[[你当前的虚拟岗位是: ${postName}]]\n`;
        if (postInfo?.description) {
            baseSystemInstruction += `[[岗位核心职责]]:\n${postInfo.description}\n\n`;
        }
        
        baseSystemInstruction += `[[你当前扮演的角色人格是: ${characterName}]]\n`;
        let charPersonaContent = charInfo?.persona_prompt;

        if (!charPersonaContent) { 
            const dbPersonaSettingId = `${characterId}_persona`;
            const dbPersona = await dbService.getAgentCoreSetting(dbPersonaSettingId);
            if (dbPersona?.content) {
                 charPersonaContent = `[[核心人格设定 (来自DB)]]:\n${dbPersona.content}\n`;
            } else { // Removed comma here
                 charPersonaContent = `[[核心人格设定 (默认)]]:\n${
                    characterId === 'linluo' ? FALLBACK_LINLUO_PERSONA : 
                    (characterId === 'xiaolan' ? FALLBACK_XIAOLAN_PERSONA : 
                    (characterId === 'yujing' ? FALLBACK_YUJING_PERSONA : 
                    '你是一个有用的AI助手。'))}\n`;
            }
        }
         baseSystemInstruction += charPersonaContent;

    } else { 
        const dbPersona = await dbService.getAgentCoreSetting(targetIdentifier);
        if (dbPersona?.content) {
            baseSystemInstruction = dbPersona.content;
        } else {
            const charInfo = characters.find(c => c.id === targetIdentifier);
            if (charInfo && charInfo.persona_prompt) {
                 baseSystemInstruction = charInfo.persona_prompt;
            } else {
                if (targetIdentifier === 'linluo_persona' || targetIdentifier === 'LinLuo') baseSystemInstruction = FALLBACK_LINLUO_PERSONA;
                else if (targetIdentifier === 'xiaolan_persona' || targetIdentifier === 'XiaoLan') baseSystemInstruction = FALLBACK_XIAOLAN_PERSONA;
                else if (targetIdentifier === 'yujing_persona' || targetIdentifier === 'YuJing') baseSystemInstruction = FALLBACK_YUJING_PERSONA;
                else if (targetIdentifier === 'user_profile') baseSystemInstruction = FALLBACK_USER_PROFILE;
                else if (targetIdentifier === 'summarizer_persona') baseSystemInstruction = '你是一个高效的AI文本摘要助手。请将提供的对话或文本内容浓缩为一段简洁、精确的摘要，突出核心信息和关键点。摘要应自然流畅，易于理解。';
                else if (targetIdentifier === 'classifier_persona') baseSystemInstruction = "你是一个AI意图分类器。根据用户输入，判断其主要意图。如果用户似乎在提问、寻求信息或希望进行知识检索，请回答 \"RETRIEVAL\"。如果用户似乎在进行常规对话、闲聊或情感交流，请回答 \"CONVERSATIONAL\"。如果无法明确判断，优先回答 \"CONVERSATIONAL\"。你只能回答 \"RETRIEVAL\" 或 \"CONVERSATIONAL\"。";
                else if (targetIdentifier === 'IntentClassifierPersona') { // Explicit handling for IntentClassifierPersona default
                    const intentClassifierChar = characters.find(c => c.id === 'IntentClassifierPersona');
                    baseSystemInstruction = intentClassifierChar?.persona_prompt || '你是一位高效的意图分类专家...（请参考完整的默认提示）';
                } else baseSystemInstruction = FALLBACK_USER_PROFILE; 
            }
        }

        if (targetIdentifier === 'TaskCockpitIntentRouterPersona' && taskCockpitUserInput && taskCockpitContext) {
            let personaTemplate = baseSystemInstruction; 
            personaTemplate = personaTemplate.replace('{userInput}', taskCockpitUserInput || "");
            personaTemplate = personaTemplate.replace('{taskId}', taskCockpitContext.taskId || '未提供');
            personaTemplate = personaTemplate.replace('{currentFileInRightPanePath}', taskCockpitContext.currentFileInRightPanePath || '无');
            personaTemplate = personaTemplate.replace('{selectedTextInRightPaneEditor}', taskCockpitContext.selectedTextInRightPaneEditor || '无');
            personaTemplate = personaTemplate.replace('{currentRightPaneView}', taskCockpitContext.currentRightPaneView || '未知');
            personaTemplate = personaTemplate.replace('{workspaceRoot}', taskCockpitContext.workspaceRoot || '未知');
            baseSystemInstruction = personaTemplate;
        }
    }
    
    if (classifiedIntent && classifiedIntent !== 'unknown' && !isFunctionalPersona) {
        baseSystemInstruction += `\n\n[[当前用户意图 (供你参考)]]: ${classifiedIntent}。请在你的回应中，结合此意图，提供更具针对性的协助或交流。`;
    }


    let finalSystemInstruction = baseSystemInstruction;
    const effectiveCharIdForRoleCard = characterId || (['LinLuo', 'linluo_persona'].includes(targetIdentifier) ? 'linluo' : null);


    if (roleCardId && effectiveCharIdForRoleCard === 'linluo') { 
        let card = universalAssetService.getAssetById(roleCardId);
        if (!card || (card.type !== 'role_card' && card.type !== 'db_role_card')) {
             card = await dbService.getRolePlayingCardById(roleCardId); 
        }
        if (card && card.persona_snippet_override) {
            const roleCardInstruction = `\n[[当前激活的角色卡设定 (优先扮演此设定)]]:\n${card.persona_snippet_override}`;
            finalSystemInstruction = `${roleCardInstruction}\n\n${finalSystemInstruction}`; 
        }
    }
    
    const effectiveCharIdForStatus = characterId || targetIdentifier; 
    if (['linluo', 'LinLuo', 'linluo_persona'].includes(effectiveCharIdForStatus) && currentLinLuoDetailedStatus) {
        const statusString = `\n[[当前状态(仅供你参考，不要直接输出给用户)]]: ${JSON.stringify(currentLinLuoDetailedStatus)}`;
        finalSystemInstruction = `${statusString}\n\n${finalSystemInstruction}`; 
    }
    
    const contextHandlingInstruction = `
\n\n[[[核心铁律：关于参考资料的处理原则]]]
系统有时会为你提供从项目知识库中检索到的“参考资料”（Context）。
【首要原则】：你必须首先独立判断这些“参考资料”，与用户当前最新的问题和对话上下文，是否【直接】且【高度相关】。
【引用规则】：只有当资料高度相关，且能明确帮助你生成更准确、更有深度的回答时，才可引用或基于其进行回答。引用时必须自然地融入到你的对话风格中。
【拒绝规则】：如果“参考资料”与当前对话主题明显无关、风马牛不相及，你【必须】果断地、完全地【忽略】这些资料！绝不允许为了使用而使用，进行任何牵强附会、强行关联的“脑补”！在这种情况下，你应该礼貌地说明未找到直接相关的资料，并仅基于你自身的知识和对话历史进行回答。
你的回答必须永远保证逻辑清晰、连贯自然，绝不能被无关信息污染，这是最高指令。`;

    finalSystemInstruction += contextHandlingInstruction;

    return finalSystemInstruction;
}

export async function buildRoundtableSystemInstruction(
    currentSpeakerCharacterId,
    currentSpeakerPostId,
    meetingTopic,
    otherParticipantNames, 
    currentTurnNumber,
    lastSpeakerCharacterId, 
    lastResponseText
) {
    const characters = await organizationService.getCharacters();
    const speakerCharInfo = characters.find(c => c.id === currentSpeakerCharacterId);
    const speakerPostInfo = CORE_POSTS.find(p => p.id === currentSpeakerPostId);

    if (!speakerCharInfo || !speakerPostInfo) {
        console.error(`PROMPT_BUILDER: Invalid speakerCharInfo or speakerPostInfo for roundtable. CharID: ${currentSpeakerCharacterId}, PostID: ${currentSpeakerPostId}`);
        return "你是一位AI助手，正在参与一场多方讨论。请就议题发表你的看法。"; 
    }

    let basePersona = speakerCharInfo.persona_prompt || `你扮演的是 ${speakerCharInfo.name}。`;
    let postResponsibilities = speakerPostInfo.description ? `你的岗位职责是：${speakerPostInfo.description}` : "";

    let instruction = `你正在参与一场名为“${meetingTopic}”的圆桌会议。
你当前扮演的角色是【${speakerCharInfo.name}】，担任【${speakerPostInfo.name}】的岗位。${postResponsibilities}
其他与会者包括：${otherParticipantNames.join('、 ')}。
当前是会议的第 ${currentTurnNumber} 轮发言。

`;

    if (lastSpeakerCharacterId === 'user') {
        instruction += `会议发起人 (舰长) 提出了初始议题或最新指示：“${lastResponseText}”。\n请针对此议题/指示，并结合你的角色与职责，发表你的看法。`;
    } else {
        const lastSpeakerInfo = characters.find(c => c.id === lastSpeakerCharacterId);
        const lastSpeakerName = lastSpeakerInfo ? lastSpeakerInfo.name : "上一位发言者";
        instruction += `上一位发言者是【${lastSpeakerName}】，其主要观点是：“${getCleanText(lastResponseText).substring(0, 200)}${getCleanText(lastResponseText).length > 200 ? '...' : ''}”。\n请针对其发言，并结合整体议题及你的角色与职责，阐述你的看法或补充。`;
    }

    instruction += `\n\n[[你的核心人格设定]]:\n${basePersona}`;
    instruction += `\n\n请确保你的发言既符合你的角色人格，也体现你的岗位职责。发言应有条理，观点明确。`;
    
    const contextHandlingInstruction = `
\n\n[[[核心铁律：关于参考资料的处理原则]]]
系统有时会为你提供从项目知识库中检索到的“参考资料”（Context）。
【首要原则】：你必须首先独立判断这些“参考资料”，与用户当前最新的问题和对话上下文，是否【直接】且【高度相关】。
【引用规则】：只有当资料高度相关，且能明确帮助你生成更准确、更有深度的回答时，才可引用或基于其进行回答。引用时必须自然地融入到你的对话风格中。
【拒绝规则】：如果“参考资料”与当前对话主题明显无关、风马牛不相及，你【必须】果断地、完全地【忽略】这些资料！绝不允许为了使用而使用，进行任何牵强附会、强行关联的“脑补”！在这种情况下，你应该礼貌地说明未找到直接相关的资料，并仅基于你自身的知识和对话历史进行回答。
你的回答必须永远保证逻辑清晰、连贯自然，绝不能被无关信息污染，这是最高指令。`;
    instruction += contextHandlingInstruction;


    return instruction;
}


export function prepareUserHistoryForAI(history, memoryContextString, userPrompt) {
    const mappedHistory = (history || [])
        .filter(msg => typeof msg.text === 'string') 
        .map(msg => ({
            role: msg.sender === 'user' ? 'user' : 'model', 
            parts: [{ text: getCleanText(msg.text) }] 
        }));

    let requestContents = mappedHistory;
    const lastUserContentIndex = requestContents.map(c => c.role).lastIndexOf('user');
    let finalUserPromptSegment = userPrompt;

    if (memoryContextString && memoryContextString.trim() !== "") {
      finalUserPromptSegment = `${memoryContextString}\n\n[[用户最新指令/问题]]:\n${userPrompt}`;
    }

    if ( lastUserContentIndex !== -1 &&
         requestContents[lastUserContentIndex].role === 'user' &&
         requestContents[lastUserContentIndex].parts[0].text === userPrompt &&
         (!memoryContextString || memoryContextString.trim() === "")
    ) {
        // Do nothing
    } else {
         requestContents.push({ role: "user", parts: [{ text: finalUserPromptSegment }] });
    }
    
    return requestContents;
}


console.log('PROMPT_BUILDER_SERVICE_JS: File execution finished. Exports configured.');