// electron/main.ts
console.log('ELECTRON MAIN PROCESS SCRIPT STARTED'); // First line

import { app, BrowserWindow, ipcMain, dialog, protocol, OpenDialogOptions } from 'electron'; 
import path from 'node:path'; 
import nodeFs from 'node:fs';
import { fileURLToPath } from 'node:url';
import crypto from 'node:crypto'; 
import os from 'node:os'; 

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
console.log(`ELECTRON MAIN: __filename: ${__filename}`);
console.log(`ELECTRON MAIN: __dirname set to: ${__dirname}`);

console.log('ELECTRON MAIN: Core Electron and Node modules imported.');

const fsPromises = nodeFs.promises;

// Import from globalConfig.ts using the '@/' alias
import { DEFAULT_SETTINGS } from '@/config/globalConfig'; 
console.log('ELECTRON MAIN: Constants imported. DEFAULT_SETTINGS.chatModel:', DEFAULT_SETTINGS?.chatModel);

process.env.DIST = path.join(__dirname, '../dist'); 
process.env.VITE_PUBLIC = app.isPackaged
  ? process.env.DIST
  : path.join(process.env.DIST, '../public');

// Corrected import paths for services - REMOVE .js from local service imports
import * as dbService from './services/databaseService';
import * as ragServiceImport from './services/ragService'; 
import * as aiOrchestrationService from './services/aiOrchestrationService'; 
import * as audioService from './services/audioService'; 
import * as aiHypothesisService from './services/aiHypothesisService';
import * as aiCodeAssistService from './services/aiCodeAssistService';
import * as universalAssetService from './services/universalAssetService';
import * as fileSystemService from './services/fileSystemService';
import * as commandExecutionService from './services/commandExecutionService'; 
import * as organizationService from './services/organizationService';


const ragService = ragServiceImport as any; 


console.log('ELECTRON MAIN: Services imported via ES module import.');


if (!dbService || !aiOrchestrationService || !audioService || !ragService || !aiHypothesisService || !aiCodeAssistService || !universalAssetService || !fileSystemService || !commandExecutionService || !organizationService) { 
  console.error("ELECTRON MAIN CRITICAL ERROR: One or more services are not loaded after import. dbService:", !!dbService, "aiOrchestrationService:", !!aiOrchestrationService, "audioService:", !!audioService, "ragService:", !!ragService, "aiHypothesisService:", !!aiHypothesisService, "aiCodeAssistService:", !!aiCodeAssistService, "universalAssetService:", !!universalAssetService, "fileSystemService:", !!fileSystemService, "commandExecutionService:", !!commandExecutionService, "organizationService:", !!organizationService);
  dialog.showErrorBox("Service Undefined", "A critical service is undefined after loading attempt. Application will exit.");
  app.quit();
  throw new Error("Critical service undefined.");
}
console.log('ELECTRON MAIN: All required services (DB, AIOrchestration, Audio, RAG, AIHypothesis, AICodeAssist, UniversalAsset, FileSystem, CommandExecution, Organization) confirmed to be loaded.');

let mainWindow: BrowserWindow | null = null; 

async function initializeServices() {
  console.log('ELECTRON MAIN: Initializing services (async)...');
  const userDataPath = app.getPath('userData');
  try {
    console.log('ELECTRON MAIN: Initializing organizationService (loads posts/characters)...');
    await organizationService.initializeOrganizationService(); 
    console.log("ELECTRON MAIN: Organization service initialized.");

    console.log('ELECTRON MAIN: Initializing dbService (aggregator, which calls core)...');
    await dbService.initializeDatabaseService(userDataPath, DEFAULT_SETTINGS); 
    console.log("ELECTRON MAIN: Database service initialized by main process.");
    
    const initialSettings = await dbService.getSettings(); 
    
    console.log('ELECTRON MAIN: Initializing ragService...'); 
    ragService.initializeRagService(dbService, userDataPath); 
    console.log("ELECTRON MAIN: RAG service initialized by main process."); 

    console.log('ELECTRON MAIN: Initializing universalAssetService...');
    universalAssetService.initializeUniversalAssetService(userDataPath); 
    console.log("ELECTRON MAIN: Universal Asset service initialized by main process.");

    console.log('ELECTRON MAIN: aiHypothesisService is available (no explicit init needed from main.ts).');
    console.log('ELECTRON MAIN: aiCodeAssistService is available (no explicit init needed from main.ts).');
    console.log('ELECTRON MAIN: fileSystemService is available (no explicit init needed from main.ts).');
    console.log('ELECTRON MAIN: commandExecutionService is available (no explicit init needed from main.ts).');


    if (initialSettings && initialSettings.apiKey && aiOrchestrationService && typeof aiOrchestrationService.initializeAIAgent === 'function') { 
      console.log('ELECTRON MAIN: Initializing AI Orchestration Service (which includes kernel) with API key from initial settings.'); 
      aiOrchestrationService.initializeAIAgent(initialSettings.apiKey); 
    } else {
      console.log('[ELECTRON MAIN] AI Orchestration Service initialization SKIPPED (No API key or service issue).');
    }

    console.log('ELECTRON MAIN: Initializing AudioService...'); 
    audioService.initializeAudioService(initialSettings, dbService); 
    console.log("ELECTRON MAIN: Audio service initialized."); 


    console.log('ELECTRON MAIN: All services initialized successfully.');
  } catch (initError: any) {
      console.error("ELECTRON MAIN CRITICAL ERROR: Failed to initialize services.", initError);
      dialog.showErrorBox("Service Initialization Error", `Failed to initialize services: ${initError.message}. Application will exit.`);
      app.quit();
      throw initError;
  }
}


function createWindow() {
  console.log("ELECTRON MAIN: createWindow() called.");
  
  let preloadScriptPath: string;
  if (app.isPackaged) {
    preloadScriptPath = path.join(__dirname, 'preload.mjs'); 
  } else {
    preloadScriptPath = path.join(__dirname, 'preload.mjs'); 
  }

  console.log(`ELECTRON MAIN: Attempting to use preload script at: ${preloadScriptPath}`);
  
  if (!nodeFs.existsSync(preloadScriptPath)) {
    const errorMsg = `Preload script not found at ${preloadScriptPath}. Vite might not have built it correctly or main.ts is looking for the wrong file extension.`;
    console.error(`ELECTRON MAIN CRITICAL ERROR: ${errorMsg}`);
    dialog.showErrorBox("Preload Script Error", `${errorMsg}\nApplication will exit.`);
    app.quit();
    return;
  }

  mainWindow = new BrowserWindow({
    width: 1600, 
    height: 950, 
    webPreferences: {
      preload: preloadScriptPath, 
      contextIsolation: true, 
      nodeIntegration: false,
      sandbox: false, 
      devTools: !app.isPackaged, 
    },
    icon: path.join(process.env.VITE_PUBLIC!, 'vite.svg') 
  });
  console.log(`ELECTRON MAIN: BrowserWindow webPreferences configured. Preload: ${preloadScriptPath}`);

  if (mainWindow && audioService) { 
      audioService.setMainWindowWebContents(mainWindow.webContents);
      console.log("ELECTRON MAIN: Passed mainWindow.webContents to audioService.");
  }

  const devServerUrl = process.env.VITE_DEV_SERVER_URL; 

  if (devServerUrl) { 
    console.log(`ELECTRON MAIN: Loading Dev Server URL: ${devServerUrl}`);
    mainWindow.loadURL(devServerUrl); 
    if(!app.isPackaged) mainWindow.webContents.openDevTools();
  } else {
    const indexPath = path.join(process.env.DIST!, 'index.html');
    console.log(`ELECTRON MAIN: Loading production file from: ${indexPath}`);
     if (!nodeFs.existsSync(indexPath)) {
        console.error(`ELECTRON MAIN CRITICAL ERROR: Production index.html not found at ${indexPath}`);
        dialog.showErrorBox("File Not Found", `Production index.html not found at ${indexPath}. Application will exit.`);
        app.quit();
        return;
    }
    mainWindow.loadFile(indexPath);
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  console.log("ELECTRON MAIN: Main window created and content loading initiated.");
}

const AVATAR_SYSTEM_PREFIX = 'system_packaged/'; 
const SYSTEM_RESOURCES_PATH_SEGMENT = 'system';

app.whenReady().then(async () => { 
  console.log("ELECTRON MAIN: App is ready.");
  
  await initializeServices(); 
  createWindow(); 

  protocol.registerFileProtocol('at-asset', (request, callback) => {
    try {
      const urlPath = request.url.slice('at-asset://'.length).split('?')[0]; 
      const decodedPath = decodeURI(urlPath); 
      const absolutePath = path.join(app.getPath('userData'), 'at_cms_assets', decodedPath);
      if (nodeFs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`AT-ASSET Protocol Error: File not found at ${absolutePath} (requested: ${request.url})`);
        callback({ error: -6 }); // FILE_NOT_FOUND
      }
    } catch (e: any) {
      console.error(`AT-ASSET Protocol Error: Error resolving path for ${request.url}`, e);
      callback({ error: -2 }); // GENERIC_FAILURE
    }
  });
  console.log("ELECTRON MAIN: 'at-asset' custom protocol registered.");
  
  protocol.registerFileProtocol('tgc-asset', (request, callback) => {
    try {
      const urlPathParts = request.url.slice('tgc-asset://'.length).split('?')[0];
      const decodedPath = decodeURI(urlPathParts); 
      const absolutePath = path.join(app.getPath('userData'), 'asset_packs', decodedPath);
      if (nodeFs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`TGC-ASSET Protocol Error: File not found at ${absolutePath} (requested: ${request.url})`);
        callback({ error: -6 }); // FILE_NOT_FOUND
      }
    } catch (e: any) {
      console.error(`TGC-ASSET Protocol Error: Exception for ${request.url}`, e);
      callback({ error: -2 }); // GENERIC_FAILURE
    }
  });
  console.log("ELECTRON MAIN: 'tgc-asset' custom protocol registered.");


  protocol.registerFileProtocol('app-avatar', (request, callback) => {
    try {
      let requestedPath = request.url.slice('app-avatar://'.length).split('?')[0]; // Strip query params
      let absolutePath;

      if (requestedPath.startsWith(AVATAR_SYSTEM_PREFIX)) {
        const relativePathInPackage = requestedPath.substring(AVATAR_SYSTEM_PREFIX.length);
        // process.env.DIST is the renderer's 'dist' directory.
        // viteStaticCopy copies 'resources/system' to 'dist/system'.
        absolutePath = path.join(process.env.DIST!, SYSTEM_RESOURCES_PATH_SEGMENT, relativePathInPackage);
        console.log(`APP-AVATAR: Resolving system_packaged avatar. Request URL: "${request.url}", RelativePathInPackage: "${relativePathInPackage}", Resolved AbsolutePath: "${absolutePath}"`);
      } else {
        // User-uploaded avatars (from userData)
        absolutePath = path.join(app.getPath('userData'), decodeURI(requestedPath));
        console.log(`APP-AVATAR: Resolving user avatar. Request URL: "${request.url}", DecodedPath: "${decodeURI(requestedPath)}", Resolved AbsolutePath: "${absolutePath}"`);
      }
      
      if (nodeFs.existsSync(absolutePath)) {
        callback({ path: absolutePath });
      } else {
        console.error(`APP-AVATAR Protocol Error: File NOT FOUND at resolved absolute path: "${absolutePath}" (Original request: "${request.url}")`);
        callback({ error: -6 }); // FILE_NOT_FOUND
      }
    } catch (e: any) {
      console.error(`APP-AVATAR Protocol Error: Exception for "${request.url}"`, e);
      callback({ error: -2 }); // GENERIC_FAILURE
    }
  });
  console.log("ELECTRON MAIN: 'app-avatar' custom protocol registered with system_packaged handling.");


  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) {
      console.log("ELECTRON MAIN: App activated and no windows open, creating new window.");
      createWindow();
    }
  });
});

app.on('will-quit', () => {
  console.log("ELECTRON MAIN: App 'will-quit' event triggered. Cleaning up services.");
  if (dbService) {
    dbService.closeDatabaseConnection(); 
  }
  console.log("ELECTRON MAIN: Services cleanup requested during 'will-quit'.");
});

app.on('window-all-closed', function () {
  console.log("ELECTRON MAIN: All windows closed.");
  if (os.platform() !== 'darwin') { 
    app.quit();
  }
});


// --- Database IPC Handlers ---
ipcMain.handle('db:getSettings', async () => {
  console.log("IPC_MAIN: Handling db:getSettings request.");
  if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:getSettings - dbService not loaded.");
    return { ...DEFAULT_SETTINGS, error: "Database service not available." }; 
  }
  try {
    const settings = await dbService.getSettings();
    if (settings && settings.apiKey && aiOrchestrationService && typeof aiOrchestrationService.initializeAIAgent === 'function') {
       aiOrchestrationService.initializeAIAgent(settings.apiKey);
       if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
          mainWindow.webContents.send('ai-service-ready');
          console.log("IPC_MAIN (db:getSettings): Sent 'ai-service-ready' due to API key presence.");
       }
    }
    if (audioService && typeof audioService.initializeAudioService === 'function') { 
        audioService.initializeAudioService(settings, dbService);
    }
    console.log("IPC_MAIN: db:getSettings returning settings.");
    return settings;
  } catch (e: any) {
    console.error("IPC_MAIN_ERROR: db:getSettings failed:", e);
    return { ...DEFAULT_SETTINGS, error: e.message };
  }
});
ipcMain.handle('db:saveSettings', async (event, settings) => {
  console.log("IPC_MAIN: Handling db:saveSettings request.");
   if (!dbService) {
    console.error("IPC_MAIN_ERROR: db:saveSettings - dbService not loaded.");
    return { success: false, error: "Database service not available." };
  }
  try {
    const result = await dbService.saveSettings(settings);
    if (result.success && settings.apiKey && aiOrchestrationService && typeof aiOrchestrationService.initializeAIAgent === 'function') {
        aiOrchestrationService.initializeAIAgent(settings.apiKey);
        if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
          mainWindow.webContents.send('ai-service-ready');
          console.log("IPC_MAIN (db:saveSettings): Sent 'ai-service-ready' after saving API key.");
        }
    }
    if (audioService && typeof audioService.initializeAudioService === 'function') { 
        audioService.initializeAudioService(settings, dbService);
    }
    console.log("IPC_MAIN: db:saveSettings result:", result.success);
    return result;
  } catch (e: any) {
    console.error("IPC_MAIN_ERROR: db:saveSettings failed:", e);
    return { success: false, error: e.message };
  }
});
// --- All other DB handlers from original file ---
ipcMain.handle('db:getAllProjects', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllProjects - dbService not loaded."); return []; }
    return await dbService.getAllProjects();
});
ipcMain.handle('db:addProject', async (event, projectData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addProject - dbService not loaded."); return { success: false, error: "Database service not available."}; }
    try {
        const newProject = await dbService.addProject(projectData);
        return { success: true, project: newProject };
    } catch (error: any) {
        console.error("IPC_MAIN_ERROR: db:addProject failed:", error);
        return { success: false, error: error.message };
    }
});
ipcMain.handle('db:getProjectById', async (event, projectId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getProjectById - dbService not loaded."); return null; }
    return await dbService.getProjectById(projectId);
});
ipcMain.handle('db:updateProject', async (event, projectData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateProject - dbService not loaded."); return { success: false, error: "Database service not available."}; }
    return await dbService.updateProject(projectData);
});
ipcMain.handle('db:deleteProject', async (event, projectId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteProject - dbService not loaded."); return { success: false, error: "Database service not available."}; }
    return await dbService.deleteProject(projectId);
});
ipcMain.handle('db:duplicateProject', async(event, projectId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:duplicateProject - dbService not loaded."); return null; }
    return await dbService.duplicateProject(projectId);
});
ipcMain.handle('db:addNoteToProject', async (event, projectId, pouchType, noteData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addNoteToProject - dbService not loaded."); return null; }
    return await dbService.addNoteToPouch(projectId, pouchType, noteData);
});
ipcMain.handle('db:updateNoteInProject', async (event, pouchType, noteData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateNoteInProject - dbService not loaded."); return null; }
    return await dbService.updateNoteInPouch(pouchType, noteData);
});
ipcMain.handle('db:deleteNoteFromProject', async (event, pouchType, noteId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteNoteFromProject - dbService not loaded."); return { success: false, error: "Database service not available."}; }
    return await dbService.deleteNoteFromPouch(pouchType, noteId);
});
ipcMain.handle('db:updateProjectMindMap', async (event, projectId, nodes, connections) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateProjectMindMap - dbService not loaded."); return { success: false, error: "Database service not available."}; }
    return await dbService.updateProjectMindMap(projectId, nodes, connections);
});
ipcMain.handle('db:getAllGlobalKnowledgeTomes', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllGlobalKnowledgeTomes - dbService not loaded."); return []; }
    return await dbService.getAllGlobalKnowledgeTomes();
});
ipcMain.handle('db:addGlobalKnowledgeTome', async (event, tomeData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addGlobalKnowledgeTome - dbService not loaded."); return null; }
    return await dbService.addGlobalKnowledgeTome(tomeData);
});
ipcMain.handle('db:updateGlobalKnowledgeTome', async (event, tomeData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateGlobalKnowledgeTome - dbService not loaded."); return null; }
    return await dbService.updateGlobalKnowledgeTome(tomeData);
});
ipcMain.handle('db:deleteGlobalKnowledgeTome', async (event, tomeId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteGlobalKnowledgeTome - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteGlobalKnowledgeTome(tomeId);
});
ipcMain.handle('db:addProjectKnowledgeTome', async (event, projectId, tomeData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addProjectKnowledgeTome - dbService not loaded."); return null; }
    return await dbService.addProjectKnowledgeTome(projectId, tomeData);
});
ipcMain.handle('db:updateProjectKnowledgeTome', async (event, projectId, tomeData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateProjectKnowledgeTome - dbService not loaded."); return null; }
    return await dbService.updateProjectKnowledgeTome(projectId, tomeData);
});
ipcMain.handle('db:deleteProjectKnowledgeTome', async (event, projectId, tomeId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteProjectKnowledgeTome - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteProjectKnowledgeTome(projectId, tomeId);
});
ipcMain.handle('db:addProjectKnowledgeCategory', async (event, projectId, categoryName) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addProjectKnowledgeCategory - dbService not loaded."); return undefined; }
    return await dbService.addProjectKnowledgeCategory(projectId, categoryName);
});
ipcMain.handle('db:removeProjectKnowledgeCategory', async (event, projectId, categoryName) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:removeProjectKnowledgeCategory - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.removeProjectKnowledgeCategory(projectId, categoryName);
});
ipcMain.handle('db:getAllGlobalQuickCommands', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllGlobalQuickCommands - dbService not loaded."); return []; }
    return await dbService.getAllGlobalQuickCommands();
});
ipcMain.handle('db:addGlobalQuickCommand', async (event, commandData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addGlobalQuickCommand - dbService not loaded."); return null; }
    return await dbService.addGlobalQuickCommand(commandData);
});
ipcMain.handle('db:updateGlobalQuickCommand', async (event, commandData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateGlobalQuickCommand - dbService not loaded."); return null; }
    return await dbService.updateGlobalQuickCommand(commandData);
});
ipcMain.handle('db:deleteGlobalQuickCommand', async (event, commandId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteGlobalQuickCommand - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteGlobalQuickCommand(commandId);
});
ipcMain.handle('db:saveChatMessage', async (event, projectId, message) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:saveChatMessage - dbService not loaded."); return null; }
    return await dbService.saveChatMessage(projectId, message);
});
ipcMain.handle('db:updateChatMessage', async (event, message) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateChatMessage - dbService not loaded."); return null; }
    return await dbService.updateChatMessage(message);
});
ipcMain.handle('db:getInitialChatMessages', async (event, projectId, limit) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getInitialChatMessages - dbService not loaded."); return []; }
    return await dbService.getInitialChatMessages(projectId, limit);
});
ipcMain.handle('db:getOlderChatMessages', async (event, projectId, beforeTimestamp, limit) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getOlderChatMessages - dbService not loaded."); return []; }
    return await dbService.getOlderChatMessages(projectId, beforeTimestamp, limit);
});
ipcMain.handle('db:summarizeAndReplaceMessages', async (event, projectId, messageIds, summaryMessage) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:summarizeAndReplaceMessages - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.summarizeAndReplaceMessages(projectId, messageIds, summaryMessage);
});
ipcMain.handle('db:getAllDevelopmentTasks', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllDevelopmentTasks - dbService not loaded."); return []; }
    return await dbService.getAllDevelopmentTasks();
});
ipcMain.handle('db:addDevelopmentTask', async (event, projectId, title) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addDevelopmentTask - dbService not loaded."); return null; }
    return await dbService.addDevelopmentTask(projectId, title);
});
ipcMain.handle('db:createDevelopmentTaskFromChat', async (event, payload) => { 
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:createDevelopmentTaskFromChat - dbService not loaded."); return null; }
    const { projectId, title, description } = payload;
    return await dbService.createDevelopmentTaskFromChat(projectId, title, description);
});
ipcMain.handle('db:deleteDevelopmentTask', async (event, taskId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteDevelopmentTask - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteDevelopmentTask(taskId);
});
ipcMain.handle('db:updateDevelopmentTaskContextFiles', async (event, taskId, contextFiles) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateDevelopmentTaskContextFiles - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.updateDevelopmentTaskContextFiles(taskId, contextFiles);
});
ipcMain.handle('db:updateDevelopmentTaskGeneratedCode', async (event, taskId, generatedCode) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateDevelopmentTaskGeneratedCode - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.updateDevelopmentTaskGeneratedCode(taskId, generatedCode);
});
ipcMain.handle('db:getAgentCoreSetting', async (event, settingId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAgentCoreSetting - dbService not loaded."); return null; }
    return await dbService.getAgentCoreSetting(settingId);
});
ipcMain.handle('db:getAllAgentCoreSettings', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllAgentCoreSettings - dbService not loaded."); return []; }
    return await dbService.getAllAgentCoreSettings();
});
ipcMain.handle('db:saveAgentCoreSetting', async (event, settingId, content) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:saveAgentCoreSetting - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.saveAgentCoreSetting(settingId, content);
});
ipcMain.handle('db:findRelevantMemories', async (event, queryEmbedding, contextInfo, limit) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:findRelevantMemories - dbService not loaded."); return {direct: [], associated: []}; }
    return await dbService.findRelevantMemories(queryEmbedding, contextInfo, limit);
});
ipcMain.handle('db:getCoreMemories', async (event, personaTarget, projectContextId, limit, importanceThreshold, keywords) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getCoreMemories - dbService not loaded."); return []; }
    return await dbService.getCoreMemories(personaTarget, projectContextId, limit, importanceThreshold, keywords);
});
ipcMain.handle('db:getAllCoreMemories', async (event, filters, sort, pagination) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllCoreMemories - dbService not loaded."); return []; }
    return await dbService.getAllCoreMemories(filters, sort, pagination);
});
ipcMain.handle('db:addCoreMemory', async (event, memoryData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addCoreMemory - dbService not loaded."); return null; }
    return await dbService.addCoreMemory(memoryData);
});
ipcMain.handle('db:addCoreMemoryFromChat', async (event, messageText, personaTarget, projectId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addCoreMemoryFromChat - dbService not loaded."); return null; }
    return await dbService.addCoreMemoryFromChat(messageText, personaTarget, projectId);
});
ipcMain.handle('db:updateCoreMemory', async (event, memoryData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateCoreMemory - dbService not loaded."); return null; }
    return await dbService.updateCoreMemory(memoryData);
});
ipcMain.handle('db:deleteCoreMemory', async (event, memoryId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:deleteCoreMemory - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteCoreMemory(memoryId);
});
ipcMain.handle('db:getCoreMemoryById', async (event, memoryId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getCoreMemoryById - dbService not loaded."); return null; }
    return await dbService.getCoreMemoryById(memoryId);
});
ipcMain.handle('settings:getAbsoluteTerritoryPassword', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: settings:getAbsoluteTerritoryPassword - dbService not loaded."); return null; }
    return await dbService.getAbsoluteTerritoryPassword();
});
ipcMain.handle('settings:setAbsoluteTerritoryPassword', async (event, password) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: settings:setAbsoluteTerritoryPassword - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.setAbsoluteTerritoryPassword(password);
});
ipcMain.handle('settings:verifyAbsoluteTerritoryPassword', async (event, password) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: settings:verifyAbsoluteTerritoryPassword - dbService not loaded."); return { isValid: false, isFirstTime: true }; }
    return await dbService.verifyAbsoluteTerritoryPassword(password);
});
ipcMain.handle('db:getAbsoluteTerritoryMessages', async (event, limit, beforeTimestamp) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAbsoluteTerritoryMessages - dbService not loaded."); return []; }
    return await dbService.getAbsoluteTerritoryMessages(limit, beforeTimestamp);
});
ipcMain.handle('db:addAbsoluteTerritoryMessage', async (event, message) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addAbsoluteTerritoryMessage - dbService not loaded."); return null; }
    return await dbService.addAbsoluteTerritoryMessage(message);
});
ipcMain.handle('db:clearAbsoluteTerritoryHistory', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:clearAbsoluteTerritoryHistory - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.clearAbsoluteTerritoryHistory();
});
ipcMain.handle('cms:getCMSItems', async (event, type) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:getCMSItems - dbService not loaded."); return []; }
    return await dbService.getCMSItems(type);
});
ipcMain.handle('cms:addCMSItem', async (event, type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:addCMSItem - dbService not loaded."); return null; }
    return await dbService.addCMSItem(type, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson);
});
ipcMain.handle('cms:updateCMSItem', async (event, type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:updateCMSItem - dbService not loaded."); return null; }
    return await dbService.updateCMSItem(type, itemId, itemData, iconBase64, cgBase64, statusEffectsJson, developmentEffectsJson, unlockRequirementsJson);
});
ipcMain.handle('cms:deleteCMSItem', async (event, type, itemId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:deleteCMSItem - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteCMSItem(type, itemId);
});
ipcMain.handle('cms:triggerHuntingTime', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:triggerHuntingTime - dbService not loaded."); return null; }
    return await dbService.triggerHuntingTime();
});
ipcMain.handle('cms:getRolePlayingCards', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:getRolePlayingCards - dbService not loaded."); return []; }
    return await dbService.getRolePlayingCards();
});
ipcMain.handle('cms:getRolePlayingCardById', async (event, cardId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:getRolePlayingCardById - dbService not loaded."); return null; }
    return await dbService.getRolePlayingCardById(cardId);
});
ipcMain.handle('cms:addRolePlayingCard', async (event, cardData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:addRolePlayingCard - dbService not loaded."); return null; }
    return await dbService.addRolePlayingCard(cardData);
});
ipcMain.handle('cms:updateRolePlayingCard', async (event, cardData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:updateRolePlayingCard - dbService not loaded."); return null; }
    return await dbService.updateRolePlayingCard(cardData);
});
ipcMain.handle('cms:deleteRolePlayingCard', async (event, cardId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: cms:deleteRolePlayingCard - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteRolePlayingCard(cardId);
});
ipcMain.handle('db:addLearningLog', async (event, logData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:addLearningLog - dbService not loaded."); return null; }
    return await dbService.addLearningLog(logData);
});
ipcMain.handle('db:getLearningLogs', async (event, filters, limit, offset) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getLearningLogs - dbService not loaded."); return []; }
    return await dbService.getLearningLogs(filters, limit, offset);
});
ipcMain.handle('db:getBodyDevelopment', async (event, zone_id) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getBodyDevelopment - dbService not loaded."); return null; }
    return await dbService.getBodyDevelopment(zone_id);
});
ipcMain.handle('db:getAllBodyDevelopment', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllBodyDevelopment - dbService not loaded."); return []; }
    return await dbService.getAllBodyDevelopment();
});
ipcMain.handle('db:updateBodyDevelopment', async (event, zone_id, pointsToAdd) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:updateBodyDevelopment - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.updateBodyDevelopment(zone_id, pointsToAdd);
});
ipcMain.handle('db:getAllAchievements', async () => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAllAchievements - dbService not loaded."); return []; }
    return await dbService.getAllAchievements();
});
ipcMain.handle('db:getUserAchievements', async (event, userId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getUserAchievements - dbService not loaded."); return []; }
    return await dbService.getUserAchievements(userId);
});
ipcMain.handle('db:unlockAchievement', async (event, userId, achievementId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:unlockAchievement - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.unlockAchievement(userId, achievementId);
});
ipcMain.handle('db:getAchievementById', async (event, achievementId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: db:getAchievementById - dbService not loaded."); return null; }
    return await dbService.getAchievementById(achievementId);
});
ipcMain.handle('tasks:getTaskById', async (event, taskId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:getTaskById - dbService not loaded."); return null; }
    return await dbService.getTaskById(taskId);
});
ipcMain.handle('tasks:getTasksByProjectId', async (event, projectId, filters, sortOptions) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:getTasksByProjectId - dbService not loaded."); return []; }
    return await dbService.getTasksByProjectId(projectId, filters, sortOptions);
});
ipcMain.handle('tasks:getTasksByStatus', async (event, projectId, statusArray) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:getTasksByStatus - dbService not loaded."); return []; }
    return await dbService.getTasksByStatus(projectId, statusArray);
});
ipcMain.handle('tasks:addTask', async (event, taskData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:addTask - dbService not loaded."); return null; }
    return await dbService.addTask(taskData);
});
ipcMain.handle('tasks:updateTask', async (event, taskId, updates) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:updateTask - dbService not loaded."); return null; }
    return await dbService.updateTask(taskId, updates);
});
ipcMain.handle('tasks:deleteTask', async (event, taskId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:deleteTask - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.deleteTask(taskId);
});
ipcMain.handle('tasks:addResourceLink', async (event, taskId, resourceData) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:addResourceLink - dbService not loaded."); return null; }
    return await dbService.addResourceLinkToTask(taskId, resourceData);
});
ipcMain.handle('tasks:getResourceLinks', async (event, taskId) => { 
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:getResourceLinks - dbService not loaded."); return []; }
    return await dbService.getResourceLinksForTask(taskId);
});
ipcMain.handle('tasks:removeResourceLink', async (event, linkId) => {
    if (!dbService) { console.error("IPC_MAIN_ERROR: tasks:removeResourceLink - dbService not loaded."); return { success: false, error: "Database service not available." }; }
    return await dbService.removeResourceLinkFromTask(linkId);
});
ipcMain.handle('tasks:suggestResources', async (event, taskId, taskTitle, taskDescription, projectId) => {
    if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: tasks:suggestResources - aiOrchestrationService not loaded."); return []; }
    return await aiOrchestrationService.suggestResourcesForTask(taskTitle, taskDescription, projectId);
});
ipcMain.handle('aiCodeAssist:generateCode', async (event, context) => {
    if (!aiCodeAssistService) { console.error("IPC_MAIN_ERROR: aiCodeAssist:generateCode - aiCodeAssistService not loaded."); return "AI Code Assist service not available."; }
    return await aiCodeAssistService.generateCode(context);
});
ipcMain.handle('aiCodeAssist:explainCode', async (event, context) => {
    if (!aiCodeAssistService) { console.error("IPC_MAIN_ERROR: aiCodeAssist:explainCode - aiCodeAssistService not loaded."); return "AI Code Assist service not available."; }
    return await aiCodeAssistService.explainCode(context);
});
ipcMain.handle('aiCodeAssist:generateDocForCode', async (event, context) => {
    if (!aiCodeAssistService) { console.error("IPC_MAIN_ERROR: aiCodeAssist:generateDocForCode - aiCodeAssistService not loaded."); return "AI Code Assist service not available."; }
    return await aiCodeAssistService.generateDocForCode(context);
});
ipcMain.handle('aiCodeAssist:reviewCode', async (event, context) => {
    if (!aiCodeAssistService) { console.error("IPC_MAIN_ERROR: aiCodeAssist:reviewCode - aiCodeAssistService not loaded."); return "AI Code Assist service not available."; }
    return await aiCodeAssistService.reviewCode(context);
});
ipcMain.handle('aiCodeAssist:analyzeErrorLog', async (event, context) => {
    if (!aiCodeAssistService) { console.error("IPC_MAIN_ERROR: aiCodeAssist:analyzeErrorLog - aiCodeAssistService not loaded."); return "AI Code Assist service not available."; }
    return await aiCodeAssistService.analyzeErrorLog(context);
});
ipcMain.handle('ai:assist:modifyCode', async (event, context) => {
    if (!aiCodeAssistService || typeof aiCodeAssistService.generateModifiedCode !== 'function') {
        console.error("IPC_MAIN_ERROR: ai:assist:modifyCode - aiCodeAssistService or generateModifiedCode function not loaded.");
        return { error: "AI Code Modification service not available." };
    }
    try {
        return await aiCodeAssistService.generateModifiedCode(context);
    } catch (error: any) {
        console.error("IPC_MAIN_ERROR: Error in ai:assist:modifyCode handler:", error);
        return { error: error.message || "Unknown error during code modification." };
    }
});
ipcMain.handle('fs:openDirectoryDialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, { properties: ['openDirectory'] });
  if (!result.canceled && result.filePaths.length > 0) { return result.filePaths[0]; }
  return null;
});
ipcMain.handle('fs:readDirectory', async (event, dirPath) => {
  try {
    const files = await fsPromises.readdir(dirPath, { withFileTypes: true });
    const fileNodes = await Promise.all(files.map(async (file) => {
      const filePath = path.join(dirPath, file.name);
      if (file.isDirectory()) { return { name: file.name, path: filePath, type: 'directory', children: [] };  } 
      else { return { name: file.name, path: filePath, type: 'file' }; }
    }));
    return fileNodes;
  } catch (error: any) { return { error: error.message }; }
});
ipcMain.handle('fs:readFileContent', async (event, filePath) => {
  try { return await fsPromises.readFile(filePath, 'utf-8'); } 
  catch (error: any) { return { error: error.message }; }
});
ipcMain.handle('fs:listFiles', async (event, directoryPath, recursive, depth) => { 
    if (!fileSystemService) { console.error("IPC_MAIN_ERROR: fs:listFiles - fileSystemService not loaded."); return { error: "File system service not available." }; }
    return await fileSystemService.listFiles(directoryPath, recursive, depth);
});
ipcMain.handle('fs:readFile', async (event, filePath) => {
    if (!fileSystemService) { console.error("IPC_MAIN_ERROR: fs:readFile - fileSystemService not loaded."); return { error: "File system service not available." }; }
    return await fileSystemService.readFile(filePath);
});
ipcMain.handle('fs:writeFile', async (event, filePath, content) => {
    if (!fileSystemService) { console.error("IPC_MAIN_ERROR: fs:writeFile - fileSystemService not loaded."); return { success: false, error: "File system service not available." }; }
    return await fileSystemService.writeFile(filePath, content);
});
ipcMain.handle('fs:archiveFileVersion', async (event, filePath, originalCode, userInstruction) => {
    if (!fileSystemService || typeof fileSystemService.archiveFileVersion !== 'function') {
        console.error("IPC_MAIN_ERROR: fs:archiveFileVersion - fileSystemService or archiveFileVersion function not loaded.");
        return { success: false, error: "File versioning service not available." };
    }
    try { return await fileSystemService.archiveFileVersion(filePath, originalCode, userInstruction); } 
    catch (error: any) { console.error("IPC_MAIN_ERROR: Error in fs:archiveFileVersion handler:", error); return { success: false, error: error.message || "Unknown error during file versioning." }; }
});
ipcMain.handle('fs:isDirectoryEmpty', async (event, directoryPath) => {
    if (!fileSystemService || typeof fileSystemService.isDirectoryEmpty !== 'function') { 
        console.error("IPC_MAIN_ERROR: fs:isDirectoryEmpty - fileSystemService or isDirectoryEmpty function not loaded."); 
        return { error: "File system service (isDirectoryEmpty) not available." }; 
    }
    return await fileSystemService.isDirectoryEmpty(directoryPath);
});
ipcMain.handle('fs:copyDirectoryContents', async (event, sourceDir, targetDir) => {
    if (!fileSystemService || typeof fileSystemService.copyDirectoryContents !== 'function') { 
        console.error("IPC_MAIN_ERROR: fs:copyDirectoryContents - fileSystemService or copyDirectoryContents function not loaded."); 
        return { success: false, error: "File system service (copyDirectoryContents) not available." }; 
    }
    return await fileSystemService.copyDirectoryContents(sourceDir, targetDir);
});
ipcMain.handle('file:read-package-json-scripts', async (event, projectPath) => {
  if (!projectPath || typeof projectPath !== 'string') { return { error: "Invalid project path provided." }; }
  const packageJsonPath = path.join(projectPath, 'package.json');
  try {
    const content = await fsPromises.readFile(packageJsonPath, 'utf-8');
    const packageJson = JSON.parse(content);
    return packageJson.scripts || {};
  } catch (error: any) { 
    if (error.code === 'ENOENT') { return { error: `package.json not found at ${packageJsonPath}` }; }
    return { error: `Failed to read or parse package.json: ${error.message}` };
  }
});
ipcMain.handle('fs:saveFileContent', async(event, filePath, content) => {
    try { await fsPromises.writeFile(filePath, content, 'utf-8'); return { success: true }; } 
    catch (error: any) { return { success: false, error: error.message }; }
});
ipcMain.handle('fs:openFileDialog', async (event, options?: OpenDialogOptions) => {
  const result = await dialog.showOpenDialog(mainWindow!, { properties: ['openFile'], ...(options || {}) });
  if (!result.canceled && result.filePaths.length > 0) { return result.filePaths[0]; }
  return null;
});
ipcMain.handle('fs:openMultipleFilesDialog', async (event, extensions) => {
  const result = await dialog.showOpenDialog(mainWindow!, { properties: ['openFile', 'multiSelections'], filters: extensions.length > 0 ? [{ name: 'Files', extensions: extensions }] : undefined });
  if (!result.canceled && result.filePaths.length > 0) { return result.filePaths; }
  return null;
});
ipcMain.handle('fs:copyFileToUserData', async (event, sourcePath, targetSubdir, targetFilename) => {
    const userDataPath = app.getPath('userData'); const targetDir = path.join(userDataPath, targetSubdir);
    try {
        await fsPromises.mkdir(targetDir, { recursive: true });
        const finalFilename = targetFilename || path.basename(sourcePath);
        const destinationPath = path.join(targetDir, finalFilename);
        await fsPromises.copyFile(sourcePath, destinationPath);
        const relativePath = path.join(targetSubdir, finalFilename).replace(/\\/g, '/');
        return relativePath;
    } catch (error: any) { return { error: error.message }; }
});
ipcMain.handle('fs:exportChatHistory', async (event, messages, format, defaultFileName) => {
  const { filePath } = await dialog.showSaveDialog(mainWindow!, { title: '保存聊天记录', defaultPath: `${defaultFileName}_${new Date().toISOString().split('T')[0]}.${format}`, filters: [ format === 'md' ? { name: 'Markdown', extensions: ['md'] } : { name: 'HTML', extensions: ['html'] } ] });
  if (filePath) {
    try { 
        let content = '';
      if (format === 'md') { content = messages.map(msg => `**${msg.senderName}** (${new Date(msg.timestamp).toLocaleString()}):\n\n${msg.text.replace(/<br\s*\/?>/gi, '\n').replace(/<[^>]+>/g, '')}\n\n---\n`).join(''); } 
      else { content = `<html>...<body><h1>聊天记录</h1>${messages.map(msg => `<div class="message ${msg.sender}"><p><span class="sender">${msg.senderName}</span> <span class="timestamp">(${new Date(msg.timestamp).toLocaleString()})</span>:</p><div>${msg.text}</div></div>`).join('')}</body></html>`; }
      await fsPromises.writeFile(filePath, content, 'utf-8'); return { success: true, path: filePath };
    } catch (error: any) { return { success: false, error: error.message }; }
  }
  return { success: false, error: '用户取消保存。' };
});
ipcMain.handle('rag:runProjectIndexing', async (event, projectId, projectPath, options) => {
    if (!ragService) { console.error("IPC_MAIN_ERROR: rag:runProjectIndexing - ragService not loaded."); return { success: false, error: "RAG service not available."}; }
    return await ragService.runProjectIndexing(projectId, projectPath, options);
});
ipcMain.handle('rag:retrieveRelevantChunks', async (event, queryText, projectId, apiKey, embeddingModelName, topK) => {
    if (!ragService) { console.error("IPC_MAIN_ERROR: rag:retrieveRelevantChunks - ragService not loaded."); return { success: false, error: "RAG service not available."}; }
    return await ragService.retrieveRelevantChunks(queryText, projectId, apiKey, embeddingModelName, topK);
});
ipcMain.handle('rag:indexFileContent', async (event, projectId, filePath, fileContent, options) => {
    if (!ragService || typeof ragService.indexFileContent !== 'function') {
        console.error("IPC_MAIN_ERROR: rag:indexFileContent - ragService or indexFileContent function not loaded.");
        return { success: false, error: "RAG File Indexing service not available." };
    }
    return await ragService.indexFileContent(projectId, filePath, fileContent, options);
});

// --- New IPC Handlers for Sandbox and Territory ---
ipcMain.handle('ai:invoke-sandbox-request', async (event, args) => {
  console.log("IPC_MAIN (ai:invoke-sandbox-request): Received request. Args (brief):", { promptLength: args?.prompt?.length, historyLength: args?.history?.length, persona: args?.persona, projectId: args?.projectId, contextType: args?.otherSandboxContext?.contextType });
  if (!aiOrchestrationService) { 
    console.error("IPC_MAIN_ERROR (ai:invoke-sandbox-request): aiOrchestrationService not available.");
    return { text: "AI Orchestration service not available.", status: null }; 
  }
  const context = { 
    ...(args.otherSandboxContext || {}), 
    projectId: args.projectId, 
    contextType: 'workspace',
    history: args.history 
  };
  console.log("IPC_MAIN (ai:invoke-sandbox-request): Constructed context for callAI:", JSON.stringify(context, (key, value) => key === 'history' ? `[${value?.length} messages]` : value, 2));
  
  try {
    const result = await aiOrchestrationService.callAI(args.prompt, args.persona || 'LinLuo', context);
    console.log("IPC_MAIN (ai:invoke-sandbox-request): Result from callAI:", JSON.stringify(result, null, 2));
    return result;
  } catch (e: any) {
    console.error("IPC_MAIN_ERROR (ai:invoke-sandbox-request): Error during callAI:", e.message, e.stack);
    return { text: `Sandbox AI call failed: ${e.message}`, status: null };
  }
});

ipcMain.handle('ai:invoke-territory-request', async (event, args) => {
  console.log("IPC_MAIN: Handling ai:invoke-territory-request", args);
  if (!aiOrchestrationService) { return { text: "AI Orchestration service not available.", status: null }; }
  const context = { 
    ...args.otherTerritoryContext, 
    contextType: 'training_room',
    history: args.history
  };
  const result = await aiOrchestrationService.callAI(args.prompt, args.persona || 'LinLuo', context);
  return result;
});

// --- Existing AI IPC Handlers (some might be deprecated or used by other parts) ---
ipcMain.handle('ai:discussWithAI', async (event, contents, config, modelName, apiKey) => {
  if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: ai:discussWithAI - aiOrchestrationService not loaded."); return "AI Orchestration service not available."; }
  return await aiOrchestrationService.executeRawLLMQuery(contents, config, modelName, apiKey);
});
ipcMain.handle('ai:getAvailableModels', async () => {
  if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: ai:getAvailableModels - aiOrchestrationService not loaded."); return []; }
  return await aiOrchestrationService.getAvailableModels();
});
ipcMain.handle('ai:callAI', async (event, prompt, persona, context) => {
  console.warn("IPC_MAIN: Generic 'ai:callAI' was called. Ensure this is intentional and not from refactored chat components.");
  if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: ai:callAI - aiOrchestrationService not loaded."); return { text: "AI Orchestration service not available.", status: null }; }
  return await aiOrchestrationService.callAI(prompt, persona, context);
});
ipcMain.handle('ai:routeUserIntent', async (event, userInputText, context) => {
  console.log(`IPC_MAIN: Handling ai:routeUserIntent. Input: "${userInputText.substring(0,50)}..."`);
  if (!aiOrchestrationService) {
    console.error("IPC_MAIN_ERROR: ai:routeUserIntent - aiOrchestrationService not loaded.");
    return { type: 'error', data: { message: "AI 路由服务不可用。" } };
  }
  try {
    return await aiOrchestrationService.routeUserIntent(userInputText, context);
  } catch (error: any) {
    console.error("IPC_MAIN_ERROR: Error in ai:routeUserIntent handler:", error);
    return { type: 'error', data: { message: `处理意图时发生错误: ${error.message}` } };
  }
});
ipcMain.handle('ai:summarizeConversation', async (event, historyChunk) => {
  if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: ai:summarizeConversation - aiOrchestrationService not loaded."); return "AI Orchestration service not available."; }
  return await aiOrchestrationService.summarizeConversation(historyChunk);
});
ipcMain.handle('ai:decomposeRequirementToTasks', async (event, requirementText, projectId) => {
  if (!aiOrchestrationService) {  console.error("IPC_MAIN_ERROR: ai:decomposeRequirementToTasks - aiOrchestrationService not loaded.");  return { success: false, error: "AI Orchestration service not available.", tasks: [] }; }
  return await aiOrchestrationService.decomposeRequirementToTasks(requirementText, projectId);
});
ipcMain.handle('ai:analyzeAndDecomposeAideProject', async (event, projectId, projectPath) => {
  if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: ai:analyzeAndDecomposeAideProject - aiOrchestrationService not loaded."); return { success: false, error: "AI Orchestration service not available.", tasks: [] }; }
  try { return await aiOrchestrationService.analyzeAndDecomposeAideProject(projectId, projectPath); } 
  catch (e: any) { console.error(`IPC_MAIN_ERROR: Error in ai:analyzeAndDecomposeAideProject handler:`, e); return { success: false, error: e.message || "Unknown error during AIDE project analysis.", tasks: [] }; }
});
ipcMain.handle('bridgeAi:processIntent', async (event, userInputText) => {
    if (!aiOrchestrationService) { console.error("IPC_MAIN_ERROR: bridgeAi:processIntent - Main aiOrchestrationService not loaded."); return "AI编排服务不可用。"; }
    try {
        const response = await aiOrchestrationService.callAI(userInputText, 'OrchestratorPersona', { contextType: 'file_orchestration' });

        if (response && response.json && response.json.command) {
            return response.text || (response.json.error ? `AI指令解析错误: ${response.json.error}` : "操作已尝试执行，但无明确文本反馈。");
        } else if (response && typeof response.text === 'string') {
            return response.text;
        }
        return "处理完成，但AI未返回明确文本结果或有效JSON指令。";

    } catch (error: any) { console.error(`IPC_MAIN_ERROR: bridgeAi:processIntent failed:`, error); return `处理舰桥指令时发生错误: ${error.message}`; }
});

ipcMain.handle('ai:startRoundtableMeeting', async (event, initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory) => {
    if (!aiOrchestrationService || typeof aiOrchestrationService.startRoundtableMeeting !== 'function') {
        console.error("IPC_MAIN_ERROR: ai:startRoundtableMeeting - aiOrchestrationService or startRoundtableMeeting not loaded.");
        return { success: false, error: "圆桌会议服务不可用。" };
    }
    try {
        const result = await aiOrchestrationService.startRoundtableMeeting(initialPrompt, participantCharacterIds, turnLimit, projectId, initialHistory, mainWindow?.webContents);
        return result;
    } catch (error: any) {
        console.error("IPC_MAIN_ERROR: Error in ai:startRoundtableMeeting handler:", error);
        return { success: false, error: error.message || "启动圆桌会议时发生未知错误。" };
    }
});


ipcMain.handle('audio:playSound', async (event, soundName, loop) => {
  if (!audioService) { console.error("IPC_MAIN_ERROR: audio:playSound - audioService not loaded."); return; }
  return await audioService.playSound(soundName, loop);
});
ipcMain.handle('audio:stopSound', async (event, soundName) => {
  if (!audioService) { console.error("IPC_MAIN_ERROR: audio:stopSound - audioService not loaded."); return; }
  return await audioService.stopSound(soundName);
});
ipcMain.handle('audio:setVolume', async (event, volume) => {
  if (!audioService) { console.error("IPC_MAIN_ERROR: audio:setVolume - audioService not loaded."); return; }
  return await audioService.setVolume(volume);
});
ipcMain.handle('audio:getPlaybackState', async () => {
  if (!audioService) { console.error("IPC_MAIN_ERROR: audio:getPlaybackState - audioService not loaded."); return { currentSound: null, volume: 0.5, isLooping: false, isPlaying: false }; }
  return await audioService.getPlaybackState();
});
ipcMain.handle('electronUtils:getPlatform', async () => { return os.platform(); });
ipcMain.handle('utils:combinePaths', (event, basePath, relativePath) => {
  return path.join(basePath, relativePath);
});
ipcMain.handle('assets:getLoadedAssets', async () => {
  if (!universalAssetService) { console.error("IPC_MAIN_ERROR: assets:getLoadedAssets - universalAssetService not loaded."); return { props: [], costumes: [], poses: [], scene_cards: [], achievements: [], scripts: [], role_cards: [] }; }

  try {
    // 获取资产包中的资产
    const assetPackAssets = universalAssetService.getAllLoadedAssets();

    // 获取 CMS 数据库中的资产
    const cmsProps = await cmsDbService.getCMSItems('props');
    const cmsCostumes = await cmsDbService.getCMSItems('costumes');
    const cmsPoses = await cmsDbService.getCMSItems('poses');

    // 合并资产，CMS 数据库中的资产优先
    const mergedAssets = {
      ...assetPackAssets,
      props: [...(cmsProps || []), ...(assetPackAssets.props || [])],
      costumes: [...(cmsCostumes || []), ...(assetPackAssets.costumes || [])],
      poses: [...(cmsPoses || []), ...(assetPackAssets.poses || [])]
    };

    return mergedAssets;
  } catch (error: any) {
    console.error("IPC_MAIN_ERROR: assets:getLoadedAssets - Error merging assets:", error.message);
    return universalAssetService.getAllLoadedAssets();
  }
});
ipcMain.handle('assets:refreshAssetPacks', async () => {
  if (!universalAssetService) { console.error("IPC_MAIN_ERROR: assets:refreshAssetPacks - universalAssetService not loaded."); return { success: false, error: "Asset service not available." }; }
  try { await universalAssetService.refreshAssetPacks(); return { success: true }; }
  catch (e: any) { return { success: false, error: e.message }; }
});

ipcMain.handle('assets:createAssetPack', async (event, fileName: string, assetPackData: any) => {
  try {
    const yaml = require('js-yaml');
    const fs = require('fs').promises;
    const path = require('path');

    // 获取用户数据目录中的 asset_packs 路径
    const assetPacksPath = path.join(app.getPath('userData'), 'asset_packs');

    // 确保目录存在
    await fs.mkdir(assetPacksPath, { recursive: true });

    // 创建 YAML 文件
    const filePath = path.join(assetPacksPath, fileName);
    const yamlContent = yaml.dump(assetPackData, {
      indent: 2,
      lineWidth: -1,
      noRefs: true,
      quotingType: '"',
      forceQuotes: false
    });

    await fs.writeFile(filePath, yamlContent, 'utf8');
    console.log(`Asset pack created: ${filePath}`);

    return { success: true, filePath };
  } catch (error: any) {
    console.error('Error creating asset pack:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('assets:deleteAssetPack', async (event, fileName: string) => {
  try {
    const fs = require('fs').promises;
    const path = require('path');

    const assetPacksPath = path.join(app.getPath('userData'), 'asset_packs');
    const filePath = path.join(assetPacksPath, fileName);

    await fs.unlink(filePath);
    console.log(`Asset pack deleted: ${filePath}`);

    return { success: true };
  } catch (error: any) {
    console.error('Error deleting asset pack:', error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle('command:execute', async (event, { commandString, args, cwd }) => {
  if (!commandExecutionService || !mainWindow) { console.error("IPC_MAIN_ERROR: command:execute - commandExecutionService or mainWindow not loaded."); return { error: "Command execution service or main window not available." }; }
  const internalPid = crypto.randomUUID();
  commandExecutionService.executeCommand( internalPid, commandString, args, cwd,
    (pid, systemPid, data) => { if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) mainWindow.webContents.send('command:event', { internalPid: pid, systemPid, type: 'stdout', data }); },
    (pid, systemPid, data) => { if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) mainWindow.webContents.send('command:event', { internalPid: pid, systemPid, type: 'stderr', data }); },
    (pid, systemPid, code, signal, errorMsg) => { if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) { if (errorMsg) mainWindow.webContents.send('command:event', { internalPid: pid, systemPid, type: 'error_event', error: errorMsg, code: code ?? 1 }); else mainWindow.webContents.send('command:event', { internalPid: pid, systemPid, type: 'exit', code, signal }); } }
  );
  return { internalPid };
});
ipcMain.handle('command:kill', async (event, internalPid) => {
  if (!commandExecutionService) { console.error("IPC_MAIN_ERROR: command:kill - commandExecutionService not loaded."); return { success: false, error: "Command execution service not available." }; }
  return { success: commandExecutionService.killCommand(internalPid) };
});
ipcMain.handle('command:analyze-log', async(event, logContent: string) => {
    if (!aiOrchestrationService || typeof aiOrchestrationService.analyzeErrorLogFromService !== 'function') { console.error("IPC_MAIN_ERROR: command:analyze-log - aiOrchestrationService or analyzeErrorLogFromService not available."); return "AI日志分析服务不可用。"; }
    try { return await aiOrchestrationService.analyzeErrorLogFromService(logContent); } 
    catch (e: any) { console.error("IPC_MAIN_ERROR: Error in command:analyze-log handler:", e); return `AI分析错误日志时发生意外: ${e.message}`; }
});

// --- Organization Service IPC Handlers ---
ipcMain.handle('org:getPosts', async () => {
    if (!organizationService) { console.error("IPC_MAIN_ERROR: org:getPosts - organizationService not loaded."); return []; }
    return organizationService.getPosts(); 
});
ipcMain.handle('org:getCharacters', async () => {
    if (!organizationService) { console.error("IPC_MAIN_ERROR: org:getCharacters - organizationService not loaded."); return []; }
    return await organizationService.getCharacters(); 
});
ipcMain.handle('org:getAssignments', async () => {
    if (!organizationService) { console.error("IPC_MAIN_ERROR: org:getAssignments - organizationService not loaded."); return []; }
    return organizationService.getAssignments(); 
});
ipcMain.handle('org:setAssignment', async (event, postId, characterId) => {
    if (!organizationService) { console.error("IPC_MAIN_ERROR: org:setAssignment - organizationService not loaded."); return { success: false, error: "Organization service not available."}; }
    return organizationService.setAssignment(postId, characterId); 
});
ipcMain.handle('org:getAssignmentByPostId', async (event, postId) => {
    if (!organizationService) { console.error("IPC_MAIN_ERROR: org:getAssignmentByPostId - organizationService not loaded."); return null;}
    return organizationService.getAssignmentByPostId(postId); 
});


console.log('ELECTRON MAIN: All IPC Handlers registered.');