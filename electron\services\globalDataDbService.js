

// electron/services/globalDataDbService.js
console.log('GLOBAL_DATA_DB_SERVICE_JS: File execution started.');

import { db, parseJsonArray, crypto } from './databaseCore'; // crypto needed for new IDs if not passed

export function getAllGlobalKnowledgeTomes() {
    if (!db) { console.error("GLOBAL_DB_ERROR: getAllGlobalKnowledgeTomes - db not available."); return []; }
    try {
        const tomesRaw = db.prepare('SELECT * FROM global_knowledge_tomes ORDER BY lastModifiedAt DESC').all();
        console.log(`GLOBAL_DB: Retrieved ${tomesRaw.length} global knowledge tomes.`);
        return tomesRaw.map(t => ({...t, tags: parseJsonArray(t.tags)}));
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: getting all global tomes:', e);
        return [];
    }
}

export function addGlobalKnowledgeTome(tome) {
    if (!db) { console.error("GLOBAL_DB_ERROR: addGlobalKnowledgeTome - db not available."); return null; }
    const stmt = db.prepare('INSERT INTO global_knowledge_tomes (id, title, content, category, tags, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?, ?, ?)');
    try {
        // Ensure tome has an ID, createdAt, lastModifiedAt if not provided
        const tomeToSave = {
            id: tome.id || crypto.randomUUID(),
            createdAt: tome.createdAt || new Date().toISOString(),
            lastModifiedAt: tome.lastModifiedAt || new Date().toISOString(),
            ...tome
        };
        stmt.run(tomeToSave.id, tomeToSave.title, tomeToSave.content, tomeToSave.category, JSON.stringify(tomeToSave.tags || []), tomeToSave.createdAt, tomeToSave.lastModifiedAt);
        console.log(`GLOBAL_DB: Added global knowledge tome ${tomeToSave.id}.`);
        return tomeToSave;
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: adding global tome:',e);
        return null;
    }
}

export function updateGlobalKnowledgeTome(tome) {
    if (!db) { console.error("GLOBAL_DB_ERROR: updateGlobalKnowledgeTome - db not available."); return null; }
    const stmt = db.prepare('UPDATE global_knowledge_tomes SET title = ?, content = ?, category = ?, tags = ?, lastModifiedAt = ? WHERE id = ?');
    try {
        const tomeToUpdate = { ...tome, lastModifiedAt: new Date().toISOString() };
        stmt.run(tomeToUpdate.title, tomeToUpdate.content, tomeToUpdate.category, JSON.stringify(tomeToUpdate.tags || []), tomeToUpdate.lastModifiedAt, tomeToUpdate.id);
        console.log(`GLOBAL_DB: Updated global knowledge tome ${tomeToUpdate.id}.`);
        return tomeToUpdate;
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: updating global tome:',e);
        return null;
    }
}

export function deleteGlobalKnowledgeTome(tomeId) {
    if (!db) { console.error("GLOBAL_DB_ERROR: deleteGlobalKnowledgeTome - db not available."); return { success: false, error: "Database not available." }; }
    const stmt = db.prepare('DELETE FROM global_knowledge_tomes WHERE id = ?');
    try {
        stmt.run(tomeId);
        console.log(`GLOBAL_DB: Deleted global knowledge tome ${tomeId}.`);
        return { success: true };
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: deleting global tome:',e);
        return { success: false, error: e.message };
    }
}

export function getAllGlobalQuickCommands() {
    if (!db) { console.error("GLOBAL_DB_ERROR: getAllGlobalQuickCommands - db not available."); return []; }
    try { 
        const commands = db.prepare('SELECT * FROM global_quick_commands ORDER BY title ASC').all();
        console.log(`GLOBAL_DB: Retrieved ${commands.length} global quick commands.`);
        return commands;
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: getting all global commands:',e);
        return [];
    }
}

export function addGlobalQuickCommand(cmd) {
    if (!db) { console.error("GLOBAL_DB_ERROR: addGlobalQuickCommand - db not available."); return null; }
    const stmt = db.prepare('INSERT INTO global_quick_commands (id, title, commandText, createdAt, lastModifiedAt) VALUES (?, ?, ?, ?, ?)');
    try {
        const cmdToSave = {
            id: cmd.id || crypto.randomUUID(),
            createdAt: cmd.createdAt || new Date().toISOString(),
            lastModifiedAt: cmd.lastModifiedAt || new Date().toISOString(),
            ...cmd
        };
        stmt.run(cmdToSave.id, cmdToSave.title, cmdToSave.commandText, cmdToSave.createdAt, cmdToSave.lastModifiedAt);
        console.log(`GLOBAL_DB: Added global quick command ${cmdToSave.id}.`);
        return cmdToSave;
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: adding global command:',e);
        return null;
    }
}

export function updateGlobalQuickCommand(cmd) {
    if (!db) { console.error("GLOBAL_DB_ERROR: updateGlobalQuickCommand - db not available."); return null; }
    const stmt = db.prepare('UPDATE global_quick_commands SET title = ?, commandText = ?, lastModifiedAt = ? WHERE id = ?');
    try {
        const cmdToUpdate = { ...cmd, lastModifiedAt: new Date().toISOString() };
        stmt.run(cmdToUpdate.title, cmdToUpdate.commandText, cmdToUpdate.lastModifiedAt, cmdToUpdate.id);
        console.log(`GLOBAL_DB: Updated global quick command ${cmdToUpdate.id}.`);
        return cmdToUpdate;
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: updating global command:',e);
        return null;
    }
}

export function deleteGlobalQuickCommand(commandId) {
    if (!db) { console.error("GLOBAL_DB_ERROR: deleteGlobalQuickCommand - db not available."); return { success: false, error: "Database not available." }; }
    const stmt = db.prepare('DELETE FROM global_quick_commands WHERE id = ?');
    try {
        stmt.run(commandId);
        console.log(`GLOBAL_DB: Deleted global quick command ${commandId}.`);
        return { success: true };
    } catch (e) {
        console.error('GLOBAL_DB_ERROR: deleting global command:',e);
        return { success: false, error: e.message };
    }
}

console.log('GLOBAL_DATA_DB_SERVICE_JS: File execution finished. Exports configured.');