// src/components/NotesList.tsx
import React, { useState } from 'react';
import type { NoteItem, ImportanceLevel } from '@/types';
import { WisdomPouchType } from '@/types'; // WisdomPouchType is now both value and type
import { Icon } from '@/components/common/Icon';

interface NotesListProps {
  title: string;
  notes: NoteItem[];
  addNote: (text: string, importance?: ImportanceLevel) => void;
  updateNote: (note: NoteItem) => void;
  deleteNote: (noteId: string) => void;
  icon: React.ReactNode; 
  pouchType: WisdomPouchType; // Use WisdomPouchType directly as type
  onSendNoteToChat: (noteText: string) => void;
}

export const NotesList: React.FC<NotesListProps> = ({ 
    title, notes, addNote, updateNote, deleteNote, icon, pouchType, onSendNoteToChat 
}) => {
  const [newNoteText, setNewNoteText] = useState('');
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [currentImportance, setCurrentImportance] = useState<ImportanceLevel>('medium');

  const canSetImportance = pouchType === WisdomPouchType.INSPIRATION || pouchType === WisdomPouchType.BUGS;

  const handleAddNote = () => {
    if (newNoteText.trim()) {
      addNote(newNoteText.trim(), canSetImportance ? currentImportance : undefined);
      setNewNoteText('');
      setCurrentImportance('medium'); 
    }
  };

  const handleEdit = (note: NoteItem) => {
    setEditingNoteId(note.id);
    setEditText(note.text);
    if (canSetImportance) {
      setCurrentImportance(note.importance || 'medium');
    }
  };

  const handleSaveEdit = (noteId: string) => {
    if (editText.trim()) {
      const originalNote = notes.find(n => n.id === noteId);
      if (originalNote) {
        updateNote({ 
          ...originalNote, 
          text: editText.trim(), 
          importance: canSetImportance ? currentImportance : originalNote.importance 
        });
      }
    }
    setEditingNoteId(null);
    setEditText('');
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setEditText('');
  };

  const handleDelete = (noteId: string) => {
    if (window.confirm("确定要删除此条目吗？")) {
      deleteNote(noteId);
    }
  };

  const handleSendToChat = (text: string) => {
    onSendNoteToChat(text);
  };

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('内容已复制到剪贴板！');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      alert('复制失败。');
    }
  };
  
  const getAccentColorVariable = () => {
    switch(pouchType) {
      case WisdomPouchType.INSPIRATION: return 'var(--color-accent-primary)';
      case WisdomPouchType.BUGS: return 'var(--color-danger)';
      case WisdomPouchType.COMMANDS: return 'var(--color-success)';
      default: return 'var(--color-border-primary)';
    }
  };

  const getImportanceStyling = (importance?: ImportanceLevel): { color: string, text: string, borderColor: string } => {
    switch (importance) {
      case 'high': return { color: 'text-red-400', text: '高', borderColor: 'border-red-500' };
      case 'medium': return { color: 'text-yellow-400', text: '中', borderColor: 'border-yellow-500' };
      case 'low': return { color: 'text-sky-400', text: '低', borderColor: 'border-sky-500' };
      default: return { color: 'text-gray-400', text: '常规', borderColor: 'border-gray-500' };
    }
  };

  const importanceOptions: ImportanceLevel[] = ['high', 'medium', 'low'];

  return (
    <div className="flex flex-col h-full text-sm">
      <h4 className="text-md font-semibold mb-2 flex items-center text-tg-text-primary">
        {icon} 
        {title}
      </h4>
      
      <div className="mb-3">
        <textarea
          value={newNoteText}
          onChange={e => setNewNoteText(e.target.value)}
          placeholder="记录新条目..."
          rows={2}
          className="w-full p-2 bg-tg-bg-tertiary text-tg-text-primary border border-tg-border-primary rounded-md focus:border-tg-accent-primary text-xs"
        />
        <div className="flex items-center justify-between mt-1.5">
          {canSetImportance && (
            <select 
              value={currentImportance} 
              onChange={e => setCurrentImportance(e.target.value as ImportanceLevel)}
              className="p-1.5 bg-tg-bg-tertiary text-tg-text-secondary border border-tg-border-primary rounded-md text-xs focus:border-tg-accent-primary"
            >
              {importanceOptions.map(level => (
                <option key={level} value={level}>{getImportanceStyling(level).text}</option>
              ))}
            </select>
          )}
          <button 
            onClick={handleAddNote} 
            className="px-3 py-1.5 bg-tg-accent-primary text-white rounded-md hover:bg-tg-accent-primary-hover text-xs flex items-center"
            style={{backgroundColor: getAccentColorVariable()}}
            disabled={!newNoteText.trim()}
          >
            <Icon name="PlusCircle" className="w-4 h-4 mr-1" /> 添加
          </button>
        </div>
      </div>

      {notes.length === 0 ? (
        <p className="text-xs text-tg-text-placeholder text-center py-4">此锦囊暂无记录。</p>
      ) : (
        <ul className="space-y-2 overflow-y-auto flex-grow pr-1 custom-scrollbar">
          {notes.map(note => (
            <li 
              key={note.id} 
              className={`p-2.5 rounded-md shadow-sm border-l-4 bg-tg-bg-tertiary text-tg-text-primary text-xs
                          ${getImportanceStyling(note.importance).borderColor}`}
            >
              {editingNoteId === note.id ? (
                <>
                  <textarea 
                    value={editText} 
                    onChange={e => setEditText(e.target.value)} 
                    className="w-full p-1.5 mb-1 bg-tg-bg-primary border border-tg-border-interactive rounded-md focus:border-tg-accent-primary" 
                    rows={3}
                  />
                  {canSetImportance && (
                    <select 
                      value={currentImportance} 
                      onChange={e => setCurrentImportance(e.target.value as ImportanceLevel)}
                      className="w-full p-1 mb-1.5 bg-tg-bg-primary border border-tg-border-interactive rounded-md text-xs"
                    >
                      {importanceOptions.map(level => (
                        <option key={level} value={level}>{getImportanceStyling(level).text}</option>
                      ))}
                    </select>
                  )}
                  <div className="flex space-x-1.5 text-xs">
                    <button onClick={() => handleSaveEdit(note.id)} className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">保存</button>
                    <button onClick={handleCancelEdit} className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">取消</button>
                  </div>
                </>
              ) : (
                <>
                  <p className="whitespace-pre-wrap break-words mb-1.5 leading-relaxed">{note.text}</p>
                  <div className="flex items-center justify-between text-[11px] text-tg-text-placeholder">
                    {canSetImportance && (
                      <span className={`${getImportanceStyling(note.importance).color} font-medium`}>
                        重要性: {getImportanceStyling(note.importance).text}
                      </span>
                    )}
                    <span>{new Date(note.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="mt-1.5 pt-1 border-t border-tg-border-primary/50 flex items-center space-x-1">
                    <button onClick={() => handleEdit(note)} className="p-1 hover:bg-tg-bg-hover rounded" title="编辑"><Icon name="Pencil" className="w-3.5 h-3.5 text-tg-warning"/></button>
                    <button onClick={() => handleDelete(note.id)} className="p-1 hover:bg-tg-bg-hover rounded" title="删除"><Icon name="Trash2" className="w-3.5 h-3.5 text-tg-danger"/></button>
                    <button onClick={() => handleCopyToClipboard(note.text)} className="p-1 hover:bg-tg-bg-hover rounded" title="复制"><Icon name="Copy" className="w-3.5 h-3.5 text-tg-accent-secondary"/></button>
                    <button onClick={() => handleSendToChat(note.text)} className="p-1 hover:bg-tg-bg-hover rounded" title="发送至研讨区"><Icon name="SendHorizontal" className="w-3.5 h-3.5 text-tg-accent-primary"/></button>
                  </div>
                </>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
